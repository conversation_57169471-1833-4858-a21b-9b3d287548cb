import { useId } from "react";
import { <PERSON>, <PERSON><PERSON>hart, CartesianGrid, XAxis, <PERSON>Axis } from "recharts";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "../ui/card";
import {
  type ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "../ui/chart";
import { Badge } from "../ui/badge";
import { useAnalytics } from "../../hooks/use-analytics";
import { Skeleton } from "../ui/skeleton";
import { TrendingUp, Users, Building2 } from "lucide-react";

const chartConfig = {
  users: {
    label: "Usuários",
    color: "hsl(var(--chart-1))",
  },
  clients: {
    label: "Clientes",
    color: "hsl(var(--chart-3))",
  },
} satisfies ChartConfig;

export function DashboardGrowthChart() {
  const id = useId();
  const { data: analytics, isLoading } = useAnalytics();

  // Usar dados reais do analytics
  const chartData = analytics?.chartData || [];

  // Calcular totais e crescimento baseados em dados reais
  const totalUsers = analytics?.totalUsers || 0;
  const totalClients = analytics?.totalClients || 0;

  const usersGrowth = analytics?.userGrowth?.toFixed(1) || "0";
  const clientsGrowth = analytics?.clientGrowth?.toFixed(1) || "0";

  const firstMonth = chartData[0]?.month as string;
  const lastMonth = chartData[chartData.length - 1]?.month as string;

  if (isLoading) {
    return <Skeleton className="h-[400px] w-full" />;
  }

  const hasData = chartData.length > 0;

  if (!hasData) {
    return (
      <Card className="gap-4">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Crescimento de Base
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-60 text-muted-foreground">
            <div className="text-center">
              <p className="text-lg font-medium">Não há dados disponíveis</p>
              <p className="text-sm">Dados de analytics ainda não foram coletados</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="gap-4">
      <CardHeader>
        <div className="flex flex-wrap items-center justify-between gap-3">
          <div className="space-y-0.5">
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Crescimento de Base
            </CardTitle>
            <div className="flex items-start gap-4">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-chart-1" />
                <div>
                  <div className="font-semibold text-lg">{totalUsers.toLocaleString('pt-BR')}</div>
                  <div className="text-xs text-muted-foreground">Usuários</div>
                </div>
                <Badge className={`border-none ${
                  parseFloat(usersGrowth) >= 0
                    ? 'bg-emerald-500/24 text-emerald-500'
                    : 'bg-red-500/24 text-red-500'
                }`}>
                  {parseFloat(usersGrowth) >= 0 ? '+' : ''}{usersGrowth}%
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <Building2 className="h-4 w-4 text-chart-3" />
                <div>
                  <div className="font-semibold text-lg">{totalClients.toLocaleString('pt-BR')}</div>
                  <div className="text-xs text-muted-foreground">Clientes</div>
                </div>
                <Badge className={`border-none ${
                  parseFloat(clientsGrowth) >= 0
                    ? 'bg-emerald-500/24 text-emerald-500'
                    : 'bg-red-500/24 text-red-500'
                }`}>
                  {parseFloat(clientsGrowth) >= 0 ? '+' : ''}{clientsGrowth}%
                </Badge>
              </div>
            </div>
          </div>
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center gap-2">
              <div
                aria-hidden="true"
                className="size-1.5 shrink-0 rounded-xs bg-chart-1"
              />
              <div className="text-[13px]/3 text-muted-foreground/50">
                Usuários
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div
                aria-hidden="true"
                className="size-1.5 shrink-0 rounded-xs bg-chart-3"
              />
              <div className="text-[13px]/3 text-muted-foreground/50">
                Clientes
              </div>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={chartConfig}
          className="aspect-auto h-60 w-full [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-[var(--chart-1)]/15 [&_.recharts-rectangle.recharts-tooltip-inner-cursor]:fill-white/20"
        >
          <LineChart
            accessibilityLayer
            data={chartData}
            margin={{ left: -12, right: 12, top: 12 }}
          >
            <defs>
              <linearGradient id={`${id}-gradient`} x1="0" y1="0" x2="1" y2="0">
                <stop offset="0%" stopColor="var(--chart-2)" />
                <stop offset="100%" stopColor="var(--chart-1)" />
              </linearGradient>
            </defs>
            <CartesianGrid
              vertical={false}
              strokeDasharray="2 2"
              stroke="var(--border)"
            />
            <XAxis
              dataKey="month"
              tickLine={false}
              tickMargin={12}
              tickFormatter={(value) => String(value).slice(0, 3)}
              stroke="var(--border)"
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tickFormatter={(value) => {
                if (value === 0) return "0";
                return `${(value / 1000).toFixed(0)}k`;
              }}
              interval="preserveStartEnd"
            />
            <Line
              type="linear"
              dataKey="users"
              stroke="var(--color-users)"
              strokeWidth={3}
              dot={{
                fill: "var(--color-users)",
                strokeWidth: 2,
                r: 4,
              }}
              activeDot={{
                r: 6,
                stroke: "var(--color-users)",
                strokeWidth: 2,
              }}
            />
            <Line
              type="linear"
              dataKey="clients"
              stroke="var(--color-clients)"
              strokeWidth={3}
              dot={{
                fill: "var(--color-clients)",
                strokeWidth: 2,
                r: 4,
              }}
              activeDot={{
                r: 6,
                stroke: "var(--color-clients)",
                strokeWidth: 2,
              }}
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  labelFormatter={(value) => value}
                  formatter={(value, name) => [
                    Number(value).toLocaleString('pt-BR'),
                    name === 'users' ? 'Usuários' : 'Clientes'
                  ]}
                />
              }
            />
          </LineChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
