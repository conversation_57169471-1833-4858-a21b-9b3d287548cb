/**
 * Script para verificar atributos específicos das collections
 */

require('dotenv').config({ path: '../.env' });
const { Client, Databases } = require('node-appwrite');

// Configuração do cliente Appwrite
const client = new Client();
client
  .setEndpoint(process.env.APPWRITE_ENDPOINT)
  .setProject(process.env.APPWRITE_PROJECT_ID)
  .setKey(process.env.APPWRITE_API_KEY);

const databases = new Databases(client);
const DATABASE_ID = process.env.APPWRITE_DATABASE_ID;

async function checkAttributes() {
  try {
    console.log('🔍 Verificando atributos das collections...\n');

    // Collections que estão causando problemas
    const collectionsToCheck = [
      { name: 'clients', id: process.env.NEXT_PUBLIC_APPWRITE_CLIENTS_ID },
      { name: 'notifications', id: process.env.NEXT_PUBLIC_APPWRITE_NOTIFICATIONS_ID },
      { name: 'public_profiles', id: process.env.NEXT_PUBLIC_APPWRITE_PUBLIC_PROFILES_ID },
      { name: 'activity_logs', id: process.env.NEXT_PUBLIC_APPWRITE_ACTIVITY_LOGS_ID },
      { name: 'team_chats', id: process.env.NEXT_PUBLIC_APPWRITE_TEAM_CHATS_ID },
      { name: 'chat_messages', id: process.env.NEXT_PUBLIC_APPWRITE_CHAT_MESSAGES_ID },
      { name: 'events', id: process.env.NEXT_PUBLIC_APPWRITE_EVENTS_ID },
      { name: 'event_categories', id: process.env.NEXT_PUBLIC_APPWRITE_EVENT_CATEGORIES_ID },
      { name: 'kanban_boards', id: process.env.NEXT_PUBLIC_APPWRITE_KANBAN_BOARDS_ID },
      { name: 'kanban_columns', id: process.env.NEXT_PUBLIC_APPWRITE_KANBAN_COLUMNS_ID },
      { name: 'kanban_tasks', id: process.env.NEXT_PUBLIC_APPWRITE_KANBAN_TASKS_ID },
    ];

    for (const collection of collectionsToCheck) {
      console.log(`📋 Collection: ${collection.name} (${collection.id})`);
      
      try {
        const response = await databases.getCollection(DATABASE_ID, collection.id);
        
        console.log(`   Total de atributos: ${response.attributes.length}`);
        
        // Verificar atributos específicos que podem estar causando problemas
        const hasUserId = response.attributes.find(attr => attr.key === 'userId');
        const hasTeamId = response.attributes.find(attr => attr.key === 'teamId');
        const hasIsDeleted = response.attributes.find(attr => attr.key === 'isDeleted');
        
        console.log(`   ✓ userId: ${hasUserId ? '✅' : '❌'}`);
        console.log(`   ✓ teamId: ${hasTeamId ? '✅' : '❌'}`);
        console.log(`   ✓ isDeleted: ${hasIsDeleted ? '✅' : '❌'}`);
        
        // Listar todos os atributos
        console.log('   Atributos:');
        response.attributes.forEach(attr => {
          console.log(`     - ${attr.key} (${attr.type})`);
        });
        
      } catch (error) {
        console.log(`   ❌ Erro ao acessar collection: ${error.message}`);
      }
      
      console.log('');
    }

  } catch (error) {
    console.error('❌ Erro geral:', error);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  checkAttributes();
}

module.exports = { checkAttributes };
