/**
 * Exemplo de uso do sistema de Import/Export de dados
 * Demonstra como usar as funcionalidades programaticamente
 *
 * NOTA: Este é um arquivo de exemplo para demonstração.
 * Alguns imports podem não estar disponíveis em desenvolvimento.
 * Use como referência para implementação.
 */

// @ts-ignore - Exemplo de uso, pode ter imports não disponíveis
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Alert, AlertDescription } from '../ui/alert';
import { Badge } from '../ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import {
  Upload,
  Download,
  FileText,
  Sparkles,
  CheckCircle,
  AlertTriangle,
  Crown,
  Info
} from 'lucide-react';

// @ts-ignore - Hook pode não estar disponível em desenvolvimento
import { useDataImportExport, useQuickDataImport } from '../../hooks/use-data-import-export';

export function DataImportExportExample() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [exportFormat, setExportFormat] = useState<'csv' | 'excel' | 'pdf'>('csv');

  // Hooks principais
  const {
    importData,
    exportData,
    isImporting,
    isExporting,
    importResult,
    exportResult,
    error,
    canUseFeatures,
    canUseAI,
    upgradeInfo,
    reset
  } = useDataImportExport();

  const {
    quickImport,
    isLoading: isQuickLoading,
    result: quickResult,
    canUseFeatures: canUseQuickFeatures
  } = useQuickDataImport();

  // Handlers
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      reset(); // Limpar resultados anteriores
    }
  };

  const handleImport = async () => {
    if (!selectedFile) return;

    const result = await importData(selectedFile, {
      fileType: selectedFile.type.includes('csv') ? 'csv' :
                selectedFile.type.includes('excel') ? 'excel' :
                selectedFile.type.includes('pdf') ? 'pdf' : 'image',
      useAI: true,
      targetCollection: 'clients',
      customPrompt: 'Extrair dados de clientes com nome, email, telefone e empresa'
    });

    if (result) {
      console.log('Import realizado:', result);
    }
  };

  const handleQuickImport = async () => {
    if (!selectedFile) return;

    const result = await quickImport(selectedFile, 'clients', true);
    if (result) {
      console.log('Quick import realizado:', result);
    }
  };

  const handleExport = async () => {
    const result = await exportData('clients', {
      format: exportFormat,
      filters: {
        status: ['ativo'],
        priority: ['alta', 'critica']
      },
      columns: ['name', 'email', 'phone', 'company', 'status', 'priority'],
      includeMetadata: false,
      dateRange: {
        start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 dias atrás
        end: new Date().toISOString()
      }
    });

    if (result) {
      console.log('Export realizado:', result);
      // Abrir download
      window.open(result.downloadUrl, '_blank');
    }
  };

  // Se não tem acesso, mostrar upgrade
  if (!canUseFeatures && upgradeInfo) {
    return (
      <Card className="border-yellow-200 bg-yellow-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Crown className="h-5 w-5 text-yellow-500" />
            Upgrade Necessário - {upgradeInfo.requiredPlan}
          </CardTitle>
          <CardDescription>
            {upgradeInfo.message}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              As funcionalidades de import/export estão disponíveis apenas para planos pagos.
              Faça upgrade para desbloquear essas funcionalidades.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Sistema de Import/Export de Dados</CardTitle>
          <CardDescription>
            Demonstração das funcionalidades de importação e exportação com IA
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2 mb-4">
            <Badge variant={canUseFeatures ? "default" : "secondary"}>
              Import/Export: {canUseFeatures ? "Disponível" : "Indisponível"}
            </Badge>
            <Badge variant={canUseAI ? "default" : "secondary"}>
              IA: {canUseAI ? "Disponível" : "Indisponível"}
            </Badge>
          </div>

          <Tabs defaultValue="import" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="import">Import de Dados</TabsTrigger>
              <TabsTrigger value="export">Export de Dados</TabsTrigger>
            </TabsList>

            <TabsContent value="import" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="file-input">Selecionar Arquivo</Label>
                  <Input
                    id="file-input"
                    type="file"
                    accept=".csv,.xlsx,.xls,.pdf,.jpg,.jpeg,.png,.webp"
                    onChange={handleFileSelect}
                    className="mt-1"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Suportados: CSV, Excel, PDF, Imagens (máx. 10MB)
                  </p>
                </div>

                {selectedFile && (
                  <Card>
                    <CardContent className="pt-4">
                      <div className="flex items-center gap-3">
                        <FileText className="h-8 w-8 text-blue-500" />
                        <div className="flex-1">
                          <p className="font-medium">{selectedFile.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                          </p>
                        </div>
                        <Badge variant="outline">
                          {selectedFile.type}
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>
                )}

                <div className="flex gap-2">
                  <Button
                    onClick={handleImport}
                    disabled={!selectedFile || isImporting || !canUseFeatures}
                    className="flex-1"
                  >
                    {isImporting ? (
                      <>
                        <Sparkles className="h-4 w-4 mr-2 animate-spin" />
                        Processando com IA...
                      </>
                    ) : (
                      <>
                        <Upload className="h-4 w-4 mr-2" />
                        Import Completo
                      </>
                    )}
                  </Button>

                  <Button
                    variant="outline"
                    onClick={handleQuickImport}
                    disabled={!selectedFile || isQuickLoading || !canUseQuickFeatures}
                    className="flex-1"
                  >
                    {isQuickLoading ? (
                      <>
                        <Sparkles className="h-4 w-4 mr-2 animate-spin" />
                        Processando...
                      </>
                    ) : (
                      <>
                        <Upload className="h-4 w-4 mr-2" />
                        Quick Import
                      </>
                    )}
                  </Button>
                </div>

                {/* Resultados do Import */}
                {(importResult || quickResult) && (
                  <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      Import realizado com sucesso!
                      {importResult && ` ${importResult.processedRecords} registros processados.`}
                      {quickResult && ` ${quickResult.processedRecords} registros processados.`}
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </TabsContent>

            <TabsContent value="export" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label>Formato de Export</Label>
                  <div className="flex gap-2 mt-2">
                    {(['csv', 'excel', 'pdf'] as const).map((format) => (
                      <Button
                        key={format}
                        variant={exportFormat === format ? "default" : "outline"}
                        onClick={() => setExportFormat(format)}
                        className="flex-1"
                      >
                        {format.toUpperCase()}
                      </Button>
                    ))}
                  </div>
                </div>

                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    Este exemplo exportará clientes ativos com prioridade alta/crítica dos últimos 30 dias.
                  </AlertDescription>
                </Alert>

                <Button
                  onClick={handleExport}
                  disabled={isExporting || !canUseFeatures}
                  className="w-full"
                >
                  {isExporting ? (
                    <>
                      <Download className="h-4 w-4 mr-2 animate-spin" />
                      Gerando {exportFormat.toUpperCase()}...
                    </>
                  ) : (
                    <>
                      <Download className="h-4 w-4 mr-2" />
                      Exportar como {exportFormat.toUpperCase()}
                    </>
                  )}
                </Button>

                {/* Resultado do Export */}
                {exportResult && (
                  <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription className="flex items-center justify-between">
                      <span>Export concluído! Arquivo: {exportResult.fileName}</span>
                      <Button size="sm" variant="outline" asChild>
                        <a href={exportResult.downloadUrl} target="_blank" rel="noopener noreferrer">
                          Download
                        </a>
                      </Button>
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </TabsContent>
          </Tabs>

          {/* Erros */}
          {error && (
            <Alert variant="destructive" className="mt-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Informações Técnicas */}
      <Card>
        <CardHeader>
          <CardTitle>Informações Técnicas</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-medium mb-2">Funcionalidades Disponíveis:</h4>
            <ul className="space-y-1 text-sm">
              <li className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                Import com processamento IA (Gemini)
              </li>
              <li className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                Suporte a múltiplos formatos (CSV, Excel, PDF, Imagens)
              </li>
              <li className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                Export com filtros avançados
              </li>
              <li className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                Validação automática de dados
              </li>
              <li className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                Restrição por planos de usuário
              </li>
            </ul>
          </div>

          <div>
            <h4 className="font-medium mb-2">Exemplo de Uso Programático:</h4>
            <pre className="bg-muted p-3 rounded text-xs overflow-x-auto">
{`// Import de dados
const result = await importData(file, {
  fileType: 'csv',
  useAI: true,
  targetCollection: 'clients',
  customPrompt: 'Extrair dados de clientes...'
});

// Export de dados
const exportResult = await exportData('clients', {
  format: 'excel',
  filters: { status: ['ativo'] },
  columns: ['name', 'email', 'company']
});`}
            </pre>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
