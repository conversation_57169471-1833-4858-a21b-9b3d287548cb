# 🔒 Middleware Server-Side - Documentação

## 📋 Visão Geral

O middleware foi otimizado para funcionar 100% no server-side, mantendo o fetch de dados de usuários no client-side conforme solicitado. Esta implementação oferece melhor performance e segurança.

## 🏗️ Arquitetura

### Server-Side (Middleware)
- **Verificação de autenticação**: Rápida verificação de sessão via cookies
- **Redirecionamentos**: Baseados apenas na presença/ausência de sessão
- **Verificação de admin**: Apenas quando necessário (rotas admin)

### Client-Side (Componentes)
- **Fetch de dados**: Mantido nos hooks e contextos React
- **Estado do usuário**: Gerenciado pelo AuthContext
- **Sincronização**: TanStack Query para cache e sincronização

## 🔧 Funções Server-Side

### `hasValidSession()`
```typescript
// Verificação rápida de sessão (sem fetch de dados)
const hasSession = await hasValidSession();
```
- **Uso**: Verificação inicial no middleware
- **Performance**: <PERSON><PERSON> (apenas verifica cookie)
- **Retorno**: `boolean`

### `getUserWithAdminCheck()`
```typescript
// Fetch completo apenas quando necessário
const { user, isAdmin } = await getUserWithAdminCheck();
```
- **Uso**: Apenas para rotas admin
- **Performance**: Fetch completo dos dados
- **Retorno**: `{ user: User | null, isAdmin: boolean }`

### `getUser()`
```typescript
// Fetch completo dos dados do usuário
const user = await getUser();
```
- **Uso**: Server actions e componentes server
- **Cache**: 60 segundos de revalidação
- **Retorno**: `User | null`

## 🚀 Fluxo de Autenticação

### 1. Middleware (Server-Side)
```
Requisição → Verificar rota pública → Verificar sessão → Redirecionar se necessário
```

### 2. Componentes (Client-Side)
```
Componente → useAuth() → AuthContext → getCurrentUser() → Appwrite SDK
```

## 📊 Performance

### Antes (Client-Side Middleware)
- ❌ Fetch completo em toda requisição
- ❌ Dependência de JavaScript no cliente
- ❌ Possível flash de conteúdo não autenticado

### Depois (Server-Side Otimizado)
- ✅ Verificação rápida de cookie (< 1ms)
- ✅ Fetch completo apenas quando necessário
- ✅ Redirecionamentos instantâneos
- ✅ Cache de 60 segundos para dados do usuário

## 🔐 Segurança

### Middleware
- **Verificação de sessão**: Server-side via cookies HTTP-only
- **Redirecionamentos**: Antes do carregamento da página
- **Proteção de rotas**: Admin e protegidas

### Client-Side
- **Dados do usuário**: Fetch seguro via Appwrite SDK
- **Estado local**: Sincronizado com servidor
- **Cache**: Invalidação automática

## 📝 Configuração

### Rotas Protegidas
```typescript
const protectedRoutes = ["/dashboard"];
const adminRoutes = ["/admin"];
const publicApiRoutes = [
  "/api/webhook/stripe",
  "/api/webhooks/mercadopago",
  "/api/create-checkout-session",
  "/api/pusher/auth"
];
```

### Matcher do Middleware
```typescript
export const config = {
  matcher: [
    "/dashboard/:path*",
    "/admin/:path*",
    "/auth/:path*",
    "/api/webhook/stripe/:path*",
    "/api/webhooks/mercadopago/:path*",
    "/api/create-checkout-session/:path*",
    "/api/socket/:path*"
  ],
};
```

## 🔄 Integração com Client-Side

### AuthContext (Mantido)
```typescript
// Continua funcionando normalmente
const { user, isLoading, login, logout } = useAuth();
```

### Hooks de Usuários (Mantidos)
```typescript
// Fetch de dados no client-side
const { data: users } = useUsers();
const { data: user } = useCurrentUser();
```

### Server Actions (Melhoradas)
```typescript
// Funções server-side otimizadas
import { getUser, hasValidSession } from '@/actions/auth';
```

## 🎯 Benefícios

1. **Performance**: Verificação rápida de sessão
2. **Segurança**: Middleware 100% server-side
3. **Flexibilidade**: Client-side mantido para dados
4. **Cache**: Otimização automática
5. **SEO**: Redirecionamentos server-side

## 🔧 Manutenção

### Adicionar Nova Rota Protegida
```typescript
const protectedRoutes = ["/dashboard", "/nova-rota"];
```

### Adicionar Nova Rota Admin
```typescript
const adminRoutes = ["/admin", "/nova-admin"];
```

### Modificar Verificação de Admin
```typescript
// Em actions/auth.ts
const isAdmin = user && (
  user.email === '<EMAIL>' ||
  user.labels?.includes('admin') ||
  // Adicionar nova condição aqui
);
```

## 📚 Referências

- [Next.js Middleware](https://nextjs.org/docs/app/building-your-application/routing/middleware)
- [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations)
- [Appwrite Authentication](https://appwrite.io/docs/client/account)
