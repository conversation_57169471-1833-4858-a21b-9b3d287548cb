/**
 * Exemplo de componente para gerenciar documentos soft deleted (Lixeira)
 * Demonstra como usar as funcionalidades de soft delete implementadas
 */

import { useState } from 'react';
import { Trash2, RotateCcw, AlertTriangle, Search } from 'lucide-react';
import { <PERSON><PERSON> } from '../ui/button';
import { Input } from '../ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '../ui/alert-dialog';
import { useDeletedClients, useRestoreClient, useHardDeleteClient } from '../../hooks/api/use-clients';
import type { Client } from '@/schemas/clients';

export function TrashManager() {
  const [searchTerm, setSearchTerm] = useState('');
  const { data: deletedClients, isLoading } = useDeletedClients();
  const restoreClient = useRestoreClient();
  const hardDeleteClient = useHardDeleteClient();

  // Filtrar clientes deletados por termo de busca
  const filteredClients = deletedClients?.filter(client =>
    client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.email?.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const handleRestore = (client: Client) => {
    restoreClient.mutate(client.$id);
  };

  const handlePermanentDelete = (client: Client) => {
    hardDeleteClient.mutate(client.$id);
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5" />
            Lixeira
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Trash2 className="h-5 w-5" />
          Lixeira
        </CardTitle>
        <CardDescription>
          Gerencie clientes excluídos. Você pode restaurá-los ou removê-los permanentemente.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Barra de busca */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Buscar clientes na lixeira..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Estatísticas */}
        <div className="flex items-center gap-4 text-sm text-gray-600">
          <span>{deletedClients?.length || 0} clientes na lixeira</span>
          {searchTerm && (
            <span>{filteredClients.length} resultados encontrados</span>
          )}
        </div>

        {/* Lista de clientes deletados */}
        {filteredClients.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            {searchTerm ? (
              <div>
                <Search className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>Nenhum cliente encontrado com "{searchTerm}"</p>
              </div>
            ) : (
              <div>
                <Trash2 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>A lixeira está vazia</p>
                <p className="text-sm">Clientes excluídos aparecerão aqui</p>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-3">
            {filteredClients.map((client) => (
              <ClientTrashItem
                key={client.$id}
                client={client}
                onRestore={() => handleRestore(client)}
                onPermanentDelete={() => handlePermanentDelete(client)}
                isRestoring={restoreClient.isPending}
                isDeleting={hardDeleteClient.isPending}
              />
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

interface ClientTrashItemProps {
  client: Client;
  onRestore: () => void;
  onPermanentDelete: () => void;
  isRestoring: boolean;
  isDeleting: boolean;
}

function ClientTrashItem({ 
  client, 
  onRestore, 
  onPermanentDelete, 
  isRestoring, 
  isDeleting 
}: ClientTrashItemProps) {
  const deletedDate = new Date(client.$updatedAt).toLocaleDateString('pt-BR');

  return (
    <div className="flex items-center justify-between p-4 border rounded-lg bg-gray-50">
      <div className="flex-1">
        <div className="flex items-center gap-3">
          <div>
            <h4 className="font-medium text-gray-900">{client.name}</h4>
            {client.email && (
              <p className="text-sm text-gray-600">{client.email}</p>
            )}
            <div className="flex items-center gap-2 mt-1">
              <Badge variant="secondary" className="text-xs">
                {client.type === 'pessoa_fisica' ? 'Pessoa Física' : 'Pessoa Jurídica'}
              </Badge>
              <span className="text-xs text-gray-500">
                Excluído em {deletedDate}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="flex items-center gap-2">
        {/* Botão Restaurar */}
        <Button
          variant="outline"
          size="sm"
          onClick={onRestore}
          disabled={isRestoring || isDeleting}
          className="flex items-center gap-2"
        >
          <RotateCcw className="h-4 w-4" />
          {isRestoring ? 'Restaurando...' : 'Restaurar'}
        </Button>

        {/* Botão Excluir Permanentemente */}
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button
              variant="destructive"
              size="sm"
              disabled={isRestoring || isDeleting}
              className="flex items-center gap-2"
            >
              <AlertTriangle className="h-4 w-4" />
              Excluir
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-red-500" />
                Excluir Permanentemente
              </AlertDialogTitle>
              <AlertDialogDescription>
                Tem certeza que deseja excluir permanentemente o cliente{' '}
                <strong>{client.name}</strong>? Esta ação não pode ser desfeita.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancelar</AlertDialogCancel>
              <AlertDialogAction
                onClick={onPermanentDelete}
                className="bg-red-600 hover:bg-red-700"
              >
                {isDeleting ? 'Excluindo...' : 'Excluir Permanentemente'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
}

/**
 * Exemplo de uso em uma página ou dashboard
 */
export function TrashPage() {
  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Lixeira</h1>
        <p className="text-gray-600 mt-2">
          Gerencie clientes excluídos e restaure quando necessário
        </p>
      </div>
      
      <TrashManager />
      
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-medium text-blue-900 mb-2">💡 Dicas sobre Soft Delete</h3>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Clientes excluídos ficam na lixeira por segurança</li>
          <li>• Use "Restaurar" para desfazer uma exclusão acidental</li>
          <li>• "Excluir Permanentemente" remove o cliente definitivamente</li>
          <li>• Dados na lixeira não aparecem nas listagens normais</li>
        </ul>
      </div>
    </div>
  );
}
