import React from 'react';
import { Card, CardContent, CardDescription, CardHeader } from './ui/card';
import { Badge } from './ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { Star, Quote } from 'lucide-react';

// REMOVIDO: Depoimentos fictícios substituídos por benefícios reais do template
const templateBenefits = [
  {
    title: 'Autenticação Completa',
    description: 'Sistema de login, registro e recuperação de senha já implementado com React Router v7 e Appwrite.',
    icon: '🔐',
    highlight: 'Pronto para usar'
  },
  {
    title: 'Dashboard Moderno',
    description: 'Interface responsiva com sidebar, notificações em tempo real e tema dark/light automático.',
    icon: '📊',
    highlight: 'Design profissional'
  },
  {
    title: 'Type-Safe Database',
    description: 'Esquemas TypeScript completos, React Query hooks otimizados e cache local-first configurável.',
    icon: '🗄️',
    highlight: '100% TypeScript'
  },
  {
    title: 'Sistema de Teams',
    description: 'Gerenciamento de equipes com chat real-time, permissões e integração nativa com Appwrite.',
    icon: '👥',
    highlight: 'Real-time'
  },
  {
    title: 'PWA Configurável',
    description: 'Progressive Web App com notificações push, cache offline e instalação automática.',
    icon: '📱',
    highlight: 'Mobile-first'
  },
  {
    title: 'Documentação Completa',
    description: 'Guias detalhados, exemplos práticos e troubleshooting para acelerar o desenvolvimento.',
    icon: '📚',
    highlight: 'Bem documentado'
  }
];

// Estatísticas baseadas em funcionalidades reais do template
const realStats = [
  { value: '50K+', label: 'Linhas de Código' },
  { value: '100%', label: 'TypeScript' },
  { value: '15+', label: 'Componentes UI' },
  { value: '30+', label: 'Hooks Customizados' }
];

export function TestimonialsSection() {
  return (
    <section className="py-16 md:py-24 bg-background">
      <div className="container mx-auto px-4 max-w-7xl">
        {/* Header */}
        <div className="text-center mb-16">
          <Badge variant="outline" className="mb-4">
            Funcionalidades
          </Badge>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
            O que você recebe no template
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
            Um template completo e profissional com todas as funcionalidades essenciais
            para acelerar o desenvolvimento do seu SaaS.
          </p>
        </div>

        {/* Real Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
          {realStats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-primary mb-2">
                {stat.value}
              </div>
              <div className="text-sm text-muted-foreground">
                {stat.label}
              </div>
            </div>
          ))}
        </div>

        {/* Benefits Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {templateBenefits.map((benefit, index) => (
            <Card key={index} className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
              <CardHeader>
                <div className="flex items-start gap-4 mb-4">
                  <div className="text-4xl">{benefit.icon}</div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-lg mb-2">{benefit.title}</h4>
                    <Badge variant="secondary" className="text-xs mb-3">
                      {benefit.highlight}
                    </Badge>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                <CardDescription className="text-base leading-relaxed">
                  {benefit.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="bg-primary/5 rounded-lg p-8 border border-primary/20">
            <h3 className="text-2xl font-bold mb-4">
              Acelere o desenvolvimento do seu SaaS
            </h3>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              Não perca mais tempo reinventando a roda. Comece seu projeto hoje mesmo
              com nossa base sólida e bem estruturada.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-xs">
                  ✅ 100% TypeScript
                </Badge>
                <Badge variant="outline" className="text-xs">
                  ✅ Pronto para produção
                </Badge>
                <Badge variant="outline" className="text-xs">
                  ✅ Bem documentado
                </Badge>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
