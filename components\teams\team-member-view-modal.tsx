import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { Badge } from '../ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Separator } from '../ui/separator';
import { 
  Crown, 
  Shield, 
  User, 
  Mail, 
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  Users,
  Eye
} from 'lucide-react';
import type { TeamMembership } from '@/schemas/teams';

interface TeamMemberViewModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  member: TeamMembership | null;
}

const getRoleIcon = (roles: string[]) => {
  if (roles.includes('owner')) return <Crown className="h-4 w-4 text-yellow-500" />;
  if (roles.includes('admin')) return <Shield className="h-4 w-4 text-blue-500" />;
  return <User className="h-4 w-4 text-gray-500" />;
};

const getRoleLabel = (roles: string[]) => {
  if (roles.includes('owner')) return 'Proprietário';
  if (roles.includes('admin')) return 'Administrador';
  return 'Membro';
};

const getRoleBadgeVariant = (roles: string[]) => {
  if (roles.includes('owner')) return 'default';
  if (roles.includes('admin')) return 'secondary';
  return 'outline';
};

const getStatusColor = (confirmed: boolean) => {
  return confirmed 
    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
    : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
};

export function TeamMemberViewModal({
  open,
  onOpenChange,
  member
}: TeamMemberViewModalProps) {
  if (!member) return null;

  const initials = member.userName
    ? member.userName.split(' ').map(n => n[0]).join('').toUpperCase()
    : member.userEmail.substring(0, 2).toUpperCase();

  const roles = member.roles || [];
  const joinedDate = new Date(member.$createdAt);
  const updatedDate = new Date(member.$updatedAt);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Detalhes do Membro
          </DialogTitle>
          <DialogDescription>
            Visualize as informações detalhadas do membro do time.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Informações Básicas */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Informações Pessoais</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Avatar e Nome */}
              <div className="flex items-center space-x-4">
                <Avatar className="h-16 w-16">
                  <AvatarImage src={undefined} />
                  <AvatarFallback className="text-lg font-semibold">
                    {initials}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <h3 className="text-xl font-semibold">
                    {member.userName || 'Usuário'}
                  </h3>
                  <div className="flex items-center gap-2 mt-1">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span className="text-muted-foreground">{member.userEmail}</span>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Status e Função */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Status
                  </label>
                  <div className="mt-1">
                    <Badge className={getStatusColor(member.confirm)}>
                      {member.confirm ? (
                        <>
                          <CheckCircle className="mr-1 h-3 w-3" />
                          Ativo
                        </>
                      ) : (
                        <>
                          <XCircle className="mr-1 h-3 w-3" />
                          Convite Pendente
                        </>
                      )}
                    </Badge>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Função no Time
                  </label>
                  <div className="mt-1">
                    <Badge variant={getRoleBadgeVariant(roles)} className="flex items-center gap-1 w-fit">
                      {getRoleIcon(roles)}
                      {getRoleLabel(roles)}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Informações do Time */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Users className="h-5 w-5" />
                Informações do Time
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    ID do Time
                  </label>
                  <p className="mt-1 font-mono text-sm bg-muted px-2 py-1 rounded">
                    {member.teamId}
                  </p>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Nome do Time
                  </label>
                  <p className="mt-1">
                    {member.teamName || 'Nome não disponível'}
                  </p>
                </div>
              </div>

              <Separator />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    Adicionado em
                  </label>
                  <p className="mt-1">
                    {joinedDate.toLocaleDateString('pt-BR', {
                      day: '2-digit',
                      month: 'long',
                      year: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </p>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    Última atualização
                  </label>
                  <p className="mt-1">
                    {updatedDate.toLocaleDateString('pt-BR', {
                      day: '2-digit',
                      month: 'long',
                      year: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Informações Técnicas */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Informações Técnicas</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    ID do Usuário
                  </label>
                  <p className="mt-1 font-mono text-sm bg-muted px-2 py-1 rounded">
                    {member.userId}
                  </p>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    ID da Membership
                  </label>
                  <p className="mt-1 font-mono text-sm bg-muted px-2 py-1 rounded">
                    {member.$id}
                  </p>
                </div>
              </div>

              {roles.length > 0 && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Todas as Funções
                  </label>
                  <div className="mt-1 flex flex-wrap gap-1">
                    {roles.map((role, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {role}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
}
