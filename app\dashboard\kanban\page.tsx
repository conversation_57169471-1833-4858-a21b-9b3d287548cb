'use client';

/**
 * Kanban Dashboard Route
 * Main kanban page with board selection and management
 */

import React, { useState } from 'react';
import { useSnapshot } from 'valtio';
import {
  Plus,
  Search,
  Filter,
  Grid3X3,
  List,
  Star,
  Archive,
  MoreHorizontal,
  Folder,
  Users,
  Lock
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { KanbanBoard } from '@/components/kanban/KanbanBoard';
import { BoardFormModal } from '@/components/kanban/BoardFormModal';
import { WorkspaceSelector, useWorkspaceSelection } from '@/components/kanban/WorkspaceSelector';

import { useBoards, useDeleteBoard } from '@/hooks/api/use-kanban';
import { useUserWorkspaces } from '@/hooks/api/use-workspaces';
import { kanbanStore, kanbanActions } from '@/stores/kanban-store';
import { useAuth } from '@/hooks/use-auth';
import { useIsMobile } from '@/hooks/use-mobile';
import type { Board } from '@/schemas/kanban';

export default function KanbanPage() {
  const { user } = useAuth();
  const snap = useSnapshot(kanbanStore);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('my-boards');
  const isMobile = useIsMobile();

  const { data: boards, isLoading, error } = useBoards();
  const { data: workspacesResponse } = useUserWorkspaces(user?.$id);
  const deleteBoard = useDeleteBoard();

  const workspaces = workspacesResponse?.documents || [];

  // Workspace selection
  const { selectedWorkspace, selectWorkspace, getFilteredBoards } = useWorkspaceSelection(workspaces as any);

  // Filter boards based on search, tab, and workspace
  const filteredBoards = React.useMemo(() => {
    if (!boards) return [];

    let filtered = boards;

    // Apply workspace filter
    filtered = getFilteredBoards(filtered);

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(board =>
        board.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        board.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by tab
    switch (activeTab) {
      case 'my-boards':
        filtered = filtered.filter(board => board.userId === user?.$id);
        break;
      case 'team-boards':
        filtered = filtered.filter(board => board.teamId && board.visibility === 'team');
        break;
      case 'favorites':
        filtered = filtered.filter(board => board.isFavorite);
        break;
      case 'templates':
        filtered = filtered.filter(board => board.isTemplate);
        break;
      case 'archived':
        filtered = filtered.filter(board => board.isArchived);
        break;
    }

    return filtered;
  }, [boards, searchTerm, activeTab, user?.$id, getFilteredBoards]);

  const handleBoardSelect = (boardId: string) => {
    kanbanActions.setSelectedBoard(boardId);
  };

  const handleDeleteBoard = (board: Board) => {
    if (confirm(`Tem certeza que deseja excluir o board "${board.title}"?`)) {
      deleteBoard.mutate({ boardId: board.$id, userId: user!.$id });
    }
  };

  const getVisibilityIcon = (visibility: string) => {
    switch (visibility) {
      case 'private':
        return <Lock className="h-3 w-3" />;
      case 'team':
        return <Users className="h-3 w-3" />;
      case 'public':
        return <Folder className="h-3 w-3" />;
      default:
        return <Lock className="h-3 w-3" />;
    }
  };

  const getVisibilityLabel = (visibility: string) => {
    switch (visibility) {
      case 'private':
        return 'Privado';
      case 'team':
        return 'Time';
      case 'public':
        return 'Público';
      default:
        return 'Privado';
    }
  };

  // If a board is selected, show the kanban board
  if (snap.selectedBoardId) {
    return <KanbanBoard boardId={snap.selectedBoardId} />;
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex flex-col gap-4 p-4 md:p-6 border-b md:flex-row md:items-center md:justify-between">
        <div className="min-w-0 space-y-2">
          <h1 className="text-xl md:text-2xl font-bold">Kanban Boards</h1>
          <p className="text-muted-foreground text-sm md:text-base">
            Organize suas tarefas com quadros kanban
          </p>

          {/* Workspace Selector */}
          {workspaces.length > 0 && (
            <WorkspaceSelector
              workspaces={workspaces as any}
              selectedWorkspace={selectedWorkspace as any}
              onSelectWorkspace={selectWorkspace}
              className="w-fit"
            />
          )}
        </div>

        <div className="flex items-center gap-2 shrink-0">
          {/* Search */}
          <div className="relative flex-1 md:flex-none">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Buscar boards..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`pl-10 ${isMobile ? 'w-full' : 'w-64'}`}
            />
          </div>

          {/* View Toggle - Hidden on mobile */}
          {!isMobile && (
            <Button variant="outline" size="sm">
              <Grid3X3 className="h-4 w-4" />
            </Button>
          )}

          {/* Create Board */}
          <Button onClick={() => kanbanActions.openBoardCreate()} size={isMobile ? "sm" : "default"}>
            <Plus className="h-4 w-4" />
            {!isMobile && <span className="ml-2">Novo Board</span>}
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 p-4 md:p-6">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className={`grid w-full ${isMobile ? 'grid-cols-3' : 'grid-cols-5'}`}>
            <TabsTrigger value="my-boards" className={isMobile ? "text-xs" : ""}>
              {isMobile ? "Meus" : "Meus Boards"}
            </TabsTrigger>
            {!isMobile && <TabsTrigger value="team-boards">Time</TabsTrigger>}
            <TabsTrigger value="favorites" className={isMobile ? "text-xs" : ""}>
              {isMobile ? "Fav" : "Favoritos"}
            </TabsTrigger>
            {!isMobile && <TabsTrigger value="templates">Templates</TabsTrigger>}
            <TabsTrigger value="archived" className={isMobile ? "text-xs" : ""}>
              {isMobile ? "Arq" : "Arquivados"}
            </TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className={isMobile ? "mt-4" : "mt-6"}>
            {/* Loading State */}
            {isLoading && (
              <div className={`grid gap-4 ${
                isMobile
                  ? 'grid-cols-1 sm:grid-cols-2'
                  : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
              }`}>
                {[1, 2, 3, 4, 5, 6].map((i) => (
                  <Card key={i}>
                    <CardHeader className={isMobile ? "pb-2" : ""}>
                      <Skeleton className="h-4 w-3/4" />
                      <Skeleton className="h-3 w-1/2" />
                    </CardHeader>
                    <CardContent>
                      <Skeleton className={`w-full ${isMobile ? 'h-16' : 'h-20'}`} />
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {/* Error State */}
            {error && (
              <Card>
                <CardContent className="pt-6">
                  <p className="text-center text-muted-foreground">
                    Erro ao carregar boards. Tente novamente.
                  </p>
                </CardContent>
              </Card>
            )}

            {/* Empty State */}
            {!isLoading && !error && filteredBoards.length === 0 && (
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center space-y-4">
                    <div className="mx-auto w-12 h-12 bg-muted rounded-lg flex items-center justify-center">
                      <Grid3X3 className="h-6 w-6 text-muted-foreground" />
                    </div>
                    <div>
                      <h3 className="font-medium">Nenhum board encontrado</h3>
                      <p className="text-sm text-muted-foreground">
                        {searchTerm
                          ? 'Tente ajustar sua busca ou criar um novo board.'
                          : 'Crie seu primeiro board para começar a organizar suas tarefas.'
                        }
                      </p>
                    </div>
                    {!searchTerm && (
                      <Button onClick={() => kanbanActions.openBoardCreate()}>
                        <Plus className="h-4 w-4 mr-2" />
                        Criar Primeiro Board
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Boards Grid */}
            {!isLoading && !error && filteredBoards.length > 0 && (
              <div className={`grid gap-4 ${
                isMobile
                  ? 'grid-cols-1 sm:grid-cols-2'
                  : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
              }`}>
                {filteredBoards.map((board) => (
                  <Card
                    key={board.$id}
                    className="cursor-pointer hover:shadow-md transition-all group"
                    onClick={() => handleBoardSelect(board.$id)}
                  >
                    <CardHeader className={isMobile ? "pb-2" : "pb-3"}>
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <CardTitle className={`${isMobile ? 'text-sm' : 'text-base'} truncate`}>
                            {board.title}
                          </CardTitle>
                          {board.description && !isMobile && (
                            <CardDescription className="line-clamp-2 mt-1">
                              {board.description}
                            </CardDescription>
                          )}
                        </div>

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100"
                              onClick={(e) => e.stopPropagation()}
                            >
                              <MoreHorizontal className="h-3 w-3" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => kanbanActions.openBoardEdit(board)}>
                              Editar
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              {board.isFavorite ? 'Remover dos Favoritos' : 'Adicionar aos Favoritos'}
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              Duplicar
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleDeleteBoard(board)}
                              className="text-destructive"
                            >
                              Excluir
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </CardHeader>

                    <CardContent className="pt-0">
                      {/* Board Preview/Background */}
                      <div
                        className={`${isMobile ? 'h-16' : 'h-20'} rounded-md mb-3 border`}
                        style={{
                          backgroundColor: board.backgroundColor || '#3B82F6',
                          backgroundImage: board.backgroundImage ? `url(${board.backgroundImage})` : undefined,
                          backgroundSize: 'cover',
                          backgroundPosition: 'center',
                        }}
                      />

                      {/* Board Metadata */}
                      <div className={`space-y-${isMobile ? '1' : '2'}`}>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            {getVisibilityIcon(board.visibility)}
                            {!isMobile && <span>{getVisibilityLabel(board.visibility)}</span>}
                          </div>

                          <div className="flex items-center gap-1">
                            {board.isFavorite && (
                              <Star className="h-3 w-3 text-yellow-500 fill-current" />
                            )}
                            {board.isTemplate && !isMobile && (
                              <Badge variant="outline" className="text-xs">
                                Template
                              </Badge>
                            )}
                          </div>
                        </div>

                        {!isMobile && (
                          <div className="text-xs text-muted-foreground">
                            Atualizado {new Date(board.$updatedAt).toLocaleDateString('pt-BR')}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>

      {/* Modals */}
      <BoardFormModal />
    </div>
  );
}
