import { useEffect } from 'react';
import { useAuth } from '../hooks/use-auth';
import { useRouter } from 'next/navigation';

interface AuthBackgroundCheckProps {
  /**
   * Whether to redirect authenticated users to dashboard
   * @default false
   */
  redirectIfAuthenticated?: boolean;
  /**
   * Route to redirect to if user is authenticated
   * @default '/dashboard'
   */
  redirectTo?: string;
  /**
   * Whether to show any loading state
   * @default false
   */
  showLoading?: boolean;
}

/**
 * Component for background authentication checking
 * This component performs auth checks without blocking the UI
 * Use this when you want to check auth state but still show content immediately
 */
export function AuthBackgroundCheck({
  redirectIfAuthenticated = false,
  redirectTo = '/dashboard',
  showLoading = false
}: AuthBackgroundCheckProps) {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && redirectIfAuthenticated && isAuthenticated) {
      router.replace(redirectTo);
    }
  }, [isAuthenticated, isLoading, redirectIfAuthenticated, redirectTo, router]);

  // This component doesn't render anything by default
  // It just performs background checks
  if (showLoading && isLoading) {
    return (
      <div className="fixed top-4 right-4 z-50">
        <div className="bg-background border rounded-lg p-3 shadow-lg">
          <p className="text-sm text-muted-foreground">Verificando autenticação...</p>
        </div>
      </div>
    );
  }

  return null;
}
