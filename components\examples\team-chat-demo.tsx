'use client';

/**
 * Demonstração do sistema de chat do time
 * Mostra como usar o chat integrado com as preferências do time
 */

import { useState } from 'react';
import { Button } from '../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Separator } from '../ui/separator';
import { MessageCircle, Send, Users, Settings, Loader2, CheckCircle } from 'lucide-react';
import { useTeams, useGetOrCreateTeamChat, useTeamChat } from '../../hooks/api/use-teams';
import { useMessages, useSendMessage, useChatRealtime } from '../../hooks/use-api';
import { useAuth } from '../../hooks/use-auth';
import { toast } from 'sonner';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';

export function TeamChatDemo() {
  const { user } = useAuth();
  const [selectedTeamId, setSelectedTeamId] = useState<string>('');
  const [message, setMessage] = useState('');

  // Buscar teams do usuário
  const { data: teams, isLoading: loadingTeams } = useTeams();

  // Hooks para o team selecionado
  const selectedTeam = teams?.find(team => team.$id === selectedTeamId);
  const { data: teamChat, isLoading: loadingTeamChat } = useTeamChat(selectedTeamId);
  const getOrCreateChatMutation = useGetOrCreateTeamChat();
  const { data: messages = [], isLoading: loadingMessages } = useMessages(teamChat?.$id || '', selectedTeamId);
  const sendMessageMutation = useSendMessage();
  const { isConnected } = useChatRealtime(selectedTeamId);
  const typingUsers: any[] = []; // Simplificado por enquanto

  const handleCreateChat = async () => {
    if (!selectedTeam) return;

    try {
      await getOrCreateChatMutation.mutateAsync({
        teamId: selectedTeam.$id,
        teamName: selectedTeam.name,
      });
      toast.success('Chat configurado com sucesso!');
    } catch (error) {
      console.error('Erro ao configurar chat:', error);
      toast.error('Erro ao configurar chat');
    }
  };

  const handleSendMessage = async () => {
    if (!message.trim() || !teamChat?.$id || !selectedTeamId) return;

    try {
      await sendMessageMutation.mutateAsync({
        content: message.trim(),
        chatId: teamChat.$id,
        teamId: selectedTeamId,
        type: 'text',
      });
      setMessage('');
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
      toast.error('Erro ao enviar mensagem');
    }
  };

  const isLoading = loadingTeams || loadingTeamChat || loadingMessages ||
                   getOrCreateChatMutation.isPending || sendMessageMutation.isPending;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Demo: Sistema de Chat do Time
          </CardTitle>
          <CardDescription>
            Demonstração completa do sistema de chat integrado com teams
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Seleção de Time */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Selecionar Time:</label>
            <select
              value={selectedTeamId}
              onChange={(e) => setSelectedTeamId(e.target.value)}
              className="w-full p-2 border rounded-md"
              disabled={loadingTeams}
            >
              <option value="">Selecione um time...</option>
              {teams?.map(team => (
                <option key={team.$id} value={team.$id}>
                  {team.name} ({team.total} membros)
                </option>
              ))}
            </select>
          </div>

          {selectedTeam && (
            <>
              <Separator />

              {/* Status do Chat */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Chat: {selectedTeam.name}</h3>
                  <div className="flex items-center gap-2">
                    {isConnected && (
                      <Badge variant="outline" className="text-green-600">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Online
                      </Badge>
                    )}
                    {teamChat && (
                      <Badge variant="secondary">
                        Chat Configurado
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Informações do Chat */}
                {teamChat && (
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">ID do Chat:</span> {teamChat.$id}
                    </div>
                    <div>
                      <span className="font-medium">Membros:</span> {teamChat.members?.length || 0}
                    </div>
                    <div>
                      <span className="font-medium">Mensagens:</span> {messages.length || 0}
                    </div>
                    <div>
                      <span className="font-medium">Última atividade:</span>{' '}
                      {teamChat.lastActivity
                        ? formatDistanceToNow(new Date(teamChat.lastActivity), { addSuffix: true, locale: ptBR })
                        : 'Nenhuma'
                      }
                    </div>
                  </div>
                )}

                {/* Ação para Configurar Chat */}
                {!teamChat && (
                  <Button
                    onClick={handleCreateChat}
                    disabled={isLoading}
                    className="w-full"
                  >
                    {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Configurar Chat do Time
                  </Button>
                )}
              </div>

              {/* Chat Interface */}
              {teamChat && (
                <>
                  <Separator />

                  <div className="space-y-4">
                    <h4 className="font-medium flex items-center gap-2">
                      <MessageCircle className="h-4 w-4" />
                      Mensagens ({messages.filter(msg => !msg.isDeleted).length || 0})
                    </h4>

                    {/* Lista de Mensagens */}
                    <div className="border rounded-lg p-4 max-h-60 overflow-y-auto space-y-3">
                      {messages && messages.filter(msg => !msg.isDeleted).length > 0 ? (
                        messages.filter(msg => !msg.isDeleted).map((msg) => (
                          <div
                            key={msg.$id}
                            className={`flex ${msg.senderId === user?.$id ? 'justify-end' : 'justify-start'}`}
                          >
                            <div
                              className={`max-w-xs px-3 py-2 rounded-lg ${
                                msg.senderId === user?.$id
                                  ? 'bg-blue-500 text-white'
                                  : 'bg-gray-100 text-gray-900'
                              }`}
                            >
                              <div className="text-xs opacity-75 mb-1">
                                {msg.senderName} • {formatDistanceToNow(new Date(msg.$createdAt), { locale: ptBR })}
                              </div>
                              <div>{msg.content}</div>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-center text-gray-500 py-8">
                          Nenhuma mensagem ainda. Seja o primeiro a enviar uma mensagem!
                        </div>
                      )}
                    </div>

                    {/* Indicador de Digitação */}
                    {typingUsers.length > 0 && (
                      <div className="text-sm text-gray-500">
                        {typingUsers.map(user => user.userName).join(', ')} está digitando...
                      </div>
                    )}

                    {/* Input de Mensagem */}
                    <div className="flex gap-2">
                      <Input
                        value={message}
                        onChange={(e) => setMessage(e.target.value)}
                        placeholder="Digite sua mensagem..."
                        onKeyPress={(e) => {
                          if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            handleSendMessage();
                          }
                        }}
                        disabled={sendMessageMutation.isPending}
                      />
                      <Button
                        onClick={handleSendMessage}
                        disabled={!message.trim() || sendMessageMutation.isPending}
                        size="icon"
                      >
                        {sendMessageMutation.isPending ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Send className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                </>
              )}
            </>
          )}

          {/* Loading States */}
          {isLoading && !selectedTeam && (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              <span>Carregando...</span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Informações Técnicas */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Informações Técnicas
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm">
          <div><strong>WebSocket Status:</strong> {isConnected ? 'Conectado' : 'Desconectado'}</div>
          <div><strong>Usuário:</strong> {user?.name || 'Não autenticado'}</div>
          <div><strong>Teams Carregados:</strong> {teams?.length || 0}</div>
          <div><strong>Team Selecionado:</strong> {selectedTeam?.name || 'Nenhum'}</div>
          <div><strong>Chat ID:</strong> {teamChat?.$id || 'Não configurado'}</div>
          <div><strong>Mensagens:</strong> {messages.filter(msg => !msg.isDeleted).length || 0}</div>
        </CardContent>
      </Card>
    </div>
  );
}
