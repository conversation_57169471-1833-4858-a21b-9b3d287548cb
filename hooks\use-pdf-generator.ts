/**
 * Hook para geração de PDFs profissionais
 * Utiliza jsPDF e jsPDF-AutoTable para criar documentos bonitos
 */

import { useCallback } from 'react';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { toast } from 'sonner';
import { useAuth } from './use-auth';
import { canUseDataImportExport } from '../lib/plan-limits';

export interface PDFOptions {
  title: string;
  subtitle?: string;
  orientation?: 'portrait' | 'landscape';
  format?: 'a4' | 'letter';
  includeHeader?: boolean;
  includeFooter?: boolean;
  includeDate?: boolean;
  includeUserInfo?: boolean;
}

export interface TableColumn {
  header: string;
  dataKey: string;
  width?: number;
}

export interface PDFTableOptions extends PDFOptions {
  columns: TableColumn[];
  data: Record<string, any>[];
  showRowNumbers?: boolean;
  alternateRowColors?: boolean;
}

export function usePDFGenerator() {
  const { user } = useAuth();

  // Verificar se o usuário pode usar funcionalidades de export
  const canUseFeatures = user ? canUseDataImportExport(user).allowed : false;

  /**
   * Configurações padrão do PDF
   */
  const getDefaultConfig = useCallback((): PDFOptions => ({
    title: 'Relatório',
    orientation: 'portrait',
    format: 'a4',
    includeHeader: true,
    includeFooter: true,
    includeDate: true,
    includeUserInfo: true,
  }), []);

  /**
   * Cria um novo documento PDF
   */
  const createDocument = useCallback((options: PDFOptions) => {
    const config = { ...getDefaultConfig(), ...options };

    const doc = new jsPDF({
      orientation: config.orientation,
      unit: 'mm',
      format: config.format,
    });

    // Configurar cores e fontes
    const primaryColor: [number, number, number] = [59, 130, 246]; // blue-500
    const secondaryColor: [number, number, number] = [107, 114, 128]; // gray-500
    const textColor: [number, number, number] = [31, 41, 55]; // gray-800

    return { doc, config, colors: { primary: primaryColor, secondary: secondaryColor, text: textColor } };
  }, [getDefaultConfig]);

  /**
   * Adiciona cabeçalho ao PDF
   */
  const addHeader = useCallback((doc: jsPDF, config: PDFOptions, colors: { primary: [number, number, number], secondary: [number, number, number], text: [number, number, number] }) => {
    if (!config.includeHeader) return;

    const pageWidth = doc.internal.pageSize.width;

    // Linha superior colorida
    doc.setFillColor(colors.primary[0], colors.primary[1], colors.primary[2]);
    doc.rect(0, 0, pageWidth, 8, 'F');

    // Título
    doc.setFontSize(20);
    doc.setTextColor(colors.text[0], colors.text[1], colors.text[2]);
    doc.setFont('helvetica', 'bold');
    doc.text(config.title, 20, 25);

    // Subtítulo
    if (config.subtitle) {
      doc.setFontSize(12);
      doc.setTextColor(colors.secondary[0], colors.secondary[1], colors.secondary[2]);
      doc.setFont('helvetica', 'normal');
      doc.text(config.subtitle, 20, 35);
    }

    // Data e informações do usuário
    if (config.includeDate || config.includeUserInfo) {
      const rightMargin = pageWidth - 20;
      let yPos = 25;

      if (config.includeDate) {
        doc.setFontSize(10);
        doc.setTextColor(colors.secondary[0], colors.secondary[1], colors.secondary[2]);
        doc.text(`Gerado em: ${new Date().toLocaleDateString('pt-BR')}`, rightMargin, yPos, { align: 'right' });
        yPos += 5;
      }

      if (config.includeUserInfo && user) {
        doc.setFontSize(10);
        doc.setTextColor(colors.secondary[0], colors.secondary[1], colors.secondary[2]);
        doc.text(`Por: ${user.name}`, rightMargin, yPos, { align: 'right' });
      }
    }

    // Linha separadora
    doc.setDrawColor(colors.primary[0], colors.primary[1], colors.primary[2]);
    doc.setLineWidth(0.5);
    doc.line(20, 45, pageWidth - 20, 45);

    return 55; // Retorna a posição Y onde o conteúdo deve começar
  }, [user]);

  /**
   * Adiciona rodapé ao PDF
   */
  const addFooter = useCallback((doc: jsPDF, config: PDFOptions, colors: { primary: [number, number, number], secondary: [number, number, number], text: [number, number, number] }) => {
    if (!config.includeFooter) return;

    const pageHeight = doc.internal.pageSize.height;
    const pageWidth = doc.internal.pageSize.width;

    // Linha separadora
    doc.setDrawColor(colors.primary[0], colors.primary[1], colors.primary[2]);
    doc.setLineWidth(0.5);
    doc.line(20, pageHeight - 20, pageWidth - 20, pageHeight - 20);

    // Número da página
    doc.setFontSize(8);
    doc.setTextColor(colors.secondary[0], colors.secondary[1], colors.secondary[2]);
    doc.text(`Página ${doc.getCurrentPageInfo().pageNumber}`, pageWidth - 20, pageHeight - 10, { align: 'right' });

    // Informações da empresa/app
    doc.text('Gerado por Template Appwrite', 20, pageHeight - 10);
  }, []);

  /**
   * Gera PDF com tabela de dados
   */
  const generateTablePDF = useCallback(async (options: PDFTableOptions): Promise<void> => {
    if (!canUseFeatures) {
      toast.error('Geração de PDF disponível apenas para planos pagos');
      return;
    }

    try {
      const { doc, config, colors } = createDocument(options);

      // Adicionar cabeçalho
      const startY = addHeader(doc, config, colors) || 55;

      // Preparar dados da tabela
      const tableData = options.data.map((row, index) => {
        const rowData: any[] = [];

        if (options.showRowNumbers) {
          rowData.push(index + 1);
        }

        options.columns.forEach(col => {
          const value = row[col.dataKey];
          rowData.push(value || '');
        });

        return rowData;
      });

      // Preparar cabeçalhos
      const headers: string[] = [];
      if (options.showRowNumbers) {
        headers.push('#');
      }
      headers.push(...options.columns.map(col => col.header));

      // Configurar tabela
      autoTable(doc, {
        head: [headers],
        body: tableData,
        startY: startY + 10,
        theme: 'grid',
        styles: {
          fontSize: 9,
          cellPadding: 3,
          textColor: colors.text,
        },
        headStyles: {
          fillColor: colors.primary,
          textColor: [255, 255, 255] as [number, number, number],
          fontStyle: 'bold',
        },
        alternateRowStyles: options.alternateRowColors ? {
          fillColor: [248, 250, 252], // gray-50
        } : undefined,
        columnStyles: options.showRowNumbers ? {
          0: { cellWidth: 15, halign: 'center' }
        } : undefined,
        margin: { left: 20, right: 20 },
        didDrawPage: (data: any) => {
          addFooter(doc, config, colors);
        },
      });

      // Adicionar estatísticas no final
      const finalY = (doc as any).lastAutoTable.finalY + 10;
      doc.setFontSize(10);
      doc.setTextColor(...colors.secondary);
      doc.text(`Total de registros: ${options.data.length}`, 20, finalY);

      // Download do arquivo
      const fileName = `${config.title.toLowerCase().replace(/\s+/g, '_')}_${Date.now()}.pdf`;
      doc.save(fileName);

      toast.success('PDF gerado com sucesso!');
    } catch (error) {
      console.error('Erro ao gerar PDF:', error);
      toast.error('Erro ao gerar PDF');
    }
  }, [canUseFeatures, createDocument, addHeader, addFooter]);

  /**
   * Gera PDF simples com texto
   */
  const generateTextPDF = useCallback(async (content: string, options: PDFOptions): Promise<void> => {
    if (!canUseFeatures) {
      toast.error('Geração de PDF disponível apenas para planos pagos');
      return;
    }

    try {
      const { doc, config, colors } = createDocument(options);

      // Adicionar cabeçalho
      const startY = addHeader(doc, config, colors) || 55;

      // Adicionar conteúdo
      doc.setFontSize(11);
      doc.setTextColor(colors.text[0], colors.text[1], colors.text[2]);
      doc.setFont('helvetica', 'normal');

      const splitText = doc.splitTextToSize(content, doc.internal.pageSize.width - 40);
      doc.text(splitText, 20, startY + 10);

      // Adicionar rodapé
      addFooter(doc, config, colors);

      // Download do arquivo
      const fileName = `${config.title.toLowerCase().replace(/\s+/g, '_')}_${Date.now()}.pdf`;
      doc.save(fileName);

      toast.success('PDF gerado com sucesso!');
    } catch (error) {
      console.error('Erro ao gerar PDF:', error);
      toast.error('Erro ao gerar PDF');
    }
  }, [canUseFeatures, createDocument, addHeader, addFooter]);

  return {
    generateTablePDF,
    generateTextPDF,
    canUseFeatures,
    isConfigured: true, // jsPDF não precisa de configuração externa
  };
}

/**
 * Hook simplificado para export de clientes
 */
export function useClientsPDFExport() {
  const { generateTablePDF } = usePDFGenerator();

  const exportClientsToPDF = useCallback(async (clients: any[]) => {
    const columns: TableColumn[] = [
      { header: 'Nome', dataKey: 'name' },
      { header: 'Email', dataKey: 'email' },
      { header: 'Telefone', dataKey: 'phone' },
      { header: 'Empresa', dataKey: 'company' },
      { header: 'Status', dataKey: 'status' },
      { header: 'Prioridade', dataKey: 'priority' },
    ];

    await generateTablePDF({
      title: 'Relatório de Clientes',
      subtitle: 'Lista completa de clientes cadastrados',
      columns,
      data: clients,
      showRowNumbers: true,
      alternateRowColors: true,
    });
  }, [generateTablePDF]);

  return { exportClientsToPDF };
}

/**
 * Hook simplificado para export de teams
 */
export function useTeamsPDFExport() {
  const { generateTablePDF } = usePDFGenerator();

  const exportTeamsToPDF = useCallback(async (teams: any[]) => {
    const columns: TableColumn[] = [
      { header: 'Nome', dataKey: 'name' },
      { header: 'Total de Membros', dataKey: 'total' },
      { header: 'Data de Criação', dataKey: '$createdAt' },
      { header: 'Última Atualização', dataKey: '$updatedAt' },
    ];

    // Formatar dados para exibição
    const formattedTeams = teams.map(team => ({
      ...team,
      $createdAt: new Date(team.$createdAt).toLocaleDateString('pt-BR'),
      $updatedAt: new Date(team.$updatedAt).toLocaleDateString('pt-BR'),
    }));

    await generateTablePDF({
      title: 'Relatório de Times',
      subtitle: 'Lista completa de times cadastrados',
      columns,
      data: formattedTeams,
      showRowNumbers: true,
      alternateRowColors: true,
    });
  }, [generateTablePDF]);

  return { exportTeamsToPDF };
}
