/**
 * IndexedDB hook for local-first data storage
 * Sistema dinâmico baseado nas coleções do projeto
 */

import { useCallback } from 'react';
import { getCacheConfig, getCollectionStores, getStoreIndexes } from '../lib/cache-config';

class IndexedDBManager {
  private db: IDBDatabase | null = null;
  private initPromise: Promise<IDBDatabase> | null = null;

  private async withRetry<T>(operation: () => Promise<T>, maxRetries = 3): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        if (attempt === maxRetries) {
          console.error(`Operation failed after ${maxRetries} attempts:`, lastError);
          throw lastError;
        }

        // Wait before retry with exponential backoff
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
        console.warn(`Operation failed (attempt ${attempt}/${maxRetries}), retrying in ${delay}ms:`, lastError);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  }

  async init(): Promise<IDBDatabase> {
    if (this.db) return this.db;
    if (this.initPromise) return this.initPromise;

    const config = getCacheConfig();
    const stores = getCollectionStores();

    this.initPromise = new Promise((resolve, reject) => {
      try {
        const request = indexedDB.open(config.dbName, config.version);

        request.onerror = () => {
          console.error('IndexedDB open error:', request.error);
          reject(request.error || new Error('Failed to open IndexedDB'));
        };

        request.onsuccess = () => {
          this.db = request.result;

          // Add error handler for the database
          this.db.onerror = (event) => {
            console.error('IndexedDB error:', event);
          };

          console.log(`✅ IndexedDB conectado: ${config.dbName} (v${config.version})`);
          resolve(this.db);
        };

        request.onupgradeneeded = (event) => {
          try {
            const db = (event.target as IDBOpenDBRequest).result;
            console.log(`🔄 Atualizando IndexedDB: ${config.dbName} (v${config.version})`);

            // Create stores dinamicamente
            stores.forEach((storeName) => {
              if (!db.objectStoreNames.contains(storeName)) {
                console.log(`📦 Criando store: ${storeName}`);

                const store = db.createObjectStore(storeName, {
                  keyPath: storeName === 'cache_metadata' ? 'key' : '$id',
                });

                // Create indexes para este store
                const indexes = getStoreIndexes(storeName);
                indexes.forEach((index) => {
                  try {
                    store.createIndex(index.name, index.keyPath, {
                      unique: index.unique || false,
                    });
                  } catch (indexError) {
                    console.warn(`⚠️ Falha ao criar índice ${index.name} em ${storeName}:`, indexError);
                  }
                });
              }
            });

            console.log(`✅ Stores criados: ${stores.join(', ')}`);
          } catch (upgradeError) {
            console.error('❌ Erro no upgrade do IndexedDB:', upgradeError);
            reject(upgradeError);
          }
        };

        request.onblocked = () => {
          console.warn('⚠️ IndexedDB upgrade bloqueado. Feche outras abas do aplicativo.');
        };

      } catch (error) {
        console.error('❌ Erro na inicialização do IndexedDB:', error);
        reject(error);
      }
    });

    return this.initPromise;
  }

  async getAll<T>(storeName: string): Promise<T[]> {
    return this.withRetry(async () => {
      const db = await this.init();

      // Verificar se o store existe
      if (!db.objectStoreNames.contains(storeName)) {
        console.warn(`Store ${storeName} não existe no IndexedDB`);
        return [];
      }

      return new Promise<T[]>((resolve, reject) => {
        try {
          const transaction = db.transaction([storeName], 'readonly');
          const store = transaction.objectStore(storeName);
          const request = store.getAll();

          transaction.onerror = () => {
            console.error(`Transaction error in getAll for ${storeName}:`, transaction.error);
            reject(transaction.error || new Error('Transaction failed'));
          };

          request.onsuccess = () => resolve(request.result);
          request.onerror = () => {
            console.error(`Request error in getAll for ${storeName}:`, request.error);
            reject(request.error || new Error('Request failed'));
          };
        } catch (error) {
          console.error(`Error in getAll for ${storeName}:`, error);
          reject(error);
        }
      });
    });
  }

  async get<T>(storeName: string, key: string): Promise<T | undefined> {
    const db = await this.init();

    // Verificar se o store existe
    if (!db.objectStoreNames.contains(storeName)) {
      console.warn(`Store ${storeName} não existe no IndexedDB`);
      return undefined;
    }

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.get(key);

      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  async set<T>(storeName: string, data: T): Promise<void> {
    const db = await this.init();

    // Verificar se o store existe
    if (!db.objectStoreNames.contains(storeName)) {
      console.warn(`Store ${storeName} não existe no IndexedDB`);
      return;
    }

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.put(data);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  async setMany<T>(storeName: string, items: T[]): Promise<void> {
    const db = await this.init();

    // Verificar se o store existe
    if (!db.objectStoreNames.contains(storeName)) {
      console.warn(`Store ${storeName} não existe no IndexedDB`);
      return;
    }

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);

      let completed = 0;
      const total = items.length;

      if (total === 0) {
        resolve();
        return;
      }

      items.forEach((item) => {
        const request = store.put(item);
        request.onsuccess = () => {
          completed++;
          if (completed === total) resolve();
        };
        request.onerror = () => reject(request.error);
      });
    });
  }

  async delete(storeName: string, key: string): Promise<void> {
    const db = await this.init();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.delete(key);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  async clear(storeName: string): Promise<void> {
    const db = await this.init();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.clear();

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  async getByIndex<T>(
    storeName: string,
    indexName: string,
    value: string | number
  ): Promise<T[]> {
    const db = await this.init();

    // Verificar se o store existe
    if (!db.objectStoreNames.contains(storeName)) {
      console.warn(`Store ${storeName} não existe no IndexedDB`);
      return [];
    }

    return new Promise((resolve, reject) => {
      try {
        const transaction = db.transaction([storeName], 'readonly');
        const store = transaction.objectStore(storeName);

        // Verificar se o index existe
        if (!store.indexNames.contains(indexName)) {
          console.warn(`Index ${indexName} não existe no store ${storeName}`);
          resolve([]);
          return;
        }

        const index = store.index(indexName);
        const request = index.getAll(value);

        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
      } catch (error) {
        console.error(`Error in getByIndex for ${storeName}.${indexName}:`, error);
        reject(error);
      }
    });
  }
}

// Singleton instance
const dbManager = new IndexedDBManager();

// Cache metadata helpers
interface CacheMetadata {
  key: string;
  lastUpdated: number;
  userId?: string;
}

export function useIndexedDB() {
  const setCache = useCallback(async <T>(
    storeName: string,
    data: T[],
    userId?: string
  ): Promise<void> => {
    try {
      // Store the data
      await dbManager.setMany(storeName, data);

      // Update cache metadata
      const metadata: CacheMetadata = {
        key: `${storeName}_${userId || 'global'}`,
        lastUpdated: Date.now(),
        userId,
      };
      await dbManager.set('cache_metadata', metadata);

      console.log(`✅ Cached ${data.length} items in ${storeName}`);
    } catch (error) {
      console.error(`❌ Failed to cache data in ${storeName}:`, error);
    }
  }, []);

  const getCache = useCallback(async <T>(
    storeName: string,
    userId?: string
  ): Promise<T[]> => {
    try {
      if (userId) {
        // Get data filtered by userId
        return await dbManager.getByIndex<T>(storeName, 'userId', userId);
      } else {
        // Get all data
        return await dbManager.getAll<T>(storeName);
      }
    } catch (error) {
      console.error(`❌ Failed to get cache from ${storeName}:`, error);
      return [];
    }
  }, []);

  const getCacheMetadata = useCallback(async (
    storeName: string,
    userId?: string
  ): Promise<CacheMetadata | null> => {
    try {
      const key = `${storeName}_${userId || 'global'}`;
      return await dbManager.get<CacheMetadata>('cache_metadata', key) || null;
    } catch (error) {
      console.error(`❌ Failed to get cache metadata for ${storeName}:`, error);
      return null;
    }
  }, []);

  const isCacheValid = useCallback(async (
    storeName: string,
    maxAge: number = 5 * 60 * 1000, // 5 minutes default
    userId?: string
  ): Promise<boolean> => {
    try {
      const metadata = await getCacheMetadata(storeName, userId);
      if (!metadata) return false;

      const age = Date.now() - metadata.lastUpdated;
      return age < maxAge;
    } catch (error) {
      console.error(`❌ Failed to check cache validity for ${storeName}:`, error);
      return false;
    }
  }, [getCacheMetadata]);

  const addToCache = useCallback(async <T extends { $id: string }>(
    storeName: string,
    item: T
  ): Promise<void> => {
    try {
      await dbManager.set(storeName, item);
      console.log(`✅ Added item ${item.$id} to ${storeName} cache`);
    } catch (error) {
      console.error(`❌ Failed to add item to ${storeName} cache:`, error);
    }
  }, []);

  const updateInCache = useCallback(async <T extends { $id: string }>(
    storeName: string,
    item: T
  ): Promise<void> => {
    try {
      await dbManager.set(storeName, item);
      console.log(`✅ Updated item ${item.$id} in ${storeName} cache`);
    } catch (error) {
      console.error(`❌ Failed to update item in ${storeName} cache:`, error);
    }
  }, []);

  const removeFromCache = useCallback(async (
    storeName: string,
    itemId: string
  ): Promise<void> => {
    try {
      await dbManager.delete(storeName, itemId);
      console.log(`✅ Removed item ${itemId} from ${storeName} cache`);
    } catch (error) {
      console.error(`❌ Failed to remove item from ${storeName} cache:`, error);
    }
  }, []);

  const clearCache = useCallback(async (storeName: string): Promise<void> => {
    try {
      await dbManager.clear(storeName);
      console.log(`✅ Cleared ${storeName} cache`);
    } catch (error) {
      console.error(`❌ Failed to clear ${storeName} cache:`, error);
    }
  }, []);

  return {
    setCache,
    getCache,
    getCacheMetadata,
    isCacheValid,
    addToCache,
    updateInCache,
    removeFromCache,
    clearCache,
  };
}

export type { CacheMetadata };
