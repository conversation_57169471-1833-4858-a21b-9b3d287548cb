import Link from 'next/link';
import { But<PERSON> } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { 
  Home, 
  ArrowLeft, 
  Search, 
  FileQuestion,
  Users,
  BarChart3,
  Settings
} from 'lucide-react';

interface NotFoundPageProps {
  title?: string;
  description?: string;
  showSuggestions?: boolean;
}

/**
 * Página 404 - Página não encontrada
 */
export function NotFoundPage({ 
  title = "Página não encontrada",
  description = "A página que você está procurando não existe ou foi movida.",
  showSuggestions = true 
}: NotFoundPageProps) {
  const suggestions = [
    {
      title: "Dashboard",
      description: "Volte para o painel principal",
      icon: Home,
      href: "/dashboard",
      color: "text-blue-600 dark:text-blue-400"
    },
    {
      title: "Clientes",
      description: "Gerencie seus clientes",
      icon: Users,
      href: "/dashboard/clients",
      color: "text-green-600 dark:text-green-400"
    },
    {
      title: "Relatórios",
      description: "Visualize relatórios e analytics",
      icon: BarChart3,
      href: "/dashboard/reports",
      color: "text-purple-600 dark:text-purple-400"
    },
    {
      title: "Configurações",
      description: "Ajuste suas preferências",
      icon: Settings,
      href: "/dashboard/preferences",
      color: "text-orange-600 dark:text-orange-400"
    }
  ];

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <div className="max-w-2xl w-full mx-auto">
        <div className="text-center space-y-8">
          {/* Ícone e número 404 */}
          <div className="space-y-4">
            <div className="flex justify-center">
              <div className="relative">
                <div className="text-8xl font-bold text-muted-foreground/20 select-none">
                  404
                </div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="rounded-full bg-muted/50 p-4">
                    <FileQuestion className="h-12 w-12 text-muted-foreground" />
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <h1 className="text-3xl font-bold text-foreground">
                {title}
              </h1>
              <p className="text-lg text-muted-foreground max-w-md mx-auto">
                {description}
              </p>
            </div>
          </div>

          {/* Botões de ação */}
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button asChild size="lg">
              <Link href="/dashboard">
                <Home className="h-4 w-4 mr-2" />
                Ir para o Dashboard
              </Link>
            </Button>
            
            <Button variant="outline" size="lg" onClick={() => window.history.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Voltar
            </Button>
          </div>

          {/* Sugestões de páginas */}
          {showSuggestions && (
            <div className="space-y-4">
              <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
                <Search className="h-4 w-4" />
                <span>Ou explore essas páginas populares:</span>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {suggestions.map((suggestion) => {
                  const Icon = suggestion.icon;
                  return (
                    <Card key={suggestion.href} className="hover:shadow-md transition-shadow">
                      <CardHeader className="pb-3">
                        <CardTitle className="flex items-center gap-3 text-base">
                          <div className={`rounded-lg p-2 bg-muted ${suggestion.color}`}>
                            <Icon className="h-4 w-4" />
                          </div>
                          <Link 
                            href={suggestion.href}
                            className="hover:underline"
                          >
                            {suggestion.title}
                          </Link>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <p className="text-sm text-muted-foreground">
                          {suggestion.description}
                        </p>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </div>
          )}

          {/* Link para ajuda */}
          <div className="pt-4 border-t">
            <p className="text-sm text-muted-foreground">
              Precisa de ajuda?{' '}
              <Link 
                href="/dashboard/help" 
                className="text-primary hover:underline font-medium"
              >
                Visite nossa central de ajuda
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
