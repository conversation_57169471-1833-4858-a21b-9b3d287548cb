/**
 * Enhanced export hook with improved PDF limitations and configuration
 * Replaces the existing export functionality with better controls
 */

import { useState, useCallback } from 'react';
import { toast } from 'sonner';
import { useAuth } from './use-auth';
import { canExportData } from '../lib/plan-limits';
import { usePDFGenerator } from './use-pdf-generator';
import {
  type ExportConfig,
  type ExportResult,
  type ExportField,
  exportResultSchema,
  getFieldsForCollection,
  PDF_LIMITATIONS,
} from '../schemas/export';

interface UseEnhancedExportOptions {
  collection: string;
}

export function useEnhancedExport({ collection }: UseEnhancedExportOptions) {
  const { user } = useAuth();
  const { generateTablePDF } = usePDFGenerator();

  const [isExporting, setIsExporting] = useState(false);
  const [exportResult, setExportResult] = useState<ExportResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Check permissions
  const canUseExport = user ? canExportData(user).allowed : false;

  // Reset state
  const reset = useCallback(() => {
    setExportResult(null);
    setError(null);
    setIsExporting(false);
  }, []);

  // Generate CSV content
  const generateCSV = useCallback((data: Record<string, any>[], config: ExportConfig): string => {
    if (data.length === 0) return '';

    const fields = getFieldsForCollection(collection);
    const selectedFields = config.selectedFields.length > 0 ? config.selectedFields : fields.map(f => f.id);
    const csvSettings = config.csvSettings || { delimiter: ',', includeHeaders: true };

    // Create headers
    const headers = selectedFields.map(fieldId => {
      const field = fields.find(f => f.id === fieldId);
      return field?.label || fieldId;
    });

    // Create rows
    const rows = data.map(row =>
      selectedFields.map(fieldId => {
        const value = row[fieldId];
        if (value === null || value === undefined) return '';

        // Handle arrays (like tags)
        if (Array.isArray(value)) {
          return value.join('; ');
        }

        // Handle objects
        if (typeof value === 'object') {
          return JSON.stringify(value);
        }

        // Escape CSV values
        const stringValue = String(value);
        if (stringValue.includes(csvSettings.delimiter) || stringValue.includes('"') || stringValue.includes('\n')) {
          return `"${stringValue.replace(/"/g, '""')}"`;
        }

        return stringValue;
      })
    );

    // Combine headers and rows
    const csvLines = csvSettings.includeHeaders ? [headers, ...rows] : rows;
    return csvLines.map(row => row.join(csvSettings.delimiter)).join('\n');
  }, [collection]);

  // Generate Excel content
  const generateExcel = useCallback(async (data: Record<string, any>[], config: ExportConfig): Promise<Blob> => {
    const XLSX = await import('xlsx');

    if (data.length === 0) {
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.aoa_to_sheet([['Nenhum dado encontrado']]);
      XLSX.utils.book_append_sheet(wb, ws, config.excelSettings?.sheetName || 'Data');

      const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
      return new Blob([wbout], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    }

    const fields = getFieldsForCollection(collection);
    const selectedFields = config.selectedFields.length > 0 ? config.selectedFields : fields.map(f => f.id);
    const excelSettings = config.excelSettings || { sheetName: 'Data', includeHeaders: true };

    // Process data for Excel
    const processedData = data.map(row => {
      const processedRow: Record<string, any> = {};
      selectedFields.forEach(fieldId => {
        const field = fields.find(f => f.id === fieldId);
        const label = field?.label || fieldId;
        const value = row[fieldId];

        if (Array.isArray(value)) {
          processedRow[label] = value.join('; ');
        } else if (typeof value === 'object' && value !== null) {
          processedRow[label] = JSON.stringify(value);
        } else {
          processedRow[label] = value;
        }
      });
      return processedRow;
    });

    // Create workbook
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.json_to_sheet(processedData);

    // Auto-fit columns if enabled
    if ((excelSettings as any).autoFitColumns) {
      const colWidths = Object.keys(processedData[0] || {}).map(key => ({
        wch: Math.max(key.length, 15) // Minimum width of 15
      }));
      ws['!cols'] = colWidths;
    }

    // Freeze headers if enabled
    if ((excelSettings as any).freezeHeaders && (excelSettings as any).includeHeaders) {
      ws['!freeze'] = { xSplit: 0, ySplit: 1 };
    }

    XLSX.utils.book_append_sheet(wb, ws, excelSettings.sheetName);

    const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
    return new Blob([wbout], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
  }, [collection]);

  // Generate JSON content
  const generateJSON = useCallback((data: Record<string, any>[], config: ExportConfig): string => {
    const fields = getFieldsForCollection(collection);
    const selectedFields = config.selectedFields.length > 0 ? config.selectedFields : fields.map(f => f.id);
    const jsonSettings = config.jsonSettings || { pretty: true, includeSchema: false };

    // Filter data to selected fields
    const filteredData = data.map(row => {
      const filteredRow: Record<string, any> = {};
      selectedFields.forEach(fieldId => {
        if (row.hasOwnProperty(fieldId)) {
          filteredRow[fieldId] = row[fieldId];
        }
      });
      return filteredRow;
    });

    const exportData: any = {
      data: filteredData,
      metadata: {
        exportedAt: new Date().toISOString(),
        collection,
        totalRecords: data.length,
        exportedRecords: filteredData.length,
        fields: selectedFields,
      }
    };

    // Include schema if requested
    if (jsonSettings.includeSchema) {
      exportData.schema = fields.filter(f => selectedFields.includes(f.id));
    }

    return JSON.stringify(exportData, null, jsonSettings.pretty ? 2 : 0);
  }, [collection]);

  // Generate PDF with limitations
  const generatePDF = useCallback(async (data: Record<string, any>[], config: ExportConfig): Promise<void> => {
    const fields = getFieldsForCollection(collection);
    const pdfSettings = config.pdfSettings || {
      maxRows: PDF_LIMITATIONS.DEFAULT_MAX_ROWS,
      maxColumns: PDF_LIMITATIONS.DEFAULT_MAX_COLUMNS,
      enablePagination: true,
      pageSize: PDF_LIMITATIONS.RECOMMENDED_ROWS,
      orientation: 'portrait',
      fontSize: 'medium',
    };

    // Filter fields to PDF-supported ones
    const pdfSupportedFields = config.selectedFields.filter(fieldId => {
      const field = fields.find(f => f.id === fieldId);
      return field?.pdfSupported !== false;
    });

    // Limit columns
    const limitedFields = pdfSupportedFields.slice(0, pdfSettings.maxColumns);

    // Limit rows
    const limitedData = data.slice(0, pdfSettings.maxRows);

    // Prepare columns for PDF
    const pdfColumns = limitedFields.map(fieldId => {
      const field = fields.find(f => f.id === fieldId);
      return {
        header: field?.label || fieldId,
        dataKey: fieldId,
        width: field?.width,
      };
    });

    // Process data for PDF
    const pdfData = limitedData.map(row => {
      const processedRow: Record<string, any> = {};
      limitedFields.forEach(fieldId => {
        const value = row[fieldId];

        if (Array.isArray(value)) {
          processedRow[fieldId] = value.slice(0, 3).join(', ') + (value.length > 3 ? '...' : '');
        } else if (typeof value === 'object' && value !== null) {
          processedRow[fieldId] = '[Object]';
        } else if (typeof value === 'string' && value.length > 50) {
          processedRow[fieldId] = value.substring(0, 47) + '...';
        } else {
          processedRow[fieldId] = value;
        }
      });
      return processedRow;
    });

    await generateTablePDF({
      title: `Relatório de ${collection.charAt(0).toUpperCase() + collection.slice(1)}`,
      subtitle: `Exportação gerada em ${new Date().toLocaleDateString('pt-BR')} - ${limitedData.length} de ${data.length} registros`,
      columns: pdfColumns,
      data: pdfData,
      showRowNumbers: true,
      alternateRowColors: true,
      orientation: pdfSettings.orientation,
    });
  }, [collection, generateTablePDF]);

  // Download file helper
  const downloadFile = useCallback((content: string | Blob, fileName: string, mimeType: string): number => {
    let blob: Blob;

    if (typeof content === 'string') {
      blob = new Blob([content], { type: mimeType });
    } else {
      blob = content;
    }

    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    return blob.size;
  }, []);

  // Main export function
  const exportData = useCallback(async (
    data: Record<string, any>[],
    config: ExportConfig
  ): Promise<ExportResult | null> => {
    if (!canUseExport) {
      toast.error('Você não tem permissão para exportar dados');
      return null;
    }

    if (!data || data.length === 0) {
      toast.error('Nenhum dado encontrado para exportar');
      return null;
    }

    setIsExporting(true);
    setError(null);

    try {
      const timestamp = Date.now();
      let fileName: string;
      let fileSize: number = 0;
      let recordsExported = data.length;
      const warnings: string[] = [];

      switch (config.format) {
        case 'csv': {
          const csvContent = generateCSV(data, config);
          fileName = `${collection}_export_${timestamp}.csv`;
          fileSize = downloadFile(csvContent, fileName, 'text/csv');
          break;
        }

        case 'excel': {
          const excelBlob = await generateExcel(data, config);
          fileName = `${collection}_export_${timestamp}.xlsx`;
          fileSize = downloadFile(excelBlob, fileName, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
          break;
        }

        case 'json': {
          const jsonContent = generateJSON(data, config);
          fileName = `${collection}_export_${timestamp}.json`;
          fileSize = downloadFile(jsonContent, fileName, 'application/json');
          break;
        }

        case 'pdf': {
          const pdfSettings = config.pdfSettings || { maxRows: PDF_LIMITATIONS.DEFAULT_MAX_ROWS, maxColumns: PDF_LIMITATIONS.DEFAULT_MAX_COLUMNS };

          if (data.length > pdfSettings.maxRows) {
            recordsExported = pdfSettings.maxRows;
            warnings.push(`PDF limitado a ${pdfSettings.maxRows} registros de ${data.length} disponíveis`);
          }

          await generatePDF(data, config);
          fileName = `${collection}_export_${timestamp}.pdf`;
          fileSize = 0; // PDF is downloaded directly
          break;
        }

        default:
          throw new Error(`Formato '${config.format}' não suportado`);
      }

      const result: ExportResult = {
        success: true,
        fileName,
        fileSize,
        recordsCount: data.length,
        recordsExported,
        warnings,
        limitations: config.format === 'pdf' ? {
          rowsLimited: recordsExported < data.length,
          columnsLimited: config.selectedFields.length > (config.pdfSettings?.maxColumns || PDF_LIMITATIONS.DEFAULT_MAX_COLUMNS),
          originalRowCount: data.length,
          originalColumnCount: config.selectedFields.length,
        } : undefined,
      };

      setExportResult(result);
      toast.success(`Export realizado com sucesso! Arquivo: ${fileName}`);

      if (warnings.length > 0) {
        warnings.forEach(warning => toast.warning(warning));
      }

      return result;

    } catch (error) {
      console.error('Erro na exportação:', error);
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      setError(errorMessage);
      toast.error(`Erro no export: ${errorMessage}`);
      return null;
    } finally {
      setIsExporting(false);
    }
  }, [canUseExport, collection, generateCSV, generateExcel, generateJSON, generatePDF, downloadFile]);

  return {
    // State
    isExporting,
    exportResult,
    error,
    canUseExport,

    // Actions
    exportData,
    reset,
  };
}
