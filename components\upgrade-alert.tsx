import type { ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { Alert, AlertDescription, AlertTitle } from './ui/alert';
import { Button } from './ui/button';
import { Crown, Zap, ArrowRight, CreditCard, Settings } from 'lucide-react';
import { useAuth } from '../hooks/use-auth';
import { getUserPlan } from '../lib/plan-limits';
import { useUpgradePlan, useManageSubscription, PaymentUtils } from '../hooks/use-payments';

interface UpgradeAlertProps {
  /** Título da funcionalidade que requer upgrade */
  title?: string;
  /** Descrição da limitação */
  description?: string;
  /** Plano necessário para acessar a funcionalidade */
  requiredPlan?: string;
  /** Lista de benefícios do upgrade */
  benefits?: string[];
  /** Tipo de alerta - upgrade (amarelo) ou warning (vermelho) */
  variant?: 'upgrade' | 'warning';
  /** Mostrar botão de upgrade */
  showButton?: boolean;
  /** Tamanho do botão */
  buttonSize?: 'sm' | 'default';
  /** Texto customizado do botão */
  buttonText?: string;
  /** Callback quando o botão é clicado */
  onButtonClick?: () => void;
  /** Conteúdo customizado adicional */
  children?: ReactNode;
  /** Classes CSS adicionais */
  className?: string;
}

export function UpgradeAlert({
  title = 'Funcionalidade Premium',
  description = 'Esta funcionalidade está disponível apenas para usuários com planos pagos.',
  requiredPlan = 'pro',
  benefits,
  variant = 'upgrade',
  showButton = true,
  buttonSize = 'sm',
  buttonText,
  onButtonClick,
  children,
  className
}: UpgradeAlertProps) {
  const router = useRouter();
  const { user } = useAuth();

  const upgradePlan = useUpgradePlan();
  const manageSubscription = useManageSubscription();

  const currentPlan = getUserPlan(user?.labels || []);
  const hasPaidPlan = currentPlan !== 'free';
  const paymentsConfigured = PaymentUtils.isConfigured();

  const handleUpgrade = () => {
    if (onButtonClick) {
      onButtonClick();
      return;
    }

    if (!paymentsConfigured) {
      // Fallback para página de planos se pagamentos não configurados
      router.push('/dashboard/plans');
      return;
    }

    if (hasPaidPlan) {
      // Se já tem plano pago, abrir portal de gerenciamento
      manageSubscription.mutate(window.location.href);
    } else {
      // Fazer upgrade direto
      upgradePlan.mutate({
        planId: requiredPlan,
        billingCycle: 'monthly',
      });
    }
  };

  const getButtonText = () => {
    if (buttonText) return buttonText;

    if (upgradePlan.isPending || manageSubscription.isPending) {
      return 'Processando...';
    }

    if (hasPaidPlan) {
      return 'Gerenciar Assinatura';
    }

    return paymentsConfigured ? 'Fazer Upgrade' : 'Ver Planos';
  };

  const getButtonIcon = () => {
    if (upgradePlan.isPending || manageSubscription.isPending) {
      return <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />;
    }

    if (hasPaidPlan) {
      return <Settings className="mr-2 h-4 w-4" />;
    }

    return paymentsConfigured ? <CreditCard className="mr-2 h-4 w-4" /> : <Zap className="mr-2 h-4 w-4" />;
  };

  return (
    <Alert variant={variant} className={className}>
      <Crown className="h-4 w-4" />
      <div className="flex items-center justify-between w-full">
        <div className="flex-1">
          <AlertTitle className="font-medium mb-1">{title}</AlertTitle>
          <AlertDescription className="text-sm mb-2">
            {description}
          </AlertDescription>

          {benefits && benefits.length > 0 && (
            <div className="mt-3 p-3 rounded-lg bg-primary/5 border border-primary/20">
              <h4 className="font-medium text-primary mb-2">
                🚀 Com o upgrade você terá:
              </h4>
              <ul className="text-sm text-primary/80 space-y-1">
                {benefits.map((benefit, index) => (
                  <li key={index}>• {benefit}</li>
                ))}
              </ul>
            </div>
          )}

          {children}
        </div>

        {showButton && (
          <Button
            variant="outline"
            size={buttonSize}
            className="ml-4 shrink-0"
            onClick={handleUpgrade}
            disabled={upgradePlan.isPending || manageSubscription.isPending}
          >
            {getButtonIcon()}
            {getButtonText()}
            {!upgradePlan.isPending && !manageSubscription.isPending && (
              <ArrowRight className="ml-2 h-4 w-4" />
            )}
          </Button>
        )}
      </div>
    </Alert>
  );
}

// Componente simplificado para casos básicos
export function SimpleUpgradeAlert({
  feature,
  requiredPlan = 'pro',
  className
}: {
  feature: string;
  requiredPlan?: string;
  className?: string;
}) {
  return (
    <UpgradeAlert
      title="Funcionalidade Premium"
      description={`${feature} está disponível apenas para usuários com plano ${requiredPlan.charAt(0).toUpperCase() + requiredPlan.slice(1)} ou superior.`}
      requiredPlan={requiredPlan}
      className={className}
    />
  );
}

// Componente para alertas de limitação de plano
export function PlanLimitationAlert({
  feature,
  requiredPlan,
  reason,
  className
}: {
  feature: string;
  requiredPlan?: string;
  reason?: string;
  className?: string;
}) {
  return (
    <UpgradeAlert
      title="Gerenciamento suspenso"
      description={reason || `Aprimore o plano para voltar a usar ${feature}.`}
      requiredPlan={requiredPlan}
      variant="warning"
      buttonText={`Upgrade para ${requiredPlan?.charAt(0).toUpperCase()}${requiredPlan?.slice(1)}`}
      className={className}
    />
  );
}
