'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useSearchParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Loader2, CheckCircle, XCircle, Mail } from 'lucide-react';
import { toast } from 'sonner';
import { completeEmailVerification, sendEmailVerification } from '@/lib/appwrite/functions/auth';

type VerificationStatus = 'loading' | 'success' | 'error' | 'pending';

export default function VerifyEmailPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [status, setStatus] = useState<VerificationStatus>('loading');
  const [isResending, setIsResending] = useState(false);

  const userId = searchParams.get('userId');
  const secret = searchParams.get('secret');

  useEffect(() => {
    if (userId && secret) {
      verifyEmail(userId, secret);
    } else {
      setStatus('pending');
    }
  }, [userId, secret]);

  const verifyEmail = async (userId: string, secret: string) => {
    try {
      await completeEmailVerification(userId, secret);
      setStatus('success');
      toast.success('Email verificado com sucesso!');
      
      // Redirect to dashboard after 3 seconds
      setTimeout(() => {
        router.replace('/dashboard');
      }, 3000);
    } catch (error) {
      console.error('Email verification error:', error);
      setStatus('error');
      toast.error('Erro ao verificar email. O link pode ter expirado.');
    }
  };

  const handleResendVerification = async () => {
    setIsResending(true);
    try {
      await sendEmailVerification();
      toast.success('Email de verificação reenviado!');
    } catch (error) {
      console.error('Resend verification error:', error);
      toast.error('Erro ao reenviar email de verificação.');
    } finally {
      setIsResending(false);
    }
  };

  if (status === 'loading') {
    return (
      <div className="flex flex-col gap-6">
        <div className="flex flex-col items-center gap-4 text-center">
          <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
            <Loader2 className="w-8 h-8 text-primary animate-spin" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">Verificando email...</h1>
            <p className="text-muted-foreground text-sm text-balance mt-2">
              Aguarde enquanto verificamos seu email
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (status === 'success') {
    return (
      <div className="flex flex-col gap-6">
        <div className="flex flex-col items-center gap-4 text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">Email verificado!</h1>
            <p className="text-muted-foreground text-sm text-balance mt-2">
              Sua conta foi verificada com sucesso. Redirecionando para o dashboard...
            </p>
          </div>
        </div>

        <Button asChild>
          <Link href="/dashboard">
            Ir para o Dashboard
          </Link>
        </Button>
      </div>
    );
  }

  if (status === 'error') {
    return (
      <div className="flex flex-col gap-6">
        <div className="flex flex-col items-center gap-4 text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
            <XCircle className="w-8 h-8 text-red-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">Erro na verificação</h1>
            <p className="text-muted-foreground text-sm text-balance mt-2">
              Não foi possível verificar seu email. O link pode ter expirado ou já foi usado.
            </p>
          </div>
        </div>

        <div className="space-y-4">
          <Button
            variant="outline"
            className="w-full"
            onClick={handleResendVerification}
            disabled={isResending}
          >
            {isResending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Reenviando...
              </>
            ) : (
              'Reenviar email de verificação'
            )}
          </Button>

          <Button variant="ghost" className="w-full" asChild>
            <Link href="/login">
              Voltar ao login
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  // Status 'pending' - waiting for email verification
  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col items-center gap-4 text-center">
        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
          <Mail className="w-8 h-8 text-blue-600" />
        </div>
        <div>
          <h1 className="text-2xl font-bold">Verifique seu email</h1>
          <p className="text-muted-foreground text-sm text-balance mt-2">
            Enviamos um link de verificação para seu email. Clique no link para ativar sua conta.
          </p>
        </div>
      </div>

      <div className="space-y-4">
        <div className="text-sm text-muted-foreground space-y-2">
          <p>• Verifique sua caixa de entrada e spam</p>
          <p>• O link expira em 24 horas</p>
          <p>• Se não receber, você pode solicitar um novo</p>
        </div>

        <Button
          variant="outline"
          className="w-full"
          onClick={handleResendVerification}
          disabled={isResending}
        >
          {isResending ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Reenviando...
            </>
          ) : (
            'Reenviar email de verificação'
          )}
        </Button>
      </div>

      <div className="text-center text-sm">
        <span className="text-muted-foreground">Problemas com a verificação? </span>
        <Link
          href="/help"
          className="text-primary hover:text-primary/80 transition-colors font-medium"
        >
          Central de ajuda
        </Link>
      </div>

      <div className="text-center text-sm">
        <Link
          href="/login"
          className="text-primary hover:text-primary/80 transition-colors"
        >
          Voltar ao login
        </Link>
      </div>
    </div>
  );
}
