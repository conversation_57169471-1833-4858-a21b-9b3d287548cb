/**
 * Gemini Data Processor Cloud Function
 * 
 * Processa arquivos de dados usando Google Gemini AI para extrair informações estruturadas
 * Suporta CSV, Excel, PDF e imagens para import de dados de clientes
 * 
 * Configuração necessária no Appwrite:
 * - GEMINI_API_KEY: Chave da API do Google AI
 * - GEMINI_MODEL: Modelo a usar (padrão: gemini-1.5-flash)
 * - APPWRITE_FUNCTION_ENDPOINT: Endpoint do Appwrite
 * - APPWRITE_FUNCTION_PROJECT_ID: ID do projeto
 * - APPWRITE_FUNCTION_API_KEY: Chave da API do Appwrite
 * 
 * Permissões necessárias:
 * - files.read (para ler arquivos do storage)
 * - databases.write (para salvar dados processados)
 */

import { GoogleGenerativeAI } from '@google/generative-ai';
import { Client, Databases, Storage } from 'node-appwrite';

// Configuração do Gemini
const GEMINI_MODEL = process.env.GEMINI_MODEL || 'gemini-1.5-flash';
const MAX_FILE_SIZE = parseInt(process.env.MAX_FILE_SIZE || '10485760'); // 10MB padrão

// Configuração do Appwrite
const client = new Client();
client
  .setEndpoint(process.env.APPWRITE_FUNCTION_ENDPOINT || 'https://cloud.appwrite.io/v1')
  .setProject(process.env.APPWRITE_FUNCTION_PROJECT_ID || '')
  .setKey(process.env.APPWRITE_FUNCTION_API_KEY || '');

const databases = new Databases(client);
const storage = new Storage(client);

// Inicializar Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '');

/**
 * Prompts para diferentes tipos de processamento
 */
const PROCESSING_PROMPTS = {
  clients: `
    Analise este arquivo e extraia informações de clientes/contatos.
    Retorne um JSON array com objetos contendo os seguintes campos quando disponíveis:
    - name (string, obrigatório)
    - email (string, obrigatório)
    - phone (string, opcional)
    - company (string, opcional)
    - document (string, CPF/CNPJ, opcional)
    - type (string, "pessoa_fisica" ou "pessoa_juridica", padrão "pessoa_fisica")
    - status (string, "ativo", "inativo", "prospecto", "perdido", padrão "ativo")
    - priority (string, "baixa", "media", "alta", "critica", padrão "media")
    - notes (string, opcional)
    - tags (array de strings, opcional)
    
    Exemplo de resposta:
    [
      {
        "name": "João Silva",
        "email": "<EMAIL>",
        "phone": "+55 11 99999-9999",
        "company": "Empresa ABC",
        "type": "pessoa_fisica",
        "status": "ativo",
        "priority": "alta"
      }
    ]
    
    Retorne apenas o JSON válido, sem texto adicional.
  `,
  
  teams: `
    Analise este arquivo e extraia informações de equipes/times.
    Retorne um JSON array com objetos contendo:
    - name (string, obrigatório)
    - description (string, opcional)
    - members (array de objetos com name e email, opcional)
    
    Retorne apenas o JSON válido, sem texto adicional.
  `,
  
  custom: `
    Analise este arquivo e extraia dados estruturados.
    Identifique automaticamente os campos e tipos de dados.
    Retorne um JSON array com os dados extraídos.
    Retorne apenas o JSON válido, sem texto adicional.
  `
};

/**
 * Função principal
 */
export default async ({ req, res, log, error }) => {
  try {
    // Verificar se a API key do Gemini está configurada
    if (!process.env.GEMINI_API_KEY) {
      return res.json({
        success: false,
        error: 'Gemini API key não configurada'
      }, 500);
    }

    // Parse do payload
    const payload = JSON.parse(req.payload || '{}');
    const { 
      fileId, 
      bucketId, 
      fileType, 
      targetCollection = 'clients',
      customPrompt,
      mappingRules = {}
    } = payload;

    if (!fileId || !bucketId) {
      return res.json({
        success: false,
        error: 'fileId e bucketId são obrigatórios'
      }, 400);
    }

    log(`Processando arquivo ${fileId} para coleção ${targetCollection}`);

    // 1. Baixar arquivo do storage
    const fileBuffer = await storage.getFileDownload(bucketId, fileId);
    
    // Verificar tamanho do arquivo
    if (fileBuffer.byteLength > MAX_FILE_SIZE) {
      return res.json({
        success: false,
        error: `Arquivo muito grande. Máximo: ${MAX_FILE_SIZE / 1024 / 1024}MB`
      }, 400);
    }

    // 2. Preparar prompt baseado no tipo de processamento
    let prompt = customPrompt || PROCESSING_PROMPTS[targetCollection] || PROCESSING_PROMPTS.custom;
    
    if (Object.keys(mappingRules).length > 0) {
      prompt += `\n\nRegras de mapeamento específicas: ${JSON.stringify(mappingRules)}`;
    }

    // 3. Processar com Gemini baseado no tipo de arquivo
    let extractedData;
    
    if (fileType === 'image') {
      // Processar imagem
      extractedData = await processImageWithGemini(fileBuffer, prompt, log);
    } else {
      // Processar texto (CSV, PDF, etc.)
      const textContent = fileBuffer.toString('utf-8');
      extractedData = await processTextWithGemini(textContent, prompt, log);
    }

    // 4. Validar e limpar dados extraídos
    const validatedData = validateAndCleanData(extractedData, targetCollection);

    // 5. Retornar resultado
    const result = {
      success: true,
      processedRecords: validatedData.length,
      previewData: validatedData.slice(0, 5), // Primeiros 5 registros para preview
      errors: [],
      fileId,
      targetCollection
    };

    log(`Processamento concluído: ${validatedData.length} registros extraídos`);
    
    return res.json(result);

  } catch (err) {
    error(`Erro no processamento: ${err.message}`);
    return res.json({
      success: false,
      error: err.message
    }, 500);
  }
};

/**
 * Processa imagem com Gemini Vision
 */
async function processImageWithGemini(imageBuffer, prompt, log) {
  try {
    const model = genAI.getGenerativeModel({ model: GEMINI_MODEL });
    
    const imagePart = {
      inlineData: {
        data: imageBuffer.toString('base64'),
        mimeType: 'image/jpeg' // Assumir JPEG por padrão
      }
    };

    const result = await model.generateContent([prompt, imagePart]);
    const response = await result.response;
    const text = response.text();
    
    log(`Resposta do Gemini (imagem): ${text.substring(0, 200)}...`);
    
    // Tentar fazer parse do JSON
    return JSON.parse(text.trim());
  } catch (err) {
    throw new Error(`Erro no processamento de imagem: ${err.message}`);
  }
}

/**
 * Processa texto com Gemini
 */
async function processTextWithGemini(textContent, prompt, log) {
  try {
    const model = genAI.getGenerativeModel({ model: GEMINI_MODEL });
    
    const fullPrompt = `${prompt}\n\nConteúdo do arquivo:\n${textContent}`;
    
    const result = await model.generateContent(fullPrompt);
    const response = await result.response;
    const text = response.text();
    
    log(`Resposta do Gemini (texto): ${text.substring(0, 200)}...`);
    
    // Tentar fazer parse do JSON
    return JSON.parse(text.trim());
  } catch (err) {
    throw new Error(`Erro no processamento de texto: ${err.message}`);
  }
}

/**
 * Valida e limpa dados extraídos
 */
function validateAndCleanData(data, targetCollection) {
  if (!Array.isArray(data)) {
    throw new Error('Dados extraídos devem ser um array');
  }

  const validatedData = [];
  
  for (const item of data) {
    if (targetCollection === 'clients') {
      // Validação específica para clientes
      if (!item.name || !item.email) {
        continue; // Pular registros sem nome ou email
      }
      
      // Limpar e validar dados
      const cleanItem = {
        name: String(item.name).trim(),
        email: String(item.email).toLowerCase().trim(),
        phone: item.phone ? String(item.phone).trim() : undefined,
        company: item.company ? String(item.company).trim() : undefined,
        document: item.document ? String(item.document).trim() : undefined,
        type: ['pessoa_fisica', 'pessoa_juridica'].includes(item.type) ? item.type : 'pessoa_fisica',
        status: ['ativo', 'inativo', 'prospecto', 'perdido'].includes(item.status) ? item.status : 'ativo',
        priority: ['baixa', 'media', 'alta', 'critica'].includes(item.priority) ? item.priority : 'media',
        notes: item.notes ? String(item.notes).trim() : undefined,
        tags: Array.isArray(item.tags) ? item.tags.map(tag => String(tag).trim()) : [],
        // Campos calculados
        totalProjects: 0,
        totalRevenue: 0,
        averageProjectValue: 0,
        firstContactDate: new Date().toISOString(),
      };
      
      validatedData.push(cleanItem);
    } else {
      // Validação genérica para outros tipos
      validatedData.push(item);
    }
  }
  
  return validatedData;
}
