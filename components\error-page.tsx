import { useState } from 'react';
import Link from 'next/link';
import { But<PERSON> } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Alert, AlertDescription } from './ui/alert';
import {
  AlertTriangle,
  Home,
  RefreshCw,
  ArrowLeft,
  Copy,
  Check,
  Bug,
  Wifi,
  Shield,
  Server,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { useCopyToClipboard } from '../hooks/use-copy-to-clipboard';
import { cn } from '../lib/utils';

interface ErrorPageProps {
  error?: Error | unknown;
  title?: string;
  description?: string;
  statusCode?: number;
  showDetails?: boolean;
  showRetry?: boolean;
  onRetry?: () => void;
  className?: string;
}

/**
 * Página de erro geral com opções de ação e detalhes
 */
export function ErrorPage({
  error,
  title,
  description,
  statusCode,
  showDetails = true,
  showRetry = true,
  onRetry,
  className
}: ErrorPageProps) {
  const [showErrorDetails, setShowErrorDetails] = useState(false);
  const { copyToClipboard, isCopied } = useCopyToClipboard();

  // Determinar tipo de erro e configurações baseado no status code
  const getErrorConfig = () => {
    switch (statusCode) {
      case 404:
        return {
          icon: AlertTriangle,
          title: title || "Página não encontrada",
          description: description || "A página que você está procurando não existe.",
          color: "text-orange-600 dark:text-orange-400",
          bgColor: "bg-orange-100 dark:bg-orange-900/20"
        };
      case 403:
        return {
          icon: Shield,
          title: title || "Acesso negado",
          description: description || "Você não tem permissão para acessar esta página.",
          color: "text-red-600 dark:text-red-400",
          bgColor: "bg-red-100 dark:bg-red-900/20"
        };
      case 500:
        return {
          icon: Server,
          title: title || "Erro interno do servidor",
          description: description || "Ocorreu um erro interno. Nossa equipe foi notificada.",
          color: "text-red-600 dark:text-red-400",
          bgColor: "bg-red-100 dark:bg-red-900/20"
        };
      case 503:
        return {
          icon: Wifi,
          title: title || "Serviço indisponível",
          description: description || "O serviço está temporariamente indisponível.",
          color: "text-yellow-600 dark:text-yellow-400",
          bgColor: "bg-yellow-100 dark:bg-yellow-900/20"
        };
      default:
        return {
          icon: Bug,
          title: title || "Algo deu errado",
          description: description || "Ocorreu um erro inesperado. Tente novamente.",
          color: "text-destructive",
          bgColor: "bg-destructive/10"
        };
    }
  };

  const config = getErrorConfig();
  const Icon = config.icon;

  // Preparar detalhes do erro para cópia
  const getErrorDetails = () => {
    const details = {
      timestamp: new Date().toISOString(),
      statusCode,
      url: window.location.href,
      userAgent: navigator.userAgent,
      error: error instanceof Error ? {
        name: error.name,
        message: error.message,
        stack: error.stack
      } : error
    };

    return JSON.stringify(details, null, 2);
  };

  const handleCopyError = () => {
    const errorDetails = getErrorDetails();
    copyToClipboard(errorDetails, 'Detalhes do erro copiados!');
  };

  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    } else {
      window.location.reload();
    }
  };

  return (
    <div className={cn("min-h-screen bg-background flex items-center justify-center p-4", className)}>
      <div className="max-w-2xl w-full mx-auto">
        <Card className="border-0 shadow-lg">
          <CardHeader className="text-center space-y-4">
            {/* Ícone do erro */}
            <div className="flex justify-center">
              <div className={cn("rounded-full p-4", config.bgColor)}>
                <Icon className={cn("h-12 w-12", config.color)} />
              </div>
            </div>

            {/* Status code badge */}
            {statusCode && (
              <div className="flex justify-center">
                <Badge variant="outline" className="text-sm">
                  Erro {statusCode}
                </Badge>
              </div>
            )}

            {/* Título e descrição */}
            <div className="space-y-2">
              <CardTitle className="text-2xl font-bold">
                {config.title}
              </CardTitle>
              <p className="text-muted-foreground">
                {config.description}
              </p>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Botões de ação */}
            <div className="flex flex-col sm:flex-row gap-3">
              {showRetry ? (
                <Button onClick={handleRetry} className="flex-1">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Tentar novamente
                </Button>
              ) : null}

              <Button variant="outline" className="flex-1" asChild>
                <Link href="/dashboard">
                  <Home className="h-4 w-4 mr-2" />
                  Ir para o Dashboard
                </Link>
              </Button>

              <Button
                variant="ghost"
                onClick={() => window.history.back()}
                className="flex-1"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Voltar
              </Button>
            </div>

            {/* Detalhes do erro */}
            {showDetails && error ? (
              <div className="space-y-3">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowErrorDetails(!showErrorDetails)}
                  className="w-full justify-between"
                >
                  <span className="text-sm">Detalhes técnicos</span>
                  {showErrorDetails ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>

                {showErrorDetails ? (
                  <div className="space-y-3">
                    <Alert>
                      <Bug className="h-4 w-4" />
                      <AlertDescription className="text-sm">
                        {error instanceof Error ? error.message : 'Erro desconhecido'}
                      </AlertDescription>
                    </Alert>

                    {/* Stack trace em desenvolvimento */}
                    {process.env.NODE_ENV === "development" && error instanceof Error && error.stack ? (
                      <div className="bg-muted p-3 rounded-lg">
                        <pre className="text-xs text-muted-foreground overflow-auto max-h-32">
                          {error.stack}
                        </pre>
                      </div>
                    ) : null}

                    {/* Botão para copiar erro completo */}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCopyError}
                      className="w-full"
                    >
                      {isCopied ? (
                        <>
                          <Check className="h-4 w-4 mr-2 text-green-600" />
                          Copiado!
                        </>
                      ) : (
                        <>
                          <Copy className="h-4 w-4 mr-2" />
                          Copiar detalhes completos
                        </>
                      )}
                    </Button>
                  </div>
                ) : null}
              </div>
            ) : null}

            {/* Informações de ajuda */}
            <div className="pt-4 border-t text-center">
              <p className="text-sm text-muted-foreground">
                Se o problema persistir, entre em contato com{' '}
                <Link
                  href="/dashboard/help"
                  className="text-primary hover:underline font-medium"
                >
                  nossa equipe de suporte
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
