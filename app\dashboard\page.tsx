'use client';

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { BarChart3 } from "lucide-react";
import { ChartAreaInteractive } from "@/components/chart-area-interactive";
import { SectionCards } from "@/components/section-cards";
import { ReportGenerationModal } from "@/components/dashboard/report-generation-modal";
import {
  ClientsBarChart,
  KanbanPie<PERSON>hart,
  ActivityLogsChart,
  EventsMiniCalendar
} from "@/components/dashboard";
import { useAuth } from "@/hooks/use-auth";
import { useAnalytics } from "@/hooks/use-analytics";

export default function DashboardPage() {
  const { user } = useAuth();
  const { data: analytics } = useAnalytics();
  const [showReportModal, setShowReportModal] = useState(false);

  return (
    <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
      {/* Welcome Section with Report Button */}
      <div className="px-4 lg:px-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div className="space-y-2">
            <h1 className="text-2xl font-bold tracking-tight">
              Bem-vindo, {user?.name || 'Usuário'}!
            </h1>
            <p className="text-muted-foreground">
              Aqui está um resumo da sua atividade e métricas importantes.
            </p>
          </div>

          {/* Report Generation Button */}
          <Button
            onClick={() => setShowReportModal(true)}
            className="flex items-center gap-2"
          >
            <BarChart3 className="h-4 w-4" />
            Gerar Relatório
          </Button>
        </div>
      </div>

      {/* Dashboard Cards */}
      <SectionCards />

      {/* Charts Section */}
      <div className="px-4 lg:px-6">
        <div className="grid gap-6">
          {/* Interactive Chart */}
          <ChartAreaInteractive />

          {/* New Dashboard Charts Grid */}
          <div className="grid gap-6 md:grid-cols-2">
            {/* Dados de Clientes */}
            <ClientsBarChart />

            {/* Status do Kanban */}
            <KanbanPieChart />

            {/* Logs de Atividade */}
            <ActivityLogsChart />

            {/* Mini Calendário de Eventos */}
            <EventsMiniCalendar />
          </div>
        </div>
      </div>

      {/* Report Generation Modal */}
      <ReportGenerationModal
        open={showReportModal}
        onOpenChange={setShowReportModal}
      />
    </div>
  );
}
