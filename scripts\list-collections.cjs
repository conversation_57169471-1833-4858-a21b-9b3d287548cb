/**
 * Script para listar collections existentes no Appwrite
 */

require('dotenv').config({ path: '../.env' });
const { Client, Databases } = require('node-appwrite');

// Configuração do cliente Appwrite
const client = new Client();
client
  .setEndpoint(process.env.APPWRITE_ENDPOINT)
  .setProject(process.env.APPWRITE_PROJECT_ID)
  .setKey(process.env.APPWRITE_API_KEY);

const databases = new Databases(client);
const DATABASE_ID = process.env.APPWRITE_DATABASE_ID;

async function listCollections() {
  try {
    console.log('📋 Listando collections existentes...\n');

    const response = await databases.listCollections(DATABASE_ID);

    console.log(`Total de collections: ${response.total}\n`);

    response.collections.forEach((collection, index) => {
      console.log(`${index + 1}. ${collection.name}`);
      console.log(`   ID: ${collection.$id}`);
      console.log(`   Criada em: ${collection.$createdAt}`);
      console.log(`   Atributos: ${collection.attributes?.length || 0}`);
      console.log('');
    });

    // Verificar collections específicas do chat
    const teamChatsId = process.env.NEXT_PUBLIC_APPWRITE_TEAM_CHATS_ID;
    const chatMessagesId = process.env.NEXT_PUBLIC_APPWRITE_CHAT_MESSAGES_ID;

    console.log('🔍 Verificando collections do chat:');
    console.log(`   NEXT_PUBLIC_APPWRITE_TEAM_CHATS_ID: ${teamChatsId}`);
    console.log(`   NEXT_PUBLIC_APPWRITE_CHAT_MESSAGES_ID: ${chatMessagesId}`);

    const teamChatsExists = response.collections.find(col => col.$id === teamChatsId);
    const chatMessagesExists = response.collections.find(col => col.$id === chatMessagesId);

    console.log(`   Team Chats existe: ${teamChatsExists ? '✅' : '❌'}`);
    console.log(`   Chat Messages existe: ${chatMessagesExists ? '✅' : '❌'}`);

    if (teamChatsExists) {
      console.log(`   Team Chats atributos: ${teamChatsExists.attributes?.length || 0}`);
    }
    if (chatMessagesExists) {
      console.log(`   Chat Messages atributos: ${chatMessagesExists.attributes?.length || 0}`);
    }

  } catch (error) {
    console.error('❌ Erro ao listar collections:', error);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  listCollections();
}

module.exports = { listCollections };
