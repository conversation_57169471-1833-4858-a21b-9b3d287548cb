/**
 * Modal de Busca do Kanban para Mobile
 * Interface otimizada para busca em dispositivos móveis
 */

import React, { useState, useEffect, useRef } from 'react';
import { useSnapshot } from 'valtio';
import { Search, X, Filter, Clock, User, Tag } from 'lucide-react';

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Badge } from '../ui/badge';
import { ScrollArea } from '../ui/scroll-area';
import { Separator } from '../ui/separator';

import { kanbanStore, kanbanActions, kanbanSelectors } from '../../stores/kanban-store';
import { useBoard } from '../../hooks/api/use-kanban';
import type { EmbeddedTask } from '@/schemas/kanban';

interface KanbanSearchModalProps {
  boardId?: string;
}

export function KanbanSearchModal({ boardId }: KanbanSearchModalProps) {
  const snap = useSnapshot(kanbanStore);
  const isOpen = snap.searchModal.isOpen;
  const [searchTerm, setSearchTerm] = useState(snap.filters.search);
  const inputRef = useRef<HTMLInputElement>(null);

  // Get board data for search suggestions
  const { data: boardData } = useBoard(boardId || '');

  // Focus input when modal opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  // Update local search when modal opens
  useEffect(() => {
    if (isOpen) {
      setSearchTerm(snap.filters.search);
    }
  }, [isOpen, snap.filters.search]);

  // Filter tasks based on search term - ESTRUTURA OTIMIZADA
  const filteredTasks = React.useMemo(() => {
    if (!boardData?.columns || !searchTerm.trim()) return [];

    // Extrair todas as tasks de todas as colunas
    const allTasks = boardData.columns.flatMap((column: any) =>
      column.tasks.map((task: any) => ({
        ...task,
        columnId: column.id
      }))
    );

    return allTasks.filter((task: any) => {
      const searchLower = searchTerm.toLowerCase();
      return (
        task.title.toLowerCase().includes(searchLower) ||
        task.description?.toLowerCase().includes(searchLower) ||
        task.tags.some((tag: string) => tag.toLowerCase().includes(searchLower))
      );
    }).slice(0, 10); // Limit to 10 results for performance
  }, [boardData?.columns, searchTerm]);

  // Get recent searches (could be stored in localStorage)
  const recentSearches = React.useMemo(() => {
    // For now, return some example searches
    // In a real app, you'd get this from localStorage or user preferences
    return ['Bug fixes', 'Feature', 'Design', 'Testing'];
  }, []);

  // Get popular tags from board - ESTRUTURA OTIMIZADA
  const popularTags = React.useMemo(() => {
    if (!boardData?.columns) return [];
    const tagCount: Record<string, number> = {};

    boardData.columns.forEach((column: any) => {
      column.tasks?.forEach((task: any) => {
        if (task.tags) {
          task.tags.forEach((tag: string) => {
            tagCount[tag] = (tagCount[tag] || 0) + 1;
          });
        }
      });
    });

    return Object.entries(tagCount)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 6)
      .map(([tag]) => tag);
  }, [boardData?.columns]);

  const handleClose = () => {
    kanbanActions.closeSearchModal();
  };

  const handleSearch = (term: string) => {
    kanbanActions.setSearch(term);
    handleClose();
  };

  const handleTaskClick = (task: EmbeddedTask) => {
    kanbanActions.openTaskView(task);
    handleClose();
  };

  const handleClearSearch = () => {
    setSearchTerm('');
    kanbanActions.setSearch('');
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'baixa': return 'bg-green-500';
      case 'media': return 'bg-yellow-500';
      case 'alta': return 'bg-orange-500';
      case 'critica': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'todo': return 'A Fazer';
      case 'in_progress': return 'Em Progresso';
      case 'review': return 'Em Revisão';
      case 'done': return 'Concluído';
      default: return status;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-[95vw] max-h-[90vh] overflow-hidden flex flex-col p-0">
        <DialogHeader className="flex-shrink-0 p-4 pb-0">
          <DialogTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Buscar Tarefas
          </DialogTitle>
        </DialogHeader>

        {/* Search Input */}
        <div className="flex-shrink-0 p-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              ref={inputRef}
              placeholder="Buscar por título, descrição ou tags..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && searchTerm.trim()) {
                  handleSearch(searchTerm);
                }
              }}
              className="pl-10 pr-10"
            />
            {searchTerm && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
                onClick={handleClearSearch}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>

        <ScrollArea className="flex-1">
          <div className="p-4 pt-0 space-y-4">
            {/* Search Results */}
            {searchTerm.trim() && (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium">Resultados da Busca</h3>
                  {filteredTasks.length > 0 && (
                    <Badge variant="secondary">{filteredTasks.length}</Badge>
                  )}
                </div>

                {filteredTasks.length > 0 ? (
                  <div className="space-y-2">
                    {filteredTasks.map((task: any) => (
                      <div
                        key={task.id}
                        className="p-3 border rounded-lg cursor-pointer hover:bg-muted transition-colors"
                        onClick={() => handleTaskClick(task)}
                      >
                        <div className="flex items-start justify-between gap-2">
                          <div className="flex-1 min-w-0">
                            <h4 className="font-medium text-sm truncate">{task.title}</h4>
                            {task.description && (
                              <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                                {task.description}
                              </p>
                            )}
                            <div className="flex items-center gap-2 mt-2">
                              <div className={`w-2 h-2 rounded-full ${getPriorityColor(task.priority)}`} />
                              <span className="text-xs text-muted-foreground">
                                {getStatusLabel(task.status)}
                              </span>
                              {task.tags.length > 0 && (
                                <div className="flex gap-1">
                                  {task.tags.slice(0, 2).map((tag: string) => (
                                    <Badge key={tag} variant="outline" className="text-xs">
                                      {tag}
                                    </Badge>
                                  ))}
                                  {task.tags.length > 2 && (
                                    <Badge variant="outline" className="text-xs">
                                      +{task.tags.length - 2}
                                    </Badge>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">Nenhuma tarefa encontrada</p>
                  </div>
                )}
              </div>
            )}

            {/* Quick Actions when no search */}
            {!searchTerm.trim() && (
              <>
                {/* Recent Searches */}
                {recentSearches.length > 0 && (
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <h3 className="text-sm font-medium">Buscas Recentes</h3>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {recentSearches.map((search) => (
                        <Badge
                          key={search}
                          variant="outline"
                          className="cursor-pointer"
                          onClick={() => handleSearch(search)}
                        >
                          {search}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                <Separator />

                {/* Popular Tags */}
                {popularTags.length > 0 && (
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <Tag className="h-4 w-4 text-muted-foreground" />
                      <h3 className="text-sm font-medium">Tags Populares</h3>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {popularTags.map((tag) => (
                        <Badge
                          key={tag}
                          variant="outline"
                          className="cursor-pointer"
                          onClick={() => handleSearch(tag)}
                        >
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                <Separator />

                {/* Quick Filter Access */}
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Filter className="h-4 w-4 text-muted-foreground" />
                    <h3 className="text-sm font-medium">Filtros Rápidos</h3>
                  </div>
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => {
                      handleClose();
                      kanbanActions.openFiltersModal();
                    }}
                  >
                    <Filter className="h-4 w-4 mr-2" />
                    Abrir Filtros Avançados
                    {kanbanSelectors.hasActiveFilters() && (
                      <Badge variant="secondary" className="ml-auto">
                        {Object.values(snap.filters).flat().filter(Boolean).length}
                      </Badge>
                    )}
                  </Button>
                </div>
              </>
            )}
          </div>
        </ScrollArea>

        {/* Actions */}
        {searchTerm.trim() && (
          <div className="flex-shrink-0 p-4 border-t">
            <Button
              className="w-full"
              onClick={() => handleSearch(searchTerm)}
            >
              Aplicar Busca: "{searchTerm}"
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
