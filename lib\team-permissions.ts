/**
 * Sistema de Permissões para Times
 * Define e verifica permissões baseadas nas roles dos usuários nos times
 */

import type { TeamMembership } from '@/schemas/teams';

/**
 * Roles disponíveis nos times
 */
export type TeamRole = 'owner' | 'admin' | 'member' | 'guest';

/**
 * Ações disponíveis nos times
 */
export type TeamAction =
  | 'update_team'
  | 'delete_team'
  | 'inNEXT_PUBLIC_member'
  | 'remove_member'
  | 'update_member_role'
  | 'update_own_profile'
  | 'view_team'
  | 'view_members';

/**
 * Mapeamento de permissões por role
 */
const ROLE_PERMISSIONS: Record<TeamRole, TeamAction[]> = {
  owner: [
    'update_team',
    'delete_team',
    'inNEXT_PUBLIC_member',
    'remove_member',
    'update_member_role',
    'update_own_profile',
    'view_team',
    'view_members',
  ],
  admin: [
    'inNEXT_PUBLIC_member',
    'remove_member',
    'update_member_role', // Admin pode alterar roles de member e guest
    'update_own_profile',
    'view_team',
    'view_members',
    // Admin NÃO pode: update_team, delete_team, alterar roles de owner/admin
  ],
  member: [
    'update_own_profile',
    'view_team',
    'view_members',
    // Member pode criar, editar próprios itens (definido nas permissões de recursos)
  ],
  guest: [
    'view_team',
    'view_members',
    // Guest apenas visualiza (sem update_own_profile)
  ],
};

/**
 * Verifica se um usuário tem uma permissão específica no time
 */
export function hasTeamPermission(
  membership: TeamMembership | null | undefined,
  action: TeamAction,
  targetUserId?: string
): boolean {
  if (!membership) return false;

  const userRole = getUserHighestRole(membership.roles);
  const allowedActions = ROLE_PERMISSIONS[userRole];

  // Verifica se a ação está permitida para a role
  if (!allowedActions.includes(action)) return false;

  // Regras especiais para ações específicas
  switch (action) {
    case 'update_own_profile':
      // Qualquer membro pode atualizar seu próprio perfil
      return targetUserId ? membership.userId === targetUserId : true;

    case 'remove_member':
    case 'update_member_role':
      // Owner pode fazer qualquer coisa
      if (userRole === 'owner') return true;

      // Admin não pode alterar/remover owner ou outros admins
      if (userRole === 'admin' && targetUserId) {
        // Precisaríamos da membership do target para verificar sua role
        // Por enquanto, assumimos que a validação será feita no backend
        return true;
      }

      return false;

    default:
      return true;
  }
}

/**
 * Obtém a role mais alta do usuário (caso tenha múltiplas)
 */
export function getUserHighestRole(roles: string[]): TeamRole {
  if (roles.includes('owner')) return 'owner';
  if (roles.includes('admin')) return 'admin';
  if (roles.includes('member')) return 'member';
  return 'guest'; // Default para usuários sem role específica
}

/**
 * Verifica se um usuário pode alterar a role de outro usuário
 */
export function canUpdateMemberRole(
  userMembership: TeamMembership,
  targetMembership: TeamMembership,
  newRole: TeamRole
): {
  allowed: boolean;
  reason?: string;
} {
  const userRole = getUserHighestRole(userMembership.roles);
  const targetRole = getUserHighestRole(targetMembership.roles);

  // Owner pode alterar qualquer role, Admin pode alterar member e guest
  if (userRole === 'owner') {
    // Owner pode alterar qualquer role (exceto promover para owner)
  } else if (userRole === 'admin') {
    // Admin pode alterar apenas member e guest
    if (targetRole === 'owner' || targetRole === 'admin') {
      return {
        allowed: false,
        reason: 'Administradores não podem alterar roles de proprietários ou outros administradores',
      };
    }
    if (newRole === 'admin' || newRole === 'owner') {
      return {
        allowed: false,
        reason: 'Administradores não podem promover usuários para administrador ou proprietário',
      };
    }
  } else {
    return {
      allowed: false,
      reason: 'Apenas proprietários e administradores podem alterar roles',
    };
  }

  // Owner não pode alterar sua própria role
  if (userMembership.userId === targetMembership.userId) {
    return {
      allowed: false,
      reason: 'Você não pode alterar sua própria role',
    };
  }

  // Não pode promover alguém para owner
  if (newRole === 'owner') {
    return {
      allowed: false,
      reason: 'Não é possível promover outro usuário para proprietário',
    };
  }

  return { allowed: true };
}

/**
 * Verifica se um usuário pode remover outro membro
 */
export function canRemoveMember(
  userMembership: TeamMembership,
  targetMembership: TeamMembership
): {
  allowed: boolean;
  reason?: string;
} {
  const userRole = getUserHighestRole(userMembership.roles);
  const targetRole = getUserHighestRole(targetMembership.roles);

  // Owner pode remover qualquer um (exceto a si mesmo)
  if (userRole === 'owner') {
    if (userMembership.userId === targetMembership.userId) {
      return {
        allowed: false,
        reason: 'O proprietário não pode se remover do time',
      };
    }
    return { allowed: true };
  }

  // Admin pode remover apenas members e guests
  if (userRole === 'admin') {
    if (targetRole === 'owner') {
      return {
        allowed: false,
        reason: 'Administradores não podem remover o proprietário',
      };
    }
    if (targetRole === 'admin') {
      return {
        allowed: false,
        reason: 'Administradores não podem remover outros administradores',
      };
    }
    return { allowed: true }; // Pode remover member e guest
  }

  // Members e guests não podem remover ninguém
  return {
    allowed: false,
    reason: 'Apenas proprietários e administradores podem remover usuários',
  };
}

/**
 * Obtém as ações disponíveis para um usuário no time
 */
export function getAvailableActions(membership: TeamMembership | null): TeamAction[] {
  if (!membership) return [];

  const userRole = getUserHighestRole(membership.roles);
  return ROLE_PERMISSIONS[userRole];
}

/**
 * Verifica se um usuário é proprietário do time
 */
export function isTeamOwner(membership: TeamMembership | null): boolean {
  if (!membership) return false;
  return membership.roles.includes('owner');
}

/**
 * Verifica se um usuário é admin do time
 */
export function isTeamAdmin(membership: TeamMembership | null): boolean {
  if (!membership) return false;
  return membership.roles.includes('admin');
}

/**
 * Verifica se um usuário tem role de gerenciamento (owner ou admin)
 */
export function hasManagementRole(membership: TeamMembership | null): boolean {
  return isTeamOwner(membership) || isTeamAdmin(membership);
}

/**
 * Obtém a descrição da role para exibição
 */
export function getRoleDescription(role: TeamRole): string {
  switch (role) {
    case 'owner':
      return 'Proprietário - Controle total do time e pode alterar preferências';
    case 'admin':
      return 'Administrador - CRUD completo e pode convidar membros';
    case 'member':
      return 'Usuário - Pode criar, ver e editar próprios itens';
    case 'guest':
      return 'Convidado - Apenas visualização';
    default:
      return 'Role desconhecida';
  }
}

/**
 * Obtém as permissões de uma role para exibição
 */
export function getRolePermissions(role: TeamRole): string[] {
  const actions = ROLE_PERMISSIONS[role];
  const descriptions: Record<TeamAction, string> = {
    update_team: 'Editar informações do time',
    delete_team: 'Excluir o time',
    inNEXT_PUBLIC_member: 'Convidar novos membros',
    remove_member: 'Remover membros',
    update_member_role: 'Alterar roles de membros',
    update_own_profile: 'Editar próprio perfil no time',
    view_team: 'Visualizar informações do time',
    view_members: 'Visualizar lista de membros',
  };

  return actions.map(action => descriptions[action]);
}
