/**
 * Appwrite Cloud Function: Admin Users
 *
 * Lista todos os usuários do projeto (apenas para administradores).
 *
 * CONFIGURAÇÃO DE PERMISSÕES NO APPWRITE CONSOLE:
 * 1. Acesse Functions > admin-users > Settings > Execute Access
 * 2. Configure: ["role:admin"] ou ["users"] (se validação manual)
 * 3. Scopes necessários: ["users.read"]
 *
 * VALIDAÇÃO DE ACESSO:
 * - Verifica se o usuário tem label "admin" ou "role:admin"
 * - Apenas administradores podem listar usuários
 *
 * Method: GET
 * Headers: x-appwrite-user-jwt (automático)
 *
 * Query Parameters:
 * - limit: número máximo de usuários (default: 25, max: 100)
 * - offset: offset para paginação (default: 0)
 * - search: termo de busca para filtrar por nome ou email
 *
 * Response:
 * {
 *   "success": true,
 *   "data": {
 *     "users": [...],
 *     "total": 123
 *   }
 * }
 */

import { Client, Users, Account, Query } from 'node-appwrite';

export default async ({ req, res, log, error }) => {
  // Initialize Appwrite client
  const client = new Client();
  const users = new Users(client);
  const account = new Account(client);

  // Configure client
  client
    .setEndpoint(process.env.APPWRITE_FUNCTION_ENDPOINT)
    .setProject(process.env.APPWRITE_FUNCTION_PROJECT_ID)
    .setKey(process.env.APPWRITE_FUNCTION_API_KEY);

  try {
    // Verificar se o usuário está autenticado - usar header do Appwrite
    const jwt = req.headers['x-appwrite-user-jwt'];
    if (!jwt) {
      return res.json({
        success: false,
        error: 'Token de autenticação necessário'
      }, 401);
    }

    // Configurar cliente com JWT do usuário
    const userClient = new Client();
    const userAccount = new Account(userClient);

    userClient
      .setEndpoint(process.env.APPWRITE_FUNCTION_ENDPOINT)
      .setProject(process.env.APPWRITE_FUNCTION_PROJECT_ID)
      .setJWT(jwt);

    // Verificar se o usuário atual é admin
    let currentUser;
    try {
      currentUser = await userAccount.get();
    } catch (authError) {
      log('Erro de autenticação:', authError);
      return res.json({
        success: false,
        error: 'Token inválido ou expirado'
      }, 401);
    }

    // Verificar se o usuário tem permissão de admin
    const isAdmin = currentUser.labels?.includes('admin') || false;
    if (!isAdmin) {
      return res.json({
        success: false,
        error: 'Acesso negado. Permissão de admin necessária.'
      }, 403);
    }

    // Parse query parameters
    const limit = Math.min(parseInt(req.query.limit) || 25, 100);
    const offset = parseInt(req.query.offset) || 0;
    const search = req.query.search || '';

    // Listar usuários
    let usersList;
    try {
      const queries = search ? [Query.search('name', search), Query.search('email', search)] : [];
      usersList = await users.list(queries);
    } catch (listError) {
      log('Erro ao listar usuários:', listError);
      return res.json({
        success: false,
        error: 'Erro ao buscar usuários'
      }, 500);
    }

    // Transformar dados dos usuários para formato seguro
    const transformedUsers = usersList.users.map(user => ({
      $id: user.$id,
      $createdAt: user.$createdAt,
      $updatedAt: user.$updatedAt,
      name: user.name,
      email: user.email,
      emailVerification: user.emailVerification,
      status: user.status,
      labels: user.labels,
      accessedAt: user.accessedAt,
      registration: user.registration,
      // Não incluir dados sensíveis como hash de senha, etc.
    }));

    log(`Listados ${transformedUsers.length} usuários para admin ${currentUser.email}`);

    return res.json({
      success: true,
      data: {
        users: transformedUsers,
        total: usersList.total
      }
    });

  } catch (err) {
    error('Erro na função admin-users:', err);
    return res.json({
      success: false,
      error: 'Erro interno do servidor'
    }, 500);
  }
};
