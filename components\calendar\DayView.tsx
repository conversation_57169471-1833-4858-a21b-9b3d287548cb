/**
 * Day View Component
 * Visualização diária do calendário
 */

import React, { useMemo } from 'react';
import {
  format,
  eachHourOfInterval,
  startOfDay,
  addHours,
  isToday,
  isSameDay,
  getHours,
  getMinutes
} from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { cn } from '../../lib/utils';
import { Badge } from '../ui/badge';
import { Clock } from 'lucide-react';
import { useIsMobile } from '../../hooks/use-mobile';
import type { Event } from '@/schemas/events';

export interface DayViewProps {
  currentDate: Date;
  events: Event[];
  onEventSelect?: (event: Event) => void;
  onEventCreate?: (date: Date, hour: number) => void;
  className?: string;
}

const START_HOUR = 6;
const END_HOUR = 22;
const HOUR_HEIGHT = 80; // pixels per hour - larger for day view

interface PositionedEvent {
  event: Event;
  top: number;
  height: number;
  left: number;
  width: number;
}

export function DayView({
  currentDate,
  events,
  onEventSelect,
  onEventCreate,
  className,
}: DayViewProps) {
  const isMobile = useIsMobile();
  // Generate hours
  const hours = useMemo(() => {
    const dayStart = startOfDay(currentDate);
    return eachHourOfInterval({
      start: addHours(dayStart, START_HOUR),
      end: addHours(dayStart, END_HOUR - 1),
    });
  }, [currentDate]);

  // Filter and position events for the current day
  const positionedEvents = useMemo(() => {
    const dayEvents = events.filter(event => {
      const eventStart = new Date(event.startDate);
      return isSameDay(eventStart, currentDate);
    });

    const result: PositionedEvent[] = [];

    dayEvents.forEach(event => {
      const eventStart = new Date(event.startDate);
      const eventEnd = new Date(event.endDate);

      const startHour = getHours(eventStart);
      const startMinute = getMinutes(eventStart);
      const endHour = getHours(eventEnd);
      const endMinute = getMinutes(eventEnd);

      // Calculate position
      const top = ((startHour - START_HOUR) * HOUR_HEIGHT) + (startMinute / 60 * HOUR_HEIGHT);
      const duration = (endHour - startHour) + ((endMinute - startMinute) / 60);
      const height = Math.max(duration * HOUR_HEIGHT, 30); // Minimum 30px height

      result.push({
        event,
        top,
        height,
        left: 0,
        width: 100,
      });
    });

    return result;
  }, [currentDate, events]);

  const handleTimeSlotClick = (hour: number) => {
    if (onEventCreate) {
      onEventCreate(currentDate, hour);
    }
  };

  const handleEventClick = (event: Event, e: React.MouseEvent) => {
    e.stopPropagation();
    if (onEventSelect) {
      onEventSelect(event);
    }
  };

  const isDayToday = isToday(currentDate);

  return (
    <div className={cn('h-full flex flex-col', className)}>
      {/* Day header */}
      <div className={cn(
        'p-4 border-b bg-background/80 sticky top-0 z-10',
        isDayToday && 'bg-accent/20'
      )}>
        <div className="text-center">
          <div className={cn(
            'text-3xl font-bold',
            isDayToday && 'text-primary'
          )}>
            {format(currentDate, 'd')}
          </div>
          <div className={cn(
            'text-lg font-medium',
            isDayToday && 'text-primary'
          )}>
            {format(currentDate, 'EEEE', { locale: ptBR })}
          </div>
          <div className="text-sm text-muted-foreground">
            {format(currentDate, "MMMM 'de' yyyy", { locale: ptBR })}
          </div>

          {isDayToday && (
            <Badge variant="default" className="mt-2">
              Hoje
            </Badge>
          )}
        </div>
      </div>

      {/* Time grid */}
      <div className="flex-1 overflow-auto">
        <div className="relative">
          {/* Time slots */}
          {hours.map((hour) => (
            <div
              key={hour.toString()}
              className="flex border-b hover:bg-accent/20 cursor-pointer transition-colors"
              onClick={() => handleTimeSlotClick(getHours(hour))}
            >
              {/* Time label */}
              <div className={cn(
                "flex-shrink-0 text-sm text-muted-foreground text-right border-r",
                isMobile ? "w-12 p-2 text-xs" : "w-20 p-4"
              )}>
                {format(hour, isMobile ? 'HH' : 'HH:mm')}
              </div>

              {/* Event area */}
              <div className="flex-1 relative" style={{ height: `${isMobile ? 60 : HOUR_HEIGHT}px` }}>
                {/* Hour line */}
                <div className="absolute top-0 left-0 right-0 border-t border-border/30" />
              </div>
            </div>
          ))}

          {/* Positioned events */}
          <div className={cn(
            "absolute top-0 right-0",
            isMobile ? "left-12" : "left-20"
          )}>
            {positionedEvents.map((positionedEvent) => (
              <div
                key={positionedEvent.event.$id}
                className={cn(
                  'absolute z-10 rounded-lg cursor-pointer shadow-sm border',
                  'bg-primary/90 text-primary-foreground hover:bg-primary transition-all',
                  'hover:shadow-md',
                  isMobile ? 'left-1 right-1 p-2' : 'left-2 right-2 p-3 hover:scale-[1.02]'
                )}
                style={{
                  top: `${isMobile ? positionedEvent.top * 0.75 : positionedEvent.top}px`,
                  height: `${isMobile ? Math.max(positionedEvent.height * 0.75, 40) : positionedEvent.height}px`,
                }}
                onClick={(e) => handleEventClick(positionedEvent.event, e)}
              >
                <div className="h-full flex flex-col">
                  <div className={cn(
                    "font-semibold mb-1 line-clamp-2",
                    isMobile ? "text-xs" : "text-sm"
                  )}>
                    {positionedEvent.event.title}
                  </div>

                  <div className={cn(
                    "opacity-90 mb-1",
                    isMobile ? "text-xs" : "text-xs mb-2"
                  )}>
                    <Clock className={cn("inline mr-1", isMobile ? "h-2 w-2" : "h-3 w-3")} />
                    {format(new Date(positionedEvent.event.startDate), 'HH:mm')}
                    {!isMobile && ` - ${format(new Date(positionedEvent.event.endDate), 'HH:mm')}`}
                  </div>

                  {!isMobile && positionedEvent.event.description && (
                    <div className="text-xs opacity-80 line-clamp-3 flex-1">
                      {positionedEvent.event.description}
                    </div>
                  )}

                  {!isMobile && positionedEvent.event.location && (
                    <div className="text-xs opacity-80 mt-1 truncate">
                      📍 {positionedEvent.event.location}
                    </div>
                  )}

                  {!isMobile && positionedEvent.event.category && (
                    <Badge
                      variant="secondary"
                      className="mt-2 text-xs self-start bg-white/20 text-white border-white/30"
                    >
                      {positionedEvent.event.category}
                    </Badge>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Current time indicator */}
      {isDayToday && (
        <div
          className={cn(
            "absolute right-0 z-20 pointer-events-none",
            isMobile ? "left-12" : "left-20"
          )}
          style={{
            top: `${((new Date().getHours() - START_HOUR) * (isMobile ? 60 : HOUR_HEIGHT)) + (new Date().getMinutes() / 60 * (isMobile ? 60 : HOUR_HEIGHT)) + (isMobile ? 100 : 120)}px`,
          }}
        >
          <div className="flex items-center">
            <div className={cn(
              "bg-red-500 rounded-full border-2 border-white shadow-sm",
              isMobile ? "w-2 h-2" : "w-3 h-3"
            )} />
            <div className={cn(
              "flex-1 bg-red-500",
              isMobile ? "h-0.5" : "h-0.5"
            )} />
          </div>
        </div>
      )}
    </div>
  );
}
