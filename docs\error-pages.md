# 🚨 Páginas de Erro

Este documento explica o sistema de páginas de erro implementado no template, incluindo páginas 404, páginas de erro geral e funcionalidades de debugging.

## 📋 Visão Geral

O sistema de páginas de erro oferece:

- **Páginas de erro modernas** com design consistente
- **Funcionalidade de copiar erro** para debugging
- **Diferentes tipos de erro** (404, 403, 500, etc.)
- **Integração com ErrorBoundary** do React Router
- **Sugestões de navegação** úteis para o usuário

## 🏗️ Componentes

### ErrorPage
Componente principal para exibir erros gerais:

```typescript
import { ErrorPage } from '@/components/error-page';

<ErrorPage
  error={error}
  statusCode={500}
  title="Erro interno do servidor"
  description="Ocorreu um erro interno no servidor."
  showDetails={true}
  showRetry={true}
  onRetry={() => window.location.reload()}
/>
```

**Props:**
- `error` - Objeto de erro (Error | unknown)
- `statusCode` - Código de status HTTP (opcional)
- `title` - Título personalizado
- `description` - Descrição do erro
- `showDetails` - Mostrar detalhes técnicos (padrão: true)
- `showRetry` - Mostrar botão de retry (padrão: true)
- `onRetry` - Função customizada para retry

### NotFoundPage
Componente específico para páginas 404:

```typescript
import { NotFoundPage } from '@/components/not-found-page';

<NotFoundPage
  title="Página não encontrada"
  description="A página que você está procurando não existe."
  showSuggestions={true}
/>
```

**Props:**
- `title` - Título personalizado
- `description` - Descrição personalizada
- `showSuggestions` - Mostrar sugestões de páginas (padrão: true)

## 🎯 Tipos de Erro Suportados

### Por Status Code
- **404** - Página não encontrada
- **403** - Acesso negado
- **500** - Erro interno do servidor
- **503** - Serviço indisponível

### Por Contexto
- **Network** - Erros de conexão
- **JavaScript** - Erros de aplicação
- **Authentication** - Erros de autenticação
- **Authorization** - Erros de permissão

## 🔧 Funcionalidades

### Copiar Detalhes do Erro
Permite copiar informações completas do erro para debugging:

```json
{
  "timestamp": "2024-01-15T10:30:00.000Z",
  "statusCode": 500,
  "url": "https://app.example.com/dashboard",
  "userAgent": "Mozilla/5.0...",
  "error": {
    "name": "TypeError",
    "message": "Cannot read property of undefined",
    "stack": "TypeError: Cannot read property..."
  }
}
```

### Hook useCopyToClipboard
Hook personalizado para funcionalidade de cópia:

```typescript
import { useCopyToClipboard } from '@/hooks/use-copy-to-clipboard';

function MyComponent() {
  const { copyToClipboard, isCopied } = useCopyToClipboard();

  const handleCopy = () => {
    copyToClipboard('Texto para copiar', 'Mensagem de sucesso');
  };

  return (
    <button onClick={handleCopy}>
      {isCopied ? 'Copiado!' : 'Copiar'}
    </button>
  );
}
```

## 🚀 Integração com React Router

### ErrorBoundary Global
O `root.tsx` inclui um ErrorBoundary que automaticamente usa os componentes de erro:

```typescript
export function ErrorBoundary({ error }: Route.ErrorBoundaryProps) {
  if (isRouteErrorResponse(error)) {
    if (error.status === 404) {
      return <NotFoundPage />;
    }
    return <ErrorPage statusCode={error.status} error={error} />;
  }
  
  return <ErrorPage error={error} />;
}
```

### Rotas Específicas
- `/404` - Página 404 dedicada
- `/dashboard/error-examples` - Exemplos de erro (apenas desenvolvimento)

## 🎨 Design e UX

### Características Visuais
- **Ícones contextuais** para cada tipo de erro
- **Cores temáticas** baseadas no tipo de erro
- **Layout responsivo** para mobile e desktop
- **Animações sutis** para melhor experiência

### Ações Disponíveis
- **Tentar novamente** - Recarrega a página ou executa função customizada
- **Ir para Dashboard** - Navega para página principal
- **Voltar** - Usa history.back() do navegador
- **Copiar erro** - Copia detalhes técnicos

### Sugestões de Navegação
A página 404 inclui sugestões de páginas populares:
- Dashboard principal
- Clientes
- Relatórios
- Configurações

## 🛠️ Desenvolvimento

### Testando Páginas de Erro
Em desenvolvimento, acesse `/dashboard/error-examples` para visualizar todos os tipos de erro implementados.

### Adicionando Novos Tipos
Para adicionar um novo tipo de erro:

1. **Adicione configuração no ErrorPage:**
```typescript
case 'NEW_ERROR_TYPE':
  return {
    icon: CustomIcon,
    title: "Título do erro",
    description: "Descrição do erro",
    color: "text-custom-color",
    bgColor: "bg-custom-color/10"
  };
```

2. **Atualize o ErrorHandler se necessário:**
```typescript
// app/lib/error-handler.ts
export enum ErrorType {
  // ... outros tipos
  NEW_ERROR_TYPE = 'NEW_ERROR_TYPE',
}
```

### Customização
Os componentes são altamente customizáveis através de props e podem ser estilizados usando Tailwind CSS.

## 📱 Responsividade

- **Mobile First** - Design otimizado para dispositivos móveis
- **Breakpoints** - Adaptação automática para diferentes tamanhos de tela
- **Touch Friendly** - Botões e elementos otimizados para touch

## ♿ Acessibilidade

- **ARIA Labels** - Elementos semânticos apropriados
- **Navegação por teclado** - Suporte completo para navegação via teclado
- **Contraste** - Cores com contraste adequado
- **Screen Readers** - Compatibilidade com leitores de tela

## 🔍 Debugging

### Informações Coletadas
- Timestamp do erro
- URL atual
- User Agent
- Stack trace (em desenvolvimento)
- Contexto adicional

### Logs
Todos os erros são automaticamente logados usando o sistema de logging da aplicação.

## 📚 Exemplos de Uso

### Erro Customizado
```typescript
import { ErrorPage } from '@/components/error-page';

function MyComponent() {
  const [error, setError] = useState(null);

  if (error) {
    return (
      <ErrorPage
        error={error}
        title="Erro ao carregar dados"
        description="Não foi possível carregar os dados solicitados."
        onRetry={() => {
          setError(null);
          // Lógica de retry
        }}
      />
    );
  }

  return <div>Conteúdo normal</div>;
}
```

### Página 404 Customizada
```typescript
import { NotFoundPage } from '@/components/not-found-page';

export default function CustomNotFound() {
  return (
    <NotFoundPage
      title="Produto não encontrado"
      description="O produto que você está procurando não existe em nosso catálogo."
      showSuggestions={false}
    />
  );
}
```

## 🚀 Próximos Passos

- [ ] Integração com serviços de monitoramento (Sentry)
- [ ] Páginas de erro específicas por contexto
- [ ] Métricas de erro para analytics
- [ ] Feedback do usuário em páginas de erro
- [ ] Sugestões inteligentes baseadas em histórico
