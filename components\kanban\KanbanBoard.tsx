/**
 * KanbanBoard Component
 * Main kanban board with drag and drop functionality
 */

import React, { useCallback, useMemo } from 'react';
import {
  DndContext,
  PointerSensor,
  TouchSensor,
  useSensor,
  useSensors,
  closestCorners,
  type DragEndEvent,
  type DragOverEvent,
  type DragStartEvent,
} from '@dnd-kit/core';
import { arrayMove, SortableContext, horizontalListSortingStrategy } from '@dnd-kit/sortable';
import { useSnapshot } from 'valtio';
import { Plus, Settings, Filter, Search, MoreHorizontal, ArrowLeft } from 'lucide-react';

import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '../ui/dropdown-menu';
import { Skeleton } from '../ui/skeleton';

import { KanbanColumn } from './KanbanColumn';
import { TaskFormModal } from './TaskFormModal';
import { TaskViewModal } from './TaskViewModal';
import { BoardFormModal } from './BoardFormModal';
import { ColumnFormModal } from './ColumnFormModal';
import { KanbanFiltersModal } from './KanbanFiltersModal';
import { KanbanSearchModal } from './KanbanSearchModal';


import { useBoardWithData, useMoveTask } from '../../hooks/api/use-kanban';
import { kanbanStore, kanbanActions, kanbanSelectors } from '../../stores/kanban-store';
import { useIsMobile } from '../../hooks/use-mobile';
import { useAuth } from '../../hooks/use-auth';
import type { Task, Column, Board, DragEndResult, BoardWithData } from '@/schemas/kanban';

interface KanbanBoardProps {
  boardId: string;
}

export function KanbanBoard({ boardId }: KanbanBoardProps) {
  const snap = useSnapshot(kanbanStore);
  const { data: boardData, isLoading, error } = useBoardWithData(boardId);
  const moveTaskMutation = useMoveTask();
  const isMobile = useIsMobile();
  const { user } = useAuth();

  // Drag and drop sensors - TouchSensor for mobile, PointerSensor for desktop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 3,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 150,
        tolerance: 8,
      },
    })
  );

  // Filter tasks based on current filters - ESTRUTURA OTIMIZADA
  const filteredTasks = useMemo(() => {
    if (!boardData || !boardData.columns) return [];

    // Extrair todas as tasks de todas as colunas
    const allTasks = boardData.columns.flatMap((column: any) =>
      column.tasks.map((task: any) => ({
        ...task,
        columnId: column.id // Adicionar referência da coluna
      }))
    );

    return allTasks.filter((task: any) => {
      const { filters } = snap;

      // Search filter
      if (filters.search && !task.title.toLowerCase().includes(filters.search.toLowerCase())) {
        return false;
      }

      // Priority filter
      if (filters.priority.length > 0 && !filters.priority.includes(task.priority)) {
        return false;
      }

      // Status filter
      if (filters.status.length > 0 && !filters.status.includes(task.status)) {
        return false;
      }

      // Assigned to filter
      if (filters.assignedTo.length > 0 && task.assignedTo && !filters.assignedTo.includes(task.assignedTo)) {
        return false;
      }

      // Tags filter
      if (filters.tags.length > 0 && !filters.tags.some(tag => task.tags.includes(tag))) {
        return false;
      }

      // Archived filter
      if (!filters.showArchived && task.isArchived) {
        return false;
      }

      return true;
    });
  }, [boardData?.columns || [], snap.filters]);

  // Group tasks by column - ESTRUTURA OTIMIZADA
  const tasksByColumn = useMemo(() => {
    const grouped: Record<string, any[]> = {};

    if (boardData && boardData.columns) {
      boardData.columns.forEach((column: any) => {
        grouped[column.id] = filteredTasks
          .filter((task: any) => task.columnId === column.id)
          .sort((a: any, b: any) => (a.position || 0) - (b.position || 0));
      });
    }

    return grouped;
  }, [boardData?.columns || [], filteredTasks]);

  // Drag handlers
  const handleDragStart = useCallback((event: DragStartEvent) => {
    const { active } = event;
    const task = filteredTasks.find((t: any) => t.id === active.id);
    if (task) {
      kanbanActions.setDraggedTask(task as any);
    }
  }, [filteredTasks]);

  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;

    kanbanActions.setDraggedTask(null);

    if (!over || active.id === over.id) return;

    const taskId = active.id as string;
    const overId = over.id as string;

    // Find source task
    const sourceTask = filteredTasks.find((t: any) => t.id === taskId);
    if (!sourceTask || !user) return;

    let destinationColumnId: string;
    let destinationIndex: number;

    // Check if dropped on a column or another task
    if (overId.startsWith('column-')) {
      // Dropped on column - add to end
      destinationColumnId = overId.replace('column-', '');
      const columnTasks = tasksByColumn[destinationColumnId] || [];
      destinationIndex = columnTasks.length;
    } else {
      // Dropped on task - insert before/after
      const destinationTask = filteredTasks.find((t: any) => t.id === overId);
      if (!destinationTask) return;

      destinationColumnId = destinationTask.columnId;
      const columnTasks = tasksByColumn[destinationColumnId] || [];
      const taskIndex = columnTasks.findIndex(t => t.id === overId);
      destinationIndex = taskIndex >= 0 ? taskIndex : columnTasks.length;
    }

    // Don't move if same position
    if (sourceTask.columnId === destinationColumnId &&
        Math.abs((sourceTask.position || 0) - destinationIndex) <= 1) {
      return;
    }

    // Execute move with optimistic update
    moveTaskMutation.mutate({
      boardId,
      taskId,
      newColumnId: destinationColumnId,
      newPosition: destinationIndex,
      userId: user.$id,
    });
  }, [filteredTasks, tasksByColumn, boardId, moveTaskMutation]);

  // Loading state
  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <Skeleton className="h-8 w-48" />
          <div className="flex gap-2">
            <Skeleton className="h-9 w-24" />
            <Skeleton className="h-9 w-24" />
          </div>
        </div>
        <div className="flex gap-4 overflow-x-auto">
          {[1, 2, 3].map((i) => (
            <div key={i} className="min-w-80">
              <Skeleton className="h-32 w-full mb-4" />
              <div className="space-y-3">
                <Skeleton className="h-24 w-full" />
                <Skeleton className="h-24 w-full" />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="p-6">
        <Card>
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground">
              Erro ao carregar o board. Tente novamente.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!boardData) {
    return (
      <div className="p-6">
        <Card>
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground">
              Board não encontrado.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!boardData || !boardData.columns) {
    return (
      <div className="p-6">
        <Card>
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground">
              Dados do board não encontrados.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Na estrutura otimizada, boardData É o board
  const typedBoard = boardData as any;
  const typedColumns = boardData.columns as any[];

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 md:p-6 border-b">
        <div className="flex items-center gap-2 md:gap-4 flex-1 min-w-0">
          {/* Back Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => kanbanActions.setSelectedBoard(null)}
            className="shrink-0"
          >
            <ArrowLeft className="h-4 w-4" />
            {!isMobile && <span className="ml-2">Voltar</span>}
          </Button>

          <div className="min-w-0 flex-1">
            <h1 className="text-lg md:text-2xl font-bold truncate">{typedBoard.title}</h1>
            {typedBoard.description && !isMobile && (
              <p className="text-muted-foreground truncate">{typedBoard.description}</p>
            )}
          </div>

          {!isMobile && <Badge variant="outline">{typedBoard.visibility}</Badge>}
        </div>

        <div className="flex items-center gap-1 md:gap-2 shrink-0">
          {/* Search - Hidden on mobile, show in dropdown */}
          {!isMobile && (
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Buscar tarefas..."
                value={snap.filters.search}
                onChange={(e) => kanbanActions.setSearch(e.target.value)}
                className="pl-10 w-48 lg:w-64"
              />
            </div>
          )}

          {/* Filters */}
          {!isMobile && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => kanbanActions.openFiltersModal()}
            >
              <Filter className="h-4 w-4" />
              Filtros
              {kanbanSelectors.hasActiveFilters() && (
                <Badge variant="secondary" className="ml-2">
                  {Object.values(snap.filters).flat().filter(Boolean).length}
                </Badge>
              )}
            </Button>
          )}

          {/* Label Manager - Será implementado na estrutura otimizada */}

          {/* Add Column */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => kanbanActions.openColumnCreate(boardId)}
          >
            <Plus className="h-4 w-4" />
            {!isMobile && <span className="ml-2">Coluna</span>}
          </Button>

          {/* Board Settings */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {isMobile && (
                <>
                  <DropdownMenuItem onClick={() => kanbanActions.openSearchModal()}>
                    <Search className="h-4 w-4 mr-2" />
                    Buscar
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => kanbanActions.openFiltersModal()}>
                    <Filter className="h-4 w-4 mr-2" />
                    Filtros
                    {kanbanSelectors.hasActiveFilters() && (
                      <Badge variant="secondary" className="ml-2">
                        {Object.values(snap.filters).flat().filter(Boolean).length}
                      </Badge>
                    )}
                  </DropdownMenuItem>
                </>
              )}
              <DropdownMenuItem onClick={() => kanbanActions.openBoardEdit(typedBoard)}>
                <Settings className="h-4 w-4 mr-2" />
                Configurações
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Board Content */}
      <div className="flex-1 overflow-hidden">
        <DndContext
          sensors={sensors}
          collisionDetection={closestCorners}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
        >
          <div className={`flex gap-2 md:gap-4 p-3 md:p-6 h-full overflow-x-auto ${
            isMobile ? 'pb-safe-area-inset-bottom' : ''
          }`}>
            <SortableContext
              items={typedColumns.map((col: any) => col.id)}
              strategy={horizontalListSortingStrategy}
            >
              {typedColumns.map((column: any) => (
                <KanbanColumn
                  key={column.id}
                  column={column}
                  tasks={tasksByColumn[column.id] || []}
                  boardId={boardId}
                />
              ))}
            </SortableContext>
          </div>
        </DndContext>
      </div>

      {/* Modals */}
      <TaskFormModal />
      <TaskViewModal />
      <BoardFormModal />
      <ColumnFormModal />
      <KanbanFiltersModal boardId={boardId} />
      <KanbanSearchModal boardId={boardId} />
    </div>
  );
}
