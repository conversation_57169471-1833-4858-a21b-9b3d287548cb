/**
 * PWA Initializer Component
 * Handles PWA initialization and configuration based on environment variables
 */

import { useEffect } from 'react';
import { initializePWA, logPWAConfiguration } from '../lib/pwa-config';
import { log } from '../lib/logger';

export function PWAInitializer() {
  useEffect(() => {
    const initPWA = async () => {
      try {
        // Log PWA configuration
        logPWAConfiguration();

        // Initialize PWA based on configuration
        await initializePWA();

        log.pwa('PWA Initializer completed successfully');
      } catch (error) {
        log.error('PWA Initializer failed', error instanceof Error ? error : undefined);
      }
    };

    initPWA();
  }, []);

  // This component doesn't render anything
  return null;
}

export default PWAInitializer;
