/**
 * Página de Debug do Realtime
 * Para testar se o sistema está funcionando
 */

import { RealtimeDebug } from '@/components/debug/realtime-debug';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Debug Realtime - Template',
  description: 'Ferramenta de debug para testar funcionalidade realtime',
};

export default function RealtimeDebugPage() {
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Debug: Sistema Realtime</h1>
      <RealtimeDebug />
    </div>
  );
}
