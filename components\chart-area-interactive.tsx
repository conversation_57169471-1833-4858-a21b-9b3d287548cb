import * as React from "react"
import { Area, AreaChart, CartesianGrid, XAxis } from "recharts"

import { useIsMobile } from "../hooks/use-mobile"
import { useAnalytics, useAnalyticsChartData } from "../hooks/use-analytics"
import { Skeleton } from "./ui/skeleton"
import {
  Card,
  CardAction,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "./ui/card"
import {
  type ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "./ui/chart"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select"
import {
  ToggleGroup,
  ToggleGroupItem,
} from "./ui/toggle-group"
import { DashboardErrorWrapper } from "./dashboard-error-wrapper"

export const description = "Gráfico interativo com dados reais de analytics"

const chartConfig = {
  analytics: {
    label: "Analytics",
  },
  users: {
    label: "Usuários",
    color: "hsl(var(--chart-1))",
  },
  clients: {
    label: "Clientes",
    color: "hsl(var(--chart-2))",
  },
  events: {
    label: "Eventos",
    color: "hsl(var(--chart-3))",
  },
  tasks: {
    label: "Tarefas",
    color: "hsl(var(--chart-4))",
  },
} satisfies ChartConfig

export function ChartAreaInteractive() {
  const isMobile = useIsMobile()
  const [timeRange, setTimeRange] = React.useState(() => isMobile ? "6m" : "12m")
  const { chartData } = useAnalyticsChartData()
  const { isLoading } = useAnalytics()

  React.useEffect(() => {
    if (isMobile && timeRange === "12m") {
      setTimeRange("6m")
    }
  }, [isMobile, timeRange])

  // Filtrar dados baseado no período selecionado
  const filteredData = React.useMemo(() => {
    if (!chartData || chartData.length === 0) return []

    let monthsToShow = 12
    if (timeRange === "6m") {
      monthsToShow = 6
    } else if (timeRange === "3m") {
      monthsToShow = 3
    }

    return chartData.slice(-monthsToShow)
  }, [chartData, timeRange])

  // Calcular totais para exibição
  const totalUsers = React.useMemo(() => {
    return filteredData.reduce((sum, item) => sum + item.users, 0)
  }, [filteredData])

  const totalClients = React.useMemo(() => {
    return filteredData.reduce((sum, item) => sum + item.clients, 0)
  }, [filteredData])

  const totalEvents = React.useMemo(() => {
    return filteredData.reduce((sum, item) => sum + item.events, 0)
  }, [filteredData])

  const totalTasks = React.useMemo(() => {
    return filteredData.reduce((sum, item) => sum + item.tasks, 0)
  }, [filteredData])

  // Mover toda a lógica de renderização para useMemo
  const content = React.useMemo(() => {
    if (isLoading) {
      return (
        <Card className="@container/card">
          <CardHeader>
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-4 w-48" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[250px] w-full" />
          </CardContent>
        </Card>
      )
    }

    if (!chartData || chartData.length === 0) {
      return (
        <Card className="@container/card">
          <CardHeader>
            <CardTitle>Evolução de Usuários e Clientes</CardTitle>
            <CardDescription>Dados não disponíveis</CardDescription>
          </CardHeader>
          <CardContent className="flex items-center justify-center h-[250px]">
            <p className="text-muted-foreground">Nenhum dado encontrado</p>
          </CardContent>
        </Card>
      )
    }

    return (
      <DashboardErrorWrapper
        fallbackTitle="Erro no Gráfico Interativo"
        fallbackDescription="Não foi possível carregar o gráfico de evolução."
        showSkeleton={true}
      >
        <Card className="@container/card">
          <CardHeader>
            <CardTitle>Evolução de Dados do Sistema</CardTitle>
            <CardDescription>
              <span className="hidden @[540px]/card:block">
                {totalUsers.toLocaleString('pt-BR')} usuários, {totalClients.toLocaleString('pt-BR')} clientes, {totalEvents.toLocaleString('pt-BR')} eventos e {totalTasks.toLocaleString('pt-BR')} tarefas nos últimos {timeRange === "12m" ? "12 meses" : timeRange === "6m" ? "6 meses" : "3 meses"}
              </span>
              <span className="@[540px]/card:hidden">
                Últimos {timeRange === "12m" ? "12 meses" : timeRange === "6m" ? "6 meses" : "3 meses"}
              </span>
            </CardDescription>
            <CardAction>
              <ToggleGroup
                type="single"
                value={timeRange}
                onValueChange={setTimeRange}
                variant="outline"
                className="hidden *:data-[slot=toggle-group-item]:!px-4 @[767px]/card:flex"
              >
                <ToggleGroupItem value="12m">12 meses</ToggleGroupItem>
              <ToggleGroupItem value="6m">6 meses</ToggleGroupItem>
              <ToggleGroupItem value="3m">3 meses</ToggleGroupItem>
            </ToggleGroup>
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger
                className="flex w-40 **:data-[slot=select-value]:block **:data-[slot=select-value]:truncate @[767px]/card:hidden"
                size="sm"
                aria-label="Selecionar período"
              >
                <SelectValue placeholder="12 meses" />
              </SelectTrigger>
              <SelectContent className="rounded-xl">
                <SelectItem value="12m" className="rounded-lg">
                  12 meses
                </SelectItem>
                <SelectItem value="6m" className="rounded-lg">
                  6 meses
                </SelectItem>
                <SelectItem value="3m" className="rounded-lg">
                  3 meses
                </SelectItem>
              </SelectContent>
            </Select>
          </CardAction>
        </CardHeader>
        <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
          <ChartContainer
            config={chartConfig}
            className="aspect-auto h-[250px] w-full"
          >
            <AreaChart data={filteredData}>
              <defs>
                <linearGradient id="fillUsers" x1="0" y1="0" x2="0" y2="1">
                  <stop
                    offset="5%"
                    stopColor="var(--color-users)"
                    stopOpacity={0.8}
                  />
                  <stop
                    offset="95%"
                    stopColor="var(--color-users)"
                    stopOpacity={0.1}
                  />
                </linearGradient>
                <linearGradient id="fillClients" x1="0" y1="0" x2="0" y2="1">
                  <stop
                    offset="5%"
                    stopColor="var(--color-clients)"
                    stopOpacity={0.8}
                  />
                  <stop
                    offset="95%"
                    stopColor="var(--color-clients)"
                    stopOpacity={0.1}
                  />
                </linearGradient>
                <linearGradient id="fillEvents" x1="0" y1="0" x2="0" y2="1">
                  <stop
                    offset="5%"
                    stopColor="var(--color-events)"
                    stopOpacity={0.8}
                  />
                  <stop
                    offset="95%"
                    stopColor="var(--color-events)"
                    stopOpacity={0.1}
                  />
                </linearGradient>
                <linearGradient id="fillTasks" x1="0" y1="0" x2="0" y2="1">
                  <stop
                    offset="5%"
                    stopColor="var(--color-tasks)"
                    stopOpacity={0.8}
                  />
                  <stop
                    offset="95%"
                    stopColor="var(--color-tasks)"
                    stopOpacity={0.1}
                  />
                </linearGradient>
              </defs>
              <CartesianGrid vertical={false} />
              <XAxis
                dataKey="month"
                tickLine={false}
                axisLine={false}
                tickMargin={8}
                minTickGap={32}
              />
              <ChartTooltip
                cursor={false}
                defaultIndex={isMobile ? -1 : Math.floor(filteredData.length / 2)}
                content={
                  <ChartTooltipContent
                    labelFormatter={(value) => `${value}`}
                    indicator="dot"
                    formatter={(value, name) => {
                      const labels = {
                        users: 'Usuários',
                        clients: 'Clientes',
                        events: 'Eventos',
                        tasks: 'Tarefas'
                      };
                      return [
                        new Intl.NumberFormat('pt-BR').format(value as number),
                        labels[name as keyof typeof labels] || name
                      ];
                    }}
                  />
                }
              />
              <Area
                dataKey="tasks"
                type="natural"
                fill="url(#fillTasks)"
                stroke="var(--color-tasks)"
                stackId="a"
              />
              <Area
                dataKey="events"
                type="natural"
                fill="url(#fillEvents)"
                stroke="var(--color-events)"
                stackId="a"
              />
              <Area
                dataKey="clients"
                type="natural"
                fill="url(#fillClients)"
                stroke="var(--color-clients)"
                stackId="a"
              />
              <Area
                dataKey="users"
                type="natural"
                fill="url(#fillUsers)"
                stroke="var(--color-users)"
                stackId="a"
              />
            </AreaChart>
          </ChartContainer>
        </CardContent>
      </Card>
      </DashboardErrorWrapper>
    )
  }, [isLoading, chartData, filteredData, totalUsers, totalClients, totalEvents, totalTasks, timeRange, setTimeRange, isMobile]);

  return content;
}
