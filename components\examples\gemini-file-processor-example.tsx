/**
 * Exemplo de uso da Cloud Function Gemini File Processor
 *
 * Este componente demonstra como usar o processamento de arquivos com Gemini AI
 * para extrair dados estruturados de documentos de clientes.
 *
 * NOTA: Este é um arquivo de exemplo para demonstração.
 * Erros de TypeScript são ignorados pois este é apenas um exemplo.
 */

'use client';

// @ts-nocheck - Ignorar erros TypeScript neste exemplo

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Progress } from '../ui/progress';
import { Badge } from '../ui/badge';
import { Textarea } from '../ui/textarea';
import { Alert, AlertDescription } from '../ui/alert';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import {
  useGeminiProcessor,
  useQuickGeminiProcessor,
  PROCESSING_TYPES,
  PROCESSING_TYPE_DESCRIPTIONS
} from '../../hooks/use-gemini-processor';
import { Upload, FileText, Image, AlertCircle, CheckCircle, Loader2 } from 'lucide-react';

export function GeminiFileProcessorExample() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [processingType, setProcessingType] = useState(PROCESSING_TYPES.GENERAL);
  const [customPrompt, setCustomPrompt] = useState('');

  const processor = useGeminiProcessor();
  const quickProcessor = useQuickGeminiProcessor();

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const validation = processor.validateFile(file);
      if (validation.valid) {
        setSelectedFile(file);
        processor.reset();
      } else {
        alert(validation.error);
      }
    }
  };

  const handleProcess = async () => {
    if (!selectedFile) return;

    await processor.processFile(selectedFile, {
      processingType,
      customPrompt: customPrompt || undefined,
    });
  };

  const handleQuickProcess = async () => {
    if (!selectedFile) return;
    await quickProcessor.quickProcess(selectedFile, processingType);
  };

  const renderProcessedData = (data: any) => {
    if (!data) return null;

    return (
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <strong>Arquivo:</strong> {data.file_info?.name}
          </div>
          <div>
            <strong>Tamanho:</strong> {(data.file_info?.size / 1024).toFixed(1)} KB
          </div>
          <div>
            <strong>Tipo:</strong> {data.file_info?.mimeType}
          </div>
          <div>
            <strong>Processado em:</strong> {new Date(data.processed_at).toLocaleString()}
          </div>
        </div>

        <div>
          <Label className="text-sm font-medium">Dados Extraídos:</Label>
          <pre className="mt-2 p-4 bg-muted rounded-lg text-xs overflow-auto max-h-96">
            {JSON.stringify(data.processed_data, null, 2)}
          </pre>
        </div>
      </div>
    );
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Gemini File Processor - Exemplo
          </CardTitle>
          <CardDescription>
            Demonstração do processamento de arquivos com Google Gemini AI para extração de dados estruturados.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="basic">Processamento Básico</TabsTrigger>
              <TabsTrigger value="advanced">Processamento Avançado</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="file-upload">Selecionar Arquivo</Label>
                  <Input
                    id="file-upload"
                    type="file"
                    accept=".jpg,.jpeg,.png,.webp,.pdf"
                    onChange={handleFileSelect}
                    className="mt-1"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Suportados: JPEG, PNG, WebP, PDF (máx. 10MB)
                  </p>
                </div>

                <div>
                  <Label htmlFor="processing-type">Tipo de Processamento</Label>
                  <Select value={processingType} onValueChange={(value) => setProcessingType(value as any)}>
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(PROCESSING_TYPE_DESCRIPTIONS).map(([key, description]) => (
                        <SelectItem key={key} value={key}>
                          {description}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex gap-2">
                  <Button
                    onClick={handleQuickProcess}
                    disabled={!selectedFile || quickProcessor.isLoading}
                    className="flex-1"
                  >
                    {quickProcessor.isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Processando...
                      </>
                    ) : (
                      <>
                        <Upload className="mr-2 h-4 w-4" />
                        Processar Rápido
                      </>
                    )}
                  </Button>
                </div>

                {quickProcessor.error && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{quickProcessor.error}</AlertDescription>
                  </Alert>
                )}

                {quickProcessor.processedData && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-green-500" />
                        Processamento Concluído
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {renderProcessedData(quickProcessor.processedData)}
                    </CardContent>
                  </Card>
                )}
              </div>
            </TabsContent>

            <TabsContent value="advanced" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="file-upload-advanced">Selecionar Arquivo</Label>
                  <Input
                    id="file-upload-advanced"
                    type="file"
                    accept=".jpg,.jpeg,.png,.webp,.pdf"
                    onChange={handleFileSelect}
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor="processing-type-advanced">Tipo de Processamento</Label>
                  <Select value={processingType} onValueChange={(value) => setProcessingType(value as any)}>
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(PROCESSING_TYPE_DESCRIPTIONS).map(([key, description]) => (
                        <SelectItem key={key} value={key}>
                          {description}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="custom-prompt">Prompt Personalizado (Opcional)</Label>
                  <Textarea
                    id="custom-prompt"
                    placeholder="Digite um prompt personalizado para o processamento..."
                    value={customPrompt}
                    onChange={(e) => setCustomPrompt(e.target.value)}
                    className="mt-1"
                    rows={3}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Se fornecido, este prompt será usado em vez do prompt padrão do tipo selecionado.
                  </p>
                </div>

                {selectedFile && (
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-4">
                        <div className="flex-shrink-0">
                          {selectedFile.type.startsWith('image/') ? (
                            <Image className="h-8 w-8 text-blue-500" />
                          ) : (
                            <FileText className="h-8 w-8 text-red-500" />
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium truncate">{selectedFile.name}</p>
                          <p className="text-xs text-muted-foreground">
                            {(selectedFile.size / 1024).toFixed(1)} KB • {selectedFile.type}
                          </p>
                        </div>
                        <Badge variant="outline">
                          {PROCESSING_TYPE_DESCRIPTIONS[processingType]}
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {(processor.isUploading || processor.isProcessing) && (
                  <Card>
                    <CardContent className="pt-6">
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>
                            {processor.isUploading ? 'Enviando arquivo...' : 'Processando com Gemini...'}
                          </span>
                          <span>{processor.uploadProgress}%</span>
                        </div>
                        <Progress value={processor.uploadProgress} className="w-full" />
                      </div>
                    </CardContent>
                  </Card>
                )}

                <Button
                  onClick={handleProcess}
                  disabled={!selectedFile || processor.isLoading}
                  className="w-full"
                >
                  {processor.isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {processor.isUploading ? 'Enviando...' : 'Processando...'}
                    </>
                  ) : (
                    <>
                      <Upload className="mr-2 h-4 w-4" />
                      Processar com Gemini
                    </>
                  )}
                </Button>

                {processor.error && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{processor.error}</AlertDescription>
                  </Alert>
                )}

                {processor.processedData && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-green-500" />
                        Processamento Concluído
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {renderProcessedData(processor.processedData)}
                    </CardContent>
                  </Card>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Informações sobre configuração */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Configuração Necessária</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <strong>Status da Cloud Function:</strong>
              <Badge variant={processor.isFunctionConfigured ? "default" : "destructive"} className="ml-2">
                {processor.isFunctionConfigured ? "Configurada" : "Não Configurada"}
              </Badge>
            </div>
            <div>
              <strong>Modelo Gemini:</strong> gemini-1.5-flash (mais barato)
            </div>
          </div>

          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Para usar esta funcionalidade:</strong>
              <ol className="list-decimal list-inside mt-2 space-y-1">
                <li>Configure a variável GEMINI_API_KEY no Appwrite</li>
                <li>Faça deploy da cloud function gemini-file-processor</li>
                <li>Configure a URL da função no .env: NEXT_PUBLIC_CLOUDFUNCTION_GEMINI_FILE_PROCESSOR</li>
              </ol>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    </div>
  );
}
