import type { <PERSON>ada<PERSON> } from "next";
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  ArrowLeft,
  Search,
  Book,
  MessageCircle,
  Mail,
  Phone,
  Code,
  Settings,
  Users,
  Zap,
  Shield,
  ExternalLink
} from 'lucide-react';

export const metadata: Metadata = {
  title: "Central de Ajuda - Template Appwrite",
  description: "Encontre respostas, documentação e suporte para usar nosso template",
};

const helpCategories = [
  {
    icon: Code,
    title: "Desenvolvimento",
    description: "Guias técnicos e documentação do código",
    articles: [
      "Como configurar o ambiente de desenvolvimento",
      "Estrutura de pastas e arquivos",
      "Customizando componentes",
      "Integrando com APIs externas"
    ]
  },
  {
    icon: Settings,
    title: "Configuração",
    description: "Setup inicial e configurações do sistema",
    articles: [
      "Configurando Appwrite",
      "Variáveis de ambiente",
      "Deploy em produção",
      "Configurações de segurança"
    ]
  },
  {
    icon: Users,
    title: "Autenticação",
    description: "Gerenciamento de usuários e permissões",
    articles: [
      "Sistema de login e registro",
      "Recuperação de senha",
      "Permissões e roles",
      "Autenticação social"
    ]
  },
  {
    icon: Zap,
    title: "Funcionalidades",
    description: "Como usar os recursos disponíveis",
    articles: [
      "Dashboard e analytics",
      "Sistema de notificações",
      "Chat em tempo real",
      "Gerenciamento de arquivos"
    ]
  }
];

const faqs = [
  {
    question: "Como faço para começar a usar o template?",
    answer: "Primeiro, clone o repositório, instale as dependências com 'yarn install', configure as variáveis de ambiente e execute 'yarn dev' para iniciar o servidor de desenvolvimento."
  },
  {
    question: "Posso usar este template comercialmente?",
    answer: "Sim! O template é fornecido sob licença MIT, permitindo uso comercial. Você pode criar e vender aplicações baseadas neste template."
  },
  {
    question: "Como personalizar o design e cores?",
    answer: "O template usa Tailwind CSS e shadcn/ui. Você pode personalizar cores editando o arquivo de configuração do Tailwind e as variáveis CSS."
  },
  {
    question: "Há suporte para diferentes bancos de dados?",
    answer: "O template é otimizado para Appwrite, mas você pode adaptar para outros bancos modificando as funções de API na pasta lib/appwrite."
  }
];

export default function HelpPage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Voltar
              </Link>
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Central de Ajuda</h1>
              <p className="text-muted-foreground">Encontre respostas e documentação</p>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12 max-w-6xl">
        {/* Search */}
        <section className="mb-12">
          <Card className="bg-primary/5 border-primary/20">
            <CardContent className="p-8 text-center">
              <h2 className="text-2xl font-bold mb-4">Como podemos ajudar?</h2>
              <p className="text-muted-foreground mb-6">
                Pesquise na nossa base de conhecimento ou navegue pelas categorias abaixo
              </p>
              <div className="relative max-w-md mx-auto">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <Input 
                  placeholder="Pesquisar artigos..." 
                  className="pl-10"
                />
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Categories */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold mb-8">Categorias de Ajuda</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {helpCategories.map((category, index) => (
              <Card key={index} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                    <category.icon className="w-6 h-6 text-primary" />
                  </div>
                  <CardTitle className="text-lg">{category.title}</CardTitle>
                  <CardDescription>{category.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {category.articles.slice(0, 3).map((article, articleIndex) => (
                      <li key={articleIndex}>
                        <Link 
                          href="#" 
                          className="text-sm text-muted-foreground hover:text-primary transition-colors"
                        >
                          {article}
                        </Link>
                      </li>
                    ))}
                  </ul>
                  <Button variant="ghost" size="sm" className="mt-4 p-0 h-auto">
                    Ver todos os artigos
                    <ExternalLink className="w-3 h-3 ml-1" />
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Quick Links */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold mb-8">Links Rápidos</h2>
          <div className="grid md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <div className="flex items-center gap-3">
                  <Book className="w-5 h-5 text-primary" />
                  <CardTitle>Documentação</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Documentação técnica completa com exemplos de código
                </p>
                <Button variant="outline" size="sm">
                  Acessar Docs
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="flex items-center gap-3">
                  <Code className="w-5 h-5 text-primary" />
                  <CardTitle>Exemplos</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Exemplos práticos e demos funcionais
                </p>
                <Button variant="outline" size="sm">
                  Ver Exemplos
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="flex items-center gap-3">
                  <MessageCircle className="w-5 h-5 text-primary" />
                  <CardTitle>Comunidade</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Participe da nossa comunidade no Discord
                </p>
                <Button variant="outline" size="sm">
                  Entrar no Discord
                </Button>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* FAQ */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold mb-8">Perguntas Frequentes</h2>
          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="text-lg">{faq.question}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">{faq.answer}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Contact Support */}
        <section>
          <Card className="bg-muted/50">
            <CardHeader className="text-center">
              <CardTitle>Ainda precisa de ajuda?</CardTitle>
              <CardDescription>
                Nossa equipe de suporte está pronta para ajudar
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <Mail className="w-6 h-6 text-primary" />
                  </div>
                  <h4 className="font-semibold mb-2">Email</h4>
                  <p className="text-sm text-muted-foreground mb-4">
                    Resposta em até 24 horas
                  </p>
                  <Button variant="outline" size="sm">
                    Enviar Email
                  </Button>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <MessageCircle className="w-6 h-6 text-primary" />
                  </div>
                  <h4 className="font-semibold mb-2">Chat</h4>
                  <p className="text-sm text-muted-foreground mb-4">
                    Suporte em tempo real
                  </p>
                  <Button variant="outline" size="sm">
                    Iniciar Chat
                  </Button>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <Phone className="w-6 h-6 text-primary" />
                  </div>
                  <h4 className="font-semibold mb-2">Telefone</h4>
                  <p className="text-sm text-muted-foreground mb-4">
                    Seg-Sex, 9h às 18h
                  </p>
                  <Button variant="outline" size="sm">
                    Ligar Agora
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  );
}
