/**
 * Sistema de Permissões
 * Gerencia verificação de permissões e cargos customizáveis usando preferências do time
 */

import { ID } from 'appwrite';
import type { TeamMembership } from '@/schemas/teams';
import type {
  UserType,
  SystemResource,
  ResourceAction,
  ResourcePermission,
  TeamRole,
  TeamMemberRole,
  UserPermissionContext,
  PermissionCheck,
  CreateTeamRoleData,
  UpdateTeamRoleData,
  AssignRoleData,
  TeamPermissionPreferences,
} from '@/schemas/permissions';

import {
  OWNER_PERMISSIONS,
  DEFAULT_GUEST_PERMISSIONS,
  DEFAULT_USER_PERMISSIONS,
  DEFAULT_ADMIN_PERMISSIONS,
  DEFAULT_OWNER_ROLE,
  DEFAULT_ADMIN_ROLE,
  DEFAULT_USER_ROLE,
  DEFAULT_GUEST_ROLE,
} from '../schemas/permissions';

// ============================================================================
// VERIFICAÇÃO DE PERMISSÕES
// ============================================================================

/**
 * Verifica se um usuário tem permissão para uma ação específica
 */
export function hasPermission(
  context: UserPermissionContext,
  resource: SystemResource,
  action: ResourceAction
): PermissionCheck {
  // Owners têm acesso total
  if (context.hasFullAccess) {
    return { allowed: true };
  }

  // Verificar se o recurso está nas permissões do usuário
  const resourcePermission = context.permissions.find(p => p.resource === resource);

  if (!resourcePermission) {
    return {
      allowed: false,
      reason: `Sem acesso ao recurso ${resource}`,
      requiredPermission: { resource, action }
    };
  }

  // Verificar se a ação está permitida
  if (!resourcePermission.actions.includes(action)) {
    return {
      allowed: false,
      reason: `Sem permissão para ${action} em ${resource}`,
      requiredPermission: { resource, action }
    };
  }

  return { allowed: true };
}

/**
 * Obtém o contexto de permissões de um usuário
 */
export function getUserPermissionContext(
  userId: string,
  teamId: string,
  membership: TeamMembership | null,
  teamPreferences: TeamPermissionPreferences | null
): UserPermissionContext {
  // Se não há membership, usuário não tem acesso
  if (!membership) {
    return {
      userId,
      currentTeamId: teamId,
      userType: 'guest',
      permissions: [],
      hasFullAccess: false,
      accessibleResources: [],
    };
  }

  // Verificar se é owner (baseado nas roles do Appwrite)
  const isOwner = membership.roles.includes('owner');

  if (isOwner) {
    return {
      userId,
      currentTeamId: teamId,
      userType: 'owner',
      permissions: OWNER_PERMISSIONS,
      hasFullAccess: true,
      accessibleResources: OWNER_PERMISSIONS.map(p => p.resource),
    };
  }

  // Para outros tipos, buscar o cargo atribuído
  const memberRole = teamPreferences?.memberRoles?.find(mr => mr.userId === userId);
  const role = memberRole ? teamPreferences?.roles?.find(r => r.id === memberRole.roleId) : null;

  // Determinar tipo de usuário e permissões baseado no cargo ou roles do Appwrite
  let userType: UserType = 'user'; // padrão
  let permissions: ResourcePermission[] = DEFAULT_USER_PERMISSIONS;

  if (role) {
    // Se tem cargo customizado, usar suas permissões
    userType = role.userType;
    permissions = role.permissions;
  } else {
    // Se não tem cargo customizado, verificar roles do Appwrite
    if (membership.roles.includes('admin')) {
      userType = 'admin';
      permissions = DEFAULT_ADMIN_PERMISSIONS;
    } else if (membership.roles.includes('member')) {
      userType = 'user';
      permissions = DEFAULT_USER_PERMISSIONS;
    } else if (membership.roles.includes('guest')) {
      userType = 'guest';
      permissions = DEFAULT_GUEST_PERMISSIONS;
    }
  }

  return {
    userId,
    currentTeamId: teamId,
    userType,
    roleId: role?.id,
    roleName: role?.name,
    permissions,
    hasFullAccess: false,
    accessibleResources: permissions.map(p => p.resource),
  };
}

/**
 * Filtra recursos da sidebar baseado nas permissões do usuário
 */
export function getAccessibleSidebarItems<T extends { title: string; url: string; resource?: SystemResource }>(
  context: UserPermissionContext,
  allItems: T[]
): T[] {
  return allItems.filter(item => {
    // Se não tem resource definido, sempre mostrar (ex: ajuda)
    if (!item.resource) return true;

    // Verificar se tem permissão de visualizar
    const check = hasPermission(context, item.resource, 'view');
    return check.allowed;
  });
}

// ============================================================================
// GERENCIAMENTO DE CARGOS
// ============================================================================

/**
 * Cria um novo cargo no time
 */
export function createTeamRole(
  teamPreferences: TeamPermissionPreferences,
  data: CreateTeamRoleData,
  createdBy: string
): TeamPermissionPreferences {
  const now = new Date().toISOString();
  const roleId = ID.unique();

  const newRole: TeamRole = {
    id: roleId,
    name: data.name,
    description: data.description,
    color: data.color || '#3b82f6',
    userType: data.userType,
    permissions: data.permissions,
    isDefault: data.isDefault || false,
    isActive: true,
    createdBy,
    createdAt: now,
  };

  // Se é cargo padrão, remover flag de outros cargos
  const updatedRoles = teamPreferences.roles || [];
  if (newRole.isDefault) {
    updatedRoles.forEach(role => {
      if (role.userType === newRole.userType) {
        role.isDefault = false;
      }
    });
  }

  return {
    ...teamPreferences,
    roles: [...updatedRoles, newRole],
    permissionsVersion: '1.0',
    lastPermissionUpdate: now,
  };
}

/**
 * Atualiza um cargo existente
 */
export function updateTeamRole(
  teamPreferences: TeamPermissionPreferences,
  roleId: string,
  data: UpdateTeamRoleData,
  updatedBy: string
): TeamPermissionPreferences {
  const now = new Date().toISOString();
  const roles = teamPreferences.roles || [];

  const updatedRoles = roles.map(role => {
    if (role.id === roleId) {
      const updatedRole = {
        ...role,
        ...data,
        updatedBy,
        updatedAt: now,
      };

      // Se está sendo marcado como padrão, remover flag de outros
      if (data.isDefault) {
        roles.forEach(r => {
          if (r.id !== roleId && r.userType === role.userType) {
            r.isDefault = false;
          }
        });
      }

      return updatedRole;
    }
    return role;
  });

  return {
    ...teamPreferences,
    roles: updatedRoles,
    lastPermissionUpdate: now,
  };
}

/**
 * Remove um cargo (soft delete)
 */
export function deleteTeamRole(
  teamPreferences: TeamPermissionPreferences,
  roleId: string
): TeamPermissionPreferences {
  const now = new Date().toISOString();
  const roles = teamPreferences.roles || [];

  const updatedRoles = roles.map(role =>
    role.id === roleId
      ? { ...role, isActive: false, updatedAt: now }
      : role
  );

  // Remover atribuições deste cargo
  const memberRoles = teamPreferences.memberRoles || [];
  const updatedMemberRoles = memberRoles.filter(mr => mr.roleId !== roleId);

  return {
    ...teamPreferences,
    roles: updatedRoles,
    memberRoles: updatedMemberRoles,
    lastPermissionUpdate: now,
  };
}

/**
 * Atribui um cargo a um membro
 */
export function assignRoleToMember(
  teamPreferences: TeamPermissionPreferences,
  data: AssignRoleData,
  assignedBy: string
): TeamPermissionPreferences {
  const now = new Date().toISOString();
  const role = teamPreferences.roles?.find(r => r.id === data.roleId);

  if (!role) {
    throw new Error('Cargo não encontrado');
  }

  const memberRoles = teamPreferences.memberRoles || [];

  // Remover atribuição anterior se existir
  const filteredMemberRoles = memberRoles.filter(mr => mr.userId !== data.userId);

  // Adicionar nova atribuição
  const newMemberRole: TeamMemberRole = {
    userId: data.userId,
    roleId: data.roleId,
    roleName: role.name,
    userType: role.userType,
    assignedBy,
    assignedAt: now,
  };

  return {
    ...teamPreferences,
    memberRoles: [...filteredMemberRoles, newMemberRole],
    lastPermissionUpdate: now,
  };
}

/**
 * Remove atribuição de cargo de um membro
 */
export function unassignRoleFromMember(
  teamPreferences: TeamPermissionPreferences,
  userId: string
): TeamPermissionPreferences {
  const now = new Date().toISOString();
  const memberRoles = teamPreferences.memberRoles || [];

  const updatedMemberRoles = memberRoles.filter(mr => mr.userId !== userId);

  return {
    ...teamPreferences,
    memberRoles: updatedMemberRoles,
    lastPermissionUpdate: now,
  };
}

// ============================================================================
// INICIALIZAÇÃO DE CARGOS PADRÃO
// ============================================================================

/**
 * Inicializa cargos padrão para um time novo
 */
export function initializeDefaultRoles(
  teamPreferences: TeamPermissionPreferences,
  createdBy: string
): TeamPermissionPreferences {
  const now = new Date().toISOString();

  const ownerRole: TeamRole = {
    ...DEFAULT_OWNER_ROLE,
    id: ID.unique(),
    createdBy,
    createdAt: now,
  };

  const adminRole: TeamRole = {
    ...DEFAULT_ADMIN_ROLE,
    id: ID.unique(),
    createdBy,
    createdAt: now,
  };

  const userRole: TeamRole = {
    ...DEFAULT_USER_ROLE,
    id: ID.unique(),
    createdBy,
    createdAt: now,
  };

  const guestRole: TeamRole = {
    ...DEFAULT_GUEST_ROLE,
    id: ID.unique(),
    createdBy,
    createdAt: now,
  };

  return {
    ...teamPreferences,
    roles: [ownerRole, adminRole, userRole, guestRole],
    memberRoles: [],
    permissionsVersion: '2.0', // Incrementar versão para nova estrutura
    lastPermissionUpdate: now,
  };
}

/**
 * Obtém o cargo padrão para um tipo de usuário
 */
export function getDefaultRoleForUserType(
  teamPreferences: TeamPermissionPreferences,
  userType: UserType
): TeamRole | null {
  const roles = teamPreferences.roles || [];
  return roles.find(role => role.userType === userType && role.isDefault && role.isActive) || null;
}
