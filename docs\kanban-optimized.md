# 🚀 Kanban Otimizado - Nova Estrutura

Este documento explica a nova estrutura otimizada do sistema Kanban, que reduz de **7 coleções** para apenas **2 coleções**, melhorando drasticamente a performance e simplificando as operações.

## 📊 Comparação: An<PERSON> vs De<PERSON><PERSON>

### ❌ Estrutura Antiga (7 coleções)
```
📁 WORKSPACES
📁 KANBAN_BOARDS
📁 KANBAN_COLUMNS  
📁 KANBAN_TASKS
📁 LABELS
📁 CHECKLISTS
📁 COMMENTS
📁 ATTACHMENTS
```

**Problemas:**
- 🐌 Múltiplas queries para carregar um board completo
- 🔄 Relacionamentos complexos entre coleções
- 📈 Alto número de operações de I/O
- 🐛 Possibilidade de inconsistências de dados

### ✅ Estrutura Nova (2 coleções)
```
📁 WORKSPACES
📁 KANBAN_BOARDS (com tudo embarcado)
   └── labels: EmbeddedLabel[]
   └── columns: EmbeddedColumn[]
       └── tasks: EmbeddedTask[]
           ├── checklists: EmbeddedChecklist[]
           ├── comments: EmbeddedComment[]
           └── attachments: EmbeddedAttachment[]
```

**Vantagens:**
- ⚡ Uma única query carrega tudo
- 🎯 Estrutura simples e consistente
- 📉 Redução drástica de I/O
- 🔒 Consistência garantida por design

## 🏗️ Arquitetura da Nova Estrutura

### 1. Workspace (mantido igual)
```typescript
interface OptimizedWorkspace {
  $id: string;
  name: string;
  description?: string;
  members: string[];
  // ... outros campos
}
```

### 2. Board Otimizado (tudo embarcado)
```typescript
interface OptimizedBoard {
  $id: string;
  title: string;
  description?: string;
  
  // 🎯 DADOS EMBARCADOS
  labels: EmbeddedLabel[];      // Antes: coleção separada
  columns: EmbeddedColumn[];    // Antes: coleção separada
  
  // ... configurações e metadados
}
```

### 3. Estruturas Embarcadas

#### EmbeddedColumn
```typescript
interface EmbeddedColumn {
  id: string;
  title: string;
  tasks: EmbeddedTask[];  // Tasks embarcadas na coluna
  // ... outros campos
}
```

#### EmbeddedTask
```typescript
interface EmbeddedTask {
  id: string;
  title: string;
  description?: string;
  
  // 🎯 DADOS EMBARCADOS
  checklists: EmbeddedChecklist[];    // Antes: coleção separada
  comments: EmbeddedComment[];        // Antes: coleção separada
  attachments: EmbeddedAttachment[];  // Antes: coleção separada
  
  // ... outros campos
}
```

## 🔧 Como Usar a Nova Estrutura

### 1. Importar os Novos Tipos
```typescript
import type {
  OptimizedWorkspace,
  OptimizedBoard,
  EmbeddedColumn,
  EmbeddedTask,
  CreateBoardData,
  UpdateTaskData,
} from '@/schemas/kanban-optimized';
```

### 2. Usar os Novos Hooks
```typescript
import {
  useOptimizedBoard,
  useOptimizedUserBoards,
  useAddTaskToBoard,
  useMoveTaskInBoard,
} from '@/hooks/api/use-kanban-optimized';

// Carregar board completo com UMA query
const { data: board } = useOptimizedBoard(boardId);

// Acessar dados embarcados diretamente
const columns = board?.columns || [];
const tasks = columns.flatMap(col => col.tasks);
const labels = board?.labels || [];
```

### 3. Usar as Novas Funções Backend
```typescript
import * as kanbanOptimized from '@/lib/appwrite/functions/kanban-optimized';

// Adicionar task (atualiza o board inteiro)
await kanbanOptimized.addTaskToBoard(boardId, columnId, taskData, userId);

// Mover task (uma operação atômica)
await kanbanOptimized.moveTaskInBoard(boardId, taskId, newColumnId, newPosition, userId);

// Adicionar comentário (embarcado na task)
await kanbanOptimized.addCommentToTask(boardId, taskId, commentData, userId);
```

## 🚀 Performance Gains

### Antes (7 queries para carregar um board)
```typescript
// ❌ Estrutura antiga
const board = await getBoard(boardId);           // Query 1
const columns = await listColumns(boardId);     // Query 2  
const tasks = await listTasks(boardId);         // Query 3
const labels = await listLabels(boardId);       // Query 4
const checklists = await listChecklists(tasks); // Query 5
const comments = await listComments(tasks);     // Query 6
const attachments = await listAttachments(tasks); // Query 7

// Total: 7 queries + processamento de relacionamentos
```

### Depois (1 query para carregar tudo)
```typescript
// ✅ Estrutura otimizada
const board = await getBoardWithData(boardId);  // Query 1 - TUDO!

// Total: 1 query, dados já estruturados
```

### Resultados Esperados
- 📈 **85% menos queries** para operações de leitura
- ⚡ **3-5x mais rápido** para carregar boards
- 🔄 **50% menos operações** para atualizações
- 💾 **Cache mais eficiente** (um objeto vs múltiplos)

## 🛠️ Migração dos Dados Existentes

### 1. Executar Script de Migração
```bash
# Instalar dependências se necessário
npm install tsx

# Executar migração
npx tsx scripts/migrate-to-optimized-kanban.ts
```

### 2. Verificar Migração
```typescript
// Testar se os dados foram migrados corretamente
const { data: boards } = useOptimizedUserBoards(userId);
console.log('Boards migrados:', boards?.length);
```

### 3. Atualizar Configuração
```typescript
// Usar nova configuração
import config from '@/lib/appwrite/config-optimized';

// Em vez de
import config from '@/lib/appwrite/config';
```

## 🔄 Operações Comuns

### Criar Nova Task
```typescript
const addTask = useAddTaskToBoard();

await addTask.mutateAsync({
  boardId,
  columnId,
  taskData: {
    title: 'Nova tarefa',
    description: 'Descrição da tarefa',
    priority: 'alta',
    assignedTo: userId,
  },
  userId,
});
```

### Mover Task (Drag & Drop)
```typescript
const moveTask = useMoveTaskInBoard();

await moveTask.mutateAsync({
  boardId,
  taskId,
  newColumnId,
  newPosition,
  userId,
});
```

### Adicionar Comentário
```typescript
// Usar função backend diretamente
await kanbanOptimized.addCommentToTask(boardId, taskId, {
  content: 'Ótimo trabalho!',
  mentions: ['user123'],
}, userId);
```

### Adicionar Checklist
```typescript
await kanbanOptimized.addChecklistToTask(boardId, taskId, {
  title: 'Lista de verificação',
}, userId);
```

## 🎯 Helpers Utilitários

### Encontrar Task em Board
```typescript
import { findTaskInBoard } from '@/schemas/kanban-optimized';

const result = findTaskInBoard(board, taskId);
if (result) {
  const { task, columnId } = result;
  console.log(`Task "${task.title}" está na coluna ${columnId}`);
}
```

### Calcular Estatísticas
```typescript
import { calculateBoardStats } from '@/schemas/kanban-optimized';

const stats = calculateBoardStats(board);
console.log(`Total de tasks: ${stats.totalTasks}`);
console.log(`Tasks concluídas: ${stats.completedTasks}`);
```

## ⚠️ Considerações Importantes

### 1. Tamanho dos Documentos
- Boards grandes podem ter documentos maiores
- Appwrite suporta até 1MB por documento
- Para boards muito grandes, considere paginação

### 2. Atualizações Atômicas
- Todas as operações são atômicas por board
- Não há risco de inconsistências entre coleções
- Conflitos de concorrência são minimizados

### 3. Cache Otimizado
- Um board = um objeto no cache
- Invalidação de cache mais simples
- Menos memória utilizada

### 4. Real-time Updates
```typescript
// Escutar mudanças em um board
const channel = `databases.${DATABASE_ID}.collections.${COLLECTIONS.KANBAN_BOARDS}.documents.${boardId}`;

client.subscribe(channel, (response) => {
  // Board inteiro atualizado
  const updatedBoard = response.payload;
  // Atualizar UI automaticamente
});
```

## 🔮 Próximos Passos

1. ✅ Implementar nova estrutura
2. ✅ Criar hooks otimizados  
3. ✅ Migrar dados existentes
4. 🔄 Atualizar componentes UI
5. 🧪 Testes de performance
6. 🗑️ Remover coleções antigas
7. 📚 Documentar mudanças

## 🤝 Contribuindo

Para contribuir com melhorias na estrutura otimizada:

1. Teste a performance em cenários reais
2. Identifique gargalos ou limitações
3. Sugira otimizações adicionais
4. Documente casos de uso específicos

---

**🎉 Com essa nova estrutura, o sistema Kanban está pronto para escalar e oferecer uma experiência muito mais rápida e consistente!**
