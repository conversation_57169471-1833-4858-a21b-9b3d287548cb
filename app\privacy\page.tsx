import type { <PERSON>ada<PERSON> } from "next";
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, Shield, Eye, Lock, Database, Mail } from 'lucide-react';

export const metadata: Metadata = {
  title: "Política de Privacidade - Template Appwrite",
  description: "Nossa política de privacidade e como protegemos seus dados",
};

export default function PrivacyPage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Voltar
              </Link>
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Política de Privacidade</h1>
              <p className="text-muted-foreground">Última atualização: Janeiro 2024</p>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12 max-w-4xl">
        {/* Introduction */}
        <section className="mb-12">
          <Card className="bg-primary/5 border-primary/20">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                  <Shield className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <CardTitle>Seu Privacidade é Nossa Prioridade</CardTitle>
                  <CardDescription>
                    Levamos a proteção dos seus dados muito a sério
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Esta Política de Privacidade descreve como coletamos, usamos e protegemos suas informações 
                quando você utiliza nosso template e serviços relacionados.
              </p>
            </CardContent>
          </Card>
        </section>

        {/* Data Collection */}
        <section className="mb-12">
          <Card>
            <CardHeader>
              <div className="flex items-center gap-3">
                <Database className="w-5 h-5 text-primary" />
                <CardTitle>Informações que Coletamos</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Informações de Conta</h4>
                <p className="text-sm text-muted-foreground">
                  Nome, email, senha (criptografada) e informações de perfil que você fornece ao criar uma conta.
                </p>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Dados de Uso</h4>
                <p className="text-sm text-muted-foreground">
                  Informações sobre como você interage com nossa aplicação, incluindo páginas visitadas, 
                  recursos utilizados e tempo de sessão.
                </p>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Informações Técnicas</h4>
                <p className="text-sm text-muted-foreground">
                  Endereço IP, tipo de navegador, sistema operacional e identificadores de dispositivo 
                  para fins de segurança e otimização.
                </p>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Data Usage */}
        <section className="mb-12">
          <Card>
            <CardHeader>
              <div className="flex items-center gap-3">
                <Eye className="w-5 h-5 text-primary" />
                <CardTitle>Como Usamos Suas Informações</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Prestação de Serviços</h4>
                <p className="text-sm text-muted-foreground">
                  Para fornecer, manter e melhorar nossos serviços, incluindo autenticação, 
                  personalização e suporte técnico.
                </p>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Comunicação</h4>
                <p className="text-sm text-muted-foreground">
                  Para enviar notificações importantes, atualizações de serviço e responder 
                  às suas solicitações de suporte.
                </p>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Segurança</h4>
                <p className="text-sm text-muted-foreground">
                  Para detectar, prevenir e responder a atividades fraudulentas, abuso 
                  ou violações de segurança.
                </p>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Análise e Melhoria</h4>
                <p className="text-sm text-muted-foreground">
                  Para analisar tendências de uso, medir a eficácia de recursos e 
                  melhorar a experiência do usuário.
                </p>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Data Protection */}
        <section className="mb-12">
          <Card>
            <CardHeader>
              <div className="flex items-center gap-3">
                <Lock className="w-5 h-5 text-primary" />
                <CardTitle>Como Protegemos Seus Dados</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Criptografia</h4>
                <p className="text-sm text-muted-foreground">
                  Todos os dados são criptografados em trânsito (HTTPS/TLS) e em repouso 
                  usando padrões de segurança da indústria.
                </p>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Controle de Acesso</h4>
                <p className="text-sm text-muted-foreground">
                  Implementamos controles rigorosos de acesso, autenticação multifator 
                  e princípio de menor privilégio.
                </p>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Monitoramento</h4>
                <p className="text-sm text-muted-foreground">
                  Monitoramos continuamente nossos sistemas para detectar e responder 
                  rapidamente a possíveis ameaças de segurança.
                </p>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Backup e Recuperação</h4>
                <p className="text-sm text-muted-foreground">
                  Mantemos backups seguros e testamos regularmente nossos procedimentos 
                  de recuperação de dados.
                </p>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* User Rights */}
        <section className="mb-12">
          <Card>
            <CardHeader>
              <CardTitle>Seus Direitos</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Acesso e Portabilidade</h4>
                <p className="text-sm text-muted-foreground">
                  Você pode acessar, baixar ou exportar seus dados pessoais a qualquer momento 
                  através das configurações da conta.
                </p>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Correção</h4>
                <p className="text-sm text-muted-foreground">
                  Você pode atualizar ou corrigir suas informações pessoais através do seu perfil.
                </p>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Exclusão</h4>
                <p className="text-sm text-muted-foreground">
                  Você pode solicitar a exclusão da sua conta e dados pessoais. Alguns dados 
                  podem ser mantidos por obrigações legais.
                </p>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Objeção</h4>
                <p className="text-sm text-muted-foreground">
                  Você pode optar por não receber comunicações de marketing ou limitar 
                  o processamento de seus dados.
                </p>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Contact */}
        <section>
          <Card className="bg-muted/50">
            <CardHeader>
              <div className="flex items-center gap-3">
                <Mail className="w-5 h-5 text-primary" />
                <CardTitle>Entre em Contato</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Se você tiver dúvidas sobre esta Política de Privacidade ou sobre como 
                tratamos seus dados, entre em contato conosco:
              </p>
              
              <div className="space-y-2 text-sm">
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Endereço:</strong> Rua da Privacidade, 123 - São Paulo, SP</p>
              </div>
              
              <div className="mt-6 pt-6 border-t">
                <p className="text-xs text-muted-foreground">
                  Esta política pode ser atualizada periodicamente. Notificaremos sobre 
                  mudanças significativas através do email cadastrado em sua conta.
                </p>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  );
}
