/**
 * BoardFormModal Component
 * Modal for creating and editing kanban boards
 */

import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useSnapshot } from 'valtio';
import { Palette, Eye, Users, Settings } from 'lucide-react';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '../ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Button } from '../ui/button';
import { Switch } from '../ui/switch';
import { Label } from '../ui/label';

import { kanbanStore, kanbanActions } from '../../stores/kanban-store';
import { useCreateBoard, useUpdateBoard } from '../../hooks/api/use-kanban';
import { useAuth } from '../../hooks/use-auth';
import { createBoardSchema, type CreateBoardData } from '../../schemas/kanban';

const backgroundColors = [
  '#3B82F6', // Blue
  '#10B981', // Green
  '#F59E0B', // Yellow
  '#EF4444', // Red
  '#8B5CF6', // Purple
  '#06B6D4', // Cyan
  '#F97316', // Orange
  '#84CC16', // Lime
  '#EC4899', // Pink
  '#6B7280', // Gray
];

export function BoardFormModal() {
  const { user } = useAuth();
  const snap = useSnapshot(kanbanStore);
  const createBoardMutation = useCreateBoard();
  const updateBoardMutation = useUpdateBoard();

  const isOpen = snap.boardCreate.isOpen || snap.boardEdit.isOpen;
  const isEditing = snap.boardEdit.isOpen;
  const board = snap.boardEdit.board;

  const form = useForm({
    resolver: zodResolver(createBoardSchema),
    defaultValues: {
      userId: user?.$id || '',
      teamId: '',
      title: '',
      description: '',
      visibility: 'private' as const,
      backgroundColor: backgroundColors[0],
      backgroundImage: '',
      allowComments: true,
      allowAttachments: true,
      enableTimeTracking: false,
      isTemplate: false,
      isArchived: false,
      isFavorite: false,
    },
  });

  // Reset form when modal opens/closes or board changes
  useEffect(() => {
    if (isOpen) {
      if (isEditing && board) {
        form.reset({
          userId: board.userId,
          teamId: board.teamId || '',
          title: board.title,
          description: board.description || '',
          visibility: board.visibility,
          backgroundColor: board.backgroundColor || backgroundColors[0],
          backgroundImage: board.backgroundImage || '',
          allowComments: board.allowComments,
          allowAttachments: board.allowAttachments,
          enableTimeTracking: board.enableTimeTracking,
          isTemplate: board.isTemplate,
          isArchived: board.isArchived,
          isFavorite: board.isFavorite,
        });
      } else {
        form.reset({
          userId: user?.$id || '',
          teamId: '',
          title: '',
          description: '',
          visibility: 'private' as const,
          backgroundColor: backgroundColors[0],
          backgroundImage: '',
          allowComments: true,
          allowAttachments: true,
          enableTimeTracking: false,
          isTemplate: false,
          isArchived: false,
          isFavorite: false,
        });
      }
    }
  }, [isOpen, isEditing, board, form, user?.$id]);

  const onSubmit = async (data: CreateBoardData) => {
    try {
      if (isEditing && board) {
        await updateBoardMutation.mutateAsync({
          boardId: board.$id,
          data,
        });
        kanbanActions.closeBoardEdit();
      } else {
        await createBoardMutation.mutateAsync(data);
        kanbanActions.closeBoardCreate();
      }
      form.reset();
    } catch (error) {
      console.error('Error submitting board:', error);
    }
  };

  const handleClose = () => {
    if (isEditing) {
      kanbanActions.closeBoardEdit();
    } else {
      kanbanActions.closeBoardCreate();
    }
    form.reset();
  };

  const isLoading = createBoardMutation.isPending || updateBoardMutation.isPending;
  const selectedColor = form.watch('backgroundColor');

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Editar Board' : 'Novo Board'}
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? 'Atualize as configurações do board.'
              : 'Crie um novo board para organizar suas tarefas.'
            }
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Title */}
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Título *</FormLabel>
                  <FormControl>
                    <Input placeholder="Digite o título do board..." {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Descrição</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Descreva o propósito do board..."
                      className="resize-none"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Visibility */}
            <FormField
              control={form.control}
              name="visibility"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Visibilidade *</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione a visibilidade" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="private">
                        <div className="flex items-center gap-2">
                          <Eye className="h-4 w-4" />
                          Privado - Apenas você
                        </div>
                      </SelectItem>
                      <SelectItem value="team">
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4" />
                          Time - Membros do time
                        </div>
                      </SelectItem>
                      <SelectItem value="public">
                        <div className="flex items-center gap-2">
                          <Settings className="h-4 w-4" />
                          Público - Todos podem ver
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Background Color */}
            <FormField
              control={form.control}
              name="backgroundColor"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Cor de Fundo</FormLabel>
                  <FormControl>
                    <div className="space-y-3">
                      <div className="grid grid-cols-5 gap-2">
                        {backgroundColors.map((color) => (
                          <button
                            key={color}
                            type="button"
                            className={`w-12 h-12 rounded-lg border-2 transition-all ${
                              selectedColor === color
                                ? 'border-foreground scale-110'
                                : 'border-border hover:scale-105'
                            }`}
                            style={{ backgroundColor: color }}
                            onClick={() => field.onChange(color)}
                          />
                        ))}
                      </div>
                      <Input
                        placeholder="#000000"
                        value={field.value}
                        onChange={field.onChange}
                        className="font-mono"
                      />
                    </div>
                  </FormControl>
                  <FormDescription>
                    Escolha uma cor ou digite um código hexadecimal
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Background Image */}
            <FormField
              control={form.control}
              name="backgroundImage"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Imagem de Fundo (URL)</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="https://exemplo.com/imagem.jpg"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    URL de uma imagem para usar como fundo do board
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Settings */}
            <div className="space-y-4">
              <Label className="text-base font-medium">Configurações</Label>

              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="allowComments"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Permitir Comentários</FormLabel>
                        <FormDescription>
                          Usuários podem comentar nas tarefas
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="allowAttachments"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Permitir Anexos</FormLabel>
                        <FormDescription>
                          Usuários podem anexar arquivos nas tarefas
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="enableTimeTracking"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Controle de Tempo</FormLabel>
                        <FormDescription>
                          Habilitar tracking de tempo nas tarefas
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="isTemplate"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Usar como Template</FormLabel>
                        <FormDescription>
                          Este board pode ser usado como modelo
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex justify-end gap-2 pt-4">
              <Button type="button" variant="outline" onClick={handleClose}>
                Cancelar
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Salvando...' : isEditing ? 'Atualizar' : 'Criar'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
