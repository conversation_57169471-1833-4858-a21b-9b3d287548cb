import { cn } from '../lib/utils'

export const Logo = ({ className, uniColor }: { className?: string; uniColor?: boolean }) => {
    return (
        <svg
            viewBox="0 0 120 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={cn('text-foreground h-6 w-auto', className)}>
            {/* Logo Icon - Stylized C with code brackets */}
            <g>
                {/* Main C shape */}
                <path
                    d="M2 6C2 3.79086 3.79086 2 6 2H10V4H6C4.89543 4 4 4.89543 4 6V18C4 19.1046 4.89543 20 6 20H10V22H6C3.79086 22 2 20.2091 2 18V6Z"
                    fill={uniColor ? 'currentColor' : 'hsl(var(--primary))'}
                />
                {/* Code brackets */}
                <path
                    d="M14 8L12 12L14 16H16L14.5 12L16 8H14Z"
                    fill={uniColor ? 'currentColor' : 'hsl(var(--primary))'}
                />
                <path
                    d="M18 8L19.5 12L18 16H20L22 12L20 8H18Z"
                    fill={uniColor ? 'currentColor' : 'hsl(var(--primary))'}
                />
            </g>

            {/* Text "CODEXA" */}
            <g transform="translate(30, 3)" className="text-foreground">
                {/* C */}
                <path
                    d="M2 3C2 1.34315 3.34315 0 5 0H8V2H5C4.44772 2 4 2.44772 4 3V15C4 15.5523 4.44772 16 5 16H8V18H5C3.34315 18 2 16.6569 2 15V3Z"
                    fill="currentColor"
                />
                {/* O */}
                <circle
                    cx="17"
                    cy="9"
                    r="8"
                    stroke="currentColor"
                    strokeWidth="2"
                    fill="none"
                />
                {/* D */}
                <path
                    d="M28 0H33C36.3137 0 39 2.68629 39 6V12C39 15.3137 36.3137 18 33 18H28V0ZM30 2V16H33C35.2091 16 37 14.2091 37 12V6C37 3.79086 35.2091 2 33 2H30Z"
                    fill="currentColor"
                />
                {/* E */}
                <path
                    d="M43 0H53V2H45V8H51V10H45V16H53V18H43V0Z"
                    fill="currentColor"
                />
                {/* X */}
                <path
                    d="M57 0L62 9L57 18H59.5L63 11.5L66.5 18H69L64 9L69 0H66.5L63 6.5L59.5 0H57Z"
                    fill="currentColor"
                />
                {/* A */}
                <path
                    d="M73 18L78 0H80L85 18H83L81.8 15H76.2L75 18H73ZM77 13H81L79 7L77 13Z"
                    fill="currentColor"
                />
            </g>

            <defs>
                <linearGradient
                    id="codexa-gradient"
                    x1="0"
                    y1="0"
                    x2="24"
                    y2="24"
                    gradientUnits="userSpaceOnUse">
                    <stop stopColor="hsl(var(--primary))" />
                    <stop
                        offset="1"
                        stopColor="hsl(var(--primary) / 0.6)"
                    />
                </linearGradient>
            </defs>
        </svg>
    )
}

export const LogoIcon = ({ className, uniColor }: { className?: string; uniColor?: boolean }) => {
    return (
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={cn('size-6', className)}>
            {/* Stylized C with code brackets */}
            <g>
                {/* Main C shape */}
                <path
                    d="M2 6C2 3.79086 3.79086 2 6 2H10V4H6C4.89543 4 4 4.89543 4 6V18C4 19.1046 4.89543 20 6 20H10V22H6C3.79086 22 2 20.2091 2 18V6Z"
                    fill={uniColor ? 'currentColor' : 'hsl(var(--primary))'}
                />
                {/* Code brackets */}
                <path
                    d="M14 8L12 12L14 16H16L14.5 12L16 8H14Z"
                    fill={uniColor ? 'currentColor' : 'hsl(var(--primary))'}
                />
                <path
                    d="M18 8L19.5 12L18 16H20L22 12L20 8H18Z"
                    fill={uniColor ? 'currentColor' : 'hsl(var(--primary))'}
                />
            </g>

            <defs>
                <linearGradient
                    id="codexa-icon-gradient"
                    x1="0"
                    y1="0"
                    x2="24"
                    y2="24"
                    gradientUnits="userSpaceOnUse">
                    <stop stopColor="hsl(var(--primary))" />
                    <stop
                        offset="1"
                        stopColor="hsl(var(--primary) / 0.6)"
                    />
                </linearGradient>
            </defs>
        </svg>
    )
}

export const LogoStroke = ({ className }: { className?: string }) => {
    return (
        <svg
            className={cn('size-7 w-7', className)}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            {/* Stylized C with code brackets - stroke version */}
            <g>
                {/* Main C shape */}
                <path
                    d="M2 6C2 3.79086 3.79086 2 6 2H10V4H6C4.89543 4 4 4.89543 4 6V18C4 19.1046 4.89543 20 6 20H10V22H6C3.79086 22 2 20.2091 2 18V6Z"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="1.5"
                />
                {/* Code brackets */}
                <path
                    d="M14 8L12 12L14 16H16L14.5 12L16 8H14Z"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="1.5"
                />
                <path
                    d="M18 8L19.5 12L18 16H20L22 12L20 8H18Z"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="1.5"
                />
            </g>
        </svg>
    )
}

export const LogoCompact = ({ className, uniColor }: { className?: string; uniColor?: boolean }) => {
    return (
        <svg
            viewBox="0 0 60 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={cn('text-foreground h-6 w-auto', className)}>
            {/* Logo Icon */}
            <g>
                {/* Main C shape */}
                <path
                    d="M2 6C2 3.79086 3.79086 2 6 2H10V4H6C4.89543 4 4 4.89543 4 6V18C4 19.1046 4.89543 20 6 20H10V22H6C3.79086 22 2 20.2091 2 18V6Z"
                    fill={uniColor ? 'currentColor' : 'hsl(var(--primary))'}
                />
                {/* Code brackets */}
                <path
                    d="M14 8L12 12L14 16H16L14.5 12L16 8H14Z"
                    fill={uniColor ? 'currentColor' : 'hsl(var(--primary))'}
                />
                <path
                    d="M18 8L19.5 12L18 16H20L22 12L20 8H18Z"
                    fill={uniColor ? 'currentColor' : 'hsl(var(--primary))'}
                />
            </g>

            {/* Simplified "CX" text */}
            <g transform="translate(30, 3)" className="text-foreground">
                {/* C */}
                <path
                    d="M2 3C2 1.34315 3.34315 0 5 0H8V2H5C4.44772 2 4 2.44772 4 3V15C4 15.5523 4.44772 16 5 16H8V18H5C3.34315 18 2 16.6569 2 15V3Z"
                    fill="currentColor"
                />
                {/* X */}
                <path
                    d="M12 0L17 9L12 18H14.5L18 11.5L21.5 18H24L19 9L24 0H21.5L18 6.5L14.5 0H12Z"
                    fill="currentColor"
                />
            </g>
        </svg>
    )
}