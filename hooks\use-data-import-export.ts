/**
 * Hook para gerenciar import/export de dados
 * Integra com Gemini AI para processamento inteligente de dados
 */

import { useState, useCallback } from 'react';
import * as React from 'react';
import { useCloudFunction } from './use-cloud-function';
import { useAuth } from './use-auth';
import { useLocalExport } from './use-local-export';
import { canUseDataImportExport, canExportData, canImportData, canUseAIProcessing, getUpgradeInfo } from '../lib/plan-limits';
import { toast } from 'sonner';
import { storage, STORAGE_BUCKET_ID } from '../lib/appwrite/config';
import { ID } from 'appwrite';

export interface ImportOptions {
  fileType: 'csv' | 'excel' | 'pdf' | 'image';
  useAI: boolean;
  customPrompt?: string;
  targetCollection: 'clients' | 'teams' | 'custom';
  mappingRules?: Record<string, string>;
}

export interface ExportOptions {
  format: 'csv' | 'excel' | 'pdf';
  filters?: Record<string, any>;
  columns?: string[];
  includeMetadata?: boolean;
  dateRange?: {
    start: string;
    end: string;
  };
}

export interface ImportResult {
  success: boolean;
  processedRecords: number;
  errors: string[];
  previewData?: any[];
  fileId?: string;
}

export interface ExportResult {
  success: boolean;
  downloadUrl: string;
  fileName: string;
  fileSize: number;
}

export interface DataImportExportState {
  isImporting: boolean;
  isExporting: boolean;
  uploadProgress: number;
  importResult: ImportResult | null;
  exportResult: ExportResult | null;
  error: string | null;
  canUseFeatures: boolean;
  canExportData: boolean;
  canImportData: boolean;
  canUseAI: boolean;
  upgradeInfo: any;
}

/**
 * Hook principal para import/export de dados
 */
export function useDataImportExport() {
  const { user } = useAuth();
  const [state, setState] = useState<DataImportExportState>({
    isImporting: false,
    isExporting: false,
    uploadProgress: 0,
    importResult: null,
    exportResult: null,
    error: null,
    canUseFeatures: false,
    canExportData: false,
    canImportData: false,
    canUseAI: false,
    upgradeInfo: null,
  });

  // Cloud functions
  const geminiDataProcessor = useCloudFunction('GEMINI_DATA_PROCESSOR');

  // Local export fallback
  const localExport = useLocalExport();

  // Verificar permissões do usuário
  const checkPermissions = useCallback(() => {
    if (!user) return;

    const importExportCheck = canUseDataImportExport(user);
    const exportCheck = canExportData(user);
    const importCheck = canImportData(user);
    const aiCheck = canUseAIProcessing(user);

    setState(prev => ({
      ...prev,
      canUseFeatures: importExportCheck.allowed, // Mantém para compatibilidade
      canExportData: exportCheck.allowed,
      canImportData: importCheck.allowed,
      canUseAI: aiCheck.allowed,
      upgradeInfo: !importCheck.allowed
        ? getUpgradeInfo(user.labels?.[0] || 'free', importCheck.planRequired)
        : null,
    }));
  }, [user]);

  // Executar verificação de permissões quando o usuário mudar
  React.useEffect(() => {
    checkPermissions();
  }, [checkPermissions]);

  /**
   * Valida arquivo para import
   */
  const validateImportFile = useCallback((file: File, options: ImportOptions): { valid: boolean; error?: string } => {
    // Verificar permissões
    if (!state.canImportData) {
      return {
        valid: false,
        error: 'Import de dados disponível apenas para planos pagos',
      };
    }

    // Verificar se pode usar IA
    if (options.useAI && !state.canUseAI) {
      return {
        valid: false,
        error: 'Processamento com IA disponível apenas para planos pagos',
      };
    }

    // Verificar tamanho do arquivo (10MB máximo)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return {
        valid: false,
        error: 'Arquivo muito grande. Tamanho máximo: 10MB',
      };
    }

    // Verificar tipo do arquivo
    const allowedTypes = {
      csv: ['text/csv', 'application/csv'],
      excel: ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'],
      pdf: ['application/pdf'],
      image: ['image/jpeg', 'image/png', 'image/webp'],
    };

    const validTypes = allowedTypes[options.fileType];
    if (!validTypes.includes(file.type)) {
      return {
        valid: false,
        error: `Tipo de arquivo inválido para ${options.fileType}`,
      };
    }

    return { valid: true };
  }, [state.canImportData, state.canUseAI]);

  /**
   * Importa dados de um arquivo
   */
  const importData = useCallback(async (
    file: File,
    options: ImportOptions
  ): Promise<ImportResult | null> => {
    try {
      // Validar arquivo
      const validation = validateImportFile(file, options);
      if (!validation.valid) {
        toast.error(validation.error);
        return null;
      }

      setState(prev => ({
        ...prev,
        isImporting: true,
        uploadProgress: 0,
        error: null,
        importResult: null,
      }));

      // 1. Upload do arquivo
      const uploadedFile = await storage.createFile(
        STORAGE_BUCKET_ID,
        ID.unique(),
        file
      );

      setState(prev => ({ ...prev, uploadProgress: 50 }));

      // 2. Processar arquivo
      let result;
      if (options.useAI) {
        // Usar Gemini para processamento inteligente
        result = await geminiDataProcessor.execute({
          data: {
            fileId: uploadedFile.$id,
            bucketId: STORAGE_BUCKET_ID,
            fileType: options.fileType,
            targetCollection: options.targetCollection,
            customPrompt: options.customPrompt,
            mappingRules: options.mappingRules,
          },
        });
      } else {
        // Processamento tradicional não está mais disponível via cloud function
        // Usar processamento local ou fallback
        throw new Error('Processamento tradicional de import não está disponível. Use processamento com IA ou implemente processamento local.');
      }

      setState(prev => ({ ...prev, uploadProgress: 100 }));

      if (result.success) {
        const resultData = result.data as any;
        const importResult: ImportResult = {
          success: true,
          processedRecords: resultData?.processedRecords || 0,
          errors: resultData?.errors || [],
          previewData: resultData?.previewData || [],
          fileId: uploadedFile.$id,
        };

        setState(prev => ({
          ...prev,
          isImporting: false,
          importResult,
        }));

        toast.success(`${importResult.processedRecords} registros processados com sucesso!`);
        return importResult;
      } else {
        throw new Error(result.error || 'Erro no processamento');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';

      setState(prev => ({
        ...prev,
        isImporting: false,
        error: errorMessage,
      }));

      toast.error(`Erro no import: ${errorMessage}`);
      return null;
    }
  }, [validateImportFile, geminiDataProcessor]);

  /**
   * Exporta dados
   */
  const exportData = useCallback(async (
    collection: string,
    options: ExportOptions,
    data?: Record<string, any>[] // Dados para exportação local
  ): Promise<ExportResult | null> => {
    try {
      if (!state.canExportData) {
        toast.error('Erro: não foi possível verificar permissões de export');
        return null;
      }

      setState(prev => ({
        ...prev,
        isExporting: true,
        error: null,
        exportResult: null,
      }));

      // Usar exportação local (cloud function de export foi removida)
      if (!data || data.length === 0) {
        throw new Error('Dados são obrigatórios para exportação local');
      }

      const localResult = await localExport.exportData(collection, data, {
        format: options.format,
        columns: options.columns,
        includeMetadata: options.includeMetadata,
        dateRange: options.dateRange,
      });

      if (localResult) {
        const exportResult: ExportResult = {
          success: true,
          downloadUrl: '', // Arquivo baixado diretamente
          fileName: localResult.fileName,
          fileSize: localResult.fileSize,
        };

        setState(prev => ({
          ...prev,
          isExporting: false,
          exportResult,
        }));

        toast.success('Export realizado com sucesso!');
        return exportResult;
      } else {
        throw new Error('Falha na exportação local');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';

      setState(prev => ({
        ...prev,
        isExporting: false,
        error: errorMessage,
      }));

      toast.error(`Erro no export: ${errorMessage}`);
      return null;
    }
  }, [state.canExportData, localExport]);

  /**
   * Reset do estado
   */
  const reset = useCallback(() => {
    setState(prev => ({
      ...prev,
      isImporting: false,
      isExporting: false,
      uploadProgress: 0,
      importResult: null,
      exportResult: null,
      error: null,
    }));
  }, []);

  return {
    ...state,
    importData,
    exportData,
    validateImportFile,
    reset,
    checkPermissions,
  };
}

/**
 * Hook simplificado para import rápido
 */
export function useQuickDataImport() {
  const dataImportExport = useDataImportExport();

  const quickImport = async (
    file: File,
    targetCollection: ImportOptions['targetCollection'] = 'clients',
    useAI: boolean = true
  ) => {
    return dataImportExport.importData(file, {
      fileType: file.type.includes('csv') ? 'csv' :
                file.type.includes('excel') ? 'excel' :
                file.type.includes('pdf') ? 'pdf' : 'image',
      useAI,
      targetCollection,
    });
  };

  return {
    quickImport,
    isLoading: dataImportExport.isImporting,
    result: dataImportExport.importResult,
    error: dataImportExport.error,
    canUseFeatures: dataImportExport.canUseFeatures,
    canExportData: dataImportExport.canExportData,
    canImportData: dataImportExport.canImportData,
    canUseAI: dataImportExport.canUseAI,
    upgradeInfo: dataImportExport.upgradeInfo,
    reset: dataImportExport.reset,
  };
}
