import { usePathname } from 'next/navigation'
import { But<PERSON> } from "./ui/button"
import { Separator } from "./ui/separator"
import { SidebarTrigger } from "./ui/sidebar"
import SearchComponent from "./search"
import NotificationComponent from "./notification"
import { ThemeToggle } from "./theme-toggle"
import { ModeToggle } from "./mode-toggle"
import { ThemeSelector } from "./theme-selector"
import { useIsMobile } from "../hooks/use-mobile"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "./ui/popover"
import { Palette } from "lucide-react"

// Mapeamento de rotas para títulos
const routeTitles: Record<string, string> = {
  "/dashboard": "Dashboard",
  "/dashboard/preferences": "Preferências",
  "/dashboard/analytics": "Analytics",
  "/dashboard/projects": "Projetos",
  "/dashboard/help": "Ajuda",
  "/dashboard/plans": "Planos",
  "/dashboard/calendar": "Calendário",
  "/dashboard/documents": "Documentos",
  "/dashboard/clients": "Clientes",
  "/dashboard/teams": "Teams",
  "/dashboard/notifications": "Notificações",
  "/dashboard/activities": "Atividades",
  "/dashboard/kanban": "Kanban",
  "/dashboard/team-chat": "Chat de Time",
}

export function SiteHeader() {
  const pathname = usePathname()
  const pageTitle = routeTitles[pathname] || "Dashboard"
  const isMobile = useIsMobile()

  return (
    <header className="flex h-(--header-height) shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-(--header-height)">
      <div className="flex w-full items-center gap-1 px-4 lg:gap-2 lg:px-6">
        <SidebarTrigger className="-ml-1" />
        <Separator
          orientation="vertical"
          className="mx-2 data-[orientation=vertical]:h-4"
        />
        <h1 className="text-base font-medium">{pageTitle}</h1>
        <div className="ml-auto flex items-center gap-2">
          {/* Componente de Pesquisa */}
          <SearchComponent />

          {/* Componente de Notificações */}
          <NotificationComponent />

          {/* Controles de Tema - Ocultos no mobile */}
          {!isMobile && (
            <>
              {/* Seletor de Tema Avançado */}
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    size="icon"
                    aria-label="Configurar tema"
                  >
                    <Palette className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80" align="end">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <h4 className="font-medium leading-none">Configurações de Tema</h4>
                      <p className="text-sm text-muted-foreground">
                        Personalize a aparência da aplicação
                      </p>
                    </div>
                    <div className="space-y-4">
                      <ThemeSelector />
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Modo de Cor</label>
                        <div className="flex items-center gap-2">
                          <ModeToggle />
                        </div>
                      </div>
                    </div>
                  </div>
                </PopoverContent>
              </Popover>

              {/* Toggle rápido de modo claro/escuro */}
              <ThemeToggle />
            </>
          )}
        </div>
      </div>
    </header>
  )
}
