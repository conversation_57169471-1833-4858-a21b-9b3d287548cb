/**
 * Utilitários para funcionalidades de chat
 */

export interface ParsedReaction {
  emoji: string;
  userId: string;
}

export interface ReactionSummary {
  emoji: string;
  count: number;
  users: string[];
  hasCurrentUser: boolean;
}

/**
 * Converte uma reação para o formato de string usado no banco
 */
export function formatReaction(emoji: string, userId: string): string {
  return `${emoji}:${userId}`;
}

/**
 * Converte uma string de reação do banco para objeto
 */
export function parseReaction(reactionString: string): ParsedReaction | null {
  const parts = reactionString.split(':');
  if (parts.length !== 2) return null;
  
  return {
    emoji: parts[0],
    userId: parts[1],
  };
}

/**
 * Agrupa reações por emoji e conta usuários
 */
export function summarizeReactions(reactions: string[] = [], currentUserId?: string): ReactionSummary[] {
  const reactionMap = new Map<string, Set<string>>();
  
  // Agrupa reações por emoji
  reactions.forEach(reactionString => {
    const parsed = parseReaction(reactionString);
    if (!parsed) return;
    
    if (!reactionMap.has(parsed.emoji)) {
      reactionMap.set(parsed.emoji, new Set());
    }
    reactionMap.get(parsed.emoji)!.add(parsed.userId);
  });
  
  // Converte para array de resumos
  return Array.from(reactionMap.entries()).map(([emoji, userSet]) => ({
    emoji,
    count: userSet.size,
    users: Array.from(userSet),
    hasCurrentUser: currentUserId ? userSet.has(currentUserId) : false,
  }));
}

/**
 * Adiciona uma reação a uma lista existente
 */
export function addReaction(reactions: string[] = [], emoji: string, userId: string): string[] {
  const reactionString = formatReaction(emoji, userId);
  
  // Verifica se o usuário já reagiu com este emoji
  const existingReaction = reactions.find(r => {
    const parsed = parseReaction(r);
    return parsed?.emoji === emoji && parsed?.userId === userId;
  });
  
  if (existingReaction) {
    return reactions; // Já existe, não adiciona
  }
  
  return [...reactions, reactionString];
}

/**
 * Remove uma reação de uma lista existente
 */
export function removeReaction(reactions: string[] = [], emoji: string, userId: string): string[] {
  const reactionString = formatReaction(emoji, userId);
  return reactions.filter(r => r !== reactionString);
}

/**
 * Verifica se um usuário já reagiu com um emoji específico
 */
export function hasUserReacted(reactions: string[] = [], emoji: string, userId: string): boolean {
  return reactions.some(r => {
    const parsed = parseReaction(r);
    return parsed?.emoji === emoji && parsed?.userId === userId;
  });
}

/**
 * Extrai menções (@username) de um texto
 */
export function extractMentions(text: string): string[] {
  const mentionRegex = /@(\w+)/g;
  const mentions: string[] = [];
  let match;
  
  while ((match = mentionRegex.exec(text)) !== null) {
    mentions.push(match[1]);
  }
  
  return [...new Set(mentions)]; // Remove duplicatas
}

/**
 * Substitui menções no texto por elementos clicáveis
 */
export function formatMentions(text: string, userMap?: Map<string, { name: string; id: string }>): string {
  if (!userMap) return text;
  
  return text.replace(/@(\w+)/g, (match, username) => {
    const user = Array.from(userMap.values()).find(u => 
      u.name.toLowerCase() === username.toLowerCase()
    );
    
    if (user) {
      return `<span class="mention" data-user-id="${user.id}">@${user.name}</span>`;
    }
    
    return match;
  });
}

/**
 * Valida se um emoji é válido (básico)
 */
export function isValidEmoji(emoji: string): boolean {
  // Regex básico para emojis Unicode
  const emojiRegex = /^[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]$/u;
  return emojiRegex.test(emoji) && emoji.length <= 4;
}

/**
 * Limita o número de reações por mensagem
 */
export function limitReactions(reactions: string[] = [], maxReactions: number = 50): string[] {
  return reactions.slice(0, maxReactions);
}

/**
 * Obtém estatísticas de reações
 */
export function getReactionStats(reactions: string[] = []) {
  const summary = summarizeReactions(reactions);
  const totalReactions = reactions.length;
  const uniqueEmojis = summary.length;
  const uniqueUsers = new Set(
    reactions.map(r => parseReaction(r)?.userId).filter(Boolean)
  ).size;
  
  return {
    totalReactions,
    uniqueEmojis,
    uniqueUsers,
    mostUsedEmoji: summary.length > 0 
      ? summary.reduce((prev, current) => prev.count > current.count ? prev : current).emoji
      : null,
  };
}
