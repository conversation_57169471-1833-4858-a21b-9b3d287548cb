import React from 'react';
import { <PERSON><PERSON> } from './ui/button';
import { Card, CardContent } from './ui/card';
import { Badge } from './ui/badge';
import Link from 'next/link';
import {
  Rocket,
  Clock,
  Shield,
  Star,
  CheckCircle,
  Zap,
  ArrowRight,
  Gift,
  Timer
} from 'lucide-react';

const urgencyFeatures = [
  'Setup completo em 5 minutos',
  'Documentação passo a passo',
  'Suporte técnico incluído',
  'Atualizações vitalícias',
  'Garantia de 30 dias',
  'Comunidade exclusiva'
];

export function CTASection() {
  return (
    <section className="py-16 md:py-24 bg-gradient-to-br from-primary/5 via-background to-primary/5">
      <div className="container mx-auto px-4 max-w-6xl">
        {/* Main CTA Card */}
        <Card className="relative overflow-hidden border-primary/20 shadow-2xl">
          {/* Background Pattern */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-transparent" />
          <div className="absolute top-0 right-0 w-64 h-64 bg-primary/5 rounded-full -translate-y-32 translate-x-32" />
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-primary/5 rounded-full translate-y-24 -translate-x-24" />

          <CardContent className="relative p-8 md:p-12 text-center">
            {/* Header */}
            <div className="mb-8">
              <Badge variant="secondary" className="mb-4 bg-primary/10 text-primary">
                <Gift className="w-4 h-4 mr-2" />
                Oferta Especial
              </Badge>
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
                Transforme sua ideia em realidade
                <span className="block text-primary mt-2">em apenas 2 semanas</span>
              </h2>
              <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                Pare de perder tempo com configurações básicas. Nosso template completo
                te permite focar no que realmente importa: seu produto e seus clientes.
              </p>
            </div>

            {/* Features Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
              {urgencyFeatures.map((feature, index) => (
                <div key={index} className="flex items-center gap-3 text-left">
                  <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                  <span className="text-sm">{feature}</span>
                </div>
              ))}
            </div>

            {/* Pricing */}
            <div className="bg-background/80 backdrop-blur-sm rounded-lg p-6 mb-8 border border-primary/20">
              <div className="flex flex-col md:flex-row items-center justify-center gap-6">
                <div className="text-center">
                  <div className="text-sm text-muted-foreground mb-1">Valor normal</div>
                  <div className="text-2xl text-muted-foreground line-through">R$ 497</div>
                </div>
                <div className="text-center">
                  <div className="text-sm text-primary font-semibold mb-1">Preço especial</div>
                  <div className="text-4xl md:text-5xl font-bold text-primary">R$ 297</div>
                  <div className="text-sm text-muted-foreground">pagamento único</div>
                </div>
                <div className="text-center">
                  <Badge variant="destructive" className="mb-2">
                    <Timer className="w-4 h-4 mr-1" />
                    40% OFF
                  </Badge>
                  <div className="text-sm text-muted-foreground">Economia de R$ 200</div>
                </div>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
              <Button size="lg" className="text-lg px-8 py-6 group" asChild>
                <Link href="/register">
                  <Rocket className="w-5 h-5 mr-2 group-hover:translate-x-1 transition-transform" />
                  Começar Agora
                  <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="text-lg px-8 py-6" asChild>
                <Link href="/login">
                  Ver Demo ao Vivo
                </Link>
              </Button>
            </div>

            {/* Trust Indicators */}
            <div className="flex flex-col sm:flex-row items-center justify-center gap-6 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <Shield className="w-4 h-4 text-green-500" />
                <span>Pagamento 100% seguro</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="w-4 h-4 text-yellow-500" />
                <span>4.9/5 estrelas (500+ avaliações)</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-blue-500" />
                <span>Acesso imediato</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Urgency Section */}
        <div className="mt-12 text-center">
          <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-6 max-w-2xl mx-auto">
            <div className="flex items-center justify-center gap-2 mb-4">
              <Timer className="w-5 h-5 text-destructive" />
              <span className="font-semibold text-destructive">Oferta por tempo limitado</span>
            </div>
            <p className="text-sm text-muted-foreground mb-4">
              Apenas <strong>50 licenças</strong> disponíveis com este desconto especial.
              Não perca a oportunidade de economizar R$ 200 e acelerar seu projeto.
            </p>
            <div className="flex items-center justify-center gap-4 text-xs text-muted-foreground">
              <span>✓ Sem mensalidades</span>
              <span>✓ Código-fonte completo</span>
              <span>✓ Uso comercial liberado</span>
            </div>
          </div>
        </div>

        {/* Final Guarantee */}
        <div className="mt-12 text-center">
          <div className="bg-green-50 border border-green-200 rounded-lg p-6 max-w-xl mx-auto">
            <div className="flex items-center justify-center gap-2 mb-3">
              <Shield className="w-6 h-6 text-green-600" />
              <span className="font-semibold text-green-800">Garantia de 30 dias</span>
            </div>
            <p className="text-sm text-green-700">
              Se você não economizar pelo menos 1 mês de desenvolvimento ou não ficar
              100% satisfeito, devolvemos todo seu dinheiro. Sem perguntas.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
