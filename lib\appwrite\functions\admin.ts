import { Query } from 'appwrite';
import { databases, account, teams } from '../config';
import { DATABASE_ID, COLLECTIONS } from '../config';
import type { Models } from 'appwrite';
import { log } from '../../logger';

/**
 * Admin functions for user and system management
 */

// Interface simplificada para usuários admin baseada em dados de teams
export interface AdminUser {
  $id: string;
  email: string;
  name: string;
  role: UserRole;
  isActive: boolean;
  teamCount: number;
  lastActivity?: string;
  $createdAt: string;
  $updatedAt: string;
}

export type UserRole = 'admin' | 'moderator' | 'user';
export type UserStatus = 'active' | 'suspended' | 'pending';

export interface AdminStats {
  totalUsers: number;
  activeUsers: number;
  totalTeams: number;
  totalDocuments: number;
  recentActivity: ActivityLog[];
}

export interface ActivityLog extends Models.Document {
  userId: string;
  action: string;
  resource: string;
  resourceId?: string;
  metadata?: Record<string, unknown>;
  timestamp: string;
}

export interface UpdateUserData {
  labels?: string[]; // Para roles e status via labels
  status?: boolean; // Para ativar/desativar usuário
  name?: string;
}

/**
 * Check if current user is admin
 * Usa labels do Appwrite para determinar role
 */
export async function isCurrentUserAdmin(): Promise<boolean> {
  try {
    const user = await account.get();

    // Verificar se o usuário tem label 'admin' ou 'moderator'
    return user.labels?.includes('admin') || user.labels?.includes('moderator') || false;
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

/**
 * Get user role from labels
 */
export function getUserRole(user: Models.User<Models.Preferences>): UserRole {
  if (user.labels?.includes('admin')) return 'admin';
  if (user.labels?.includes('moderator')) return 'moderator';
  return 'user';
}

/**
 * Check if user is active (not disabled)
 */
export function isUserActive(user: Models.User<Models.Preferences>): boolean {
  return user.status && !user.labels?.includes('suspended');
}

/**
 * Get all users (admin only)
 * Nota: Esta função requer permissões de servidor. Em produção, implementar via Cloud Function.
 */
export async function getAllUsers(
  queries?: string[],
  limit: number = 50
): Promise<AdminUser[]> {
  try {
    // Verify admin access
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      throw new Error('Unauthorized: Admin access required');
    }

    // NOTA: Esta função requer API Key do servidor para listar usuários
    // Em produção, deve ser implementada como Cloud Function
    log.warn('getAllUsers: Esta função requer implementação via Cloud Function para acesso completo aos usuários');

    // Por enquanto, retornar informações limitadas baseadas em teams
    const userTeams = await teams.list();
    const users: AdminUser[] = [];

    // Coletar usuários únicos de todos os teams
    const userMap = new Map<string, AdminUser>();

    for (const team of userTeams.teams) {
      const memberships = await teams.listMemberships(team.$id);

      for (const membership of memberships.memberships) {
        if (!userMap.has(membership.userId)) {
          userMap.set(membership.userId, {
            $id: membership.userId,
            email: membership.userEmail || '',
            name: membership.userName || '',
            role: getUserRoleFromMembership(membership),
            isActive: membership.confirm,
            teamCount: 1,
            lastActivity: membership.$updatedAt,
            $createdAt: membership.$createdAt,
            $updatedAt: membership.$updatedAt
          });
        } else {
          const existingUser = userMap.get(membership.userId)!;
          existingUser.teamCount++;
        }
      }
    }

    return Array.from(userMap.values()).slice(0, limit);
  } catch (error) {
    console.error('Error getting all users:', error);
    throw error;
  }
}

/**
 * Get user role from team membership
 */
function getUserRoleFromMembership(membership: Models.Membership): UserRole {
  if (membership.roles.includes('owner') || membership.roles.includes('admin')) {
    return 'admin';
  }
  if (membership.roles.includes('moderator')) {
    return 'moderator';
  }
  return 'user';
}

/**
 * Get user by ID (admin only)
 * Busca informações do usuário através dos teams
 */
export async function getUserById(userId: string): Promise<AdminUser> {
  try {
    // Verify admin access
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      throw new Error('Unauthorized: Admin access required');
    }

    // Buscar usuário através dos teams onde ele é membro
    const userTeams = await teams.list();
    let userInfo: AdminUser | null = null;
    let teamCount = 0;

    for (const team of userTeams.teams) {
      try {
        const memberships = await teams.listMemberships(team.$id, [
          Query.equal('userId', userId)
        ]);

        if (memberships.memberships.length > 0) {
          const membership = memberships.memberships[0];
          teamCount++;

          if (!userInfo) {
            userInfo = {
              $id: membership.userId,
              email: membership.userEmail || '',
              name: membership.userName || '',
              role: getUserRoleFromMembership(membership),
              isActive: membership.confirm,
              teamCount: 0, // Will be updated below
              lastActivity: membership.$updatedAt,
              $createdAt: membership.$createdAt,
              $updatedAt: membership.$updatedAt
            };
          }
        }
      } catch (teamError) {
        // Continue searching in other teams
        console.warn(`Error searching user in team ${team.$id}:`, teamError);
      }
    }

    if (!userInfo) {
      throw new Error('User not found');
    }

    userInfo.teamCount = teamCount;
    return userInfo;
  } catch (error) {
    console.error('Error getting user by ID:', error);
    throw error;
  }
}

/**
 * Update user (admin only)
 * Atualiza informações do usuário através das APIs nativas do Appwrite
 * NOTA: Algumas operações requerem Cloud Functions para acesso completo
 */
export async function updateUser(
  userId: string,
  data: UpdateUserData
): Promise<AdminUser> {
  try {
    // Verify admin access
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      throw new Error('Unauthorized: Admin access required');
    }

    // NOTA: Atualizar usuários requer permissões de servidor
    // Em produção, implementar via Cloud Function
    console.warn('updateUser: Esta operação requer implementação via Cloud Function para acesso completo');

    // Por enquanto, podemos apenas atualizar roles em teams específicos
    if (data.labels) {
      await logAdminAction('update_user_labels', 'user', userId, {
        newLabels: data.labels
      });
    }

    // Retornar informações atualizadas do usuário
    return await getUserById(userId);
  } catch (error) {
    console.error('Error updating user:', error);
    throw error;
  }
}

/**
 * Update user role in a specific team
 */
export async function updateUserRoleInTeam(
  teamId: string,
  userId: string,
  roles: string[]
): Promise<void> {
  try {
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      throw new Error('Unauthorized: Admin access required');
    }

    // Primeiro, precisamos obter o membershipId
    const memberships = await teams.listMemberships(teamId, [
      Query.equal('userId', userId)
    ]);

    if (memberships.memberships.length === 0) {
      throw new Error('User is not a member of this team');
    }

    const membershipId = memberships.memberships[0].$id;
    await teams.updateMembership(teamId, membershipId, roles);
    await logAdminAction('update_user_team_role', 'user', userId, {
      teamId,
      newRoles: roles
    });
  } catch (error) {
    console.error('Error updating user role in team:', error);
    throw error;
  }
}

/**
 * Suspend user (admin only)
 * Remove usuário de todos os teams ou marca como suspenso
 */
export async function suspendUser(userId: string, reason?: string): Promise<void> {
  try {
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      throw new Error('Unauthorized: Admin access required');
    }

    // NOTA: Em produção, implementar via Cloud Function para suspender conta
    log.warn('suspendUser: Esta operação requer implementação via Cloud Function');

    // Por enquanto, remover usuário de todos os teams como forma de "suspensão"
    const userTeams = await teams.list();

    for (const team of userTeams.teams) {
      try {
        const memberships = await teams.listMemberships(team.$id, [
          Query.equal('userId', userId)
        ]);

        if (memberships.memberships.length > 0) {
          await teams.deleteMembership(team.$id, memberships.memberships[0].$id);
        }
      } catch (teamError) {
        log.warn(`Error removing user from team ${team.$id}`, { teamId: team.$id, error: teamError });
      }
    }

    await logAdminAction('suspend_user', 'user', userId, { reason });
  } catch (error) {
    log.error('Error suspending user', error, { userId, reason });
    throw error;
  }
}

/**
 * Activate user (admin only)
 * Reativa usuário (implementação limitada sem Cloud Functions)
 */
export async function activateUser(userId: string): Promise<void> {
  try {
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      throw new Error('Unauthorized: Admin access required');
    }

    // NOTA: Em produção, implementar via Cloud Function para reativar conta
    log.warn('activateUser: Esta operação requer implementação via Cloud Function');

    await logAdminAction('activate_user', 'user', userId);
  } catch (error) {
    log.error('Error activating user', error, { userId });
    throw error;
  }
}

/**
 * Get admin statistics
 * Calcula estatísticas baseadas nas APIs nativas do Appwrite
 */
export async function getAdminStats(): Promise<AdminStats> {
  try {
    // Verify admin access
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      throw new Error('Unauthorized: Admin access required');
    }

    // Get recent activity
    const recentActivity = await getRecentActivity(10);

    // Calcular estatísticas baseadas em teams e collections
    const userTeams = await teams.list();
    const totalTeams = userTeams.total;

    // Contar usuários únicos através dos teams
    const uniqueUsers = new Set<string>();
    let activeUsers = 0;

    for (const team of userTeams.teams) {
      try {
        const memberships = await teams.listMemberships(team.$id);
        for (const membership of memberships.memberships) {
          uniqueUsers.add(membership.userId);
          if (membership.confirm) {
            activeUsers++;
          }
        }
      } catch (teamError) {
        console.warn(`Error getting memberships for team ${team.$id}:`, teamError);
      }
    }

    // Contar documentos nas collections principais
    let totalDocuments = 0;
    try {
      const clientsResponse = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.CLIENTS,
        [Query.limit(1)]
      );
      totalDocuments += clientsResponse.total;

      const notificationsResponse = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.NOTIFICATIONS,
        [Query.limit(1)]
      );
      totalDocuments += notificationsResponse.total;
    } catch (dbError) {
      log.warn('Error counting documents', { error: dbError });
    }

    return {
      totalUsers: uniqueUsers.size,
      activeUsers,
      totalTeams,
      totalDocuments,
      recentActivity,
    };
  } catch (error) {
    log.error('Error getting admin stats', error);
    throw error;
  }
}

/**
 * Log admin action
 */
export async function logAdminAction(
  action: string,
  resource: string,
  resourceId?: string,
  metadata?: Record<string, unknown>
): Promise<void> {
  try {
    const user = await account.get();

    const logData = {
      userId: user.$id,
      action,
      resource,
      resourceId,
      metadata,
      timestamp: new Date().toISOString(),
    };

    await databases.createDocument(
      DATABASE_ID,
      COLLECTIONS.ACTIVITY_LOGS || 'activity_logs',
      'unique()',
      logData
    );
  } catch (error) {
    log.error('Error logging admin action', error, { action, resource, resourceId });
    // Don't throw error for logging failures
  }
}

/**
 * Get recent activity logs
 */
export async function getRecentActivity(limit: number = 20): Promise<ActivityLog[]> {
  try {
    const response = await databases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.ACTIVITY_LOGS || 'activity_logs',
      [
        Query.limit(limit),
        Query.orderDesc('timestamp'),
      ]
    );

    return response.documents as ActivityLog[];
  } catch (error) {
    console.error('Error getting recent activity:', error);
    return [];
  }
}
