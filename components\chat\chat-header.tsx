import * as React from "react";
import { Users, MoreVertical, Wifi, WifiOff } from "lucide-react";
import { But<PERSON> } from "../ui/button";
import { Badge } from "../ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { cn } from "../../lib/utils";

interface ChatHeaderProps {
  chatName: string;
  memberCount: number;
  isConnected: boolean;
  onViewMembers?: () => void;
  className?: string;
}

export function ChatHeader({
  chatName,
  memberCount,
  isConnected,
  onViewMembers,
  className
}: ChatHeaderProps) {
  return (
    <div className={cn(
      "flex items-center justify-between p-4 bg-card/50 backdrop-blur-sm border-b border-border/50",
      className
    )}>
      {/* Chat Info */}
      <div className="flex items-center gap-3 min-w-0 flex-1">
        {/* Chat Avatar/Icon */}
        <div className="flex-shrink-0">
          <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-medium shadow-md">
            <Users className="h-5 w-5" />
          </div>
        </div>

        {/* Chat Details */}
        <div className="min-w-0 flex-1">
          <h3 className="font-semibold text-foreground truncate">
            {chatName}
          </h3>

          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span>{memberCount} {memberCount === 1 ? 'membro' : 'membros'}</span>

            {/* Connection Status */}
            <div className="flex items-center gap-1">
              {isConnected ? (
                <Wifi className="h-3 w-3 text-green-500" />
              ) : (
                <WifiOff className="h-3 w-3 text-red-500" />
              )}
              <span className={cn(
                "text-xs",
                isConnected ? "text-green-600" : "text-red-600"
              )}>
                {isConnected ? 'Online' : 'Offline'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center gap-2">
        {/* Member Count Badge */}
        <Badge
          variant="secondary"
          className="cursor-pointer hover:bg-secondary/80 transition-colors"
          onClick={onViewMembers}
        >
          <Users className="h-3 w-3 mr-1" />
          {memberCount}
        </Badge>

        {/* More Options */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 rounded-full"
            >
              <MoreVertical className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem onClick={onViewMembers}>
              <Users className="h-4 w-4 mr-2" />
              Ver membros
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
