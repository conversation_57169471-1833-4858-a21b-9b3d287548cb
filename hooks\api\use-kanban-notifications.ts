/**
 * Kanban Notifications Hooks
 * Specific notifications for kanban tasks and boards
 */

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useAuth } from '../use-auth';
import { useCreateNotification } from './use-notifications';
import { usePWA } from '../use-pwa';
import type { Task, Board } from '@/schemas/kanban';
import type { NotificationType, NotificationPriority } from '@/schemas/database';

interface KanbanNotificationData {
  type: NotificationType;
  title: string;
  message: string;
  priority?: NotificationPriority;
  actionUrl?: string;
  data?: Record<string, unknown>;
}

/**
 * Hook for creating kanban-specific notifications
 */
export function useKanbanNotifications() {
  const { user } = useAuth();
  const createNotification = useCreateNotification();
  const { sendNotification: sendPWANotification } = usePWA();

  const sendKanbanNotification = async (
    targetUserId: string,
    notificationData: KanbanNotificationData
  ) => {
    try {
      // Create database notification
      await createNotification.mutateAsync({
        userId: targetUserId,
        type: notificationData.type,
        title: notificationData.title,
        message: notificationData.message,
        priority: notificationData.priority || 'normal',
        actionUrl: notificationData.actionUrl,
        data: notificationData.data,
        read: false,
      });

      // Send PWA notification if it's for the current user
      if (targetUserId === user?.$id) {
        await sendPWANotification({
          title: notificationData.title,
          body: notificationData.message,
          tag: `kanban-${notificationData.type}`,
          data: notificationData.data,
        });
      }
    } catch (error) {
      console.error('Error sending kanban notification:', error);
    }
  };

  return { sendKanbanNotification };
}

/**
 * Hook for task assignment notifications
 */
export function useTaskAssignmentNotifications() {
  const { sendKanbanNotification } = useKanbanNotifications();

  const notifyTaskAssigned = async (task: Task, assignedUserId: string, boardTitle: string) => {
    await sendKanbanNotification(assignedUserId, {
      type: 'info',
      title: 'Nova tarefa atribuída',
      message: `Você foi atribuído à tarefa "${task.title}" no board "${boardTitle}"`,
      priority: task.priority === 'critica' ? 'urgent' : task.priority === 'alta' ? 'high' : 'normal',
      actionUrl: `/dashboard/kanban?board=${task.boardId}&task=${task.id}`,
      data: {
        taskId: task.id,
        boardId: task.boardId,
        type: 'task_assigned',
      },
    });
  };

  const notifyTaskUnassigned = async (task: Task, unassignedUserId: string, boardTitle: string) => {
    await sendKanbanNotification(unassignedUserId, {
      type: 'info',
      title: 'Tarefa removida',
      message: `Você foi removido da tarefa "${task.title}" no board "${boardTitle}"`,
      priority: 'normal',
      actionUrl: `/dashboard/kanban?board=${task.boardId}`,
      data: {
        taskId: task.id,
        boardId: task.boardId,
        type: 'task_unassigned',
      },
    });
  };

  return { notifyTaskAssigned, notifyTaskUnassigned };
}

/**
 * Hook for task due date notifications
 */
export function useTaskDueDateNotifications() {
  const { sendKanbanNotification } = useKanbanNotifications();

  const notifyTaskDueSoon = async (task: Task, assignedUserId: string, boardTitle: string, hoursUntilDue: number) => {
    const timeText = hoursUntilDue < 24
      ? `${Math.round(hoursUntilDue)} horas`
      : `${Math.round(hoursUntilDue / 24)} dias`;

    await sendKanbanNotification(assignedUserId, {
      type: 'warning',
      title: 'Tarefa vencendo em breve',
      message: `A tarefa "${task.title}" vence em ${timeText}`,
      priority: hoursUntilDue < 24 ? 'high' : 'normal',
      actionUrl: `/dashboard/kanban?board=${task.boardId}&task=${task.id}`,
      data: {
        taskId: task.id,
        boardId: task.boardId,
        dueDate: task.dueDate,
        hoursUntilDue,
        type: 'task_due_soon',
      },
    });
  };

  const notifyTaskOverdue = async (task: Task, assignedUserId: string, boardTitle: string) => {
    await sendKanbanNotification(assignedUserId, {
      type: 'error',
      title: 'Tarefa em atraso',
      message: `A tarefa "${task.title}" está em atraso`,
      priority: 'urgent',
      actionUrl: `/dashboard/kanban?board=${task.boardId}&task=${task.id}`,
      data: {
        taskId: task.id,
        boardId: task.boardId,
        dueDate: task.dueDate,
        type: 'task_overdue',
      },
    });
  };

  return { notifyTaskDueSoon, notifyTaskOverdue };
}

/**
 * Hook for task status change notifications
 */
export function useTaskStatusNotifications() {
  const { sendKanbanNotification } = useKanbanNotifications();

  const notifyTaskStatusChanged = async (
    task: Task,
    oldStatus: string,
    newStatus: string,
    changedByUserId: string,
    assignedUserId?: string,
    boardTitle?: string
  ) => {
    if (!assignedUserId || assignedUserId === changedByUserId) return;

    const statusLabels: Record<string, string> = {
      todo: 'A Fazer',
      in_progress: 'Em Progresso',
      review: 'Em Revisão',
      done: 'Concluído',
    };

    const notificationType: NotificationType = newStatus === 'done' ? 'success' : 'info';

    await sendKanbanNotification(assignedUserId, {
      type: notificationType,
      title: 'Status da tarefa alterado',
      message: `A tarefa "${task.title}" foi movida de "${statusLabels[oldStatus]}" para "${statusLabels[newStatus]}"`,
      priority: 'normal',
      actionUrl: `/dashboard/kanban?board=${task.boardId}&task=${task.id}`,
      data: {
        taskId: task.id,
        boardId: task.boardId,
        oldStatus,
        newStatus,
        changedBy: changedByUserId,
        type: 'task_status_changed',
      },
    });
  };

  return { notifyTaskStatusChanged };
}

/**
 * Hook for board collaboration notifications
 */
export function useBoardCollaborationNotifications() {
  const { sendKanbanNotification } = useKanbanNotifications();

  const notifyBoardShared = async (board: Board, sharedWithUserId: string, sharedByUserId: string) => {
    if (sharedWithUserId === sharedByUserId) return;

    await sendKanbanNotification(sharedWithUserId, {
      type: 'info',
      title: 'Board compartilhado',
      message: `O board "${board.title}" foi compartilhado com você`,
      priority: 'normal',
      actionUrl: `/dashboard/kanban?board=${board.$id}`,
      data: {
        boardId: board.$id,
        sharedBy: sharedByUserId,
        type: 'board_shared',
      },
    });
  };

  const notifyBoardUpdated = async (board: Board, updatedByUserId: string, teamMemberIds: string[]) => {
    const notificationPromises = teamMemberIds
      .filter(memberId => memberId !== updatedByUserId)
      .map(memberId =>
        sendKanbanNotification(memberId, {
          type: 'info',
          title: 'Board atualizado',
          message: `O board "${board.title}" foi atualizado`,
          priority: 'low',
          actionUrl: `/dashboard/kanban?board=${board.$id}`,
          data: {
            boardId: board.$id,
            updatedBy: updatedByUserId,
            type: 'board_updated',
          },
        })
      );

    await Promise.all(notificationPromises);
  };

  return { notifyBoardShared, notifyBoardUpdated };
}

/**
 * Hook for checking and sending due date notifications
 * This would typically be called by a background job or cron
 */
export function useDueDateChecker() {
  const { notifyTaskDueSoon, notifyTaskOverdue } = useTaskDueDateNotifications();

  const checkTasksDueDates = async (tasks: Task[], boardTitle: string) => {
    const now = new Date();

    for (const task of tasks) {
      if (!task.dueDate || !task.assignedTo || task.status === 'done') continue;

      const dueDate = new Date(task.dueDate);
      const hoursUntilDue = (dueDate.getTime() - now.getTime()) / (1000 * 60 * 60);

      if (hoursUntilDue < 0) {
        // Task is overdue
        await notifyTaskOverdue(task, task.assignedTo, boardTitle);
      } else if (hoursUntilDue <= 24) {
        // Task is due within 24 hours
        await notifyTaskDueSoon(task, task.assignedTo, boardTitle, hoursUntilDue);
      }
    }
  };

  return { checkTasksDueDates };
}

/**
 * Main hook that combines all kanban notification functionality
 */
export function useKanbanNotificationSystem() {
  const taskAssignment = useTaskAssignmentNotifications();
  const taskDueDate = useTaskDueDateNotifications();
  const taskStatus = useTaskStatusNotifications();
  const boardCollaboration = useBoardCollaborationNotifications();
  const dueDateChecker = useDueDateChecker();

  return {
    ...taskAssignment,
    ...taskDueDate,
    ...taskStatus,
    ...boardCollaboration,
    ...dueDateChecker,
  };
}
