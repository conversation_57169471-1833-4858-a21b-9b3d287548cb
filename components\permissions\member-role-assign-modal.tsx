/**
 * Modal para atribuir cargos aos membros do time
 */

import { useState } from 'react';
import { Crown, Users, Shield, UserCheck, UserX, User, Eye } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../ui/table';
import { useAssignRole, useUnassignRole } from '../../hooks/api/use-permissions';
import type { TeamRole, TeamMemberRole, UserType } from '@/schemas/permissions';
import type { TeamMembership } from '@/schemas/teams';

interface MemberRoleAssignModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  teamId: string;
  roles: TeamRole[];
  memberRoles: TeamMemberRole[];
  members: TeamMembership[];
}

export function MemberRoleAssignModal({
  open,
  onOpenChange,
  teamId,
  roles,
  memberRoles,
  members,
}: MemberRoleAssignModalProps) {
  const [selectedMember, setSelectedMember] = useState<string>('');
  const [selectedRole, setSelectedRole] = useState<string>('');

  const assignRoleMutation = useAssignRole();
  const unassignRoleMutation = useUnassignRole();

  // Obter cargo atual de um membro
  const getMemberRole = (userId: string) => {
    return memberRoles.find(mr => mr.userId === userId);
  };

  // Obter detalhes do cargo
  const getRoleDetails = (roleId: string) => {
    return roles.find(r => r.id === roleId);
  };

  // Obter iniciais do nome
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Obter ícone do tipo de usuário
  const getUserTypeIcon = (userType: UserType) => {
    switch (userType) {
      case 'owner': return Crown;
      case 'admin': return Shield;
      case 'user': return User;
      case 'guest': return Eye;
      default: return Users;
    }
  };

  // Filtrar cargos ativos
  const activeRoles = roles.filter(role => role.isActive);

  // Atribuir cargo
  const handleAssignRole = async () => {
    if (!selectedMember || !selectedRole) return;

    try {
      await assignRoleMutation.mutateAsync({
        userId: selectedMember,
        roleId: selectedRole,
      });
      setSelectedMember('');
      setSelectedRole('');
    } catch (error) {
      // Error já é tratado no hook
    }
  };

  // Remover cargo
  const handleUnassignRole = async (userId: string) => {
    try {
      await unassignRoleMutation.mutateAsync(userId);
    } catch (error) {
      // Error já é tratado no hook
    }
  };

  // Verificar se é owner (não pode alterar cargo)
  const isOwner = (member: TeamMembership) => {
    return member.roles.includes('owner');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Atribuir Cargos aos Membros
          </DialogTitle>
          <DialogDescription>
            Gerencie os cargos dos membros da sua equipe. Proprietários têm acesso total automaticamente.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Atribuir novo cargo */}
          <div className="border rounded-lg p-4">
            <h3 className="font-medium mb-4">Atribuir Cargo</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Select value={selectedMember} onValueChange={setSelectedMember}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecionar membro" />
                </SelectTrigger>
                <SelectContent>
                  {members
                    .filter(member => !isOwner(member)) // Não mostrar owners
                    .map((member) => (
                      <SelectItem key={member.userId} value={member.userId}>
                        <div className="flex items-center gap-2">
                          <Avatar className="h-6 w-6">
                            <AvatarFallback className="text-xs">
                              {getInitials(member.userName)}
                            </AvatarFallback>
                          </Avatar>
                          <span>{member.userName}</span>
                        </div>
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>

              <Select value={selectedRole} onValueChange={setSelectedRole}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecionar cargo" />
                </SelectTrigger>
                <SelectContent>
                  {activeRoles.map((role) => {
                    const Icon = getUserTypeIcon(role.userType);
                    return (
                      <SelectItem key={role.id} value={role.id}>
                        <div className="flex items-center gap-2">
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: role.color }}
                          />
                          <Icon className="h-4 w-4" />
                          <span>{role.name}</span>
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>

              <Button
                onClick={handleAssignRole}
                disabled={!selectedMember || !selectedRole || assignRoleMutation.isPending}
              >
                <UserCheck className="h-4 w-4 mr-2" />
                Atribuir
              </Button>
            </div>
          </div>

          {/* Lista de membros e seus cargos */}
          <div>
            <h3 className="font-medium mb-4">Membros e Cargos Atuais</h3>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Membro</TableHead>
                  <TableHead>Cargo Atual</TableHead>
                  <TableHead>Tipo</TableHead>
                  <TableHead className="text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {members.map((member) => {
                  const memberRole = getMemberRole(member.userId);
                  const roleDetails = memberRole ? getRoleDetails(memberRole.roleId) : null;
                  const isOwnerMember = isOwner(member);

                  return (
                    <TableRow key={member.userId}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Avatar className="h-8 w-8">
                            <AvatarFallback>
                              {getInitials(member.userName)}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{member.userName}</div>
                            <div className="text-sm text-muted-foreground">
                              {member.userEmail}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {isOwnerMember ? (
                          <Badge variant="destructive">
                            <Crown className="w-3 h-3 mr-1" />
                            Proprietário
                          </Badge>
                        ) : roleDetails ? (
                          <div className="flex items-center gap-2">
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: roleDetails.color }}
                            />
                            <span>{roleDetails.name}</span>
                          </div>
                        ) : (
                          <Badge variant="outline">Sem cargo</Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        {isOwnerMember ? (
                          <Badge variant="destructive">Owner</Badge>
                        ) : (
                          <Badge variant="secondary">Colaborador</Badge>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        {!isOwnerMember && memberRole && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleUnassignRole(member.userId)}
                            disabled={unassignRoleMutation.isPending}
                          >
                            <UserX className="h-4 w-4" />
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>

          {/* Estatísticas */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="border rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-red-600">
                {members.filter(m => isOwner(m)).length}
              </div>
              <div className="text-sm text-muted-foreground">Proprietários</div>
            </div>
            <div className="border rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-blue-600">
                {memberRoles.length}
              </div>
              <div className="text-sm text-muted-foreground">Com Cargo</div>
            </div>
            <div className="border rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-gray-600">
                {members.filter(m => !isOwner(m) && !getMemberRole(m.userId)).length}
              </div>
              <div className="text-sm text-muted-foreground">Sem Cargo</div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
