/**
 * Hook para exportação local de dados
 * Implementa exportação CSV, Excel e PDF 100% no cliente
 * Não depende de cloud functions
 */

import { useCallback } from 'react';
import { toast } from 'sonner';
import { useAuth } from './use-auth';
import { canExportData } from '../lib/plan-limits';
import { usePDFGenerator } from './use-pdf-generator';

// Tipos para exportação local
export interface LocalExportOptions {
  format: 'csv' | 'excel' | 'pdf';
  filters?: Record<string, any>;
  columns?: string[];
  includeMetadata?: boolean;
  dateRange?: {
    start: string;
    end: string;
  };
}

export interface LocalExportResult {
  success: boolean;
  fileName: string;
  fileSize: number;
  recordsCount: number;
}

/**
 * Hook principal para exportação local
 */
export function useLocalExport() {
  const { user } = useAuth();
  const { generateTablePDF } = usePDFGenerator();

  // Verificar permissões
  const canUseExport = user ? canExportData(user).allowed : false;

  /**
   * Gera arquivo CSV
   */
  const generateCSV = useCallback((data: Record<string, any>[], columns?: string[]): string => {
    if (data.length === 0) {
      return '';
    }

    // Determinar colunas a serem exportadas
    const headers = columns && columns.length > 0 ? columns : Object.keys(data[0]);

    // Criar linhas CSV
    const csvLines = [
      headers.join(','), // Cabeçalho
      ...data.map(row =>
        headers.map(header => {
          const value = row[header];
          // Escapar valores que contêm vírgulas ou aspas
          if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value || '';
        }).join(',')
      )
    ];

    return csvLines.join('\n');
  }, []);

  /**
   * Gera arquivo Excel usando SheetJS
   */
  const generateExcel = useCallback(async (data: Record<string, any>[], columns?: string[], collectionName = 'dados'): Promise<Blob> => {
    // Importar SheetJS
    const XLSX = await import('xlsx');

    if (data.length === 0) {
      // Criar workbook vazio
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.aoa_to_sheet([['Nenhum dado encontrado']]);
      XLSX.utils.book_append_sheet(wb, ws, collectionName);

      const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
      return new Blob([wbout], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    }

    // Filtrar colunas se especificado
    let processedData = data;
    if (columns && columns.length > 0) {
      processedData = data.map(row => {
        const filteredRow: Record<string, any> = {};
        columns.forEach(col => {
          if (row.hasOwnProperty(col)) {
            filteredRow[col] = row[col];
          }
        });
        return filteredRow;
      });
    }

    // Criar workbook
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.json_to_sheet(processedData);

    // Adicionar worksheet
    XLSX.utils.book_append_sheet(wb, ws, collectionName);

    // Gerar buffer
    const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
    return new Blob([wbout], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
  }, []);

  /**
   * Baixa arquivo gerado
   */
  const downloadFile = useCallback((content: string | Blob, fileName: string, mimeType: string) => {
    let blob: Blob;

    if (typeof content === 'string') {
      blob = new Blob([content], { type: mimeType });
    } else {
      blob = content;
    }

    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    return blob.size;
  }, []);

  /**
   * Exporta dados localmente
   */
  const exportData = useCallback(async (
    collection: string,
    data: Record<string, any>[],
    options: LocalExportOptions
  ): Promise<LocalExportResult | null> => {
    try {
      if (!canUseExport) {
        toast.error('Você não tem permissão para exportar dados');
        return null;
      }

      if (!data || data.length === 0) {
        toast.error('Nenhum dado encontrado para exportar');
        return null;
      }

      const timestamp = Date.now();
      let fileName: string;
      let fileSize: number;

      switch (options.format) {
        case 'csv': {
          const csvContent = generateCSV(data, options.columns);
          fileName = `${collection}_export_${timestamp}.csv`;
          fileSize = downloadFile(csvContent, fileName, 'text/csv');
          break;
        }

        case 'excel': {
          const excelBlob = await generateExcel(data, options.columns, collection);
          fileName = `${collection}_export_${timestamp}.xlsx`;
          fileSize = downloadFile(excelBlob, fileName, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
          break;
        }

        case 'pdf': {
          // Preparar dados para PDF
          const columns = options.columns && options.columns.length > 0
            ? options.columns
            : Object.keys(data[0] || {});

          const pdfColumns = columns.map(col => ({
            header: col.charAt(0).toUpperCase() + col.slice(1),
            dataKey: col
          }));

          // Filtrar dados se necessário
          let pdfData = data;
          if (options.columns && options.columns.length > 0) {
            pdfData = data.map(row => {
              const filteredRow: Record<string, any> = {};
              options.columns!.forEach(col => {
                if (row.hasOwnProperty(col)) {
                  filteredRow[col] = row[col];
                }
              });
              return filteredRow;
            });
          }

          await generateTablePDF({
            title: `Relatório de ${collection.charAt(0).toUpperCase() + collection.slice(1)}`,
            subtitle: `Exportação gerada em ${new Date().toLocaleDateString('pt-BR')}`,
            columns: pdfColumns,
            data: pdfData,
            showRowNumbers: true,
            alternateRowColors: true,
          });

          fileName = `${collection}_export_${timestamp}.pdf`;
          fileSize = 0; // PDF é baixado diretamente pelo jsPDF
          break;
        }

        default:
          throw new Error(`Formato '${options.format}' não suportado`);
      }

      toast.success(`Export realizado com sucesso! Arquivo: ${fileName}`);

      return {
        success: true,
        fileName,
        fileSize,
        recordsCount: data.length,
      };

    } catch (error) {
      console.error('Erro na exportação local:', error);
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      toast.error(`Erro no export: ${errorMessage}`);
      return null;
    }
  }, [canUseExport, generateCSV, generateExcel, generateTablePDF, downloadFile]);

  return {
    exportData,
    canUseExport,
    isConfigured: true, // Exportação local sempre está disponível
  };
}

/**
 * Hook específico para exportação de clientes
 */
export function useClientsLocalExport() {
  const { exportData } = useLocalExport();

  const exportClients = useCallback(async (
    clients: any[],
    options: Omit<LocalExportOptions, 'format'> & { format: 'csv' | 'excel' | 'pdf' }
  ) => {
    return await exportData('clients', clients, options);
  }, [exportData]);

  return { exportClients };
}

/**
 * Hook específico para exportação de teams
 */
export function useTeamsLocalExport() {
  const { exportData } = useLocalExport();

  const exportTeams = useCallback(async (
    teams: any[],
    options: Omit<LocalExportOptions, 'format'> & { format: 'csv' | 'excel' | 'pdf' }
  ) => {
    return await exportData('teams', teams, options);
  }, [exportData]);

  return { exportTeams };
}
