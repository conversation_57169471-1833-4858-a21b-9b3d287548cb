import { Client, Databases, Account, Storage, Teams } from "appwrite";

/**
 * Appwrite configuration for the React Router v7 app
 * Adapted from the existing src/lib/appwrite/config.ts
 */

// Create a default client for general use
const client = new Client();
client.setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT || "");
client.setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID || "");

// Export default instances
export const account = new Account(client);
export const databases = new Databases(client);
export const storage = new Storage(client);
export const teams = new Teams(client);

// Collection IDs from environment variables - ESTRUTURA OTIMIZADA
export const COLLECTIONS = {
  // Core collections (mantidas)
  PUBLIC_PROFILES: process.env.NEXT_PUBLIC_APPWRITE_PUBLIC_PROFILES_ID || 'public_profiles',
  NOTIFICATIONS: process.env.NEXT_PUBLIC_APPWRITE_NOTIFICATIONS_ID || 'notifications',
  ACTIVITY_LOGS: process.env.NEXT_PUBLIC_APPWRITE_ACTIVITY_LOGS_ID || 'activity_logs',
  CLIENTS: process.env.NEXT_PUBLIC_APPWRITE_CLIENTS_ID || 'clients',

  // Chat collections (mantidas)
  CHAT_MESSAGES: process.env.NEXT_PUBLIC_APPWRITE_CHAT_MESSAGES_ID || 'chat_messages',
  TEAM_CHATS: process.env.NEXT_PUBLIC_APPWRITE_TEAM_CHATS_ID || 'team_chats',

  // Calendar collections (mantidas)
  EVENTS: process.env.NEXT_PUBLIC_APPWRITE_EVENTS_ID || 'events',
  EVENT_CATEGORIES: process.env.NEXT_PUBLIC_APPWRITE_EVENT_CATEGORIES_ID || 'event_categories',

  // 🚀 KANBAN OTIMIZADO - APENAS 2 COLEÇÕES!
  WORKSPACES: process.env.NEXT_PUBLIC_APPWRITE_WORKSPACES_ID || 'workspaces',
  KANBAN_BOARDS_OPTIMIZED: process.env.NEXT_PUBLIC_APPWRITE_KANBAN_BOARDS_OPTIMIZED_ID || 'kanban_boards_optimized',

  // ❌ REMOVIDAS - Coleções antigas do kanban (não usar mais)
  // KANBAN_COLUMNS: Agora embarcado em KANBAN_BOARDS
  // KANBAN_TASKS: Agora embarcado em KANBAN_BOARDS
  // LABELS: Agora embarcado em KANBAN_BOARDS
  // CHECKLISTS: Agora embarcado em KANBAN_BOARDS
  // COMMENTS: Agora embarcado em KANBAN_BOARDS
  // ATTACHMENTS: Agora embarcado em KANBAN_BOARDS
} as const;

// Database ID
export const DATABASE_ID = process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID || 'main';

// Storage bucket ID
export const STORAGE_BUCKET_ID = process.env.NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID || 'attachments';

/**
 * Utility function to check if Appwrite is properly configured
 */
export function isAppwriteConfigured(): boolean {
  return !!(
    process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT &&
    process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID &&
    process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID
  );
}

/**
 * Get Appwrite configuration status for debugging
 */
export function getAppwriteConfig() {
  return {
    endpoint: process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT,
    projectId: process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID,
    databaseId: process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
    isConfigured: isAppwriteConfigured(),
  };
}

// Log configuration in development
if (process.env.NODE_ENV === "development") {
  const config = getAppwriteConfig();
  console.log('🔧 Appwrite Configuration:', {
    endpoint: config.endpoint || '❌ Not set',
    projectId: config.projectId || '❌ Not set',
    databaseId: config.databaseId || '❌ Not set',
    isConfigured: config.isConfigured ? '✅ Ready' : '❌ Missing configuration',
  });
}
