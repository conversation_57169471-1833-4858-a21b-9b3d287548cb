/**
 * Event Modal Component
 * Modal para criar e editar eventos
 */

import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import {
  Calendar as CalendarIcon,
  Clock,
  MapPin,
  Tag,
  X,
  Save,
  Trash2,
  Edit
} from 'lucide-react';
import { cn } from '../../lib/utils';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Switch } from '../ui/switch';
import { Badge } from '../ui/badge';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import { Calendar } from '../ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '../ui/popover';
import type { Event } from '@/schemas/events';

export interface EventModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (event: Partial<Event>) => void;
  onDelete?: (eventId: string) => void;
  onEdit?: (event: Event) => void;
  event?: Event | null;
  initialDate?: Date;
  initialTime?: string;
  mode?: 'create' | 'edit' | 'view';
}

// Predefined categories with colors
const EVENT_CATEGORIES = [
  { id: 'meeting', label: 'Reunião', color: 'bg-blue-500' },
  { id: 'task', label: 'Tarefa', color: 'bg-green-500' },
  { id: 'reminder', label: 'Lembrete', color: 'bg-yellow-500' },
  { id: 'personal', label: 'Pessoal', color: 'bg-purple-500' },
  { id: 'work', label: 'Trabalho', color: 'bg-red-500' },
  { id: 'other', label: 'Outro', color: 'bg-gray-500' },
];

// Time options for select
const TIME_OPTIONS = Array.from({ length: 24 * 4 }, (_, i) => {
  const hour = Math.floor(i / 4);
  const minute = (i % 4) * 15;
  const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
  return { value: time, label: time };
});

export function EventModal({
  isOpen,
  onClose,
  onSave,
  onDelete,
  onEdit,
  event,
  initialDate,
  initialTime,
  mode = 'create',
}: EventModalProps) {
  // Form state
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    startDate: new Date(),
    endDate: new Date(),
    startTime: '09:00',
    endTime: '10:00',
    allDay: false,
    location: '',
    category: 'meeting',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  // Initialize form data
  useEffect(() => {
    if (event && mode !== 'create') {
      setFormData({
        title: event.title || '',
        description: event.description || '',
        startDate: new Date(event.startDate),
        endDate: new Date(event.endDate),
        startTime: format(new Date(event.startDate), 'HH:mm'),
        endTime: format(new Date(event.endDate), 'HH:mm'),
        allDay: event.allDay || false,
        location: event.location || '',
        category: event.category || 'meeting',
      });
    } else if (mode === 'create') {
      const startDate = initialDate || new Date();
      const endDate = new Date(startDate);
      endDate.setHours(startDate.getHours() + 1);

      setFormData({
        title: '',
        description: '',
        startDate,
        endDate,
        startTime: initialTime || '09:00',
        endTime: initialTime ?
          format(new Date(`2000-01-01T${initialTime}`).getTime() + 60 * 60 * 1000, 'HH:mm') :
          '10:00',
        allDay: false,
        location: '',
        category: 'meeting',
      });
    }
  }, [event, mode, initialDate, initialTime]);

  // Handle form field changes
  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Título é obrigatório';
    }

    if (!formData.allDay) {
      const startDateTime = new Date(`${format(formData.startDate, 'yyyy-MM-dd')}T${formData.startTime}`);
      const endDateTime = new Date(`${format(formData.endDate, 'yyyy-MM-dd')}T${formData.endTime}`);

      if (endDateTime <= startDateTime) {
        newErrors.endTime = 'Horário de fim deve ser após o início';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle save
  const handleSave = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      const eventData: Partial<Event> = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        location: formData.location.trim(),
        category: formData.category,
        allDay: formData.allDay,
        startDate: formData.allDay
          ? formData.startDate.toISOString()
          : new Date(`${format(formData.startDate, 'yyyy-MM-dd')}T${formData.startTime}`).toISOString(),
        endDate: formData.allDay
          ? formData.endDate.toISOString()
          : new Date(`${format(formData.endDate, 'yyyy-MM-dd')}T${formData.endTime}`).toISOString(),
      };

      if (event && mode === 'edit') {
        eventData.$id = event.$id;
      }

      await onSave(eventData);
      onClose();
    } catch (error) {
      console.error('Error saving event:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle delete
  const handleDelete = async () => {
    if (!event || !onDelete) return;

    if (confirm('Tem certeza que deseja excluir este evento?')) {
      setIsLoading(true);
      try {
        await onDelete(event.$id);
        onClose();
      } catch (error) {
        console.error('Error deleting event:', error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const selectedCategory = EVENT_CATEGORIES.find(cat => cat.id === formData.category);
  const isViewMode = mode === 'view';
  const isEditMode = mode === 'edit';
  const isCreateMode = mode === 'create';

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>
              {isCreateMode && 'Criar Evento'}
              {isEditMode && 'Editar Evento'}
              {isViewMode && 'Detalhes do Evento'}
            </span>
            {isViewMode && event && onEdit && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onEdit(event)}
                className="gap-2"
              >
                <Edit className="h-4 w-4" />
                Editar
              </Button>
            )}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Title */}
          <div className="space-y-2">
            <Label htmlFor="title">
              Título <span className="text-red-500">*</span>
            </Label>
            <Input
              id="title"
              placeholder="Digite o título do evento"
              value={formData.title}
              onChange={(e) => handleChange('title', e.target.value)}
              disabled={isViewMode}
              className={errors.title ? 'border-red-500' : ''}
            />
            {errors.title && (
              <p className="text-sm text-red-500">{errors.title}</p>
            )}
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Descrição</Label>
            <Textarea
              id="description"
              placeholder="Adicione uma descrição (opcional)"
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              disabled={isViewMode}
              rows={3}
            />
          </div>

          {/* All Day Toggle */}
          <div className="flex items-center space-x-2">
            <Switch
              id="allDay"
              checked={formData.allDay}
              onCheckedChange={(checked) => handleChange('allDay', checked)}
              disabled={isViewMode}
            />
            <Label htmlFor="allDay">Dia inteiro</Label>
          </div>

          {/* Date and Time */}
          <div className="grid grid-cols-2 gap-4">
            {/* Start Date */}
            <div className="space-y-2">
              <Label>Data de Início</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !formData.startDate && "text-muted-foreground"
                    )}
                    disabled={isViewMode}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {formData.startDate ? (
                      format(formData.startDate, "dd/MM/yyyy", { locale: ptBR })
                    ) : (
                      <span>Selecione a data</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={formData.startDate}
                    onSelect={(date) => date && handleChange('startDate', date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* Start Time */}
            {!formData.allDay && (
              <div className="space-y-2">
                <Label>Horário de Início</Label>
                <Select
                  value={formData.startTime}
                  onValueChange={(value) => handleChange('startTime', value)}
                  disabled={isViewMode}
                >
                  <SelectTrigger>
                    <Clock className="mr-2 h-4 w-4" />
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {TIME_OPTIONS.map((time) => (
                      <SelectItem key={time.value} value={time.value}>
                        {time.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            {/* End Date */}
            <div className="space-y-2">
              <Label>Data de Fim</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !formData.endDate && "text-muted-foreground"
                    )}
                    disabled={isViewMode}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {formData.endDate ? (
                      format(formData.endDate, "dd/MM/yyyy", { locale: ptBR })
                    ) : (
                      <span>Selecione a data</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={formData.endDate}
                    onSelect={(date) => date && handleChange('endDate', date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* End Time */}
            {!formData.allDay && (
              <div className="space-y-2">
                <Label>Horário de Fim</Label>
                <Select
                  value={formData.endTime}
                  onValueChange={(value) => handleChange('endTime', value)}
                  disabled={isViewMode}
                >
                  <SelectTrigger className={errors.endTime ? 'border-red-500' : ''}>
                    <Clock className="mr-2 h-4 w-4" />
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {TIME_OPTIONS.map((time) => (
                      <SelectItem key={time.value} value={time.value}>
                        {time.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.endTime && (
                  <p className="text-sm text-red-500">{errors.endTime}</p>
                )}
              </div>
            )}
          </div>

          {/* Location */}
          <div className="space-y-2">
            <Label htmlFor="location">Local</Label>
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="location"
                placeholder="Adicione um local (opcional)"
                value={formData.location}
                onChange={(e) => handleChange('location', e.target.value)}
                disabled={isViewMode}
                className="pl-10"
              />
            </div>
          </div>

          {/* Category */}
          <div className="space-y-2">
            <Label>Categoria</Label>
            <div className="flex flex-wrap gap-2">
              {EVENT_CATEGORIES.map((category) => (
                <button
                  key={category.id}
                  type="button"
                  onClick={() => !isViewMode && handleChange('category', category.id)}
                  disabled={isViewMode}
                  className={cn(
                    "flex items-center gap-2 px-3 py-2 rounded-lg border transition-colors",
                    formData.category === category.id
                      ? "border-primary bg-primary/10 text-primary"
                      : "border-border hover:bg-accent",
                    isViewMode && "cursor-default"
                  )}
                >
                  <div className={cn("w-3 h-3 rounded-full", category.color)} />
                  <span className="text-sm">{category.label}</span>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-between pt-6">
          <div>
            {event && onDelete && !isViewMode && (
              <Button
                variant="destructive"
                onClick={handleDelete}
                disabled={isLoading}
                className="gap-2"
              >
                <Trash2 className="h-4 w-4" />
                Excluir
              </Button>
            )}
          </div>

          <div className="flex gap-2">
            <Button variant="outline" onClick={onClose} disabled={isLoading}>
              {isViewMode ? 'Fechar' : 'Cancelar'}
            </Button>
            {!isViewMode && (
              <Button onClick={handleSave} disabled={isLoading} className="gap-2">
                <Save className="h-4 w-4" />
                {isLoading ? 'Salvando...' : 'Salvar'}
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
