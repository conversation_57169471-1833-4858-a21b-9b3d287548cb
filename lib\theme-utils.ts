/**
 * Theme utilities for managing theme state and persistence
 */

export const THEME_STORAGE_KEY = "active_theme";
export const DEFAULT_THEME = "default";

export type ThemeVariant =
  | "default"
  | "blue"
  | "green"
  | "amber"
  | "ecommerce"
  | "mono"
  | "candyland"
  | "cyberpunk"
  | "sunset"
  | "ocean"
  | "nature";

export type ThemeMode = "light" | "dark" | "system";

export interface ThemeConfig {
  variant: ThemeVariant;
  scaled: boolean;
  mode: ThemeMode;
}

/**
 * Parse theme string into components
 */
export function parseTheme(themeString: string): {
  variant: ThemeVariant;
  scaled: boolean;
} {
  const isScaled = themeString.endsWith('-scaled');
  const variant = (isScaled
    ? themeString.replace('-scaled', '')
    : themeString) as ThemeVariant;

  return { variant, scaled: isScaled };
}

/**
 * Build theme string from components
 */
export function buildThemeString(variant: ThemeVariant, scaled: boolean = false): string {
  return scaled ? `${variant}-scaled` : variant;
}

/**
 * Get theme from localStorage with fallback
 */
export function getStoredTheme(): string {
  if (typeof window === "undefined") return DEFAULT_THEME;

  try {
    return localStorage.getItem(THEME_STORAGE_KEY) || DEFAULT_THEME;
  } catch {
    return DEFAULT_THEME;
  }
}

/**
 * Save theme to localStorage
 */
export function saveTheme(theme: string): void {
  if (typeof window === "undefined") return;

  try {
    localStorage.setItem(THEME_STORAGE_KEY, theme);
  } catch (error) {
    console.warn("Failed to save theme:", error);
  }
}

/**
 * Apply theme classes to document body
 */
export function applyThemeClasses(theme: string): void {
  if (typeof document === "undefined") return;

  // Remove existing theme classes
  Array.from(document.body.classList)
    .filter((className) => className.startsWith("theme-"))
    .forEach((className) => {
      document.body.classList.remove(className);
    });

  // Add new theme class
  document.body.classList.add(`theme-${theme}`);

  // Add scaled class if needed
  if (theme.endsWith("-scaled")) {
    document.body.classList.add("theme-scaled");
  } else {
    document.body.classList.remove("theme-scaled");
  }
}

/**
 * Get available themes with metadata
 */
export function getAvailableThemes() {
  return [
    {
      variant: "default" as ThemeVariant,
      name: "Default",
      description: "Tema padrão neutro",
      supportsScaled: true,
    },
    {
      variant: "blue" as ThemeVariant,
      name: "Blue",
      description: "Tema azul profissional",
      supportsScaled: true,
    },
    {
      variant: "green" as ThemeVariant,
      name: "Green",
      description: "Tema verde natural",
      supportsScaled: false,
    },
    {
      variant: "amber" as ThemeVariant,
      name: "Amber",
      description: "Tema âmbar caloroso",
      supportsScaled: false,
    },
    {
      variant: "ecommerce" as ThemeVariant,
      name: "E-commerce",
      description: "Tema moderno para e-commerce",
      supportsScaled: false,
    },
    {
      variant: "mono" as ThemeVariant,
      name: "Mono",
      description: "Tema minimalista monocromático",
      supportsScaled: true,
    },
  ];
}
