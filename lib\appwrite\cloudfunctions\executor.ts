/**
 * Cloud Function Execution Service
 * Handles execution of Appwrite cloud functions with fallback behavior
 * and graceful degradation when functions are not configured
 */

import { Functions } from 'appwrite';
import { toast } from 'sonner';
import {
  CLOUD_FUNCTIONS,
  FUNCTION_CONFIG,
  isFunctionConfigured,
  getFunctionUrl,
  type CloudFunctionName
} from './const';
import { account } from '../config';
import { logger, log } from '../../logger';
import { loggerError, ErrorType } from '../../error-handler';

// Create Functions client
const functions = new Functions(account.client);

export interface FunctionExecutionOptions {
  data?: Record<string, unknown>;
  async?: boolean;
  timeout?: number;
  retries?: number;
  showErrorToast?: boolean;
  fallbackData?: unknown;
  fallbackMessage?: string;
}

export interface FunctionExecutionResult<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  isFallback?: boolean;
  executionTime?: number;
}

/**
 * Execute a cloud function with fallback handling
 */
export async function executeFunction<T = unknown>(
  functionName: CloudFunctionName,
  options: FunctionExecutionOptions = {}
): Promise<FunctionExecutionResult<T>> {
  const startTime = Date.now();

  const {
    data = {},
    async: isAsync = true,
    timeout = FUNCTION_CONFIG.DEFAULT_TIMEOUT,
    retries = FUNCTION_CONFIG.MAX_RETRIES,
    showErrorToast = true,
    fallbackData,
    fallbackMessage,
  } = options;

  // Check if function is configured
  if (!isFunctionConfigured(functionName)) {
    log.warn(`Cloud function '${functionName}' is not configured`, { functionName });

    if (FUNCTION_CONFIG.ENABLE_FALLBACKS && fallbackData !== undefined) {
      if (FUNCTION_CONFIG.SHOW_FALLBACK_MESSAGES && fallbackMessage) {
        toast.info(fallbackMessage);
      }

      log.info(`Using fallback data for function '${functionName}'`, { functionName, fallback: true });

      return {
        success: true,
        data: fallbackData as T,
        isFallback: true,
        executionTime: Date.now() - startTime,
      };
    }

    const error = `Cloud function '${functionName}' is not configured`;
    if (showErrorToast) {
      await loggerError(new Error(error), { functionName }, {
        type: ErrorType.NOT_FOUND,
        customMessage: error
      });
    }

    return {
      success: false,
      error,
      executionTime: Date.now() - startTime,
    };
  }

  // Get function URL
  const functionUrl = getFunctionUrl(functionName);
  if (!functionUrl) {
    const error = `No URL configured for function '${functionName}'`;
    if (showErrorToast) {
      await loggerError(new Error(error), { functionName }, {
        type: ErrorType.NOT_FOUND,
        customMessage: error
      });
    }
    return {
      success: false,
      error,
      executionTime: Date.now() - startTime,
    };
  }

  // Execute function with retries
  let lastError: Error | null = null;

  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      log.function(`Executing cloud function '${functionName}' (attempt ${attempt + 1})`, {
        functionName,
        attempt: attempt + 1,
        maxRetries: retries + 1,
        data
      });

      const execution = await functions.createExecution(
        functionUrl,
        JSON.stringify(data),
        isAsync
      );

      // Check execution status
      if (execution.status === 'failed') {
        throw new Error((execution as any).stderr || 'Function execution failed');
      }

      let responseData: T | undefined;

      // Parse response data if available
      if ((execution as any).response) {
        try {
          responseData = JSON.parse((execution as any).response) as T;
        } catch (parseError) {
          // If parsing fails, return raw response
          responseData = (execution as any).response as T;
        }
      }

      log.success(`Cloud function '${functionName}' executed successfully`, {
        functionName,
        attempt: attempt + 1,
        executionTime: Date.now() - startTime,
        hasResponse: !!responseData
      });

      return {
        success: true,
        data: responseData,
        executionTime: Date.now() - startTime,
      };

    } catch (error) {
      lastError = error as Error;
      log.error(`Cloud function '${functionName}' failed (attempt ${attempt + 1})`, lastError, {
        functionName,
        attempt: attempt + 1,
        maxRetries: retries + 1
      });

      // Wait before retry (except on last attempt)
      if (attempt < retries) {
        await new Promise(resolve =>
          setTimeout(resolve, FUNCTION_CONFIG.RETRY_DELAY * (attempt + 1))
        );
      }
    }
  }

  // All attempts failed
  const errorMessage = `Function '${functionName}' failed after ${retries + 1} attempts: ${lastError?.message}`;

  if (showErrorToast) {
    await loggerError(lastError || new Error(errorMessage), {
      functionName,
      attempts: retries + 1,
      executionTime: Date.now() - startTime
    }, {
      type: ErrorType.SERVER,
      customMessage: `Falha na execução da função: ${lastError?.message}`
    });
  }

  // Try fallback if available
  if (FUNCTION_CONFIG.ENABLE_FALLBACKS && fallbackData !== undefined) {
    if (FUNCTION_CONFIG.SHOW_FALLBACK_MESSAGES && fallbackMessage) {
      toast.warning(fallbackMessage);
    }

    log.warn(`Using fallback data after function failure`, {
      functionName,
      attempts: retries + 1,
      fallback: true
    });

    return {
      success: true,
      data: fallbackData as T,
      isFallback: true,
      error: errorMessage,
      executionTime: Date.now() - startTime,
    };
  }

  return {
    success: false,
    error: errorMessage,
    executionTime: Date.now() - startTime,
  };
}

/**
 * Execute multiple functions in parallel
 */
export async function executeFunctions<T = unknown>(
  executions: Array<{
    functionName: CloudFunctionName;
    options?: FunctionExecutionOptions;
  }>
): Promise<Array<FunctionExecutionResult<T>>> {
  const promises = executions.map(({ functionName, options }) =>
    executeFunction<T>(functionName, options)
  );

  return Promise.all(promises);
}

/**
 * Execute function with specific data validation
 */
export async function executeValidatedFunction<TInput, TOutput>(
  functionName: CloudFunctionName,
  data: TInput,
  validator?: (result: unknown) => result is TOutput,
  options: Omit<FunctionExecutionOptions, 'data'> = {}
): Promise<FunctionExecutionResult<TOutput>> {
  const result = await executeFunction<TOutput>(functionName, {
    ...options,
    data: data as Record<string, unknown>,
  });

  if (result.success && result.data && validator) {
    if (!validator(result.data)) {
      return {
        success: false,
        error: 'Function response validation failed',
        executionTime: result.executionTime,
      };
    }
  }

  return result;
}

/**
 * Check if a function is available and responsive
 */
export async function checkFunctionHealth(
  functionName: CloudFunctionName
): Promise<{ available: boolean; responseTime?: number; error?: string }> {
  if (!isFunctionConfigured(functionName)) {
    return {
      available: false,
      error: 'Function not configured',
    };
  }

  const startTime = Date.now();

  try {
    const result = await executeFunction(functionName, {
      data: { healthCheck: true },
      timeout: 5000,
      retries: 0,
      showErrorToast: false,
    });

    return {
      available: result.success,
      responseTime: Date.now() - startTime,
      error: result.error,
    };
  } catch (error) {
    return {
      available: false,
      responseTime: Date.now() - startTime,
      error: (error as Error).message,
    };
  }
}

/**
 * Get health status of all configured functions
 */
export async function getFunctionsHealthStatus(): Promise<Record<string, {
  available: boolean;
  responseTime?: number;
  error?: string;
}>> {
  const configuredFunctions = Object.keys(CLOUD_FUNCTIONS).filter(fn =>
    isFunctionConfigured(fn as CloudFunctionName)
  ) as CloudFunctionName[];

  const healthChecks = await Promise.all(
    configuredFunctions.map(async (fn) => ({
      name: fn,
      health: await checkFunctionHealth(fn),
    }))
  );

  return healthChecks.reduce((acc, { name, health }) => {
    acc[name] = health;
    return acc;
  }, {} as Record<string, { available: boolean; responseTime?: number; error?: string }>);
}
