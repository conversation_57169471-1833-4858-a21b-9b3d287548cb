/**
 * Enhanced export system types and configurations
 * Supports multiple formats with format-specific limitations and controls
 */

import { z } from 'zod';

// ============================================================================
// EXPORT FORMAT SCHEMAS
// ============================================================================

export const exportFormatSchema = z.enum(['csv', 'excel', 'json', 'pdf']);

// ============================================================================
// PDF EXPORT LIMITATIONS
// ============================================================================

export const pdfLimitationSchema = z.object({
  maxRows: z.number().min(1).max(1000).default(100),
  maxColumns: z.number().min(1).max(20).default(10),
  enablePagination: z.boolean().default(true),
  pageSize: z.number().min(10).max(100).default(50),
  orientation: z.enum(['portrait', 'landscape']).default('portrait'),
  fontSize: z.enum(['small', 'medium', 'large']).default('medium'),
});

// ============================================================================
// EXPORT CONFIGURATION SCHEMA
// ============================================================================

export const exportConfigSchema = z.object({
  // Basic settings
  format: exportFormatSchema,
  includeAllFields: z.boolean().default(true),
  selectedFields: z.array(z.string()).default([]),
  includeMetadata: z.boolean().default(false),
  
  // Filtering options
  dateRange: z.object({
    start: z.string().optional(),
    end: z.string().optional(),
  }).optional(),
  
  filters: z.record(z.any()).default({}),
  
  // Format-specific settings
  csvSettings: z.object({
    delimiter: z.enum([',', ';', '\t']).default(','),
    encoding: z.enum(['utf-8', 'latin1']).default('utf-8'),
    includeHeaders: z.boolean().default(true),
  }).optional(),
  
  excelSettings: z.object({
    sheetName: z.string().max(31).default('Data'),
    includeHeaders: z.boolean().default(true),
    autoFitColumns: z.boolean().default(true),
    freezeHeaders: z.boolean().default(true),
  }).optional(),
  
  jsonSettings: z.object({
    pretty: z.boolean().default(true),
    includeSchema: z.boolean().default(false),
  }).optional(),
  
  pdfSettings: pdfLimitationSchema.optional(),
});

// ============================================================================
// EXPORT RESULT SCHEMAS
// ============================================================================

export const exportResultSchema = z.object({
  success: z.boolean(),
  fileName: z.string(),
  fileSize: z.number(),
  recordsCount: z.number(),
  recordsExported: z.number(),
  downloadUrl: z.string().optional(),
  warnings: z.array(z.string()).default([]),
  limitations: z.object({
    rowsLimited: z.boolean().default(false),
    columnsLimited: z.boolean().default(false),
    originalRowCount: z.number().optional(),
    originalColumnCount: z.number().optional(),
  }).optional(),
});

// ============================================================================
// FIELD CONFIGURATION SCHEMA
// ============================================================================

export const exportFieldSchema = z.object({
  id: z.string(),
  label: z.string(),
  type: z.enum(['string', 'number', 'boolean', 'date', 'array', 'object']).default('string'),
  required: z.boolean().default(false),
  pdfSupported: z.boolean().default(true),
  description: z.string().optional(),
  width: z.number().optional(), // For PDF column width
});

// ============================================================================
// EXPORT PREVIEW SCHEMA
// ============================================================================

export const exportPreviewSchema = z.object({
  totalRecords: z.number(),
  recordsToExport: z.number(),
  fieldsToExport: z.array(z.string()),
  estimatedFileSize: z.string(),
  warnings: z.array(z.string()).default([]),
  limitations: z.object({
    pdfRowLimit: z.boolean().default(false),
    pdfColumnLimit: z.boolean().default(false),
    suggestedAlternatives: z.array(exportFormatSchema).default([]),
  }).optional(),
});

// ============================================================================
// DERIVED TYPESCRIPT TYPES
// ============================================================================

export type ExportFormat = z.infer<typeof exportFormatSchema>;
export type PDFLimitation = z.infer<typeof pdfLimitationSchema>;
export type ExportConfig = z.infer<typeof exportConfigSchema>;
export type ExportResult = z.infer<typeof exportResultSchema>;
export type ExportField = z.infer<typeof exportFieldSchema>;
export type ExportPreview = z.infer<typeof exportPreviewSchema>;

// ============================================================================
// EXPORT CONSTANTS
// ============================================================================

export const EXPORT_FORMATS = [
  {
    value: 'csv' as const,
    label: 'CSV',
    description: 'Arquivo de texto separado por vírgulas',
    icon: 'FileText',
    maxRecords: -1, // Unlimited
    supportsAllFields: true,
  },
  {
    value: 'excel' as const,
    label: 'Excel',
    description: 'Planilha do Microsoft Excel',
    icon: 'FileSpreadsheet',
    maxRecords: 1000000, // Excel limit
    supportsAllFields: true,
  },
  {
    value: 'json' as const,
    label: 'JSON',
    description: 'Formato de dados estruturados',
    icon: 'Code',
    maxRecords: -1, // Unlimited
    supportsAllFields: true,
  },
  {
    value: 'pdf' as const,
    label: 'PDF',
    description: 'Documento PDF profissional',
    icon: 'FileText',
    maxRecords: 100, // Default PDF limit
    supportsAllFields: false,
  },
] as const;

export const PDF_LIMITATIONS = {
  DEFAULT_MAX_ROWS: 100,
  DEFAULT_MAX_COLUMNS: 10,
  MAX_ROWS_LIMIT: 1000,
  MAX_COLUMNS_LIMIT: 20,
  RECOMMENDED_ROWS: 50,
  RECOMMENDED_COLUMNS: 8,
} as const;

// ============================================================================
// FIELD DEFINITIONS FOR DIFFERENT COLLECTIONS
// ============================================================================

export const CLIENT_EXPORT_FIELDS: ExportField[] = [
  { id: 'name', label: 'Nome', type: 'string', required: true, pdfSupported: true, width: 80 },
  { id: 'email', label: 'Email', type: 'string', required: true, pdfSupported: true, width: 100 },
  { id: 'phone', label: 'Telefone', type: 'string', required: false, pdfSupported: true, width: 70 },
  { id: 'document', label: 'Documento', type: 'string', required: false, pdfSupported: true, width: 80 },
  { id: 'type', label: 'Tipo', type: 'string', required: false, pdfSupported: true, width: 60 },
  { id: 'company', label: 'Empresa', type: 'string', required: false, pdfSupported: true, width: 90 },
  { id: 'companyDocument', label: 'CNPJ', type: 'string', required: false, pdfSupported: false, width: 80 },
  { id: 'status', label: 'Status', type: 'string', required: false, pdfSupported: true, width: 50 },
  { id: 'priority', label: 'Prioridade', type: 'string', required: false, pdfSupported: true, width: 60 },
  { id: 'tags', label: 'Tags', type: 'array', required: false, pdfSupported: false, description: 'Lista de tags do cliente' },
  { id: 'avatar', label: 'Avatar', type: 'string', required: false, pdfSupported: false, description: 'URL do avatar' },
  { id: '$createdAt', label: 'Data de Criação', type: 'date', required: false, pdfSupported: true, width: 70 },
  { id: '$updatedAt', label: 'Última Atualização', type: 'date', required: false, pdfSupported: false, width: 70 },
  { id: 'userId', label: 'ID do Usuário', type: 'string', required: false, pdfSupported: false, description: 'Identificador do proprietário' },
  { id: 'teamId', label: 'ID do Time', type: 'string', required: false, pdfSupported: false, description: 'Identificador do time' },
];

export const TEAM_EXPORT_FIELDS: ExportField[] = [
  { id: 'name', label: 'Nome', type: 'string', required: true, pdfSupported: true, width: 100 },
  { id: 'total', label: 'Total de Membros', type: 'number', required: true, pdfSupported: true, width: 60 },
  { id: '$createdAt', label: 'Data de Criação', type: 'date', required: false, pdfSupported: true, width: 80 },
  { id: '$updatedAt', label: 'Última Atualização', type: 'date', required: false, pdfSupported: true, width: 80 },
  { id: '$id', label: 'ID', type: 'string', required: false, pdfSupported: false, description: 'Identificador único' },
];

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

export function getFieldsForCollection(collection: string): ExportField[] {
  switch (collection) {
    case 'clients':
      return CLIENT_EXPORT_FIELDS;
    case 'teams':
      return TEAM_EXPORT_FIELDS;
    default:
      return [];
  }
}

export function getDefaultFieldsForFormat(fields: ExportField[], format: ExportFormat): string[] {
  if (format === 'pdf') {
    // For PDF, only include required fields and PDF-supported fields by default
    return fields
      .filter(field => field.required || (field.pdfSupported && fields.indexOf(field) < PDF_LIMITATIONS.RECOMMENDED_COLUMNS))
      .map(field => field.id);
  }
  
  // For other formats, include all fields by default
  return fields.map(field => field.id);
}

export function validatePDFExport(config: ExportConfig, totalRecords: number): string[] {
  const warnings: string[] = [];
  const pdfSettings = config.pdfSettings || pdfLimitationSchema.parse({});
  
  if (totalRecords > pdfSettings.maxRows) {
    warnings.push(`PDF será limitado a ${pdfSettings.maxRows} registros de ${totalRecords} disponíveis`);
  }
  
  if (config.selectedFields.length > pdfSettings.maxColumns) {
    warnings.push(`PDF será limitado a ${pdfSettings.maxColumns} colunas de ${config.selectedFields.length} selecionadas`);
  }
  
  return warnings;
}
