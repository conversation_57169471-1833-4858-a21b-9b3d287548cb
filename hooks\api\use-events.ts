/**
 * Event API Hooks
 * Local-first React Query hooks para eventos
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useAuth } from '../use-auth';
import { useTeamContext } from '../../contexts/team-context';
import { useDocumentPermissions } from '../use-document-permissions';
import { useIndexedDB } from '../use-indexeddb';
import { isCacheEnabled } from '../../lib/cache-config';
import { syncAfterMutation } from '../../lib/cache-sync';
import type {
  Event,
  EventFormData,
  UpdateEventData,
  EventCategory,
  EventCategoryFormData,
  EventFilters,
  EventStats
} from '@/schemas/events';
import * as eventFunctions from '../../lib/appwrite/functions/events';

// ============================================================================
// QUERY KEYS
// ============================================================================

export const eventKeys = {
  all: ['events'] as const,
  lists: () => [...eventKeys.all, 'list'] as const,
  list: (userId: string, teamId?: string) => [...eventKeys.lists(), userId, teamId] as const,
  details: () => [...eventKeys.all, 'detail'] as const,
  detail: (id: string) => [...eventKeys.details(), id] as const,
  search: (userId: string, filters: EventFilters) => [...eventKeys.all, 'search', userId, filters] as const,
  dateRange: (userId: string, start: Date, end: Date, teamId?: string) =>
    [...eventKeys.all, 'dateRange', userId, start.toISOString(), end.toISOString(), teamId] as const,
  upcoming: (userId: string, teamId?: string) => [...eventKeys.all, 'upcoming', userId, teamId] as const,
  recurring: (parentId: string) => [...eventKeys.all, 'recurring', parentId] as const,
  stats: (userId: string, teamId?: string) => [...eventKeys.all, 'stats', userId, teamId] as const,

  // Categories
  categories: {
    all: ['eventCategories'] as const,
    lists: () => [...eventKeys.categories.all, 'list'] as const,
    list: (userId: string, teamId?: string) => [...eventKeys.categories.lists(), userId, teamId] as const,
    details: () => [...eventKeys.categories.all, 'detail'] as const,
    detail: (id: string) => [...eventKeys.categories.details(), id] as const,
    defaults: (userId: string) => [...eventKeys.categories.all, 'defaults', userId] as const,
  },
};

// ============================================================================
// EVENT HOOKS
// ============================================================================

/**
 * Listar todos os eventos
 */
export function useEvents(teamId?: string) {
  const { user } = useAuth();
  const { getCache, setCache } = useIndexedDB();

  return useQuery({
    queryKey: eventKeys.list(user?.$id || '', teamId),
    queryFn: async () => {
      if (!user?.$id) throw new Error('User not authenticated');

      // Tentar cache local primeiro
      if (isCacheEnabled()) {
        const cached = await getCache('events', user.$id);
        if (cached) {
          console.log('📦 Dados do cache local');
          return cached as Event[];
        }
      }

      // Buscar do servidor
      console.log('🌐 Buscando eventos do servidor');
      const result = await eventFunctions.listEvents(user.$id, teamId);
      const events = result.documents as unknown as Event[];

      // Salvar no cache se habilitado
      if (isCacheEnabled() && events.length > 0) {
        await setCache('events', events, user.$id);
      }

      return events;
    },
    enabled: !!user?.$id,
    staleTime: isCacheEnabled() ? 0 : 5 * 60 * 1000,
    refetchOnWindowFocus: false,
  });
}

/**
 * Buscar eventos por filtros
 */
export function useSearchEvents(filters: EventFilters) {
  const { user } = useAuth();

  return useQuery({
    queryKey: eventKeys.search(user?.$id || '', filters),
    queryFn: async () => {
      if (!user?.$id) throw new Error('User not authenticated');

      const result = await eventFunctions.searchEvents(user.$id, filters);
      return result.documents as unknown as Event[];
    },
    enabled: !!user?.$id && Object.keys(filters).length > 0,
    staleTime: 2 * 60 * 1000, // 2 minutos para buscas
  });
}

/**
 * Buscar eventos por intervalo de datas
 */
export function useEventsByDateRange(startDate: Date, endDate: Date, teamId?: string) {
  const { user } = useAuth();

  return useQuery({
    queryKey: eventKeys.dateRange(user?.$id || '', startDate, endDate, teamId),
    queryFn: async () => {
      if (!user?.$id) throw new Error('User not authenticated');

      const result = await eventFunctions.getEventsByDateRange(user.$id, startDate, endDate, teamId);
      return result.documents as unknown as Event[];
    },
    enabled: !!user?.$id,
    staleTime: 5 * 60 * 1000,
  });
}

/**
 * Obter evento por ID
 */
export function useEvent(eventId: string) {
  const { addToCache } = useIndexedDB();

  return useQuery({
    queryKey: eventKeys.detail(eventId),
    queryFn: async () => {
      const result = await eventFunctions.getEvent(eventId);
      const event = result as unknown as Event;

      // Adicionar ao cache local
      if (isCacheEnabled()) {
        await addToCache('events', event);
      }

      return event;
    },
    enabled: !!eventId,
    staleTime: 5 * 60 * 1000,
  });
}

/**
 * Buscar eventos próximos
 */
export function useUpcomingEvents(teamId?: string) {
  const { user } = useAuth();

  return useQuery({
    queryKey: eventKeys.upcoming(user?.$id || '', teamId),
    queryFn: async () => {
      if (!user?.$id) throw new Error('User not authenticated');

      const result = await eventFunctions.getUpcomingEvents(user.$id, teamId);
      return result.documents as unknown as Event[];
    },
    enabled: !!user?.$id,
    staleTime: 2 * 60 * 1000,
    refetchInterval: 5 * 60 * 1000, // Atualizar a cada 5 minutos
  });
}

/**
 * Buscar eventos recorrentes
 */
export function useRecurringEvents(parentEventId: string) {
  return useQuery({
    queryKey: eventKeys.recurring(parentEventId),
    queryFn: async () => {
      const result = await eventFunctions.getRecurringEvents(parentEventId);
      return result.documents as unknown as Event[];
    },
    enabled: !!parentEventId,
    staleTime: 5 * 60 * 1000,
  });
}

/**
 * Estatísticas de eventos
 */
export function useEventStats(teamId?: string) {
  const { user } = useAuth();

  return useQuery({
    queryKey: eventKeys.stats(user?.$id || '', teamId),
    queryFn: async () => {
      if (!user?.$id) throw new Error('User not authenticated');

      return await eventFunctions.getEventStats(user.$id, teamId);
    },
    enabled: !!user?.$id,
    staleTime: 10 * 60 * 1000, // 10 minutos para stats
  });
}

// ============================================================================
// EVENT MUTATIONS
// ============================================================================

/**
 * Criar evento
 */
export function useCreateEvent(options?: { onSuccess?: (event: Event) => void }) {
  const { user } = useAuth();
  const { permissionContext } = useTeamContext();
  const { teamId } = useDocumentPermissions();
  const queryClient = useQueryClient();
  const { addToCache } = useIndexedDB();

  return useMutation({
    mutationFn: async (data: EventFormData) => {
      if (!user?.$id) throw new Error('User not authenticated');

      const eventData = {
        ...data,
        userId: user.$id,
        createdBy: user.$id,
        teamId: teamId || undefined,
      };

      const result = await eventFunctions.createEvent(eventData, {
        userId: user.$id,
        teamId,
        permissionContext,
        isPublic: false,
      });

      return result as unknown as Event;
    },
    onSuccess: async (event) => {
      // Adicionar ao cache local
      if (isCacheEnabled()) {
        await addToCache('events', event);
      }

      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: eventKeys.lists() });
      queryClient.invalidateQueries({ queryKey: eventKeys.stats(user?.$id || '') });

      // Sync com cache
      await syncAfterMutation('events', 'create', event);

      toast.success('Evento criado com sucesso!');
      options?.onSuccess?.(event);
    },
    onError: (error) => {
      console.error('Erro ao criar evento:', error);
      toast.error('Erro ao criar evento. Tente novamente.');
    },
  });
}

/**
 * Atualizar evento
 */
export function useUpdateEvent(options?: { onSuccess?: (event: Event) => void }) {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const { updateInCache } = useIndexedDB();

  return useMutation({
    mutationFn: async ({ eventId, data }: { eventId: string; data: Partial<UpdateEventData> }) => {
      const updateData = {
        ...data,
        updatedBy: user?.$id,
      };

      const result = await eventFunctions.updateEvent(eventId, updateData);
      return result as unknown as Event;
    },
    onSuccess: async (event) => {
      // Atualizar cache local
      if (isCacheEnabled()) {
        await updateInCache('events', event);
      }

      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: eventKeys.detail(event.$id) });
      queryClient.invalidateQueries({ queryKey: eventKeys.lists() });
      queryClient.invalidateQueries({ queryKey: eventKeys.stats(user?.$id || '') });

      // Sync com cache
      await syncAfterMutation('events', 'update', event);

      toast.success('Evento atualizado com sucesso!');
      options?.onSuccess?.(event);
    },
    onError: (error) => {
      console.error('Erro ao atualizar evento:', error);
      toast.error('Erro ao atualizar evento. Tente novamente.');
    },
  });
}

/**
 * Deletar evento
 */
export function useDeleteEvent(options?: { onSuccess?: () => void }) {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const { removeFromCache } = useIndexedDB();

  return useMutation({
    mutationFn: async (eventId: string) => {
      await eventFunctions.deleteEvent(eventId);
      return eventId;
    },
    onSuccess: async (eventId) => {
      // Remover do cache local
      if (isCacheEnabled()) {
        await removeFromCache('events', eventId);
      }

      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: eventKeys.detail(eventId) });
      queryClient.invalidateQueries({ queryKey: eventKeys.lists() });
      queryClient.invalidateQueries({ queryKey: eventKeys.stats(user?.$id || '') });

      // Sync com cache
      await syncAfterMutation('events', 'delete', { $id: eventId, $updatedAt: new Date().toISOString() });

      toast.success('Evento deletado com sucesso!');
      options?.onSuccess?.();
    },
    onError: (error) => {
      console.error('Erro ao deletar evento:', error);
      toast.error('Erro ao deletar evento. Tente novamente.');
    },
  });
}

/**
 * Marcar lembrete como enviado
 */
export function useMarkReminderSent() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (eventId: string) => {
      return await eventFunctions.markReminderSent(eventId);
    },
    onSuccess: (_, eventId) => {
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: eventKeys.detail(eventId) });
      queryClient.invalidateQueries({ queryKey: eventKeys.lists() });
    },
  });
}

// ============================================================================
// EVENT CATEGORY HOOKS
// ============================================================================

/**
 * Listar categorias de eventos
 */
export function useEventCategories(teamId?: string) {
  const { user } = useAuth();
  const { getCache, setCache } = useIndexedDB();

  return useQuery({
    queryKey: eventKeys.categories.list(user?.$id || '', teamId),
    queryFn: async () => {
      if (!user?.$id) throw new Error('User not authenticated');

      // Tentar cache local primeiro
      if (isCacheEnabled()) {
        const cached = await getCache('eventCategories', user.$id);
        if (cached) {
          console.log('📦 Categorias do cache local');
          return cached as EventCategory[];
        }
      }

      // Buscar do servidor
      console.log('🌐 Buscando categorias do servidor');
      const result = await eventFunctions.listEventCategories(user.$id, teamId);
      const categories = result.documents as unknown as EventCategory[];

      // Salvar no cache se habilitado
      if (isCacheEnabled() && categories.length > 0) {
        await setCache('eventCategories', categories, user.$id);
      }

      return categories;
    },
    enabled: !!user?.$id,
    staleTime: isCacheEnabled() ? 0 : 10 * 60 * 1000, // 10 minutos
    refetchOnWindowFocus: false,
  });
}

/**
 * Obter categoria por ID
 */
export function useEventCategory(categoryId: string) {
  const { addToCache } = useIndexedDB();

  return useQuery({
    queryKey: eventKeys.categories.detail(categoryId),
    queryFn: async () => {
      const result = await eventFunctions.getEventCategory(categoryId);
      const category = result as unknown as EventCategory;

      // Adicionar ao cache local
      if (isCacheEnabled()) {
        await addToCache('eventCategories', category);
      }

      return category;
    },
    enabled: !!categoryId,
    staleTime: 10 * 60 * 1000,
  });
}

/**
 * Buscar categorias padrão
 */
export function useDefaultEventCategories() {
  const { user } = useAuth();

  return useQuery({
    queryKey: eventKeys.categories.defaults(user?.$id || ''),
    queryFn: async () => {
      if (!user?.$id) throw new Error('User not authenticated');

      const result = await eventFunctions.getDefaultEventCategories(user.$id);
      return result.documents as unknown as EventCategory[];
    },
    enabled: !!user?.$id,
    staleTime: 30 * 60 * 1000, // 30 minutos para categorias padrão
  });
}

// ============================================================================
// EVENT CATEGORY MUTATIONS
// ============================================================================

/**
 * Criar categoria de evento
 */
export function useCreateEventCategory(options?: { onSuccess?: (category: EventCategory) => void }) {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const { addToCache } = useIndexedDB();

  return useMutation({
    mutationFn: async (data: EventCategoryFormData) => {
      if (!user?.$id) throw new Error('User not authenticated');

      const categoryData = {
        ...data,
        userId: user.$id,
      };

      const result = await eventFunctions.createEventCategory(categoryData);
      return result as unknown as EventCategory;
    },
    onSuccess: async (category) => {
      // Adicionar ao cache local
      if (isCacheEnabled()) {
        await addToCache('eventCategories', category);
      }

      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: eventKeys.categories.lists() });

      // Sync com cache
      await syncAfterMutation('eventCategories', 'create', category);

      toast.success('Categoria criada com sucesso!');
      options?.onSuccess?.(category);
    },
    onError: (error) => {
      console.error('Erro ao criar categoria:', error);
      toast.error('Erro ao criar categoria. Tente novamente.');
    },
  });
}

/**
 * Atualizar categoria de evento
 */
export function useUpdateEventCategory(options?: { onSuccess?: (category: EventCategory) => void }) {
  const queryClient = useQueryClient();
  const { updateInCache } = useIndexedDB();

  return useMutation({
    mutationFn: async ({ categoryId, data }: { categoryId: string; data: Partial<EventCategoryFormData> }) => {
      const result = await eventFunctions.updateEventCategory(categoryId, data);
      return result as unknown as EventCategory;
    },
    onSuccess: async (category) => {
      // Atualizar cache local
      if (isCacheEnabled()) {
        await updateInCache('eventCategories', category);
      }

      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: eventKeys.categories.detail(category.$id) });
      queryClient.invalidateQueries({ queryKey: eventKeys.categories.lists() });

      // Sync com cache
      await syncAfterMutation('eventCategories', 'update', category);

      toast.success('Categoria atualizada com sucesso!');
      options?.onSuccess?.(category);
    },
    onError: (error) => {
      console.error('Erro ao atualizar categoria:', error);
      toast.error('Erro ao atualizar categoria. Tente novamente.');
    },
  });
}

/**
 * Deletar categoria de evento
 */
export function useDeleteEventCategory(options?: { onSuccess?: () => void }) {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const { removeFromCache } = useIndexedDB();

  return useMutation({
    mutationFn: async (categoryId: string) => {
      await eventFunctions.deleteEventCategory(categoryId);
      return categoryId;
    },
    onSuccess: async (categoryId) => {
      // Remover do cache local
      if (isCacheEnabled()) {
        await removeFromCache('eventCategories', categoryId);
      }

      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: eventKeys.categories.detail(categoryId) });
      queryClient.invalidateQueries({ queryKey: eventKeys.categories.lists() });

      // Sync com cache
      await syncAfterMutation('eventCategories', 'delete', { $id: categoryId, $updatedAt: new Date().toISOString() });

      toast.success('Categoria deletada com sucesso!');
      options?.onSuccess?.();
    },
    onError: (error) => {
      console.error('Erro ao deletar categoria:', error);
      toast.error('Erro ao deletar categoria. Tente novamente.');
    },
  });
}
