/**
 * Tipos para sistema de chat
 */

import type { Models } from 'appwrite';

/**
 * RELAÇÃO CHAT-MENSAGENS:
 * - Cada mensagem DEVE pertencer a um chat (chatId obrigatório)
 * - Um chat pode ter múltiplas mensagens
 * - Quando um chat é deletado, todas as mensagens são deletadas automaticamente
 * - Mensagens não podem existir sem um chat pai
 */

// Tipos base para mensagens - SINCRONIZADO COM const.cjs
export interface ChatMessage extends Models.Document {
  // Atributos básicos da mensagem
  content: string; // Conteúdo da mensagem
  senderId: string; // ID do remetente
  senderName: string; // Nome do remetente
  senderAvatar?: string; // Avatar do remetente
  teamId: string; // ID do time (para compatibilidade)
  type: 'text' | 'image' | 'file' | 'system';
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';

  // Edição e resposta
  edited?: boolean; // Se a mensagem foi editada
  editedAt?: string; // Data da edição
  replyTo?: string; // ID da mensagem sendo respondida

  // Reações e menções
  reactions?: string[]; // Array de reações no formato "emoji:userId"
  mentions?: string[]; // Array de user IDs mencionados

  // Anexos e mídia (campos diretos do banco)
  fileName?: string;
  fileSize?: number;
  fileType?: string;
  imageUrl?: string;
  fileUrl?: string;
  attachments?: string[]; // Array de anexos

  // ⚠️ RELACIONAMENTOS: Estes campos são criados AUTOMATICAMENTE pelo Appwrite
  // chatId existe no objeto retornado mas NÃO é definido no const.cjs
  chatId?: string; // ID do chat pai (gerenciado pelo relacionamento)
  chat?: TeamChat; // Referência para o chat pai (populado via relacionamento)

}

// Chat de time com relacionamento one-to-many - SINCRONIZADO COM const.cjs
export interface TeamChat extends Models.Document {
  // Atributos básicos do chat
  teamId: string;
  name: string;
  description?: string;
  isPrivate: boolean;
  isActive: boolean;

  // Membros do chat
  members: string[]; // Array de user IDs

  // Atividade e estatísticas
  lastActivity?: string;
  unreadCount: number;

  // Configurações do chat (campos diretos do banco)
  allowFileSharing: boolean;
  allowReactions: boolean;
  retentionDays: number;

  // ⚠️ RELACIONAMENTOS: Estes campos são criados AUTOMATICAMENTE pelo Appwrite
  // NUNCA defina manualmente array de messageIds - ele é gerenciado pelo relacionamento
  messages?: ChatMessage[]; // Array de mensagens relacionadas (populado via relacionamento)

  // Campos adicionais não persistidos no banco (calculados)
  lastMessage?: ChatMessage; // Última mensagem (pode ser calculada)
}

// Chat com Gemini
export interface GeminiChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: string;
  status: 'sending' | 'sent' | 'error';
  metadata?: {
    model?: string;
    tokens?: number;
    processingTime?: number;
    context?: string;
  };
}

export interface GeminiChatSession {
  id: string;
  userId: string;
  title: string;
  messages: GeminiChatMessage[];
  context: string; // Contexto do projeto
  createdAt: string;
  updatedAt: string;
  settings: {
    model: 'gemini-1.5-flash' | 'gemini-1.5-pro';
    temperature: number;
    maxTokens: number;
    includeProjectContext: boolean;
  };
}

// Filtros e opções
export interface ChatFilters {
  teamId?: string;
  senderId?: string;
  type?: ChatMessage['type'];
  dateFrom?: string;
  dateTo?: string;
  hasAttachments?: boolean;
  search?: string;
}

export interface ChatSortOptions {
  field: 'createdAt' | 'updatedAt';
  direction: 'asc' | 'desc';
}

// Dados para criação - SINCRONIZADO COM const.cjs
export interface CreateChatMessageData {
  content: string; // Conteúdo da mensagem
  chatId: string; // OBRIGATÓRIO - ID do chat pai (para associar via relacionamento)
  teamId: string; // ID do time (obrigatório para compatibilidade)
  type?: ChatMessage['type'];
  replyTo?: string;

  // Anexos e mídia (opcionais)
  fileName?: string;
  fileSize?: number;
  fileType?: string;
  imageUrl?: string;
  fileUrl?: string;
  attachments?: string[];

  // Reações e menções (opcionais)
  reactions?: string[];
  mentions?: string[];

  // ⚠️ IMPORTANTE: chatId é especificado na criação para associar ao chat
  // mas o campo chatId NÃO existe no const.cjs porque é gerenciado pelo relacionamento
}

export interface CreateTeamChatData {
  teamId: string;
  name: string;
  description?: string;
  isPrivate?: boolean;

  // Configurações do chat (opcionais, com defaults no banco)
  allowFileSharing?: boolean;
  allowReactions?: boolean;
  retentionDays?: number;

  // ⚠️ RELACIONAMENTOS: members e messages NÃO são definidos aqui
  // members será automaticamente preenchido com o criador
  // messages será gerenciado pelo relacionamento
}

// Respostas da API - Novo padrão com relacionamento
export interface ChatResponse {
  chat: TeamChat; // Chat com mensagens incluídas via relacionamento
}

export interface TeamChatsResponse {
  chats: TeamChat[]; // Cada chat pode incluir mensagens via relacionamento
  total: number;
}

// Para compatibilidade com código existente
export interface ChatMessagesResponse {
  messages: ChatMessage[];
  total: number;
  hasMore: boolean;
}

// Estados do chat
export interface ChatState {
  isConnected: boolean;
  isTyping: boolean;
  typingUsers: Array<{
    userId: string;
    userName: string;
  }>;
  onlineUsers: Array<{
    userId: string;
    userName: string;
    lastSeen: string;
  }>;
}

// Eventos de WebSocket para chat
export interface ChatWebSocketEvent {
  type: 'message' | 'typing' | 'stop_typing' | 'user_joined' | 'user_left' | 'message_read';
  data: {
    teamId?: string;
    userId: string;
    userName: string;
    message?: ChatMessage;
    timestamp: string;
  };
}

// Configurações do chat
export interface ChatConfig {
  maxMessageLength: number;
  maxFileSize: number;
  allowedFileTypes: string[];
  retentionDays: number;
  enableReactions: boolean;
  enableFileSharing: boolean;
  enableVoiceMessages: boolean;
  autoDeleteAfterDays?: number;
}

// Estatísticas do chat
export interface ChatStats {
  totalMessages: number;
  totalUsers: number;
  messagesThisWeek: number;
  mostActiveUser: {
    userId: string;
    userName: string;
    messageCount: number;
  };
  averageResponseTime: number; // em minutos
  peakHours: Array<{
    hour: number;
    messageCount: number;
  }>;
}

// Notificações de chat
export interface ChatNotification {
  id: string;
  type: 'new_message' | 'mention' | 'team_invite';
  title: string;
  message: string;
  teamId?: string;
  senderId: string;
  senderName: string;
  read: boolean;
  createdAt: string;
  actionUrl?: string;
}

// Permissões de chat
export interface ChatPermissions {
  canSendMessages: boolean;
  canDeleteOwnMessages: boolean;
  canDeleteAnyMessage: boolean;
  canEditOwnMessages: boolean;
  canManageChat: boolean;
  canInviteUsers: boolean;
  canUploadFiles: boolean;
  canCreateChannels: boolean;
}

// Contexto do projeto para Gemini
export interface ProjectContext {
  name: string;
  description: string;
  technologies: string[];
  features: string[];
  currentPhase: string;
  goals: string[];
  constraints: string[];
  lastUpdated: string;
}
