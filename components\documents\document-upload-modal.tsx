import { useState, useCallback } from 'react';
import { IconUpload, IconX, IconFile, IconCheck, IconLoader } from '@tabler/icons-react';
import { Button } from '../ui/button';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogDescription 
} from '../ui/dialog';
import { Progress } from '../ui/progress';
import { Badge } from '../ui/badge';
import { useFileUpload } from '../../hooks/use-file-upload';
import { useUploadFile } from '../../hooks/api/use-storage';
import { 
  formatFileSize, 
  getFileTypeIcon, 
  getFileTypeColor,
  isAllowedFileType,
  isValidFileSize,
  DEFAULT_ALLOWED_TYPES,
  DEFAULT_MAX_FILE_SIZE
} from '../../lib/file-utils';
import { toast } from 'sonner';

interface DocumentUploadModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  allowedTypes?: string[];
  maxFileSize?: number;
  maxFiles?: number;
}

interface UploadingFile {
  id: string;
  file: File;
  status: 'pending' | 'uploading' | 'success' | 'error';
  progress: number;
  error?: string;
}

export function DocumentUploadModal({
  open,
  onOpenChange,
  allowedTypes = DEFAULT_ALLOWED_TYPES,
  maxFileSize = DEFAULT_MAX_FILE_SIZE,
  maxFiles = 10
}: DocumentUploadModalProps) {
  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  
  const uploadFileMutation = useUploadFile();

  const [{ files, isDragging, errors }, { addFiles, removeFile, clearFiles, clearErrors, handleDragEnter, handleDragLeave, handleDragOver, handleDrop, handleFileChange, openFileDialog, getInputProps }] = useFileUpload({
    maxFiles,
    maxSize: maxFileSize,
    multiple: true,
    accept: allowedTypes.join(','),
    onFilesAdded: (newFiles) => {
      // Validar arquivos adicionados
      const validFiles = newFiles.filter(fileWithPreview => {
        const file = fileWithPreview.file as File;
        
        if (!isAllowedFileType(file.type, allowedTypes)) {
          toast.error(`Tipo de arquivo não permitido: ${file.name}`);
          return false;
        }
        
        if (!isValidFileSize(file.size, maxFileSize)) {
          toast.error(`Arquivo muito grande: ${file.name} (máx. ${formatFileSize(maxFileSize)})`);
          return false;
        }
        
        return true;
      });

      // Adicionar à lista de upload
      const newUploadingFiles = validFiles.map(fileWithPreview => ({
        id: fileWithPreview.id,
        file: fileWithPreview.file as File,
        status: 'pending' as const,
        progress: 0
      }));

      setUploadingFiles(prev => [...prev, ...newUploadingFiles]);
    }
  });

  const handleUploadAll = async () => {
    if (uploadingFiles.length === 0) return;

    setIsUploading(true);
    
    const pendingFiles = uploadingFiles.filter(f => f.status === 'pending');
    
    for (const uploadingFile of pendingFiles) {
      try {
        // Atualizar status para uploading
        setUploadingFiles(prev => 
          prev.map(f => 
            f.id === uploadingFile.id 
              ? { ...f, status: 'uploading', progress: 0 }
              : f
          )
        );

        // Simular progresso (Appwrite não fornece progresso real)
        const progressInterval = setInterval(() => {
          setUploadingFiles(prev => 
            prev.map(f => 
              f.id === uploadingFile.id && f.status === 'uploading'
                ? { ...f, progress: Math.min(f.progress + 10, 90) }
                : f
            )
          );
        }, 100);

        // Fazer upload
        await uploadFileMutation.mutateAsync({ 
          file: uploadingFile.file 
        });

        // Limpar intervalo e marcar como sucesso
        clearInterval(progressInterval);
        setUploadingFiles(prev => 
          prev.map(f => 
            f.id === uploadingFile.id 
              ? { ...f, status: 'success', progress: 100 }
              : f
          )
        );

      } catch (error: any) {
        // Marcar como erro
        setUploadingFiles(prev => 
          prev.map(f => 
            f.id === uploadingFile.id 
              ? { 
                  ...f, 
                  status: 'error', 
                  progress: 0,
                  error: error.message || 'Erro no upload'
                }
              : f
          )
        );
      }
    }

    setIsUploading(false);
    
    // Verificar se todos foram enviados com sucesso
    const finalFiles = uploadingFiles.filter(f => f.status === 'success' || f.status === 'pending');
    if (finalFiles.length === uploadingFiles.length) {
      toast.success('Todos os arquivos foram enviados com sucesso!');
      handleClose();
    }
  };

  const handleRemoveFile = (fileId: string) => {
    setUploadingFiles(prev => prev.filter(f => f.id !== fileId));
    removeFile(fileId);
  };

  const handleClose = () => {
    if (isUploading) {
      if (confirm('Upload em andamento. Tem certeza que deseja fechar?')) {
        setIsUploading(false);
        setUploadingFiles([]);
        clearFiles();
        onOpenChange(false);
      }
    } else {
      setUploadingFiles([]);
      clearFiles();
      clearErrors();
      onOpenChange(false);
    }
  };

  const hasFiles = uploadingFiles.length > 0;
  const hasErrors = errors.length > 0;
  const successCount = uploadingFiles.filter(f => f.status === 'success').length;
  const errorCount = uploadingFiles.filter(f => f.status === 'error').length;
  const pendingCount = uploadingFiles.filter(f => f.status === 'pending').length;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>Enviar Arquivos</DialogTitle>
          <DialogDescription>
            Selecione ou arraste arquivos para enviar. 
            Máximo {maxFiles} arquivos, {formatFileSize(maxFileSize)} cada.
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          {/* Área de Drop */}
          {!hasFiles && (
            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                isDragging 
                  ? 'border-primary bg-primary/5' 
                  : 'border-muted-foreground/25 hover:border-primary/50'
              }`}
              onDragEnter={handleDragEnter}
              onDragLeave={handleDragLeave}
              onDragOver={handleDragOver}
              onDrop={handleDrop}
            >
              <IconUpload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">
                Arraste arquivos aqui ou clique para selecionar
              </h3>
              <p className="text-muted-foreground mb-4">
                Suporte para imagens, documentos, PDFs e mais
              </p>
              <Button onClick={openFileDialog}>
                Selecionar Arquivos
              </Button>
              <input {...getInputProps()} />
            </div>
          )}

          {/* Lista de Arquivos */}
          {hasFiles && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">
                  Arquivos Selecionados ({uploadingFiles.length})
                </h3>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={openFileDialog}
                  disabled={isUploading}
                >
                  Adicionar Mais
                </Button>
                <input {...getInputProps()} />
              </div>

              <div className="space-y-2 max-h-60 overflow-y-auto">
                {uploadingFiles.map((uploadingFile) => (
                  <FileUploadItem
                    key={uploadingFile.id}
                    uploadingFile={uploadingFile}
                    onRemove={handleRemoveFile}
                    disabled={isUploading}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Erros */}
          {hasErrors && (
            <div className="mt-4 p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
              <h4 className="font-medium text-destructive mb-2">Erros encontrados:</h4>
              <ul className="text-sm text-destructive space-y-1">
                {errors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={clearErrors}
                className="mt-2"
              >
                Limpar Erros
              </Button>
            </div>
          )}

          {/* Status do Upload */}
          {(successCount > 0 || errorCount > 0) && (
            <div className="mt-4 p-3 bg-muted rounded-lg">
              <div className="flex items-center gap-4 text-sm">
                {successCount > 0 && (
                  <div className="flex items-center gap-1 text-green-600">
                    <IconCheck className="h-4 w-4" />
                    {successCount} enviado{successCount > 1 ? 's' : ''}
                  </div>
                )}
                {errorCount > 0 && (
                  <div className="flex items-center gap-1 text-red-600">
                    <IconX className="h-4 w-4" />
                    {errorCount} erro{errorCount > 1 ? 's' : ''}
                  </div>
                )}
                {pendingCount > 0 && (
                  <div className="flex items-center gap-1 text-muted-foreground">
                    <IconLoader className="h-4 w-4" />
                    {pendingCount} pendente{pendingCount > 1 ? 's' : ''}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="text-sm text-muted-foreground">
            {hasFiles && `${uploadingFiles.length} arquivo${uploadingFiles.length > 1 ? 's' : ''} selecionado${uploadingFiles.length > 1 ? 's' : ''}`}
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleClose} disabled={isUploading}>
              {isUploading ? 'Cancelar' : 'Fechar'}
            </Button>
            {hasFiles && pendingCount > 0 && (
              <Button 
                onClick={handleUploadAll} 
                disabled={isUploading || pendingCount === 0}
              >
                {isUploading ? (
                  <>
                    <IconLoader className="mr-2 h-4 w-4 animate-spin" />
                    Enviando...
                  </>
                ) : (
                  <>
                    <IconUpload className="mr-2 h-4 w-4" />
                    Enviar Todos
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Componente para item individual de upload
function FileUploadItem({ 
  uploadingFile, 
  onRemove, 
  disabled 
}: { 
  uploadingFile: UploadingFile;
  onRemove: (id: string) => void;
  disabled: boolean;
}) {
  const FileIcon = getFileTypeIcon(uploadingFile.file.type);
  const fileColor = getFileTypeColor(uploadingFile.file.type);

  return (
    <div className="flex items-center gap-3 p-3 border rounded-lg">
      <FileIcon className={`h-8 w-8 ${fileColor} flex-shrink-0`} />
      
      <div className="flex-1 min-w-0">
        <p className="font-medium truncate">{uploadingFile.file.name}</p>
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <span>{formatFileSize(uploadingFile.file.size)}</span>
          <Badge variant="outline" className="text-xs">
            {uploadingFile.file.type.split('/')[1]?.toUpperCase() || 'FILE'}
          </Badge>
        </div>
        
        {/* Barra de Progresso */}
        {uploadingFile.status === 'uploading' && (
          <Progress value={uploadingFile.progress} className="mt-2 h-1" />
        )}
        
        {/* Erro */}
        {uploadingFile.status === 'error' && uploadingFile.error && (
          <p className="text-xs text-destructive mt-1">{uploadingFile.error}</p>
        )}
      </div>

      {/* Status */}
      <div className="flex items-center gap-2">
        {uploadingFile.status === 'pending' && (
          <Badge variant="secondary">Pendente</Badge>
        )}
        {uploadingFile.status === 'uploading' && (
          <Badge variant="secondary">
            <IconLoader className="mr-1 h-3 w-3 animate-spin" />
            Enviando
          </Badge>
        )}
        {uploadingFile.status === 'success' && (
          <Badge variant="default" className="bg-green-500">
            <IconCheck className="mr-1 h-3 w-3" />
            Sucesso
          </Badge>
        )}
        {uploadingFile.status === 'error' && (
          <Badge variant="destructive">
            <IconX className="mr-1 h-3 w-3" />
            Erro
          </Badge>
        )}

        {/* Botão Remover */}
        <Button
          size="sm"
          variant="ghost"
          onClick={() => onRemove(uploadingFile.id)}
          disabled={disabled && uploadingFile.status === 'uploading'}
        >
          <IconX className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
