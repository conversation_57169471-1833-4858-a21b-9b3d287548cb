/**
 * KanbanLabel Component
 * Displays colored labels for tasks (similar to Trello labels)
 */

import React from 'react';
import { Badge } from '../ui/badge';
import { cn } from '../../lib/utils';
import type { Label, LabelColor } from '@/schemas/kanban';

interface KanbanLabelProps {
  label: Label;
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
  className?: string;
  onClick?: () => void;
}

// Cores das labels baseadas no Trello
const labelColors: Record<LabelColor, { bg: string; text: string; border: string }> = {
  red: {
    bg: 'bg-red-500 hover:bg-red-600',
    text: 'text-white',
    border: 'border-red-500',
  },
  orange: {
    bg: 'bg-orange-500 hover:bg-orange-600',
    text: 'text-white',
    border: 'border-orange-500',
  },
  yellow: {
    bg: 'bg-yellow-500 hover:bg-yellow-600',
    text: 'text-black',
    border: 'border-yellow-500',
  },
  green: {
    bg: 'bg-green-500 hover:bg-green-600',
    text: 'text-white',
    border: 'border-green-500',
  },
  blue: {
    bg: 'bg-blue-500 hover:bg-blue-600',
    text: 'text-white',
    border: 'border-blue-500',
  },
  purple: {
    bg: 'bg-purple-500 hover:bg-purple-600',
    text: 'text-white',
    border: 'border-purple-500',
  },
  pink: {
    bg: 'bg-pink-500 hover:bg-pink-600',
    text: 'text-white',
    border: 'border-pink-500',
  },
  gray: {
    bg: 'bg-gray-500 hover:bg-gray-600',
    text: 'text-white',
    border: 'border-gray-500',
  },
};

const sizeClasses = {
  sm: 'h-2 min-w-8 text-xs px-1',
  md: 'h-3 min-w-12 text-xs px-2',
  lg: 'h-4 min-w-16 text-sm px-2',
};

export function KanbanLabel({
  label,
  size = 'md',
  showText = true,
  className,
  onClick
}: KanbanLabelProps) {
  const colorConfig = labelColors[label.color];

  if (!showText) {
    // Apenas a cor, sem texto (para visualização compacta)
    return (
      <div
        className={cn(
          'rounded-sm cursor-pointer transition-colors',
          colorConfig.bg,
          sizeClasses[size],
          className
        )}
        onClick={onClick}
        title={label.name}
      />
    );
  }

  return (
    <Badge
      variant="secondary"
      className={cn(
        'rounded-sm cursor-pointer transition-colors border',
        colorConfig.bg,
        colorConfig.text,
        colorConfig.border,
        sizeClasses[size],
        className
      )}
      onClick={onClick}
    >
      {label.name}
    </Badge>
  );
}

// Componente para exibir múltiplas labels
interface KanbanLabelsProps {
  labels: Label[];
  maxVisible?: number;
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
  onLabelClick?: (label: Label) => void;
  className?: string;
}

export function KanbanLabels({
  labels,
  maxVisible = 3,
  size = 'md',
  showText = true,
  onLabelClick,
  className
}: KanbanLabelsProps) {
  const visibleLabels = labels.slice(0, maxVisible);
  const hiddenCount = labels.length - maxVisible;

  return (
    <div className={cn('flex flex-wrap gap-1', className)}>
      {visibleLabels.map((label) => (
        <KanbanLabel
          key={label.id}
          label={label}
          size={size}
          showText={showText}
          onClick={() => onLabelClick?.(label)}
        />
      ))}

      {hiddenCount > 0 && (
        <Badge
          variant="outline"
          className={cn(
            'rounded-sm text-muted-foreground',
            sizeClasses[size]
          )}
        >
          +{hiddenCount}
        </Badge>
      )}
    </div>
  );
}

// Componente para seletor de cor de label
interface LabelColorPickerProps {
  selectedColor?: LabelColor;
  onColorSelect: (color: LabelColor) => void;
  className?: string;
}

export function LabelColorPicker({ selectedColor, onColorSelect, className }: LabelColorPickerProps) {
  const colors = Object.keys(labelColors) as LabelColor[];

  return (
    <div className={cn('grid grid-cols-4 gap-2', className)}>
      {colors.map((color) => {
        const colorConfig = labelColors[color];
        const isSelected = selectedColor === color;

        return (
          <button
            key={color}
            type="button"
            className={cn(
              'w-8 h-6 rounded-sm border-2 transition-all',
              colorConfig.bg.split(' ')[0], // Remove hover classes
              isSelected ? 'border-foreground scale-110' : 'border-transparent hover:border-muted-foreground'
            )}
            onClick={() => onColorSelect(color)}
            title={color}
          />
        );
      })}
    </div>
  );
}

// Utilitário para obter a configuração de cor
export function getLabelColorConfig(color: LabelColor) {
  return labelColors[color];
}
