/**
 * Enhanced export configuration hook
 * Manages export settings, validation, and preview generation
 */

import { useState, useCallback, useMemo } from 'react';
import { toast } from 'sonner';
import {
  type ExportConfig,
  type ExportPreview,
  type ExportField,
  type ExportFormat,
  exportConfigSchema,
  getFieldsForCollection,
  getDefaultFieldsForFormat,
  validatePDFExport,
  EXPORT_FORMATS,
  PDF_LIMITATIONS,
} from '../schemas/export';

interface UseExportConfigOptions {
  collection: string;
  totalRecords: number;
  data?: Record<string, any>[];
}

export function useExportConfig({ collection, totalRecords, data = [] }: UseExportConfigOptions) {
  // Get available fields for the collection
  const availableFields = useMemo(() => getFieldsForCollection(collection), [collection]);

  // Initialize default configuration
  const [config, setConfig] = useState<ExportConfig>(() => {
    const defaultConfig = exportConfigSchema.parse({
      format: 'csv',
      includeAllFields: true,
      selectedFields: getDefaultFieldsForFormat(availableFields, 'csv'),
      pdfSettings: {
        maxRows: PDF_LIMITATIONS.DEFAULT_MAX_ROWS,
        maxColumns: PDF_LIMITATIONS.DEFAULT_MAX_COLUMNS,
        enablePagination: true,
        pageSize: PDF_LIMITATIONS.RECOMMENDED_ROWS,
        orientation: 'portrait',
        fontSize: 'medium',
      },
    });
    return defaultConfig;
  });

  // Update format and adjust settings accordingly
  const updateFormat = useCallback((format: ExportFormat) => {
    setConfig(prev => {
      const newSelectedFields = prev.includeAllFields
        ? getDefaultFieldsForFormat(availableFields, format)
        : prev.selectedFields;

      return {
        ...prev,
        format,
        selectedFields: newSelectedFields,
      };
    });
  }, [availableFields]);

  // Update field selection
  const updateSelectedFields = useCallback((fields: string[]) => {
    setConfig(prev => ({
      ...prev,
      selectedFields: fields,
      includeAllFields: fields.length === availableFields.length,
    }));
  }, [availableFields.length]);

  // Toggle field selection
  const toggleField = useCallback((fieldId: string) => {
    setConfig(prev => {
      const isSelected = prev.selectedFields.includes(fieldId);
      const newFields = isSelected
        ? prev.selectedFields.filter(id => id !== fieldId)
        : [...prev.selectedFields, fieldId];

      return {
        ...prev,
        selectedFields: newFields,
        includeAllFields: newFields.length === availableFields.length,
      };
    });
  }, [availableFields.length]);

  // Toggle include all fields
  const toggleIncludeAllFields = useCallback(() => {
    setConfig(prev => {
      const newIncludeAll = !prev.includeAllFields;
      return {
        ...prev,
        includeAllFields: newIncludeAll,
        selectedFields: newIncludeAll
          ? getDefaultFieldsForFormat(availableFields, prev.format)
          : availableFields.filter(field => field.required).map(field => field.id),
      };
    });
  }, [availableFields]);

  // Update PDF settings
  const updatePDFSettings = useCallback((settings: Partial<ExportConfig['pdfSettings']>) => {
    setConfig(prev => ({
      ...prev,
      pdfSettings: {
        ...prev.pdfSettings,
        ...settings,
      } as any,
    }));
  }, []);

  // Update other settings
  const updateSettings = useCallback((settings: Partial<ExportConfig>) => {
    setConfig(prev => ({
      ...prev,
      ...settings,
    }));
  }, []);

  // Generate export preview
  const generatePreview = useCallback((): ExportPreview => {
    const formatInfo = EXPORT_FORMATS.find(f => f.value === config.format);
    const pdfSettings = config.pdfSettings;

    let recordsToExport = totalRecords;
    let fieldsToExport = config.selectedFields;
    const warnings: string[] = [];

    // Apply format-specific limitations
    if (config.format === 'pdf' && pdfSettings) {
      // Limit records for PDF
      if (totalRecords > pdfSettings.maxRows) {
        recordsToExport = pdfSettings.maxRows;
        warnings.push(`PDF será limitado a ${pdfSettings.maxRows} registros de ${totalRecords} disponíveis`);
      }

      // Limit columns for PDF
      const pdfSupportedFields = fieldsToExport.filter(fieldId => {
        const field = availableFields.find(f => f.id === fieldId);
        return field?.pdfSupported !== false;
      });

      if (pdfSupportedFields.length > pdfSettings.maxColumns) {
        fieldsToExport = pdfSupportedFields.slice(0, pdfSettings.maxColumns);
        warnings.push(`PDF será limitado a ${pdfSettings.maxColumns} colunas de ${pdfSupportedFields.length} suportadas`);
      } else {
        fieldsToExport = pdfSupportedFields;
      }

      // Check for unsupported fields
      const unsupportedFields = config.selectedFields.filter(fieldId => {
        const field = availableFields.find(f => f.id === fieldId);
        return field?.pdfSupported === false;
      });

      if (unsupportedFields.length > 0) {
        const fieldLabels = unsupportedFields.map(fieldId => {
          const field = availableFields.find(f => f.id === fieldId);
          return field?.label || fieldId;
        }).join(', ');
        warnings.push(`Campos não suportados em PDF foram removidos: ${fieldLabels}`);
      }
    }

    // Estimate file size
    const avgRecordSize = config.format === 'pdf' ? 50 : 100; // bytes per record
    const estimatedBytes = recordsToExport * fieldsToExport.length * avgRecordSize;
    const estimatedFileSize = formatFileSize(estimatedBytes);

    // Generate suggestions for PDF limitations
    const limitations = config.format === 'pdf' ? {
      pdfRowLimit: totalRecords > (pdfSettings?.maxRows || PDF_LIMITATIONS.DEFAULT_MAX_ROWS),
      pdfColumnLimit: config.selectedFields.length > (pdfSettings?.maxColumns || PDF_LIMITATIONS.DEFAULT_MAX_COLUMNS),
      suggestedAlternatives: (totalRecords > (pdfSettings?.maxRows || PDF_LIMITATIONS.DEFAULT_MAX_ROWS) ||
                             config.selectedFields.length > (pdfSettings?.maxColumns || PDF_LIMITATIONS.DEFAULT_MAX_COLUMNS))
        ? ['excel', 'csv'] as ExportFormat[]
        : [],
    } : undefined;

    return {
      totalRecords,
      recordsToExport,
      fieldsToExport,
      estimatedFileSize,
      warnings,
      limitations,
    };
  }, [config, totalRecords, availableFields]);

  // Validate configuration
  const validateConfig = useCallback((): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    try {
      exportConfigSchema.parse(config);
    } catch (error) {
      errors.push('Configuração inválida');
    }

    if (config.selectedFields.length === 0) {
      errors.push('Selecione pelo menos um campo para exportar');
    }

    if (config.format === 'pdf') {
      const pdfWarnings = validatePDFExport(config, totalRecords);
      // PDF warnings are not errors, just warnings
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }, [config, totalRecords]);

  // Get fields filtered by PDF support
  const getFilteredFields = useCallback((pdfOnly: boolean = false): ExportField[] => {
    if (!pdfOnly) return availableFields;
    return availableFields.filter(field => field.pdfSupported !== false);
  }, [availableFields]);

  // Get format information
  const getFormatInfo = useCallback((format?: ExportFormat) => {
    return EXPORT_FORMATS.find(f => f.value === (format || config.format));
  }, [config.format]);

  return {
    // State
    config,
    availableFields,

    // Actions
    updateFormat,
    updateSelectedFields,
    toggleField,
    toggleIncludeAllFields,
    updatePDFSettings,
    updateSettings,

    // Computed
    generatePreview,
    validateConfig,
    getFilteredFields,
    getFormatInfo,

    // Helpers
    isFieldSelected: (fieldId: string) => config.selectedFields.includes(fieldId),
    isFieldRequired: (fieldId: string) => {
      const field = availableFields.find(f => f.id === fieldId);
      return field?.required || false;
    },
    isFieldPDFSupported: (fieldId: string) => {
      const field = availableFields.find(f => f.id === fieldId);
      return field?.pdfSupported !== false;
    },
  };
}

// Helper function to format file size
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
