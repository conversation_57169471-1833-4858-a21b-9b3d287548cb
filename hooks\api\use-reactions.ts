/**
 * Hooks para gerenciar reações de mensagens
 */

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useAuth } from '../use-auth';
import { chatMessages } from '../../lib/appwrite/functions/database';
import { addReaction, removeReaction, hasUserReacted } from '../../lib/chat-utils';
import { isCacheEnabled } from '../../lib/cache-config';
import { saveToIndexedDB } from '../../lib/cache-sync';
import type { ChatMessage } from '@/schemas/chat';

/**
 * Hook para adicionar reação a uma mensagem
 */
export function useAddReaction() {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async ({ 
      messageId, 
      chatId, 
      emoji 
    }: { 
      messageId: string; 
      chatId: string; 
      emoji: string; 
    }) => {
      if (!user) throw new Error('Usuário não autenticado');

      // Buscar mensagem atual
      const currentMessage = await chatMessages.get(messageId) as ChatMessage;
      const currentReactions = currentMessage.reactions || [];

      // Verificar se usuário já reagiu com este emoji
      if (hasUserReacted(currentReactions, emoji, user.$id)) {
        throw new Error('Você já reagiu com este emoji');
      }

      // Adicionar nova reação
      const updatedReactions = addReaction(currentReactions, emoji, user.$id);

      // Atualizar no banco
      const result = await chatMessages.update(messageId, {
        reactions: updatedReactions,
      });

      return { result, chatId, messageId };
    },

    onMutate: async ({ messageId, chatId, emoji }) => {
      if (!user) return;

      // Cancelar queries em andamento
      await queryClient.cancelQueries({ queryKey: ['messages', chatId, user.$id] });

      // Snapshot do estado anterior
      const previousMessages = queryClient.getQueryData<ChatMessage[]>(['messages', chatId, user.$id]);

      // Atualização otimista
      if (previousMessages) {
        const updatedMessages = previousMessages.map(msg => {
          if (msg.$id === messageId) {
            const currentReactions = msg.reactions || [];
            const updatedReactions = addReaction(currentReactions, emoji, user.$id);
            return { ...msg, reactions: updatedReactions };
          }
          return msg;
        });

        queryClient.setQueryData<ChatMessage[]>(['messages', chatId, user.$id], updatedMessages);
      }

      return { previousMessages };
    },

    onSuccess: async ({ result, chatId, messageId }, variables, context) => {
      // Atualizar com dados reais do servidor
      const currentMessages = queryClient.getQueryData<ChatMessage[]>(['messages', chatId, user?.$id]);
      if (currentMessages) {
        const updatedMessages = currentMessages.map(msg =>
          msg.$id === messageId ? { ...result as ChatMessage } : msg
        );

        queryClient.setQueryData<ChatMessage[]>(['messages', chatId, user?.$id], updatedMessages);

        // Sincronizar com IndexedDB
        if (isCacheEnabled() && user?.$id) {
          await saveToIndexedDB(`messages_${chatId}`, updatedMessages, {
            collection: `messages_${chatId}`,
            userId: user.$id
          });
        }
      }
    },

    onError: (error, variables, context) => {
      // Reverter mudanças otimistas
      if (context?.previousMessages && user) {
        queryClient.setQueryData<ChatMessage[]>(
          ['messages', variables.chatId, user.$id], 
          context.previousMessages
        );
      }
      
      toast.error('Erro ao adicionar reação');
      console.error('Erro ao adicionar reação:', error);
    },
  });
}

/**
 * Hook para remover reação de uma mensagem
 */
export function useRemoveReaction() {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async ({ 
      messageId, 
      chatId, 
      emoji 
    }: { 
      messageId: string; 
      chatId: string; 
      emoji: string; 
    }) => {
      if (!user) throw new Error('Usuário não autenticado');

      // Buscar mensagem atual
      const currentMessage = await chatMessages.get(messageId) as ChatMessage;
      const currentReactions = currentMessage.reactions || [];

      // Verificar se usuário reagiu com este emoji
      if (!hasUserReacted(currentReactions, emoji, user.$id)) {
        throw new Error('Você não reagiu com este emoji');
      }

      // Remover reação
      const updatedReactions = removeReaction(currentReactions, emoji, user.$id);

      // Atualizar no banco
      const result = await chatMessages.update(messageId, {
        reactions: updatedReactions,
      });

      return { result, chatId, messageId };
    },

    onMutate: async ({ messageId, chatId, emoji }) => {
      if (!user) return;

      // Cancelar queries em andamento
      await queryClient.cancelQueries({ queryKey: ['messages', chatId, user.$id] });

      // Snapshot do estado anterior
      const previousMessages = queryClient.getQueryData<ChatMessage[]>(['messages', chatId, user.$id]);

      // Atualização otimista
      if (previousMessages) {
        const updatedMessages = previousMessages.map(msg => {
          if (msg.$id === messageId) {
            const currentReactions = msg.reactions || [];
            const updatedReactions = removeReaction(currentReactions, emoji, user.$id);
            return { ...msg, reactions: updatedReactions };
          }
          return msg;
        });

        queryClient.setQueryData<ChatMessage[]>(['messages', chatId, user.$id], updatedMessages);
      }

      return { previousMessages };
    },

    onSuccess: async ({ result, chatId, messageId }, variables, context) => {
      // Atualizar com dados reais do servidor
      const currentMessages = queryClient.getQueryData<ChatMessage[]>(['messages', chatId, user?.$id]);
      if (currentMessages) {
        const updatedMessages = currentMessages.map(msg =>
          msg.$id === messageId ? { ...result as ChatMessage } : msg
        );

        queryClient.setQueryData<ChatMessage[]>(['messages', chatId, user?.$id], updatedMessages);

        // Sincronizar com IndexedDB
        if (isCacheEnabled() && user?.$id) {
          await saveToIndexedDB(`messages_${chatId}`, updatedMessages, {
            collection: `messages_${chatId}`,
            userId: user.$id
          });
        }
      }
    },

    onError: (error, variables, context) => {
      // Reverter mudanças otimistas
      if (context?.previousMessages && user) {
        queryClient.setQueryData<ChatMessage[]>(
          ['messages', variables.chatId, user.$id], 
          context.previousMessages
        );
      }
      
      toast.error('Erro ao remover reação');
      console.error('Erro ao remover reação:', error);
    },
  });
}

/**
 * Hook para alternar reação (adicionar se não existe, remover se existe)
 */
export function useToggleReaction() {
  const addReaction = useAddReaction();
  const removeReaction = useRemoveReaction();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async ({ 
      messageId, 
      chatId, 
      emoji, 
      currentReactions = [] 
    }: { 
      messageId: string; 
      chatId: string; 
      emoji: string; 
      currentReactions?: string[];
    }) => {
      if (!user) throw new Error('Usuário não autenticado');

      const hasReacted = hasUserReacted(currentReactions, emoji, user.$id);

      if (hasReacted) {
        return removeReaction.mutateAsync({ messageId, chatId, emoji });
      } else {
        return addReaction.mutateAsync({ messageId, chatId, emoji });
      }
    },

    onError: (error) => {
      console.error('Erro ao alternar reação:', error);
    },
  });
}
