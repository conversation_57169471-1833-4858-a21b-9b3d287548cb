/**
 * Exemplo de uso do sistema de chat com Gemini AI
 * Demonstra diferentes formas de integrar o chat no projeto
 */

import React, { useState } from 'react';
import { Bo<PERSON>, MessageSquare, Settings, TestTube } from 'lucide-react';
import { But<PERSON> } from '../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { useGeminiChat } from '../../hooks/use-gemini-chat';
import { sendMessageToGemini, isGeminiConfigured, testGeminiConnection, getGeminiConfig } from '../../lib/gemini/chat';
import { generateFullContext, generateSummaryContext, detectQuestionType } from '../../lib/gemini/context';
import { toast } from 'sonner';

export function GeminiChatExample() {
  const [testMessage, setTestMessage] = useState('Como implementar autenticação com Appwrite?');
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'unknown' | 'success' | 'error'>('unknown');

  const {
    currentSession,
    sessions,
    isLoading,
    isGeminiReady,
    sendMessage,
    createSession,
    selectSession,
    deleteSession,
    clearCurrentSession,
    isError,
    error,
  } = useGeminiChat();

  // Testar conexão com Gemini
  const handleTestConnection = async () => {
    setIsTestingConnection(true);
    try {
      const success = await testGeminiConnection();
      setConnectionStatus(success ? 'success' : 'error');
      toast.success(success ? 'Conexão com Gemini OK!' : 'Falha na conexão com Gemini');
    } catch (error) {
      setConnectionStatus('error');
      toast.error('Erro ao testar conexão');
    } finally {
      setIsTestingConnection(false);
    }
  };

  // Enviar mensagem de teste
  const handleTestMessage = async () => {
    if (!testMessage.trim()) return;
    
    try {
      await sendMessage(testMessage);
      toast.success('Mensagem enviada!');
    } catch (error) {
      toast.error('Erro ao enviar mensagem');
    }
  };

  // Testar API diretamente
  const handleDirectAPITest = async () => {
    try {
      const response = await sendMessageToGemini(
        'Explique brevemente o que é React Router v7',
        {
          includeContext: false,
          model: 'gemini-1.5-flash',
          temperature: 0.7,
          maxTokens: 200,
        }
      );
      
      toast.success(`Resposta: ${response.content.substring(0, 100)}...`);
    } catch (error) {
      toast.error('Erro na API direta');
    }
  };

  const config = getGeminiConfig();

  return (
    <div className="space-y-6">
      {/* Status e Configuração */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Status do Gemini AI
          </CardTitle>
          <CardDescription>
            Configuração e status da integração com Google Gemini
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>Status da Configuração</Label>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant={isGeminiReady ? 'default' : 'destructive'}>
                  {isGeminiReady ? 'Configurado' : 'Não Configurado'}
                </Badge>
                {isGeminiReady && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleTestConnection}
                    disabled={isTestingConnection}
                  >
                    <TestTube className="h-4 w-4 mr-1" />
                    {isTestingConnection ? 'Testando...' : 'Testar Conexão'}
                  </Button>
                )}
              </div>
            </div>

            <div>
              <Label>Status da Conexão</Label>
              <div className="mt-1">
                <Badge 
                  variant={
                    connectionStatus === 'success' ? 'default' : 
                    connectionStatus === 'error' ? 'destructive' : 
                    'secondary'
                  }
                >
                  {connectionStatus === 'success' ? 'Conectado' : 
                   connectionStatus === 'error' ? 'Erro' : 
                   'Não testado'}
                </Badge>
              </div>
            </div>
          </div>

          <Separator />

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <Label>API Key</Label>
              <p className="text-muted-foreground">{config.apiKey}</p>
            </div>
            <div>
              <Label>Modelo</Label>
              <p className="text-muted-foreground">{config.model}</p>
            </div>
            <div>
              <Label>Temperatura</Label>
              <p className="text-muted-foreground">{config.temperature}</p>
            </div>
            <div>
              <Label>Max Tokens</Label>
              <p className="text-muted-foreground">{config.maxTokens}</p>
            </div>
          </div>

          {!isGeminiReady && (
            <div className="p-4 bg-muted rounded-lg">
              <p className="text-sm font-medium mb-2">⚠️ Configuração Necessária</p>
              <p className="text-sm text-muted-foreground mb-2">
                Para usar o Gemini AI, adicione sua chave da API no arquivo .env:
              </p>
              <code className="block p-2 bg-background rounded text-xs">
                NEXT_PUBLIC_GEMINI_API_KEY=sua-chave-do-google-ai-aqui
              </code>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Teste de Mensagem */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Teste de Mensagem
          </CardTitle>
          <CardDescription>
            Teste o envio de mensagens usando o hook useGeminiChat
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="test-message">Mensagem de Teste</Label>
            <Input
              id="test-message"
              value={testMessage}
              onChange={(e) => setTestMessage(e.target.value)}
              placeholder="Digite uma pergunta sobre o projeto..."
              className="mt-1"
            />
          </div>

          <div className="flex gap-2">
            <Button
              onClick={handleTestMessage}
              disabled={!isGeminiReady || isLoading || !testMessage.trim()}
            >
              {isLoading ? 'Enviando...' : 'Enviar via Hook'}
            </Button>
            
            <Button
              variant="outline"
              onClick={handleDirectAPITest}
              disabled={!isGeminiReady}
            >
              Testar API Direta
            </Button>
          </div>

          {isError && error && (
            <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
              <p className="text-sm text-destructive">
                Erro: {error.message}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Sessões de Chat */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5" />
            Sessões de Chat
          </CardTitle>
          <CardDescription>
            Gerenciamento de sessões de conversa com o Gemini
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <p className="text-sm text-muted-foreground">
              {sessions.length} sessão(ões) ativa(s)
            </p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => createSession()}
              disabled={!isGeminiReady}
            >
              Nova Sessão
            </Button>
          </div>

          {sessions.length > 0 && (
            <div className="space-y-2">
              {sessions.map((session) => (
                <div
                  key={session.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    currentSession?.id === session.id
                      ? 'border-primary bg-primary/5'
                      : 'border-border hover:bg-muted/50'
                  }`}
                  onClick={() => selectSession(session.id)}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-sm">{session.title}</p>
                      <p className="text-xs text-muted-foreground">
                        {session.messages.length} mensagens • {session.settings.model}
                      </p>
                    </div>
                    <div className="flex gap-1">
                      {currentSession?.id === session.id && (
                        <Badge variant="default" className="text-xs">
                          Ativa
                        </Badge>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          deleteSession(session.id);
                        }}
                      >
                        ×
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {currentSession && (
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={clearCurrentSession}
                disabled={currentSession.messages.length === 0}
              >
                Limpar Conversa
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Informações de Contexto */}
      <Card>
        <CardHeader>
          <CardTitle>Sistema de Contexto</CardTitle>
          <CardDescription>
            Informações sobre como o contexto do projeto é gerado
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label>Contexto Completo</Label>
              <p className="text-sm text-muted-foreground">
                Usado para perguntas técnicas e de implementação
              </p>
            </div>
            <div>
              <Label>Contexto Resumido</Label>
              <p className="text-sm text-muted-foreground">
                Usado para perguntas gerais e simples
              </p>
            </div>
          </div>

          <Separator />

          <div>
            <Label>Detecção de Tipo de Pergunta</Label>
            <div className="mt-2 space-y-1">
              {[
                { type: 'code', example: 'Como implementar...', context: 'Contexto completo' },
                { type: 'architecture', example: 'Qual a estrutura...', context: 'Contexto completo' },
                { type: 'feature', example: 'Como adicionar...', context: 'Contexto completo' },
                { type: 'bug', example: 'Erro ao...', context: 'Contexto de página' },
                { type: 'general', example: 'O que é...', context: 'Contexto resumido' },
              ].map((item) => (
                <div key={item.type} className="flex justify-between text-xs">
                  <span className="text-muted-foreground">{item.example}</span>
                  <Badge variant="outline" className="text-xs">
                    {item.context}
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
