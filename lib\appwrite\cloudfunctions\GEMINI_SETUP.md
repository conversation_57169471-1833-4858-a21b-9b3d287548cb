# 🤖 Gemini File Processor - Guia de Configuração

Cloud function para processamento de arquivos de clientes usando Google Gemini AI. Extrai dados estruturados de documentos, imagens e PDFs de forma automatizada.

## ✨ **Características**

- 🔍 **Extração Inteligente** - Dados estruturados de documentos
- 💰 **Modelo Econômico** - Usa gemini-1.5-flash (mais barato)
- 📄 **Múltiplos Formatos** - JPEG, PNG, WebP, PDF
- 🎯 **Prompts Especializados** - Para diferentes tipos de documentos
- ✅ **Validação Automática** - Dados extraídos são validados
- 🔒 **Seguro** - Apenas usuários autenticados

## 🚀 **Configuração Rápida**

### 1. Obter API Key do Google AI

1. Acesse [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Faça login com sua conta Google
3. Clique em "Create API Key"
4. Copie a chave gerada

### 2. Configurar Variáveis de Ambiente

No console do Appwrite, configure as seguintes variáveis para a função `gemini-file-processor`:

```env
GEMINI_API_KEY=your-gemini-api-key-here
GEMINI_MODEL=gemini-1.5-flash
MAX_FILE_SIZE=10485760
ALLOWED_MIME_TYPES=image/jpeg,image/png,image/webp,application/pdf
```

### 3. Deploy da Função

```bash
# Na raiz do projeto
appwrite deploy function --functionId gemini-file-processor
```

### 4. Configurar URL no Frontend

No arquivo `.env` do seu projeto:

```env
NEXT_PUBLIC_CLOUDFUNCTION_GEMINI_FILE_PROCESSOR=https://cloud.appwrite.io/v1/functions/gemini-file-processor/executions
```

## 📋 **Tipos de Processamento**

### Documentos de Identidade (`identity_document`)
- RG, CPF, CNH, Passaporte
- Extrai: nome, documento, data nascimento, órgão emissor

### Comprovantes de Endereço (`address_proof`)
- Contas de luz, água, telefone
- Extrai: nome, endereço completo, data emissão

### Dados de Cliente (`client_data`)
- Formulários, cadastros, contratos
- Extrai: nome, email, telefone, endereço, empresa

### Faturas/Notas Fiscais (`invoice_data`)
- Notas fiscais, faturas, recibos
- Extrai: número, data, valor, itens, cliente/fornecedor

### Contratos (`contract`)
- Contratos de serviço, locação, etc.
- Extrai: partes, objeto, valor, datas, cláusulas

### Extratos Bancários (`bank_statement`)
- Extratos de conta corrente/poupança
- Extrai: titular, período, saldo, transações

## 🔧 **Uso no Frontend**

### Hook Básico

```typescript
import { useGeminiProcessor, PROCESSING_TYPES } from '@/hooks/use-gemini-processor';

function DocumentUpload() {
  const processor = useGeminiProcessor();

  const handleFileUpload = async (file: File) => {
    const result = await processor.processFile(file, {
      processingType: PROCESSING_TYPES.CLIENT_DATA
    });
    
    if (result) {
      console.log('Dados extraídos:', result.processed_data);
    }
  };

  return (
    <div>
      <input 
        type="file" 
        accept=".jpg,.png,.pdf"
        onChange={(e) => handleFileUpload(e.target.files[0])}
      />
      {processor.isLoading && <p>Processando...</p>}
    </div>
  );
}
```

### Hook Simplificado

```typescript
import { useQuickGeminiProcessor } from '@/hooks/use-gemini-processor';

function QuickProcessor() {
  const { quickProcess, isLoading, processedData } = useQuickGeminiProcessor();

  return (
    <div>
      <input 
        type="file"
        onChange={(e) => quickProcess(e.target.files[0], 'client_data')}
      />
      {isLoading && <p>Processando...</p>}
      {processedData && <pre>{JSON.stringify(processedData, null, 2)}</pre>}
    </div>
  );
}
```

## 📊 **Exemplo de Resposta**

### Dados de Cliente

```json
{
  "file_id": "64f8a1b2c3d4e5f6",
  "processing_type": "client_data",
  "processed_data": {
    "name": "João Silva Santos",
    "email": "<EMAIL>",
    "phone": "11987654321",
    "address": "Rua das Flores, 123, São Paulo, SP",
    "company": "Empresa XYZ Ltda",
    "document_type": "RG",
    "document_number": "*********",
    "additional_info": "Cliente desde 2020"
  },
  "file_info": {
    "name": "documento_cliente.jpg",
    "size": 2048576,
    "mimeType": "image/jpeg"
  },
  "processed_at": "2024-01-15T10:30:00Z"
}
```

### Fatura

```json
{
  "processing_type": "invoice_data",
  "processed_data": {
    "invoice_number": "NF-001234",
    "date": "2024-01-15",
    "due_date": "2024-02-15",
    "total_amount": 1500.00,
    "currency": "BRL",
    "items": [
      {
        "description": "Serviço de consultoria",
        "quantity": "10",
        "unit_price": "150.00",
        "total": "1500.00"
      }
    ],
    "client_info": {
      "name": "Empresa ABC Ltda",
      "address": "Av. Paulista, 1000, São Paulo, SP"
    }
  }
}
```

## ⚙️ **Configurações Avançadas**

### Prompt Personalizado

```typescript
const result = await processor.processFile(file, {
  customPrompt: `
    Analise este documento e extraia apenas:
    - Nome completo
    - CPF (se visível)
    - Endereço completo
    
    Retorne em formato JSON com essas chaves exatas.
  `
});
```

### Processamento em Lote

```typescript
const files = [file1, file2, file3];
const results = await processor.processMultipleFiles(files, {
  processingType: PROCESSING_TYPES.IDENTITY_DOCUMENT
});
```

## 🔒 **Segurança e Limites**

### Validações Automáticas
- ✅ Usuário autenticado obrigatório
- ✅ Tipos de arquivo permitidos
- ✅ Tamanho máximo (10MB padrão)
- ✅ Rate limiting por usuário

### Dados Sensíveis
- 🔐 Arquivos são temporários no storage
- 🔐 Dados não são armazenados permanentemente
- 🔐 Processamento server-side seguro

## 💰 **Custos**

### Modelo gemini-1.5-flash
- **Entrada**: $0.075 / 1M tokens
- **Saída**: $0.30 / 1M tokens
- **Imagens**: $0.075 / imagem

### Estimativa por Documento
- Documento simples: ~$0.001 - $0.005
- Documento complexo: ~$0.005 - $0.015
- PDF multi-página: ~$0.010 - $0.030

## 🐛 **Troubleshooting**

### Erro: "Missing API Key"
```bash
# Verificar se a variável está configurada
echo $GEMINI_API_KEY
```

### Erro: "File too large"
```env
# Aumentar limite no appwrite.json
MAX_FILE_SIZE=20971520  # 20MB
```

### Erro: "Unsupported file type"
```env
# Adicionar tipos permitidos
ALLOWED_MIME_TYPES=image/jpeg,image/png,image/webp,application/pdf,image/tiff
```

### Timeout
```javascript
// Aumentar timeout no hook
const result = await executeFunction('GEMINI_FILE_PROCESSOR', {
  data: { fileId, bucketId },
  timeout: 120000  // 2 minutos
});
```

## 📚 **Recursos Adicionais**

- [Google AI Studio](https://makersuite.google.com/)
- [Gemini API Documentation](https://ai.google.dev/docs)
- [Appwrite Functions Guide](https://appwrite.io/docs/functions)
- [Pricing Calculator](https://ai.google.dev/pricing)

## 🔄 **Atualizações**

Para atualizar a função:

```bash
# 1. Fazer alterações no código
# 2. Fazer deploy
appwrite deploy function --functionId gemini-file-processor

# 3. Verificar logs
appwrite functions listExecutions --functionId gemini-file-processor
```
