import {
  IconFile,
  IconFileText,
  IconPhoto,
  IconVideo,
  IconMusic,
  IconFileZip,
  IconFileCode,
  IconFileSpreadsheet,
  IconPresentation,
  type Icon
} from '@tabler/icons-react';

/**
 * Formatar tamanho de arquivo em bytes para formato legível
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Obter ícone baseado no tipo MIME do arquivo
 */
export function getFileTypeIcon(mimeType?: string): Icon {
  if (!mimeType) return IconFile;

  // Imagens
  if (mimeType.startsWith('image/')) {
    return IconPhoto;
  }

  // Vídeos
  if (mimeType.startsWith('video/')) {
    return IconVideo;
  }

  // Áudio
  if (mimeType.startsWith('audio/')) {
    return IconMusic;
  }

  // Documentos de texto
  if (mimeType.includes('text/') ||
      mimeType.includes('pdf') ||
      mimeType.includes('msword') ||
      mimeType.includes('wordprocessingml')) {
    return IconFileText;
  }

  // Planilhas
  if (mimeType.includes('spreadsheet') ||
      mimeType.includes('excel') ||
      mimeType.includes('csv')) {
    return IconFileSpreadsheet;
  }

  // Apresentações
  if (mimeType.includes('presentation') ||
      mimeType.includes('powerpoint')) {
    return IconPresentation;
  }

  // Arquivos compactados
  if (mimeType.includes('zip') ||
      mimeType.includes('rar') ||
      mimeType.includes('7z') ||
      mimeType.includes('tar') ||
      mimeType.includes('gzip')) {
    return IconFileZip;
  }

  // Código
  if (mimeType.includes('javascript') ||
      mimeType.includes('json') ||
      mimeType.includes('xml') ||
      mimeType.includes('html') ||
      mimeType.includes('css')) {
    return IconFileCode;
  }

  // Padrão
  return IconFile;
}

/**
 * Obter cor baseada no tipo MIME do arquivo
 */
export function getFileTypeColor(mimeType?: string): string {
  if (!mimeType) return 'text-gray-500';

  // Imagens
  if (mimeType.startsWith('image/')) {
    return 'text-green-500';
  }

  // Vídeos
  if (mimeType.startsWith('video/')) {
    return 'text-purple-500';
  }

  // Áudio
  if (mimeType.startsWith('audio/')) {
    return 'text-pink-500';
  }

  // PDFs
  if (mimeType.includes('pdf')) {
    return 'text-red-500';
  }

  // Documentos de texto
  if (mimeType.includes('text/') ||
      mimeType.includes('msword') ||
      mimeType.includes('wordprocessingml')) {
    return 'text-blue-500';
  }

  // Planilhas
  if (mimeType.includes('spreadsheet') ||
      mimeType.includes('excel') ||
      mimeType.includes('csv')) {
    return 'text-emerald-500';
  }

  // Apresentações
  if (mimeType.includes('presentation') ||
      mimeType.includes('powerpoint')) {
    return 'text-orange-500';
  }

  // Arquivos compactados
  if (mimeType.includes('zip') ||
      mimeType.includes('rar') ||
      mimeType.includes('7z') ||
      mimeType.includes('tar') ||
      mimeType.includes('gzip')) {
    return 'text-yellow-500';
  }

  // Código
  if (mimeType.includes('javascript') ||
      mimeType.includes('json') ||
      mimeType.includes('xml') ||
      mimeType.includes('html') ||
      mimeType.includes('css')) {
    return 'text-cyan-500';
  }

  // Padrão
  return 'text-gray-500';
}

/**
 * Verificar se o arquivo é uma imagem
 */
export function isImageFile(mimeType?: string): boolean {
  return mimeType?.startsWith('image/') || false;
}

/**
 * Verificar se o arquivo é um vídeo
 */
export function isVideoFile(mimeType?: string): boolean {
  return mimeType?.startsWith('video/') || false;
}

/**
 * Verificar se o arquivo é um PDF
 */
export function isPdfFile(mimeType?: string): boolean {
  return mimeType?.includes('pdf') || false;
}

/**
 * Verificar se o arquivo pode ser visualizado no navegador
 */
export function canPreviewFile(mimeType?: string): boolean {
  if (!mimeType) return false;

  return (
    isImageFile(mimeType) ||
    isPdfFile(mimeType) ||
    mimeType.includes('text/') ||
    mimeType.includes('json') ||
    mimeType.includes('xml') ||
    mimeType.includes('html') ||
    mimeType.includes('css') ||
    mimeType.includes('javascript')
  );
}

/**
 * Obter extensão do arquivo baseada no nome
 */
export function getFileExtension(fileName: string): string {
  const lastDot = fileName.lastIndexOf('.');
  return lastDot !== -1 ? fileName.slice(lastDot + 1).toLowerCase() : '';
}

/**
 * Obter categoria do arquivo baseada no tipo MIME
 */
export function getFileCategory(mimeType?: string): 'image' | 'video' | 'audio' | 'document' | 'archive' | 'code' | 'other' {
  if (!mimeType) return 'other';

  if (mimeType.startsWith('image/')) return 'image';
  if (mimeType.startsWith('video/')) return 'video';
  if (mimeType.startsWith('audio/')) return 'audio';

  if (mimeType.includes('text/') ||
      mimeType.includes('pdf') ||
      mimeType.includes('msword') ||
      mimeType.includes('wordprocessingml') ||
      mimeType.includes('spreadsheet') ||
      mimeType.includes('excel') ||
      mimeType.includes('csv') ||
      mimeType.includes('presentation') ||
      mimeType.includes('powerpoint')) {
    return 'document';
  }

  if (mimeType.includes('zip') ||
      mimeType.includes('rar') ||
      mimeType.includes('7z') ||
      mimeType.includes('tar') ||
      mimeType.includes('gzip')) {
    return 'archive';
  }

  if (mimeType.includes('javascript') ||
      mimeType.includes('json') ||
      mimeType.includes('xml') ||
      mimeType.includes('html') ||
      mimeType.includes('css')) {
    return 'code';
  }

  return 'other';
}

/**
 * Validar se o arquivo é permitido baseado no tipo MIME
 */
export function isAllowedFileType(mimeType: string, allowedTypes?: string[]): boolean {
  if (!allowedTypes || allowedTypes.length === 0) return true;

  return allowedTypes.some(type => {
    // Permitir wildcards como "image/*"
    if (type.endsWith('/*')) {
      const baseType = type.slice(0, -2);
      return mimeType.startsWith(baseType + '/');
    }

    return mimeType === type;
  });
}

/**
 * Validar tamanho do arquivo
 */
export function isValidFileSize(fileSize: number, maxSize?: number): boolean {
  if (!maxSize) return true;
  return fileSize <= maxSize;
}

/**
 * Obter tipos de arquivo permitidos padrão
 */
export const DEFAULT_ALLOWED_TYPES = [
  // Imagens
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/webp',
  'image/svg+xml',

  // Documentos
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-powerpoint',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'text/plain',
  'text/csv',

  // Arquivos compactados
  'application/zip',
  'application/x-rar-compressed',
  'application/x-7z-compressed',

  // Código
  'application/json',
  'text/html',
  'text/css',
  'text/javascript',
  'application/javascript',
  'text/xml',
  'application/xml',
];

/**
 * Tamanho máximo padrão (10MB)
 */
export const DEFAULT_MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
