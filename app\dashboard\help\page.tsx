'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  BookOpenIcon,
  MessageCircleIcon,
  MailIcon,
  SearchIcon,
  HelpCircleIcon,
  ExternalLinkIcon,
  VideoIcon,
  FileTextIcon
} from "lucide-react";

// FAQs específicas do template
const faqs = [
  {
    id: 1,
    question: "Como configurar o cache local-first?",
    answer: "O cache está configurado automaticamente. Você pode ajustar as configurações em app/lib/cache-config.ts",
    category: "Cache"
  },
  {
    id: 2,
    question: "Como criar um novo cliente?",
    answer: "Vá para Dashboard > Clientes > Novo Cliente ou use o hook useCreateClient()",
    category: "Clientes"
  },
  {
    id: 3,
    question: "Como convidar membros para o team?",
    answer: "Acesse Dashboard > Teams > Gerenciar Membros > Convidar. O sistema usa APIs nativas do Appwrite.",
    category: "Teams"
  },
  {
    id: 4,
    question: "Como ativar o PWA?",
    answer: "Configure NEXT_PUBLIC_PWA_ENABLED=true no arquivo .env e reinicie o servidor",
    category: "PWA"
  },
  {
    id: 5,
    question: "Como usar cloud functions?",
    answer: "Configure as URLs no arquivo app/lib/appwrite/cloudfunctions/const.ts e use o hook useCloudFunction()",
    category: "Cloud Functions"
  },
  {
    id: 6,
    question: "Como exportar dados?",
    answer: "Use o sistema de import/export na página de clientes ou o hook useDataImportExport()",
    category: "Export"
  }
];

// Recursos de ajuda específicos do template
const helpResources = [
  {
    title: "Documentação Completa",
    description: "Índice completo da documentação do template",
    icon: BookOpenIcon,
    link: "/docs/INDEX",
    type: "Documentação"
  },
  {
    title: "Hooks Customizados",
    description: "Guia completo dos hooks React Query disponíveis",
    icon: FileTextIcon,
    link: "/docs/HOOKS",
    type: "Desenvolvimento"
  },
  {
    title: "Scripts de Setup",
    description: "Automação e configuração do template",
    icon: VideoIcon,
    link: "/docs/SCRIPTS",
    type: "Configuração"
  },
  {
    title: "Troubleshooting",
    description: "Soluções para problemas comuns",
    icon: MessageCircleIcon,
    link: "/docs/TROUBLESHOOTING",
    type: "Suporte"
  }
];

export default function HelpPage() {
  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex flex-col gap-4 p-4 md:p-6 border-b md:flex-row md:items-center md:justify-between">
        <div className="min-w-0">
          <h1 className="text-xl md:text-2xl font-bold">Central de Ajuda</h1>
          <p className="text-muted-foreground text-sm md:text-base">
            Encontre respostas, tutoriais e entre em contato com nosso suporte.
          </p>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 p-4 md:p-6">
        {/* Search Bar */}
        <div className="relative max-w-md mb-6">
          <SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Pesquisar na central de ajuda..."
            className="pl-10"
          />
        </div>

        {/* Quick Actions */}
        <div className="mb-8">
          <div className="grid gap-4 md:grid-cols-3">
            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <MessageCircleIcon className="h-8 w-8 text-primary" />
                  <div>
                    <CardTitle className="text-lg">Chat ao Vivo</CardTitle>
                    <CardDescription>Fale conosco agora</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Button className="w-full">Iniciar Chat</Button>
              </CardContent>
            </Card>

            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <MailIcon className="h-8 w-8 text-primary" />
                  <div>
                    <CardTitle className="text-lg">Email Suporte</CardTitle>
                    <CardDescription>Envie sua dúvida</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Button variant="outline" className="w-full">
                  Enviar Email
                </Button>
              </CardContent>
            </Card>

            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <HelpCircleIcon className="h-8 w-8 text-primary" />
                  <div>
                    <CardTitle className="text-lg">FAQ</CardTitle>
                    <CardDescription>Perguntas frequentes</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Button variant="outline" className="w-full">
                  Ver FAQ
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Help Resources */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Recursos de Ajuda</h2>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {helpResources.map((resource, index) => (
              <Card key={index} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <resource.icon className="h-8 w-8 text-primary" />
                    <Badge variant="secondary">{resource.type}</Badge>
                  </div>
                  <CardTitle className="text-lg">{resource.title}</CardTitle>
                  <CardDescription>{resource.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <Button variant="ghost" className="w-full justify-between">
                    Acessar
                    <ExternalLinkIcon className="h-4 w-4" />
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* FAQ Section */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Perguntas Frequentes</h2>
          <div className="space-y-4">
            {faqs.map((faq) => (
              <Card key={faq.id}>
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-base">{faq.question}</CardTitle>
                    <Badge variant="outline">{faq.category}</Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">{faq.answer}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle>Informações de Contato</CardTitle>
            <CardDescription>
              Outras formas de entrar em contato conosco
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <h4 className="font-medium">Email</h4>
                <p className="text-sm text-muted-foreground"><EMAIL></p>
              </div>
              <div>
                <h4 className="font-medium">Horário de Atendimento</h4>
                <p className="text-sm text-muted-foreground">
                  Segunda a Sexta: 9h às 18h<br />
                  Sábado: 9h às 14h
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
