# ☁️ Guia Completo de Cloud Functions no Appwrite

Este guia abrangente ensina como usar, configurar e criar Cloud Functions no Appwrite usando nosso template otimizado.

## 🎯 O que são Cloud Functions?

Cloud Functions são funções serverless que executam no backend do Appwrite, permitindo:

- **Processamento Seguro**: Operações que requerem chaves API sensíveis
- **Lógica de Negócio**: Validações complexas e processamento de dados
- **Integrações**: Conectar com APIs externas (Stripe, Gemini AI, etc.)
- **Automação**: Tarefas agendadas e processamento em background

## 🏗️ Estrutura do Projeto

```
app/lib/appwrite/cloudfunctions/
├── 📁 utils/                    # Utilitários compartilhados
│   ├── gemini-utils.js         # Helpers para Gemini AI
│   └── validation-utils.js     # Validações comuns
├── 📄 admin-users.js           # Listagem de usuários (admin)
├── 📄 admin-user-management.js # Gerenciamento de usuários
├── 📄 gemini-file-processor.js # Processamento de arquivos IA
├── 📄 gemini-data-processor.js # Processamento de dados IA
├── 📄 stripe-payments.js       # Pagamentos Stripe
├── 📄 const.ts                 # Configurações e constantes
├── 📄 executor.ts              # Executor com fallbacks
├── 📄 appwrite.json           # Configuração de deploy
└── 📄 .env.example            # Variáveis de ambiente
```

### Frontend Integration
```
app/hooks/
└── 📄 use-cloud-function.ts    # Hook React para execução
```

## 🚀 Configuração Inicial

### 1. Instalar Appwrite CLI

```bash
npm install -g appwrite-cli
```

### 2. Fazer Login no Appwrite

```bash
appwrite login
```

### 3. Configurar Projeto

```bash
# Na raiz do projeto
appwrite init project
```

### 4. Configurar Variáveis de Ambiente

Copie o arquivo de exemplo:

```bash
cp app/lib/appwrite/cloudfunctions/.env.example app/lib/appwrite/cloudfunctions/.env
```

Configure as variáveis necessárias:

```env
# Appwrite Configuration
APPWRITE_FUNCTION_ENDPOINT=https://cloud.appwrite.io/v1
APPWRITE_FUNCTION_PROJECT_ID=seu-project-id
APPWRITE_FUNCTION_API_KEY=sua-api-key

# Gemini AI (opcional)
GEMINI_API_KEY=sua-gemini-api-key

# Stripe (opcional)
STRIPE_SECRET_KEY=sua-stripe-secret-key
```

### 5. Deploy das Funções

```bash
# Deploy todas as funções
appwrite deploy function

# Deploy função específica
appwrite deploy function --functionId admin-users
```

## 📋 Funções Disponíveis

### 1. **admin-users** 👥
Lista usuários do projeto (apenas admins).

**Uso:**
```typescript
const { execute, isLoading } = useCloudFunction('ADMIN_USERS');

const getUsers = async () => {
  const result = await execute();
  if (result.success) {
    console.log('Usuários:', result.data);
  }
};
```

**Permissões:** `role:admin`
**Timeout:** 15s

### 2. **admin-user-management** 🔧
Gerenciamento completo de usuários (criar, editar, deletar).

**Uso:**
```typescript
const { execute } = useCloudFunction('ADMIN_USER_MANAGEMENT');

// Criar usuário
const createUser = async () => {
  const result = await execute({
    data: {
      action: 'create',
      email: '<EMAIL>',
      password: 'senha123',
      name: 'Nome do Usuário'
    }
  });
};

// Atualizar usuário
const updateUser = async (userId: string) => {
  const result = await execute({
    data: {
      action: 'update',
      userId,
      name: 'Novo Nome',
      labels: ['admin']
    }
  });
};
```

**Permissões:** `role:admin`
**Timeout:** 30s

### 3. **gemini-file-processor** 🤖
Processa arquivos usando Gemini AI para extrair dados estruturados.

**Uso:**
```typescript
const { execute } = useCloudFunction('GEMINI_FILE_PROCESSOR');

const processFile = async (fileId: string) => {
  const result = await execute({
    data: {
      fileId,
      documentType: 'identity_document', // ou 'address_proof', 'client_data'
      prompt: 'Extrair nome, CPF e data de nascimento'
    }
  });

  if (result.success) {
    console.log('Dados extraídos:', result.data);
  }
};
```

**Tipos suportados:**
- `identity_document`: RG, CPF, CNH
- `address_proof`: Comprovantes de endereço
- `client_data`: Dados de cliente
- `invoice_data`: Faturas e notas fiscais

**Permissões:** `users`
**Timeout:** 60s

### 4. **gemini-data-processor** 📊
Processa dados textuais usando Gemini AI.

**Uso:**
```typescript
const { execute } = useCloudFunction('GEMINI_DATA_PROCESSOR');

const processData = async () => {
  const result = await execute({
    data: {
      content: 'Texto para processar...',
      prompt: 'Extrair informações de contato',
      outputFormat: 'json'
    }
  });
};
```

### 5. **stripe-payments** 💳
Processa pagamentos via Stripe.

**Uso:**
```typescript
const { execute } = useCloudFunction('STRIPE_PAYMENTS');

const createPayment = async () => {
  const result = await execute({
    data: {
      action: 'create_payment_intent',
      amount: 5000, // R$ 50,00 em centavos
      currency: 'brl',
      metadata: {
        clientId: 'client-123'
      }
    }
  });
};
```

## 🎣 Como Usar no Frontend

### Hook Básico

```typescript
import { useCloudFunction } from '@/hooks/use-cloud-function';

function MyComponent() {
  const { execute, isLoading, error } = useCloudFunction('ADMIN_USERS');

  const handleClick = async () => {
    const result = await execute();
    if (result.success) {
      // Sucesso
    } else {
      // Erro: result.error
    }
  };

  return (
    <button onClick={handleClick} disabled={isLoading}>
      {isLoading ? 'Carregando...' : 'Executar Função'}
    </button>
  );
}
```

### Hook Genérico

```typescript
import { useGenericCloudFunction } from '@/hooks/use-cloud-function';

function MyComponent() {
  const { execute, isLoading, error } = useGenericCloudFunction();

  const handleClick = async () => {
    const result = await execute('GEMINI_FILE_PROCESSOR', {
      fileId: 'file-123',
      documentType: 'identity_document'
    });
  };

  return <button onClick={handleClick}>Processar Arquivo</button>;
}
```

### Com TanStack Query

```typescript
import { useMutation } from '@tanstack/react-query';
import { executeFunction } from '@/lib/appwrite/cloudfunctions/executor';

function MyComponent() {
  const mutation = useMutation({
    mutationFn: (data: any) => executeFunction('ADMIN_USERS', { data }),
    onSuccess: (result) => {
      if (result.success) {
        // Sucesso
      }
    },
    onError: (error) => {
      console.error('Erro:', error);
    }
  });

  return (
    <button
      onClick={() => mutation.mutate({})}
      disabled={mutation.isPending}
    >
      {mutation.isPending ? 'Carregando...' : 'Executar'}
    </button>
  );
}
```

## 🔧 Criando Novas Funções

### 1. Criar o Arquivo da Função

```javascript
// app/lib/appwrite/cloudfunctions/minha-funcao.js
import { Client, Databases } from 'node-appwrite';

export default async ({ req, res, log, error }) => {
  try {
    // 1. Validar autenticação
    const jwt = req.headers['x-appwrite-user-jwt'];
    if (!jwt) {
      return res.json({ success: false, error: 'Não autenticado' }, 401);
    }

    // 2. Configurar cliente Appwrite
    const client = new Client()
      .setEndpoint(process.env.APPWRITE_FUNCTION_ENDPOINT)
      .setProject(process.env.APPWRITE_FUNCTION_PROJECT_ID)
      .setJWT(jwt);

    const databases = new Databases(client);

    // 3. Processar dados
    const { data } = req.bodyJson || {};

    // Sua lógica aqui...

    // 4. Retornar resultado
    return res.json({
      success: true,
      data: { message: 'Função executada com sucesso!' }
    });

  } catch (err) {
    error('Erro na função:', err);
    return res.json({
      success: false,
      error: err.message
    }, 500);
  }
};
```

### 2. Adicionar no appwrite.json

```json
{
  "$id": "minha-funcao",
  "name": "Minha Função",
  "runtime": "node-18.0",
  "execute": ["users"],
  "timeout": 30,
  "enabled": true,
  "logging": true,
  "entrypoint": "minha-funcao.js",
  "commands": "npm install node-appwrite",
  "path": "app/lib/appwrite/cloudfunctions"
}
```

### 3. Adicionar Constante

```typescript
// app/lib/appwrite/cloudfunctions/const.ts
export const CLOUD_FUNCTIONS = {
  // ... outras funções
  MINHA_FUNCAO: import.meta.env.NEXT_PUBLIC_CLOUDFUNCTION_MINHA_FUNCAO || '',
} as const;
```

### 4. Configurar Variável de Ambiente

```env
# .env
NEXT_PUBLIC_CLOUDFUNCTION_MINHA_FUNCAO=https://cloud.appwrite.io/v1/functions/minha-funcao/executions
```

### 5. Deploy

```bash
appwrite deploy function --functionId minha-funcao
```

## 🔐 Configuração de Permissões

### Níveis de Acesso

#### 1. **Público** (`any`)
```json
{
  "execute": ["any"]
}
```
- Qualquer pessoa pode executar
- Use apenas para webhooks ou funções públicas

#### 2. **Usuários Autenticados** (`users`)
```json
{
  "execute": ["users"]
}
```
- Apenas usuários logados
- Padrão para a maioria das funções

#### 3. **Administradores** (`role:admin`)
```json
{
  "execute": ["role:admin"]
}
```
- Apenas usuários com label `admin`
- Para funções administrativas sensíveis

#### 4. **Times Específicos** (`team:teamId`)
```json
{
  "execute": ["team:developers", "team:managers"]
}
```
- Apenas membros de times específicos

### Configurar no Console

1. Acesse **Functions** no console Appwrite
2. Selecione sua função
3. Vá em **Settings**
4. Configure **Execute Access**
5. Defina **Timeout** apropriado
6. Salve as alterações

## 🛠️ Sistema de Fallbacks

O template inclui um sistema robusto de fallbacks para quando as cloud functions não estão disponíveis:

### Configuração Automática

```typescript
// app/lib/appwrite/cloudfunctions/const.ts
export const FUNCTION_CONFIG = {
  DEFAULT_TIMEOUT: 30000,
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000,
  ENABLE_FALLBACKS: true,
  SHOW_FALLBACK_MESSAGES: import.meta.env.DEV,
};
```

### Usando Fallbacks

```typescript
const { execute } = useCloudFunction('ADMIN_USERS');

const getUsers = async () => {
  const result = await execute({
    fallbackData: [
      { id: '1', name: 'Usuário Mock', email: '<EMAIL>' }
    ],
    fallbackMessage: 'Usando dados de demonstração'
  });

  if (result.isFallback) {
    console.log('Usando fallback:', result.data);
  }
};
```

### Verificar Configuração

```typescript
import { isFunctionConfigured } from '@/lib/appwrite/cloudfunctions/const';

if (isFunctionConfigured('ADMIN_USERS')) {
  // Função configurada
} else {
  // Usar fallback ou mostrar mensagem
}
```

## 🐛 Troubleshooting

### Problemas Comuns

#### 1. **Erro: "Function not found"**
```bash
# Verificar se a função foi deployada
appwrite list functions

# Re-deploy se necessário
appwrite deploy function --functionId sua-funcao
```

#### 2. **Erro: "Unauthorized"**
- Verificar se o usuário tem as permissões corretas
- Confirmar se o JWT está sendo enviado
- Verificar configuração de `execute` na função

#### 3. **Timeout**
- Aumentar timeout na configuração da função
- Otimizar código para execução mais rápida
- Dividir processamento em etapas menores

#### 4. **Variáveis de Ambiente**
```bash
# Verificar variáveis no console Appwrite
# Functions > Sua Função > Settings > Environment Variables
```

### Debug Local

```javascript
// Adicionar logs detalhados
export default async ({ req, res, log, error }) => {
  log('Iniciando função...');
  log('Headers:', JSON.stringify(req.headers));
  log('Body:', JSON.stringify(req.bodyJson));

  try {
    // Sua lógica...
    log('Processamento concluído');
  } catch (err) {
    error('Erro detalhado:', err);
    error('Stack trace:', err.stack);
  }
};
```

### Monitoramento

```typescript
// Hook com monitoramento
const { execute } = useCloudFunction('MINHA_FUNCAO');

const executeWithMonitoring = async (data: any) => {
  const startTime = Date.now();

  try {
    const result = await execute({ data });
    const duration = Date.now() - startTime;

    console.log(`Função executada em ${duration}ms`);
    return result;
  } catch (error) {
    console.error('Erro na execução:', error);
    throw error;
  }
};
```

## 📚 Boas Práticas

### 1. **Estrutura de Código**

```javascript
// ✅ Boa estrutura
export default async ({ req, res, log, error }) => {
  try {
    // 1. Validações iniciais
    const validation = validateRequest(req);
    if (!validation.valid) {
      return res.json({ success: false, error: validation.error }, 400);
    }

    // 2. Configuração de clientes
    const { client, databases } = setupAppwriteClient(req);

    // 3. Lógica de negócio
    const result = await processBusinessLogic(req.bodyJson, databases);

    // 4. Resposta padronizada
    return res.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    });

  } catch (err) {
    error('Erro na função:', err);
    return res.json({
      success: false,
      error: err.message,
      timestamp: new Date().toISOString()
    }, 500);
  }
};
```

### 2. **Validação de Entrada**

```javascript
function validateRequest(req) {
  const jwt = req.headers['x-appwrite-user-jwt'];
  if (!jwt) {
    return { valid: false, error: 'JWT obrigatório' };
  }

  const { data } = req.bodyJson || {};
  if (!data) {
    return { valid: false, error: 'Dados obrigatórios' };
  }

  return { valid: true };
}
```

### 3. **Tratamento de Erros**

```javascript
// ✅ Tratamento específico
try {
  const result = await databases.createDocument(/* ... */);
} catch (err) {
  if (err.code === 409) {
    return res.json({ success: false, error: 'Documento já existe' }, 409);
  }

  if (err.code === 401) {
    return res.json({ success: false, error: 'Não autorizado' }, 401);
  }

  throw err; // Re-throw para outros erros
}
```

### 4. **Performance**

```javascript
// ✅ Otimizações
export default async ({ req, res, log, error }) => {
  // Cache de configurações
  const config = getConfigCache();

  // Processamento paralelo quando possível
  const [userData, clientData] = await Promise.all([
    getUserData(userId),
    getClientData(clientId)
  ]);

  // Paginação para grandes datasets
  const results = await databases.listDocuments(
    DATABASE_ID,
    COLLECTION_ID,
    [
      Query.limit(100),
      Query.offset(page * 100)
    ]
  );
};
```

### 5. **Segurança**

```javascript
// ✅ Validações de segurança
function validateUserAccess(userId, requestedUserId) {
  // Usuários só podem acessar seus próprios dados
  if (userId !== requestedUserId) {
    throw new Error('Acesso negado');
  }
}

function sanitizeInput(input) {
  // Remover caracteres perigosos
  return input.replace(/[<>]/g, '');
}
```

## 🚀 Próximos Passos

### Para Desenvolvimento
1. **Teste suas funções** localmente antes do deploy
2. **Configure logs** detalhados para debug
3. **Implemente fallbacks** para melhor UX
4. **Monitore performance** e otimize conforme necessário

### Para Produção
1. **Configure variáveis** de ambiente de produção
2. **Ajuste timeouts** baseado no uso real
3. **Implemente rate limiting** se necessário
4. **Configure alertas** para monitoramento

---

**🎯 Template pronto para cloud functions!**

Este guia cobre tudo que você precisa para usar e criar cloud functions no Appwrite. Para dúvidas específicas, consulte a [documentação oficial do Appwrite](https://appwrite.io/docs/functions).
