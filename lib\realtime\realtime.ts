/**
 * Listener Realtime Simples
 * Escuta eventos do Appwrite e atualiza o store Valtio
 */

import { Client } from 'appwrite';
import { realtimeStore } from './store';
import { DATABASE_ID } from '../appwrite/config';
import { account } from '../appwrite/config';
import { getStoreNameFromCollectionId } from '../cache-config';

let client: Client | null = null;
let unsubscribe: (() => void) | null = null;

async function setupClient(): Promise<Client | null> {
  if (!process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT || !process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID) {
    realtimeStore.error = 'Configuração do Appwrite não encontrada';
    return null;
  }

  try {
    // Verificar se há uma sessão ativa
    await account.get();

    // Usar o cliente já configurado e autenticado do config
    // O cliente do config já tem as credenciais corretas
    const appwriteClient = new Client();
    appwriteClient.setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT);
    appwriteClient.setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID);

    console.log('✅ Cliente realtime configurado com sucesso');
    return appwriteClient;
  } catch (error) {
    console.error('❌ Erro ao configurar cliente realtime:', error);
    realtimeStore.error = 'Usuário não autenticado';
    return null;
  }
}

function handleEvent(response: any) {
  try {
    const eventString = response.events?.[0] || '';
    const collection = extractCollection(eventString);
    const action = extractAction(eventString);
    const payload = response.payload;

    if (!collection || !action || !payload) {
      return;
    }

    // Atualizar store Valtio diretamente
    updateStore(collection, action, payload);
    console.log(`📡 Evento ${action} em ${collection}:`, payload.$id);
  } catch (error) {
    console.error('❌ Erro ao processar evento:', error);
  }
}

function updateStore(collection: string, action: string, payload: any) {
  // Apenas create/update - delete é ignorado
  if (action === 'delete') return;

  switch (collection) {
    case 'clients':
      updateArray('clients', action, payload);
      break;
    case 'notifications':
      updateArray('notifications', action, payload);
      break;
    case 'team_chats':
      updateArray('chats', action, payload);
      break;
    case 'chat_messages':
      updateArray('messages', action, payload);
      break;
    case 'events':
      updateArray('events', action, payload);
      break;
    case 'kanban_boards':
      updateArray('boards', action, payload);
      break;
    case 'kanban_columns':
      updateArray('columns', action, payload);
      break;
    case 'kanban_tasks':
      updateArray('tasks', action, payload);
      break;
    case 'activity_logs':
      updateArray('activities', action, payload);
      break;
    // Note: Teams são gerenciados pelo Appwrite Teams service, não por collection
    // Files são gerenciados pelo Storage service, não por collection
  }
}

function updateArray(key: keyof typeof realtimeStore, action: string, payload: any) {
  // Com Valtio, precisamos modificar diretamente o proxy, não uma referência
  switch (action) {
    case 'create':
      (realtimeStore[key] as any[]).push(payload);
      break;
    case 'update':
      const updateIndex = (realtimeStore[key] as any[]).findIndex(item => item.$id === payload.$id);
      if (updateIndex !== -1) {
        (realtimeStore[key] as any[])[updateIndex] = payload;
      } else {
        // Se não encontrar o item para atualizar, adiciona ao array
        (realtimeStore[key] as any[]).push(payload);
      }
      console.log("updateArray",key,action,payload)
      console.log(realtimeStore)
      break;
    case 'delete':
      const deleteIndex = (realtimeStore[key] as any[]).findIndex(item => item.$id === payload.$id);
      if (deleteIndex !== -1) {
        (realtimeStore[key] as any[]).splice(deleteIndex, 1);
      }
      break;
  }
}

function extractCollection(eventString: string): string | null {
  const match = eventString.match(/databases\.[^.]+\.collections\.([^.]+)\.documents/);
  if (!match) return null;

  const collectionId = match[1];

  // Usar a função centralizada de mapeamento
  const storeName = getStoreNameFromCollectionId(collectionId);

  if (!storeName) {
    console.warn('⚠️ Collection ID não reconhecida:', collectionId);
  }

  return storeName;
}

function extractAction(eventString: string): string | null {
  if (eventString.includes('.create')) return 'create';
  if (eventString.includes('.update')) return 'update';
  if (eventString.includes('.delete')) return 'delete';
  return null;
}

export async function connect(): Promise<void> {
  if (!client) {
    client = await setupClient();
  }

  if (!client) {
    throw new Error('Cliente Appwrite não configurado');
  }

  if (realtimeStore.isConnected || realtimeStore.isConnecting) {
    return;
  }

  try {
    realtimeStore.isConnecting = true;
    realtimeStore.error = null;

    // Canais específicos para as collections que queremos escutar
    const channels = [
      `databases.${DATABASE_ID}.collections.${process.env.NEXT_PUBLIC_APPWRITE_CLIENTS_ID}.documents`,
      `databases.${DATABASE_ID}.collections.${process.env.NEXT_PUBLIC_APPWRITE_NOTIFICATIONS_ID}.documents`,
      `databases.${DATABASE_ID}.collections.${process.env.NEXT_PUBLIC_APPWRITE_TEAM_CHATS_ID}.documents`,
      `databases.${DATABASE_ID}.collections.${process.env.NEXT_PUBLIC_APPWRITE_CHAT_MESSAGES_ID}.documents`,
      `databases.${DATABASE_ID}.collections.${process.env.NEXT_PUBLIC_APPWRITE_EVENTS_ID}.documents`,
      `databases.${DATABASE_ID}.collections.${process.env.NEXT_PUBLIC_APPWRITE_KANBAN_BOARDS_ID}.documents`,
      `databases.${DATABASE_ID}.collections.${process.env.NEXT_PUBLIC_APPWRITE_KANBAN_COLUMNS_ID}.documents`,
      `databases.${DATABASE_ID}.collections.${process.env.NEXT_PUBLIC_APPWRITE_KANBAN_TASKS_ID}.documents`,
      `databases.${DATABASE_ID}.collections.${process.env.NEXT_PUBLIC_APPWRITE_ACTIVITY_LOGS_ID}.documents`,
    ].filter(Boolean); // Remove canais undefined

    console.log('📡 Canais configurados:', channels);

    unsubscribe = client.subscribe(channels, (response: any) => {
      console.log('📡 Evento recebido:', response);
      handleEvent(response);
    });

    realtimeStore.isConnected = true;
    realtimeStore.isConnecting = false;

    console.log('� Realtime conectado');
  } catch (error) {
    realtimeStore.isConnecting = false;
    realtimeStore.error = error instanceof Error ? error.message : 'Erro de conexão';
    console.error('❌ Erro ao conectar realtime:', error);
    throw error;
  }
}

export function disconnect(): void {
  if (unsubscribe) {
    unsubscribe();
    unsubscribe = null;
  }

  realtimeStore.isConnected = false;
  realtimeStore.isConnecting = false;

  console.log('🔌 Realtime desconectado');
}
