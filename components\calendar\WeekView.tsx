/**
 * Week View Component
 * Visualização semanal do calendário
 */

import React, { useMemo } from 'react';
import {
  format,
  startOfWeek,
  endOfWeek,
  eachDayOfInterval,
  eachHourOfInterval,
  startOfDay,
  addHours,
  isToday,
  isSameDay,
  parseISO,
  getHours,
  getMinutes
} from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { cn } from '../../lib/utils';
import { useIsMobile } from '../../hooks/use-mobile';
import type { Event } from '@/schemas/events';

export interface WeekViewProps {
  currentDate: Date;
  events: Event[];
  onEventSelect?: (event: Event) => void;
  onEventCreate?: (date: Date, hour: number) => void;
  className?: string;
}

const START_HOUR = 6;
const END_HOUR = 22;
const HOUR_HEIGHT = 60; // pixels per hour

interface PositionedEvent {
  event: Event;
  top: number;
  height: number;
  left: number;
  width: number;
}

export function WeekView({
  currentDate,
  events,
  onEventSelect,
  onEventCreate,
  className,
}: WeekViewProps) {
  const isMobile = useIsMobile();
  // Generate week days
  const days = useMemo(() => {
    const weekStart = startOfWeek(currentDate, { weekStartsOn: 0 }); // Sunday
    const weekEnd = endOfWeek(currentDate, { weekStartsOn: 0 });
    return eachDayOfInterval({ start: weekStart, end: weekEnd });
  }, [currentDate]);

  // Generate hours
  const hours = useMemo(() => {
    const dayStart = startOfDay(currentDate);
    return eachHourOfInterval({
      start: addHours(dayStart, START_HOUR),
      end: addHours(dayStart, END_HOUR - 1),
    });
  }, [currentDate]);

  // Position events for each day
  const positionedEventsByDay = useMemo(() => {
    const result: PositionedEvent[][] = Array(7).fill(null).map(() => []);

    days.forEach((day, dayIndex) => {
      const dayEvents = events.filter(event => {
        const eventStart = new Date(event.startDate);
        return isSameDay(eventStart, day);
      });

      dayEvents.forEach(event => {
        const eventStart = new Date(event.startDate);
        const eventEnd = new Date(event.endDate);

        const startHour = getHours(eventStart);
        const startMinute = getMinutes(eventStart);
        const endHour = getHours(eventEnd);
        const endMinute = getMinutes(eventEnd);

        // Calculate position
        const top = ((startHour - START_HOUR) * HOUR_HEIGHT) + (startMinute / 60 * HOUR_HEIGHT);
        const duration = (endHour - startHour) + ((endMinute - startMinute) / 60);
        const height = Math.max(duration * HOUR_HEIGHT, 20); // Minimum 20px height

        result[dayIndex].push({
          event,
          top,
          height,
          left: 0,
          width: 100,
        });
      });
    });

    return result;
  }, [days, events]);

  const handleTimeSlotClick = (day: Date, hour: number) => {
    if (onEventCreate) {
      onEventCreate(day, hour);
    }
  };

  const handleEventClick = (event: Event, e: React.MouseEvent) => {
    e.stopPropagation();
    if (onEventSelect) {
      onEventSelect(event);
    }
  };

  return (
    <div className={cn('h-full flex flex-col', className)}>
      {/* Header with days */}
      <div className="grid grid-cols-8 border-b bg-background/80 sticky top-0 z-10">
        {/* Time column header */}
        <div className={cn(
          "p-2 text-center text-xs text-muted-foreground border-r",
          isMobile ? "w-12" : "w-20"
        )}>
          <span className="hidden sm:inline">Horário</span>
        </div>

        {/* Day headers */}
        {days.map((day) => (
          <div
            key={day.toString()}
            className={cn(
              'p-2 text-center text-xs border-r last:border-r-0',
              isToday(day) && 'bg-accent/20 font-medium'
            )}
          >
            <div className={cn(
              "font-medium",
              isMobile ? "text-xs" : "text-sm"
            )}>
              {format(day, isMobile ? 'E' : 'EEE', { locale: ptBR })}
            </div>
            <div className={cn(
              isMobile ? 'text-sm' : 'text-lg',
              isToday(day) && 'bg-primary text-primary-foreground rounded-full flex items-center justify-center mx-auto mt-1',
              isToday(day) && (isMobile ? 'w-5 h-5 text-xs' : 'w-6 h-6')
            )}>
              {format(day, 'd')}
            </div>
          </div>
        ))}
      </div>

      {/* Time grid */}
      <div className="flex-1 overflow-auto">
        <div className="grid grid-cols-8 relative">
          {/* Time column */}
          <div className="border-r">
            {hours.map((hour) => (
              <div
                key={hour.toString()}
                className={cn(
                  "border-b text-xs text-muted-foreground text-right",
                  isMobile ? "h-[50px] p-1" : "h-[60px] p-2"
                )}
              >
                {format(hour, isMobile ? 'HH' : 'HH:mm')}
              </div>
            ))}
          </div>

          {/* Day columns */}
          {days.map((day, dayIndex) => (
            <div
              key={day.toString()}
              className="border-r last:border-r-0 relative"
            >
              {/* Time slots */}
              {hours.map((hour) => (
                <div
                  key={hour.toString()}
                  className={cn(
                    "border-b hover:bg-accent/20 cursor-pointer transition-colors",
                    isMobile ? "h-[50px]" : "h-[60px]"
                  )}
                  onClick={() => handleTimeSlotClick(day, getHours(hour))}
                />
              ))}

              {/* Positioned events */}
              {positionedEventsByDay[dayIndex].map((positionedEvent) => (
                <div
                  key={positionedEvent.event.$id}
                  className={cn(
                    'absolute z-10 rounded cursor-pointer',
                    'bg-primary/90 text-primary-foreground hover:bg-primary transition-colors',
                    'border border-primary/20 shadow-sm',
                    isMobile ? 'left-0.5 right-0.5 p-1 text-xs' : 'left-1 right-1 p-1 text-xs'
                  )}
                  style={{
                    top: `${isMobile ? positionedEvent.top * 0.83 : positionedEvent.top}px`,
                    height: `${isMobile ? Math.max(positionedEvent.height * 0.83, 20) : positionedEvent.height}px`,
                  }}
                  onClick={(e) => handleEventClick(positionedEvent.event, e)}
                  title={positionedEvent.event.title}
                >
                  <div className={cn(
                    "font-medium truncate",
                    isMobile ? "text-xs" : "text-xs"
                  )}>
                    {positionedEvent.event.title}
                  </div>
                  {!isMobile && (
                    <div className="text-xs opacity-90">
                      {format(new Date(positionedEvent.event.startDate), 'HH:mm')} -
                      {format(new Date(positionedEvent.event.endDate), 'HH:mm')}
                    </div>
                  )}
                </div>
              ))}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
