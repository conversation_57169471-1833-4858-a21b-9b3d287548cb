
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import {
  Shield,
  Zap,
  Users,
  Database,
  Smartphone,
  Cloud,
  Lock,
  BarChart3,
  MessageSquare,
  Palette,
  Code,
  Globe
} from 'lucide-react';

const features = [
  {
    icon: Shield,
    title: 'Autenticação Completa',
    description: 'Sistema de autenticação robusto com OAuth, 2FA, verificação de email e recuperação de senha.',
    badge: 'Seguran<PERSON>',
    color: 'bg-blue-500'
  },
  {
    icon: Users,
    title: 'Sistema de Teams',
    description: 'Gerenciamento completo de equipes com roles, permissões e colaboração em tempo real.',
    badge: 'Colaboração',
    color: 'bg-green-500'
  },
  {
    icon: Database,
    title: 'Banco Type-Safe',
    description: 'Integração completa com Appwrite, schemas TypeScript e validação automática de dados.',
    badge: 'Backend',
    color: 'bg-purple-500'
  },
  {
    icon: Smartphone,
    title: 'PWA Nativo',
    description: 'Progressive Web App com instalação, notificações push e funcionamento offline.',
    badge: 'Mobile',
    color: 'bg-orange-500'
  },
  {
    icon: Zap,
    title: 'Cache Local-First',
    description: 'Sistema de cache inteligente com sincronização automática e performance otimizada.',
    badge: 'Performance',
    color: 'bg-yellow-500'
  },
  {
    icon: Cloud,
    title: 'Cloud Functions',
    description: 'Funções serverless para processamento de dados, IA e integrações externas.',
    badge: 'Serverless',
    color: 'bg-cyan-500'
  },
  {
    icon: MessageSquare,
    title: 'Chat em Tempo Real',
    description: 'Sistema de chat integrado com notificações, anexos e histórico completo.',
    badge: 'Comunicação',
    color: 'bg-pink-500'
  },
  {
    icon: BarChart3,
    title: 'Dashboard Moderno',
    description: 'Interface administrativa completa com gráficos, relatórios e métricas em tempo real.',
    badge: 'Analytics',
    color: 'bg-indigo-500'
  },
  {
    icon: Palette,
    title: 'UI/UX Moderna',
    description: 'Design system completo com shadcn/ui, dark mode e componentes responsivos.',
    badge: 'Design',
    color: 'bg-rose-500'
  },
  {
    icon: Lock,
    title: 'Segurança Avançada',
    description: 'Rate limiting, validação de dados, sanitização e proteção contra ataques.',
    badge: 'Proteção',
    color: 'bg-red-500'
  },
  {
    icon: Code,
    title: '100% TypeScript',
    description: 'Código completamente tipado com validação em tempo de compilação e IntelliSense.',
    badge: 'DX',
    color: 'bg-blue-600'
  },
  {
    icon: Globe,
    title: 'Pronto para Produção',
    description: 'Configuração completa para deploy, monitoramento, logs e escalabilidade.',
    badge: 'Deploy',
    color: 'bg-emerald-500'
  }
];

export function FeaturesSection() {
  return (
    <section className="py-16 md:py-24 bg-background">
      <div className="container mx-auto px-4 max-w-7xl">
        {/* Header */}
        <div className="text-center mb-16">
          <Badge variant="outline" className="mb-4">
            Funcionalidades
          </Badge>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
            Tudo que você precisa para criar seu SaaS
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
            Um template completo com todas as funcionalidades essenciais para acelerar o desenvolvimento
            do seu produto. Economize meses de desenvolvimento com nossa solução pronta para produção.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <Card key={index} className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                <CardHeader>
                  <div className="flex items-start justify-between mb-4">
                    <div className={`w-12 h-12 ${feature.color} rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      {feature.badge}
                    </Badge>
                  </div>
                  <CardTitle className="text-xl group-hover:text-primary transition-colors">
                    {feature.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base leading-relaxed">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <p className="text-muted-foreground mb-6">
            E muito mais funcionalidades sendo adicionadas constantemente
          </p>
          <div className="flex flex-wrap justify-center gap-2">
            <Badge variant="outline">React Router v7</Badge>
            <Badge variant="outline">Appwrite</Badge>
            <Badge variant="outline">TypeScript</Badge>
            <Badge variant="outline">shadcn/ui</Badge>
            <Badge variant="outline">Tailwind CSS</Badge>
            <Badge variant="outline">React Query</Badge>
            <Badge variant="outline">Vite</Badge>
            <Badge variant="outline">PWA</Badge>
          </div>
        </div>
      </div>
    </section>
  );
}
