'use client';

import { createContext, useContext, useEffect, useState, useMemo, useCallback, type ReactNode } from 'react';
import {
  type User,
  getCurrentUser,
  loginUser,
  registerUser,
  logoutUser,
  updateUserName,
  updateUserPhone,
  deleteCurrentSession,
  deleteUserAccount,
  type LoginCredentials,
  type RegisterData
} from '../lib/appwrite/functions/auth';
import { log } from '../lib/logger';
import { setupDebugHelpers } from '../lib/appwrite/debug';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  updateProfile: (data: { name?: string; phone?: string; password?: string }) => Promise<void>;
  deleteAccount: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

/**
 * Authentication provider component for React Router v7
 * Manages user authentication state and provides auth methods
 * Adapted from src/contexts/auth-context.tsx
 */
export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user;

  /**
   * Initialize auth state on app load
   */
  useEffect(() => {
    checkAuthState();
    // Setup debug helpers in development
    if (process.env.NODE_ENV === "development") {
      setupDebugHelpers();
    }
  }, []);

  /**
   * Check current authentication state
   */
  const checkAuthState = async () => {
    try {
      setIsLoading(true);
      const currentUser = await getCurrentUser();
      setUser(currentUser);
      log.auth('User authentication verified', { userId: currentUser.$id });
    } catch (error) {
      log.auth('Authentication check failed', { error: error instanceof Error ? error.message : 'Unknown error' });
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Login user
   */
  const login = useCallback(async (credentials: LoginCredentials) => {
    try {
      await loginUser(credentials);
      const currentUser = await getCurrentUser();
      setUser(currentUser);
      log.auth('User login successful', { userId: currentUser.$id, email: credentials.email });
    } catch (error) {
      log.error('Login failed', error instanceof Error ? error : undefined, { email: credentials.email });
      throw error;
    }
  }, []);

  /**
   * Register new user
   */
  const register = useCallback(async (data: RegisterData) => {
    try {
      await registerUser(data);
      const currentUser = await getCurrentUser();
      setUser(currentUser);
      log.auth('User registration successful', { userId: currentUser.$id, email: data.email });
    } catch (error) {
      log.error('Registration failed', error instanceof Error ? error : undefined, { email: data.email });
      throw error;
    }
  }, []);

  /**
   * Logout user
   */
  const logout = useCallback(async () => {
    try {
      const userId = user?.$id;
      await logoutUser();
      setUser(null);
      log.auth('User logout successful', { userId });
    } catch (error) {
      log.error('Logout failed', error instanceof Error ? error : undefined);
      throw error;
    }
  }, [user?.$id]);

  /**
   * Refresh user data
   */
  const refreshUser = useCallback(async () => {
    try {
      const currentUser = await getCurrentUser();
      setUser(currentUser);
      log.auth('User data refreshed', { userId: currentUser.$id });
    } catch (error) {
      log.auth('User refresh failed', { error: error instanceof Error ? error.message : 'Unknown error' });
      setUser(null);
    }
  }, []);

  /**
   * Update user profile
   */
  const handleUpdateProfile = useCallback(async (data: { name?: string; phone?: string; password?: string }) => {
    try {
      if (data.name) {
        await updateUserName(data.name);
      }
      if (data.phone && user) {
        if (!data.password) {
          throw new Error('Senha é obrigatória para atualizar o número de telefone');
        }
        await updateUserPhone(data.phone, data.password);
      }
      const currentUser = await getCurrentUser();
      setUser(currentUser);
      log.auth('User profile updated', { userId: currentUser.$id });
    } catch (error) {
      log.error('Profile update failed', error instanceof Error ? error : undefined);
      throw error;
    }
  }, [user]);

  /**
   * Delete user account (block account)
   */
  const handleDeleteAccount = useCallback(async () => {
    try {
      const userId = user?.$id;
      await deleteUserAccount();
      setUser(null);
      log.auth('User account blocked/deleted', { userId });
    } catch (error) {
      log.error('Account deletion failed', error instanceof Error ? error : undefined);
      throw error;
    }
  }, [user?.$id]);

  const value: AuthContextType = useMemo(() => ({
    user,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout,
    refreshUser,
    updateProfile: handleUpdateProfile,
    deleteAccount: handleDeleteAccount,
  }), [
    user,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout,
    refreshUser,
    handleUpdateProfile,
    handleDeleteAccount,
  ]);

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

/**
 * Hook to use auth context
 */
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

/**
 * Hook to require authentication
 * Returns auth state and can be used to check if user is authenticated
 */
export function useRequireAuth() {
  const auth = useAuth();

  useEffect(() => {
    if (!auth.isLoading && !auth.isAuthenticated) {
      // In React Router v7, you might want to redirect to login
      // This can be handled in the component using this hook
      console.warn('Authentication required');
    }
  }, [auth.isLoading, auth.isAuthenticated]);

  return auth;
}
