/**
 * Simple Notification API Hooks
 * Direct Appwrite calls with React Query
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { notifications } from '../../lib/appwrite/functions/database';
import { useAuth } from '../use-auth';
import { isCacheEnabled } from '../../lib/cache-config';
import { syncAfterMutation } from '../../lib/cache-sync';
import type { Notification } from '@/schemas/database';

/**
 * Get notifications for current user
 */
export function useNotifications() {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['notifications', user?.$id],
    queryFn: async () => {
      if (!user?.$id) return [];

      const result = await notifications.list();
      return result.documents as Notification[];
    },
    enabled: !!user?.$id,
  });
}

/**
 * Get single notification
 */
export function useNotification(id: string) {
  return useQuery({
    queryKey: ['notifications', id],
    queryFn: async () => {
      const result = await notifications.get(id);
      return result as Notification;
    },
    enabled: !!id,
  });
}

/**
 * Create notification
 */
export function useCreateNotification() {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async (data: any) => {
      const result = await notifications.create(data);
      const newNotification = result as Notification;

      // Sincronização completa: IndexedDB + preferências do usuário
      if (isCacheEnabled()) {
        await syncAfterMutation('notifications', 'create', newNotification, user?.$id);
      }

      return newNotification;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      toast.success('Notificação criada');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erro ao criar notificação');
    },
  });
}

/**
 * Mark notification as read
 */
export function useMarkAsRead() {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async (id: string) => {
      const result = await notifications.update(id, { read: true });
      return result as Notification;
    },
    onMutate: async (id: string) => {
      // Cancelar queries em andamento
      await queryClient.cancelQueries({ queryKey: ['notifications'] });

      // Snapshot do estado anterior
      const previousNotifications = queryClient.getQueryData(['notifications', user?.$id]);

      // Optimistic update
      queryClient.setQueryData(['notifications', user?.$id], (old: Notification[] | undefined) => {
        if (!old) return old;
        return old.map(notification =>
          notification.$id === id
            ? { ...notification, read: true }
            : notification
        );
      });

      return { previousNotifications };
    },
    onError: (error: any, id: string, context) => {
      // Reverter em caso de erro
      if (context?.previousNotifications) {
        queryClient.setQueryData(['notifications', user?.$id], context.previousNotifications);
      }
      toast.error(error.message || 'Erro ao marcar como lida');
    },
    onSettled: () => {
      // Revalidar para garantir consistência
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    },
  });
}

/**
 * Delete notification
 */
export function useDeleteNotification() {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async (id: string) => {
      await notifications.delete(id);

      // Sincronização completa: IndexedDB + preferências do usuário
      if (isCacheEnabled()) {
        await syncAfterMutation('notifications', 'delete', id, user?.$id);
      }
    },
    onMutate: async (id: string) => {
      // Cancelar queries em andamento
      await queryClient.cancelQueries({ queryKey: ['notifications'] });

      // Snapshot do estado anterior
      const previousNotifications = queryClient.getQueryData(['notifications', user?.$id]);

      // Optimistic update - remover da lista
      queryClient.setQueryData(['notifications', user?.$id], (old: Notification[] | undefined) => {
        if (!old) return old;
        return old.filter(notification => notification.$id !== id);
      });

      return { previousNotifications };
    },
    onSuccess: () => {
      toast.success('Notificação excluída');
    },
    onError: (error: any, id: string, context) => {
      // Reverter em caso de erro
      if (context?.previousNotifications) {
        queryClient.setQueryData(['notifications', user?.$id], context.previousNotifications);
      }
      toast.error(error.message || 'Erro ao excluir notificação');
    },
    onSettled: () => {
      // Revalidar para garantir consistência
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    },
  });
}

/**
 * Mark all notifications as read
 */
export function useMarkAllAsRead() {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async () => {
      // Buscar todas as notificações não lidas
      const result = await notifications.list();
      const unreadNotifications = result.documents.filter((n: any) => !n.read);

      // Marcar todas como lidas
      const updatePromises = unreadNotifications.map((notification: any) =>
        notifications.update(notification.$id, { read: true })
      );

      await Promise.all(updatePromises);
      return unreadNotifications.length;
    },
    onMutate: async () => {
      // Cancelar queries em andamento
      await queryClient.cancelQueries({ queryKey: ['notifications'] });

      // Snapshot do estado anterior
      const previousNotifications = queryClient.getQueryData(['notifications', user?.$id]);

      // Optimistic update - marcar todas como lidas
      queryClient.setQueryData(['notifications', user?.$id], (old: Notification[] | undefined) => {
        if (!old) return old;
        return old.map(notification => ({ ...notification, read: true }));
      });

      return { previousNotifications };
    },
    onSuccess: (count: number) => {
      if (count > 0) {
        toast.success(`${count} notificação${count !== 1 ? 'ões' : ''} marcada${count !== 1 ? 's' : ''} como lida${count !== 1 ? 's' : ''}`);
      }
    },
    onError: (error: any, variables, context) => {
      // Reverter em caso de erro
      if (context?.previousNotifications) {
        queryClient.setQueryData(['notifications', user?.$id], context.previousNotifications);
      }
      toast.error(error.message || 'Erro ao marcar notificações como lidas');
    },
    onSettled: () => {
      // Revalidar para garantir consistência
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    },
  });
}