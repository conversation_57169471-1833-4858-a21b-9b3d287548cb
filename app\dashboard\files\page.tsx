'use client';

import { useState } from 'react';
import {
  IconUpload,
  IconSearch,
  IconFilter,
  IconGrid3x3,
  IconList,
  IconPlus,
  IconFolder,
  IconFile,
  IconPhoto,
  IconFileText,
  IconDownload,
  IconTrash,
  IconEye,
  IconDotsVertical
} from '@tabler/icons-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useFiles, useDeleteFile, getFileDownload, getFileView } from '@/hooks/api/use-storage';
import { DocumentUploadModal } from '@/components/documents/document-upload-modal';
import { DocumentPreviewModal } from '@/components/documents/document-preview-modal';
import { formatFileSize, getFileTypeIcon, getFileTypeColor } from '@/lib/file-utils';
import { toast } from 'sonner';

type ViewMode = 'grid' | 'list';
type FilterType = 'all' | 'images' | 'documents' | 'others';

export default function DocumentsPage() {
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<FilterType>('all');
  const [uploadModalOpen, setUploadModalOpen] = useState(false);
  const [previewModalOpen, setPreviewModalOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<any>(null);

  const { data: filesData, isLoading } = useFiles();
  const deleteFileMutation = useDeleteFile();

  const files = filesData?.files || [];

  // Filtrar arquivos baseado na busca e filtro
  const filteredFiles = files.filter((file: any) => {
    const matchesSearch = file.name.toLowerCase().includes(searchQuery.toLowerCase());

    if (filterType === 'all') return matchesSearch;

    if (filterType === 'images') {
      return matchesSearch && file.mimeType?.startsWith('image/');
    }

    if (filterType === 'documents') {
      return matchesSearch && (
        file.mimeType?.includes('pdf') ||
        file.mimeType?.includes('document') ||
        file.mimeType?.includes('text')
      );
    }

    if (filterType === 'others') {
      return matchesSearch && !file.mimeType?.startsWith('image/') &&
             !file.mimeType?.includes('pdf') &&
             !file.mimeType?.includes('document') &&
             !file.mimeType?.includes('text');
    }

    return matchesSearch;
  });

  const handleDeleteFile = async (fileId: string, fileName: string) => {
    if (confirm(`Tem certeza que deseja excluir "${fileName}"?`)) {
      try {
        await deleteFileMutation.mutateAsync(fileId);
      } catch (error) {
        console.error('Erro ao excluir arquivo:', error);
      }
    }
  };

  const handlePreviewFile = (file: any) => {
    setSelectedFile(file);
    setPreviewModalOpen(true);
  };

  const handleDownloadFile = (fileId: string, fileName: string) => {
    const downloadUrl = getFileDownload(fileId);
    const link = document.createElement('a');
    link.href = downloadUrl.toString();
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    toast.success('Download iniciado');
  };

  const getFilterCount = (type: FilterType) => {
    if (type === 'all') return files.length;
    return files.filter((file: any) => {
      if (type === 'images') return file.mimeType?.startsWith('image/');
      if (type === 'documents') return file.mimeType?.includes('pdf') || file.mimeType?.includes('document') || file.mimeType?.includes('text');
      if (type === 'others') return !file.mimeType?.startsWith('image/') && !file.mimeType?.includes('pdf') && !file.mimeType?.includes('document') && !file.mimeType?.includes('text');
      return false;
    }).length;
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex flex-col gap-4 p-4 md:p-6 border-b md:flex-row md:items-center md:justify-between">
        <div className="min-w-0">
          <h1 className="text-xl md:text-2xl font-bold">Documentos</h1>
          <p className="text-muted-foreground text-sm md:text-base">
            Gerencie seus arquivos e documentos
          </p>
        </div>

        <div className="flex items-center gap-2 shrink-0">
          <Button onClick={() => setUploadModalOpen(true)}>
            <IconPlus className="mr-2 h-4 w-4" />
            Enviar Arquivos
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 p-4 md:p-6">
        {/* Filtros e Busca */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between mb-4">
          <div className="flex items-center gap-2">
            <div className="relative">
              <IconSearch className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Buscar arquivos..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9 w-[300px]"
              />
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as ViewMode)}>
              <TabsList>
                <TabsTrigger value="grid">
                  <IconGrid3x3 className="h-4 w-4" />
                </TabsTrigger>
                <TabsTrigger value="list">
                  <IconList className="h-4 w-4" />
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </div>

        {/* Filtros por tipo */}
        <Tabs value={filterType} onValueChange={(value) => setFilterType(value as FilterType)} className="mb-4">
          <TabsList>
            <TabsTrigger value="all">
              Todos ({getFilterCount('all')})
            </TabsTrigger>
            <TabsTrigger value="images">
              Imagens ({getFilterCount('images')})
            </TabsTrigger>
            <TabsTrigger value="documents">
              Documentos ({getFilterCount('documents')})
            </TabsTrigger>
            <TabsTrigger value="others">
              Outros ({getFilterCount('others')})
            </TabsTrigger>
          </TabsList>
        </Tabs>

        {/* Lista de Arquivos */}
        <div className="flex-1">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-muted-foreground">Carregando arquivos...</p>
              </div>
            </div>
          ) : filteredFiles.length === 0 ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <IconFolder className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">
                  {searchQuery || filterType !== 'all' ? 'Nenhum arquivo encontrado' : 'Nenhum arquivo enviado'}
                </h3>
                <p className="text-muted-foreground mb-4">
                  {searchQuery || filterType !== 'all'
                    ? 'Tente ajustar os filtros ou termo de busca'
                    : 'Comece enviando seus primeiros arquivos'
                  }
                </p>
                {!searchQuery && filterType === 'all' && (
                  <Button onClick={() => setUploadModalOpen(true)}>
                    <IconUpload className="mr-2 h-4 w-4" />
                    Enviar Primeiro Arquivo
                  </Button>
                )}
              </div>
            </div>
          ) : viewMode === 'grid' ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
              {filteredFiles.map((file: any) => (
                <FileGridCard
                  key={file.$id}
                  file={file}
                  onPreview={handlePreviewFile}
                  onDownload={handleDownloadFile}
                  onDelete={handleDeleteFile}
                />
              ))}
            </div>
          ) : (
            <div className="space-y-2">
              {filteredFiles.map((file: any) => (
                <FileListItem
                  key={file.$id}
                  file={file}
                  onPreview={handlePreviewFile}
                  onDownload={handleDownloadFile}
                  onDelete={handleDeleteFile}
                />
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Modais */}
      <DocumentUploadModal
        open={uploadModalOpen}
        onOpenChange={setUploadModalOpen}
      />

      <DocumentPreviewModal
        open={previewModalOpen}
        onOpenChange={setPreviewModalOpen}
        file={selectedFile}
      />
    </div>
  );
}

// Componente para card de arquivo no modo grid
function FileGridCard({
  file,
  onPreview,
  onDownload,
  onDelete
}: {
  file: any;
  onPreview: (file: any) => void;
  onDownload: (fileId: string, fileName: string) => void;
  onDelete: (fileId: string, fileName: string) => void;
}) {
  const FileIcon = getFileTypeIcon(file.mimeType);
  const isImage = file.mimeType?.startsWith('image/');

  return (
    <Card className="group hover:shadow-md transition-shadow cursor-pointer">
      <CardContent className="p-4">
        <div className="aspect-square mb-3 relative overflow-hidden rounded-lg bg-muted flex items-center justify-center">
          {isImage ? (
            <img
              src={getFileView(file.$id).toString()}
              alt={file.name}
              className="w-full h-full object-cover"
              onClick={() => onPreview(file)}
            />
          ) : (
            <FileIcon
              className={`h-12 w-12 ${getFileTypeColor(file.mimeType)}`}
              onClick={() => onPreview(file)}
            />
          )}

          {/* Overlay com ações */}
          <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
            <Button size="sm" variant="secondary" onClick={() => onPreview(file)}>
              <IconEye className="h-4 w-4" />
            </Button>
            <Button size="sm" variant="secondary" onClick={() => onDownload(file.$id, file.name)}>
              <IconDownload className="h-4 w-4" />
            </Button>
            <Button size="sm" variant="destructive" onClick={() => onDelete(file.$id, file.name)}>
              <IconTrash className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div>
          <p className="font-medium text-sm truncate" title={file.name}>
            {file.name}
          </p>
          <p className="text-xs text-muted-foreground">
            {formatFileSize(file.sizeOriginal)}
          </p>
          <Badge variant="outline" className="mt-1 text-xs">
            {file.mimeType?.split('/')[1]?.toUpperCase() || 'FILE'}
          </Badge>
        </div>
      </CardContent>
    </Card>
  );
}

// Componente para item de arquivo no modo lista
function FileListItem({
  file,
  onPreview,
  onDownload,
  onDelete
}: {
  file: any;
  onPreview: (file: any) => void;
  onDownload: (fileId: string, fileName: string) => void;
  onDelete: (fileId: string, fileName: string) => void;
}) {
  const FileIcon = getFileTypeIcon(file.mimeType);
  const isImage = file.mimeType?.startsWith('image/');

  return (
    <Card className="hover:shadow-sm transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-center gap-4">
          {/* Thumbnail/Icon */}
          <div className="w-12 h-12 rounded-lg bg-muted flex items-center justify-center flex-shrink-0">
            {isImage ? (
              <img
                src={getFileView(file.$id).toString()}
                alt={file.name}
                className="w-full h-full object-cover rounded-lg"
              />
            ) : (
              <FileIcon className={`h-6 w-6 ${getFileTypeColor(file.mimeType)}`} />
            )}
          </div>

          {/* Info */}
          <div className="flex-1 min-w-0">
            <p className="font-medium truncate">{file.name}</p>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <span>{formatFileSize(file.sizeOriginal)}</span>
              <span>•</span>
              <Badge variant="outline" className="text-xs">
                {file.mimeType?.split('/')[1]?.toUpperCase() || 'FILE'}
              </Badge>
              <span>•</span>
              <span>{new Date(file.$createdAt).toLocaleDateString()}</span>
            </div>
          </div>

          {/* Ações */}
          <div className="flex items-center gap-2">
            <Button size="sm" variant="ghost" onClick={() => onPreview(file)}>
              <IconEye className="h-4 w-4" />
            </Button>
            <Button size="sm" variant="ghost" onClick={() => onDownload(file.$id, file.name)}>
              <IconDownload className="h-4 w-4" />
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button size="sm" variant="ghost">
                  <IconDotsVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onPreview(file)}>
                  <IconEye className="mr-2 h-4 w-4" />
                  Visualizar
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onDownload(file.$id, file.name)}>
                  <IconDownload className="mr-2 h-4 w-4" />
                  Download
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onDelete(file.$id, file.name)}
                  className="text-destructive"
                >
                  <IconTrash className="mr-2 h-4 w-4" />
                  Excluir
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
