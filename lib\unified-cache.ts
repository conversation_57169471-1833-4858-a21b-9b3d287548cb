/**
 * Sistema de Cache Unificado
 * Integração transparente com React Query usando IndexedDB
 * Estratégia fixa: local-first
 */

import { getCacheConfig, isCacheEnabled, getDefaultTTL } from './cache-config';

// ============================================================================
// TIPOS
// ============================================================================

export interface CacheItem<T = any> {
  data: T;
  timestamp: number;
  ttl: number;
  queryKey: readonly unknown[];
}

export interface CacheValidationResult {
  isValid: boolean;
  shouldInvalidate: boolean;
  reason?: string;
}

// ============================================================================
// INDEXEDDB MANAGER
// ============================================================================

class IndexedDBManager {
  private db: IDBDatabase | null = null;
  private initPromise: Promise<IDBDatabase> | null = null;

  private async init(): Promise<IDBDatabase> {
    if (this.db) return this.db;
    if (this.initPromise) return this.initPromise;

    const config = getCacheConfig();

    this.initPromise = new Promise((resolve, reject) => {
      const request = indexedDB.open(config.dbName, config.version);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        console.log(`✅ Unified Cache conectado: ${config.dbName} (v${config.version})`);
        resolve(this.db);
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        console.log(`🔄 Atualizando Unified Cache: ${config.dbName} (v${config.version})`);

        // Create cache store
        if (!db.objectStoreNames.contains('cache')) {
          console.log('📦 Criando store: cache');
          db.createObjectStore('cache');
        }
      };

      request.onblocked = () => {
        console.warn('⚠️ Unified Cache upgrade bloqueado. Feche outras abas do aplicativo.');
      };
    });

    return this.initPromise;
  }



  async set<T>(key: string, value: CacheItem<T>): Promise<void> {
    if (!isCacheEnabled()) return;

    try {
      const db = await this.init();
      const transaction = db.transaction(['cache'], 'readwrite');
      const store = transaction.objectStore('cache');

      await new Promise<void>((resolve, reject) => {
        const request = store.put(value, key);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.warn('Erro ao salvar no cache:', error);
    }
  }

  async get<T>(key: string): Promise<CacheItem<T> | null> {
    if (!isCacheEnabled()) return null;

    try {
      const db = await this.init();
      const transaction = db.transaction(['cache'], 'readonly');
      const store = transaction.objectStore('cache');

      return new Promise<CacheItem<T> | null>((resolve, reject) => {
        const request = store.get(key);
        request.onsuccess = () => {
          const result = request.result as CacheItem<T> | undefined;
          resolve(result || null);
        };
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.warn('Erro ao ler do cache:', error);
      return null;
    }
  }

  async delete(key: string): Promise<void> {
    if (!isCacheEnabled()) return;

    try {
      const db = await this.init();
      const transaction = db.transaction(['cache'], 'readwrite');
      const store = transaction.objectStore('cache');

      await new Promise<void>((resolve, reject) => {
        const request = store.delete(key);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.warn('Erro ao deletar do cache:', error);
    }
  }

  async clear(): Promise<void> {
    if (!isCacheEnabled()) return;

    try {
      const db = await this.init();
      const transaction = db.transaction(['cache'], 'readwrite');
      const store = transaction.objectStore('cache');

      await new Promise<void>((resolve, reject) => {
        const request = store.clear();
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.warn('Erro ao limpar cache:', error);
    }
  }

  async getAllKeys(): Promise<string[]> {
    if (!isCacheEnabled()) return [];

    try {
      const db = await this.init();
      const transaction = db.transaction(['cache'], 'readonly');
      const store = transaction.objectStore('cache');

      return new Promise<string[]>((resolve, reject) => {
        const request = store.getAllKeys();
        request.onsuccess = () => resolve(request.result as string[]);
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.warn('Erro ao obter chaves do cache:', error);
      return [];
    }
  }
}

// ============================================================================
// INSTÂNCIA SINGLETON
// ============================================================================

const cacheManager = new IndexedDBManager();

// ============================================================================
// FUNÇÕES PÚBLICAS CRUD
// ============================================================================

/**
 * Gera chave de cache a partir da queryKey do React Query
 */
export function generateCacheKey(queryKey: readonly unknown[]): string {
  return JSON.stringify(queryKey);
}

/**
 * Verifica se um item do cache é válido
 */
export function isCacheItemValid<T>(item: CacheItem<T>): boolean {
  const now = Date.now();
  return (now - item.timestamp) < item.ttl;
}

/**
 * CREATE/SET: Armazenar dados no cache
 */
export async function setCache<T>(
  queryKey: readonly unknown[],
  data: T,
  ttl?: number
): Promise<void> {
  const key = generateCacheKey(queryKey);
  const cacheItem: CacheItem<T> = {
    data,
    timestamp: Date.now(),
    ttl: ttl || getDefaultTTL(),
    queryKey,
  };

  await cacheManager.set(key, cacheItem);
}

/**
 * READ/GET: Recuperar dados do cache
 */
export async function getCache<T>(
  queryKey: readonly unknown[]
): Promise<T | null> {
  const key = generateCacheKey(queryKey);
  const item = await cacheManager.get<T>(key);

  if (!item) return null;

  // Verificar se ainda é válido
  if (!isCacheItemValid(item)) {
    await cacheManager.delete(key);
    return null;
  }

  return item.data;
}

/**
 * UPDATE: Atualizar dados existentes
 */
export async function updateCache<T>(
  queryKey: readonly unknown[],
  data: T,
  ttl?: number
): Promise<void> {
  // Update é igual ao set para este sistema simples
  await setCache(queryKey, data, ttl);
}

/**
 * DELETE: Remover dados específicos
 */
export async function deleteCache(queryKey: readonly unknown[]): Promise<void> {
  const key = generateCacheKey(queryKey);
  await cacheManager.delete(key);
}



/**
 * CLEAR: Limpar todo o cache
 */
export async function clearCache(): Promise<void> {
  await cacheManager.clear();
}

/**
 * LIST: Obter todas as chaves do cache
 */
export async function getCacheKeys(): Promise<string[]> {
  return await cacheManager.getAllKeys();
}

// ============================================================================
// VALIDAÇÃO DE CACHE
// ============================================================================

/**
 * Valida se o cache deve ser invalidado comparando valores
 */
export function validateCache<T>(
  cachedValue: T,
  newValue: T
): CacheValidationResult {
  // Comparação simples - pode ser expandida conforme necessário
  if (!cachedValue) {
    return {
      isValid: false,
      shouldInvalidate: true,
      reason: 'No cached value'
    };
  }

  // Se os valores são diferentes, invalidar
  const isDifferent = JSON.stringify(cachedValue) !== JSON.stringify(newValue);

  return {
    isValid: !isDifferent,
    shouldInvalidate: isDifferent,
    reason: isDifferent ? 'Values differ' : 'Values match'
  };
}

// ============================================================================
// UTILITÁRIOS
// ============================================================================

/**
 * Verifica se IndexedDB está disponível
 */
export function isIndexedDBAvailable(): boolean {
  return typeof window !== 'undefined' && 'indexedDB' in window;
}

/**
 * Obtém estatísticas do cache
 */
export async function getCacheStats(): Promise<{
  totalKeys: number;
  validItems: number;
  expiredItems: number;
}> {
  const keys = await getCacheKeys();
  let validItems = 0;
  let expiredItems = 0;

  for (const key of keys) {
    try {
      const queryKey = JSON.parse(key) as readonly unknown[];
      const item = await cacheManager.get(key);

      if (item) {
        if (isCacheItemValid(item)) {
          validItems++;
        } else {
          expiredItems++;
        }
      }
    } catch (error) {
      // Chave inválida, contar como expirada
      expiredItems++;
    }
  }

  return {
    totalKeys: keys.length,
    validItems,
    expiredItems,
  };
}

/**
 * Limpa itens expirados do cache
 */
export async function cleanupExpiredCache(): Promise<number> {
  const keys = await getCacheKeys();
  let cleanedCount = 0;

  for (const key of keys) {
    try {
      const item = await cacheManager.get(key);

      if (item && !isCacheItemValid(item)) {
        await cacheManager.delete(key);
        cleanedCount++;
      }
    } catch (error) {
      // Erro ao processar item, deletar
      await cacheManager.delete(key);
      cleanedCount++;
    }
  }

  return cleanedCount;
}
