/**
 * Project Context Generator for Gemini AI
 * Gera contexto inteligente do projeto para melhorar respostas do Gemini
 */

import type { ProjectContext } from '@/schemas/chat';

/**
 * Informações base do projeto (configuráveis)
 */
const PROJECT_INFO = {
  name: process.env.NEXT_PUBLIC_APP_NAME || 'Appwrite Template',
  version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
  description: 'Template completo com Appwrite, React Router v7, TypeScript e shadcn/ui',
  repository: 'https://github.com/your-repo/template',
  documentation: '/docs',
};

/**
 * Tecnologias e ferramentas utilizadas
 */
const TECHNOLOGIES = [
  // Frontend
  'React 18', 'TypeScript', 'React Router v7', 'Vite',
  'Tailwind CSS', 'shadcn/ui', 'Lucide Icons',
  
  // Backend & Database
  'Appwrite', 'Appwrite Database', 'Appwrite Auth', 'Appwrite Storage',
  'Appwrite Teams', 'Appwrite Realtime',
  
  // State Management & Data
  'React Query (TanStack Query)', 'Valtio', 'IndexedDB',
  'Local-first Architecture',
  
  // AI & Processing
  'Google Gemini AI', 'Cloud Functions',
  
  // Development Tools
  'ESLint', 'Prettier', 'Yarn', 'PWA',
  
  // Testing & Quality
  'TypeScript Strict Mode', 'Type Safety',
];

/**
 * Funcionalidades principais do sistema
 */
const FEATURES = [
  // Autenticação
  'Sistema de autenticação completo (login, registro, OAuth)',
  'Verificação de email e recuperação de senha',
  'Perfis públicos de usuários',
  
  // Teams & Colaboração
  'Sistema de teams com convites',
  'Chat de time em tempo real',
  'Gerenciamento de membros',
  
  // Dados & CRUD
  'Sistema de clientes com CRUD completo',
  'Upload de arquivos e imagens',
  'Exportação de dados (CSV, Excel, JSON, PDF)',
  'Importação inteligente com IA',
  
  // Interface & UX
  'Dashboard responsivo e moderno',
  'Componentes reutilizáveis com shadcn/ui',
  'Tema escuro/claro',
  'PWA com suporte offline',
  
  // Integrações & IA
  'Chat com assistente IA (Gemini)',
  'Processamento de documentos com IA',
  'Notificações em tempo real',
  
  // Arquitetura
  'Cache local-first com IndexedDB',
  'Sincronização automática',
  'Offline-first approach',
];

/**
 * Estrutura de arquivos e organização
 */
const PROJECT_STRUCTURE = {
  'app/': 'Código principal da aplicação',
  'app/components/': 'Componentes React reutilizáveis',
  'app/hooks/': 'React hooks customizados',
  'app/lib/': 'Bibliotecas e utilitários',
  'app/lib/appwrite/': 'Configurações e funções do Appwrite',
  'app/routes/': 'Páginas e rotas da aplicação',
  'app/types/': 'Definições TypeScript',
  'docs/': 'Documentação completa do projeto',
  'scripts/': 'Scripts de automação e setup',
  'public/': 'Arquivos estáticos',
};

/**
 * Padrões e convenções do projeto
 */
const CONVENTIONS = [
  'Componentes sempre em PascalCase',
  'Hooks sempre começam com "use"',
  'Tipos TypeScript sempre com interface',
  'Funções async/await ao invés de Promises',
  'Preferência por componentes funcionais',
  'Uso de shadcn/ui para componentes base',
  'Cache local-first com React Query',
  'Validação com Zod schemas',
  'Formulários com react-hook-form',
];

/**
 * Gerar contexto base do projeto
 */
export function generateProjectContext(): ProjectContext {
  return {
    name: PROJECT_INFO.name,
    description: PROJECT_INFO.description,
    technologies: TECHNOLOGIES,
    features: FEATURES,
    currentPhase: 'Desenvolvimento ativo',
    goals: [
      'Fornecer template completo para projetos Appwrite',
      'Demonstrar melhores práticas de desenvolvimento',
      'Facilitar prototipagem rápida',
      'Servir como base para projetos reais',
    ],
    constraints: [
      'Manter compatibilidade com Appwrite Cloud',
      'Seguir padrões de acessibilidade',
      'Otimizar para performance',
      'Manter type safety 100%',
    ],
    lastUpdated: new Date().toISOString(),
  };
}

/**
 * Gerar contexto específico baseado na página atual
 */
export function generatePageContext(pathname: string): string {
  const pageContexts: Record<string, string> = {
    '/dashboard': 'Página principal do dashboard com visão geral do sistema',
    '/dashboard/clients': 'Gerenciamento de clientes com CRUD completo',
    '/dashboard/teams': 'Gerenciamento de teams e colaboração',
    '/dashboard/team-chat': 'Chat de time em tempo real',
    '/dashboard/profile': 'Perfil do usuário e configurações',
    '/dashboard/settings': 'Configurações gerais da aplicação',
    '/auth/login': 'Página de autenticação e login',
    '/auth/register': 'Página de registro de novos usuários',
  };

  return pageContexts[pathname] || `Página: ${pathname}`;
}

/**
 * Gerar contexto completo para o Gemini
 */
export function generateFullContext(pathname?: string): string {
  const projectContext = generateProjectContext();
  const pageContext = pathname ? generatePageContext(pathname) : '';
  
  return `
PROJETO: ${projectContext.name}
DESCRIÇÃO: ${projectContext.description}

TECNOLOGIAS PRINCIPAIS:
${projectContext.technologies.slice(0, 10).map(tech => `- ${tech}`).join('\n')}

FUNCIONALIDADES PRINCIPAIS:
${projectContext.features.slice(0, 8).map(feature => `- ${feature}`).join('\n')}

ESTRUTURA DO PROJETO:
${Object.entries(PROJECT_STRUCTURE).map(([path, desc]) => `- ${path}: ${desc}`).join('\n')}

CONVENÇÕES E PADRÕES:
${CONVENTIONS.slice(0, 6).map(conv => `- ${conv}`).join('\n')}

${pageContext ? `CONTEXTO ATUAL: ${pageContext}` : ''}

INSTRUÇÕES PARA O ASSISTENTE:
- Você é um assistente especializado neste projeto Appwrite + React
- Forneça respostas práticas e específicas para este template
- Use exemplos de código baseados na estrutura existente
- Considere as tecnologias e padrões já implementados
- Seja preciso sobre caminhos de arquivos e imports
- Sugira soluções que seguem as convenções do projeto
`.trim();
}

/**
 * Gerar contexto resumido para conversas longas
 */
export function generateSummaryContext(): string {
  return `
PROJETO: Template Appwrite + React + TypeScript
STACK: React Router v7, shadcn/ui, Appwrite, Gemini AI
FUNCIONALIDADES: Auth, Teams, Chat, CRUD, IA, PWA
PADRÕES: TypeScript strict, Local-first, React Query
`.trim();
}

/**
 * Detectar tipo de pergunta e sugerir contexto apropriado
 */
export function detectQuestionType(message: string): {
  type: 'code' | 'architecture' | 'feature' | 'bug' | 'general';
  suggestedContext: 'full' | 'summary' | 'page';
} {
  const lowerMessage = message.toLowerCase();
  
  if (lowerMessage.includes('código') || lowerMessage.includes('implementar') || lowerMessage.includes('como fazer')) {
    return { type: 'code', suggestedContext: 'full' };
  }
  
  if (lowerMessage.includes('arquitetura') || lowerMessage.includes('estrutura') || lowerMessage.includes('organização')) {
    return { type: 'architecture', suggestedContext: 'full' };
  }
  
  if (lowerMessage.includes('funcionalidade') || lowerMessage.includes('feature') || lowerMessage.includes('adicionar')) {
    return { type: 'feature', suggestedContext: 'full' };
  }
  
  if (lowerMessage.includes('erro') || lowerMessage.includes('bug') || lowerMessage.includes('problema')) {
    return { type: 'bug', suggestedContext: 'page' };
  }
  
  return { type: 'general', suggestedContext: 'summary' };
}
