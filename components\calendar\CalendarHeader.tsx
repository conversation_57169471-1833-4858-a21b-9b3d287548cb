/**
 * Calendar Header Component
 * Header com navegação e controles do calendário
 */

import React from 'react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useSnapshot } from 'valtio';
import { 
  ChevronLeft, 
  ChevronRight, 
  Calendar as CalendarIcon,
  Plus,
  Search,
  Filter,
  Settings,
  Download,
  Upload,
  Menu,
  Grid3X3,
  List,
  Columns,
  Square
} from 'lucide-react';
import { cn } from '../../lib/utils';
import { useCalendarState } from '../../lib/stores/calendar-state';
import { useCalendarModals } from '../../lib/stores/calendar-modals';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Badge } from '../ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '../ui/tooltip';

// ============================================================================
// COMPONENTE
// ============================================================================

export function CalendarHeader() {
  const { store: calendarState, actions: calendarActions } = useCalendarState();
  const { actions: modalsActions } = useCalendarModals();
  const calendarSnapshot = useSnapshot(calendarState);

  // Formatação da data atual
  const currentDateFormatted = React.useMemo(() => {
    const date = calendarSnapshot.view.startDate;
    const view = calendarSnapshot.view.view;

    switch (view) {
      case 'day':
        return format(date, "EEEE, dd 'de' MMMM 'de' yyyy", { locale: ptBR });
      case 'week':
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay() + 1); // Segunda-feira
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6); // Domingo
        return `${format(weekStart, 'dd/MM', { locale: ptBR })} - ${format(weekEnd, 'dd/MM/yyyy', { locale: ptBR })}`;
      case 'month':
        return format(date, "MMMM 'de' yyyy", { locale: ptBR });
      case 'agenda':
        return 'Agenda';
      default:
        return format(date, "MMMM 'de' yyyy", { locale: ptBR });
    }
  }, [calendarSnapshot.view.startDate, calendarSnapshot.view.view]);

  // Ícones das visualizações
  const viewIcons = {
    month: Grid3X3,
    week: Columns,
    day: Square,
    agenda: List,
  };

  // Contagem de filtros ativos
  const activeFiltersCount = React.useMemo(() => {
    const filters = calendarSnapshot.filters;
    let count = 0;
    
    if (filters.search) count++;
    if (filters.categories && filters.categories.length > 0) count++;
    if (filters.types && filters.types.length > 0) count++;
    if (filters.priorities && filters.priorities.length > 0) count++;
    if (filters.status && filters.status.length > 0) count++;
    if (filters.attendees && filters.attendees.length > 0) count++;
    if (filters.tags && filters.tags.length > 0) count++;
    if (filters.dateRange) count++;
    
    return count;
  }, [calendarSnapshot.filters]);

  return (
    <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex items-center justify-between p-4">
        {/* Left Section - Navigation */}
        <div className="flex items-center gap-4">
          {/* Sidebar Toggle */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={calendarActions.toggleSidebar}
                className="lg:hidden"
              >
                <Menu className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              {calendarSnapshot.ui.sidebarOpen ? 'Ocultar sidebar' : 'Mostrar sidebar'}
            </TooltipContent>
          </Tooltip>

          {/* Date Navigation */}
          <div className="flex items-center gap-2">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={calendarActions.navigateToToday}
                >
                  Hoje
                </Button>
              </TooltipTrigger>
              <TooltipContent>Ir para hoje</TooltipContent>
            </Tooltip>

            <div className="flex items-center">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={calendarActions.navigatePrevious}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Anterior</TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={calendarActions.navigateNext}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Próximo</TooltipContent>
              </Tooltip>
            </div>
          </div>

          {/* Current Date */}
          <div className="hidden sm:flex items-center gap-2">
            <CalendarIcon className="h-4 w-4 text-muted-foreground" />
            <h1 className="text-lg font-semibold capitalize">
              {currentDateFormatted}
            </h1>
          </div>
        </div>

        {/* Center Section - Search */}
        <div className="flex-1 max-w-md mx-4 hidden md:block">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Buscar eventos..."
              value={calendarSnapshot.filters.search || ''}
              onChange={(e) => calendarActions.setSearchFilter(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Right Section - Actions */}
        <div className="flex items-center gap-2">
          {/* View Selector */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="gap-2">
                {React.createElement(viewIcons[calendarSnapshot.view.view], { 
                  className: "h-4 w-4" 
                })}
                <span className="hidden sm:inline capitalize">
                  {calendarSnapshot.view.view === 'month' ? 'Mês' :
                   calendarSnapshot.view.view === 'week' ? 'Semana' :
                   calendarSnapshot.view.view === 'day' ? 'Dia' : 'Agenda'}
                </span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => calendarActions.setView('month')}>
                <Grid3X3 className="h-4 w-4 mr-2" />
                Mês
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => calendarActions.setView('week')}>
                <Columns className="h-4 w-4 mr-2" />
                Semana
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => calendarActions.setView('day')}>
                <Square className="h-4 w-4 mr-2" />
                Dia
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => calendarActions.setView('agenda')}>
                <List className="h-4 w-4 mr-2" />
                Agenda
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Filters */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant={activeFiltersCount > 0 ? "default" : "outline"}
                size="sm"
                onClick={modalsActions.openFilters}
                className="relative gap-2"
              >
                <Filter className="h-4 w-4" />
                <span className="hidden sm:inline">Filtros</span>
                {activeFiltersCount > 0 && (
                  <Badge 
                    variant="secondary" 
                    className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs"
                  >
                    {activeFiltersCount}
                  </Badge>
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              {activeFiltersCount > 0 
                ? `${activeFiltersCount} filtro(s) ativo(s)` 
                : 'Filtrar eventos'
              }
            </TooltipContent>
          </Tooltip>

          {/* Create Event */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={() => modalsActions.openCreateEvent()}
                size="sm"
                className="gap-2"
              >
                <Plus className="h-4 w-4" />
                <span className="hidden sm:inline">Novo Evento</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>Criar novo evento</TooltipContent>
          </Tooltip>

          {/* More Actions */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={modalsActions.openSettings}>
                <Settings className="h-4 w-4 mr-2" />
                Configurações
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={modalsActions.openExport}>
                <Download className="h-4 w-4 mr-2" />
                Exportar
              </DropdownMenuItem>
              <DropdownMenuItem onClick={modalsActions.openImport}>
                <Upload className="h-4 w-4 mr-2" />
                Importar
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => modalsActions.openSearch()}>
                <Search className="h-4 w-4 mr-2" />
                Busca Avançada
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Mobile Search */}
      <div className="px-4 pb-4 md:hidden">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Buscar eventos..."
            value={calendarSnapshot.filters.search || ''}
            onChange={(e) => calendarActions.setSearchFilter(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Active Filters Display */}
      {activeFiltersCount > 0 && (
        <div className="px-4 pb-4">
          <div className="flex items-center gap-2 flex-wrap">
            <span className="text-sm text-muted-foreground">Filtros ativos:</span>
            {calendarSnapshot.filters.search && (
              <Badge variant="secondary" className="gap-1">
                Busca: "{calendarSnapshot.filters.search}"
                <button
                  onClick={() => calendarActions.setSearchFilter('')}
                  className="ml-1 hover:bg-muted-foreground/20 rounded-full p-0.5"
                >
                  ×
                </button>
              </Badge>
            )}
            {calendarSnapshot.filters.categories && calendarSnapshot.filters.categories.length > 0 && (
              <Badge variant="secondary" className="gap-1">
                {calendarSnapshot.filters.categories.length} categoria(s)
                <button
                  onClick={() => calendarActions.setCategoryFilter([])}
                  className="ml-1 hover:bg-muted-foreground/20 rounded-full p-0.5"
                >
                  ×
                </button>
              </Badge>
            )}
            {activeFiltersCount > 2 && (
              <Badge variant="outline">
                +{activeFiltersCount - 2} mais
              </Badge>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={calendarActions.clearFilters}
              className="h-6 px-2 text-xs"
            >
              Limpar todos
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
