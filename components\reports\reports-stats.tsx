import { IconTrendingDown, IconTrendingUp, IconUsers, IconCreditCard, IconChartBar, IconFileText, IconCalendar, IconLayoutKanban, IconFolder } from "@tabler/icons-react"
import { Badge } from "../ui/badge"
import {
  Card,
  CardAction,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "../ui/card"

interface ReportsStatsProps {
  data?: {
    totalReports: number
    generatedThisMonth: number
    totalExports: number
    averageGenerationTime: number
    mostUsedFormat: string
    totalDataPoints: number
    // Novas métricas baseadas em dados reais
    totalClients: number
    totalActivities: number
    totalTeams: number
    totalEvents: number
    totalKanbanBoards: number
    totalFiles: number
    activeClientsThisMonth: number
    completedTasksThisMonth: number
    upcomingEvents: number
  }
}

export function ReportsStats({ data }: ReportsStatsProps) {
  // Usar dados reais ou fallback para demonstração
  const stats = data || {
    totalReports: 0,
    generatedThisMonth: 0,
    totalExports: 0,
    averageGenerationTime: 0,
    mostUsedFormat: "PDF",
    totalDataPoints: 0,
    totalClients: 0,
    totalActivities: 0,
    totalTeams: 0,
    totalEvents: 0,
    totalKanbanBoards: 0,
    totalFiles: 0,
    activeClientsThisMonth: 0,
    completedTasksThisMonth: 0,
    upcomingEvents: 0
  }

  // Calcular crescimento baseado em dados reais
  const monthlyGrowth = data ? Math.max(0, (data.generatedThisMonth / Math.max(1, data.totalReports)) * 100) : 0
  const exportGrowth = data ? Math.max(-20, Math.min(50, (data.totalExports / Math.max(1, data.totalReports)) * 10)) : 0
  const dataGrowth = data ? Math.max(0, Math.min(100, (data.totalDataPoints / 1000) * 5)) : 0

  return (
    <div className="space-y-6">
      {/* Cards principais de relatórios */}
      <div className="*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs @xl/main:grid-cols-2 @5xl/main:grid-cols-4">

      {/* Total de Relatórios */}
      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Total de Relatórios</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
            {stats.totalReports.toLocaleString()}
          </CardTitle>
          <CardAction>
            <Badge variant="outline">
              <IconChartBar className="size-3" />
              Todos os tipos
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            Relatórios disponíveis <IconFileText className="size-4" />
          </div>
          <div className="text-muted-foreground">
            Incluindo histórico completo
          </div>
        </CardFooter>
      </Card>

      {/* Gerados Este Mês */}
      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Gerados Este Mês</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
            {stats.generatedThisMonth}
          </CardTitle>
          <CardAction>
            <Badge variant="outline">
              <IconTrendingUp className="size-3" />
              +{monthlyGrowth.toFixed(1)}%
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            Crescimento mensal <IconTrendingUp className="size-4" />
          </div>
          <div className="text-muted-foreground">
            Comparado ao mês anterior
          </div>
        </CardFooter>
      </Card>

      {/* Total de Exports */}
      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Total de Exports</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
            {stats.totalExports}
          </CardTitle>
          <CardAction>
            <Badge variant="outline">
              {exportGrowth >= 0 ? <IconTrendingUp className="size-3" /> : <IconTrendingDown className="size-3" />}
              {exportGrowth >= 0 ? '+' : ''}{exportGrowth.toFixed(1)}%
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            Formato mais usado: {stats.mostUsedFormat} <IconFileText className="size-4" />
          </div>
          <div className="text-muted-foreground">
            Downloads realizados
          </div>
        </CardFooter>
      </Card>

      {/* Pontos de Dados */}
      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Pontos de Dados</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
            {(stats.totalDataPoints / 1000).toFixed(1)}k
          </CardTitle>
          <CardAction>
            <Badge variant="outline">
              <IconTrendingUp className="size-3" />
              +{dataGrowth.toFixed(1)}%
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            Dados processados <IconChartBar className="size-4" />
          </div>
          <div className="text-muted-foreground">
            Tempo médio: {stats.averageGenerationTime}s
          </div>
        </CardFooter>
      </Card>

      </div>

      {/* Cards de dados reais do sistema */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Dados do Sistema</h3>
        <div className="*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs @xl/main:grid-cols-2 @5xl/main:grid-cols-4">

          {/* Clientes */}
          <Card className="@container/card">
            <CardHeader>
              <CardDescription>Clientes</CardDescription>
              <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
                {stats.totalClients}
              </CardTitle>
              <CardAction>
                <Badge variant="outline">
                  <IconUsers className="size-3" />
                  {stats.activeClientsThisMonth} ativos
                </Badge>
              </CardAction>
            </CardHeader>
            <CardFooter className="flex-col items-start gap-1.5 text-sm">
              <div className="line-clamp-1 flex gap-2 font-medium">
                Novos este mês: {stats.activeClientsThisMonth} <IconTrendingUp className="size-4" />
              </div>
              <div className="text-muted-foreground">
                Total de clientes cadastrados
              </div>
            </CardFooter>
          </Card>

          {/* Atividades */}
          <Card className="@container/card">
            <CardHeader>
              <CardDescription>Atividades</CardDescription>
              <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
                {stats.totalActivities}
              </CardTitle>
              <CardAction>
                <Badge variant="outline">
                  <IconChartBar className="size-3" />
                  Sistema
                </Badge>
              </CardAction>
            </CardHeader>
            <CardFooter className="flex-col items-start gap-1.5 text-sm">
              <div className="line-clamp-1 flex gap-2 font-medium">
                Logs de atividade <IconFileText className="size-4" />
              </div>
              <div className="text-muted-foreground">
                Registros do sistema
              </div>
            </CardFooter>
          </Card>

          {/* Eventos */}
          <Card className="@container/card">
            <CardHeader>
              <CardDescription>Eventos</CardDescription>
              <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
                {stats.totalEvents}
              </CardTitle>
              <CardAction>
                <Badge variant="outline">
                  <IconCalendar className="size-3" />
                  {stats.upcomingEvents} próximos
                </Badge>
              </CardAction>
            </CardHeader>
            <CardFooter className="flex-col items-start gap-1.5 text-sm">
              <div className="line-clamp-1 flex gap-2 font-medium">
                Próximos 30 dias: {stats.upcomingEvents} <IconCalendar className="size-4" />
              </div>
              <div className="text-muted-foreground">
                Eventos do calendário
              </div>
            </CardFooter>
          </Card>

          {/* Kanban */}
          <Card className="@container/card">
            <CardHeader>
              <CardDescription>Kanban</CardDescription>
              <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
                {stats.totalKanbanBoards}
              </CardTitle>
              <CardAction>
                <Badge variant="outline">
                  <IconLayoutKanban className="size-3" />
                  {stats.completedTasksThisMonth} concluídas
                </Badge>
              </CardAction>
            </CardHeader>
            <CardFooter className="flex-col items-start gap-1.5 text-sm">
              <div className="line-clamp-1 flex gap-2 font-medium">
                Tarefas concluídas este mês <IconTrendingUp className="size-4" />
              </div>
              <div className="text-muted-foreground">
                Boards de projeto
              </div>
            </CardFooter>
          </Card>

        </div>
      </div>

      {/* Cards adicionais */}
      <div>
        <div className="*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs @xl/main:grid-cols-2 @5xl/main:grid-cols-3">

          {/* Teams */}
          <Card className="@container/card">
            <CardHeader>
              <CardDescription>Equipes</CardDescription>
              <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
                {stats.totalTeams}
              </CardTitle>
              <CardAction>
                <Badge variant="outline">
                  <IconUsers className="size-3" />
                  Ativas
                </Badge>
              </CardAction>
            </CardHeader>
            <CardFooter className="flex-col items-start gap-1.5 text-sm">
              <div className="line-clamp-1 flex gap-2 font-medium">
                Equipes colaborativas <IconUsers className="size-4" />
              </div>
              <div className="text-muted-foreground">
                Times cadastrados
              </div>
            </CardFooter>
          </Card>

          {/* Arquivos */}
          <Card className="@container/card">
            <CardHeader>
              <CardDescription>Arquivos</CardDescription>
              <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
                {stats.totalFiles}
              </CardTitle>
              <CardAction>
                <Badge variant="outline">
                  <IconFolder className="size-3" />
                  Documentos
                </Badge>
              </CardAction>
            </CardHeader>
            <CardFooter className="flex-col items-start gap-1.5 text-sm">
              <div className="line-clamp-1 flex gap-2 font-medium">
                Arquivos armazenados <IconFolder className="size-4" />
              </div>
              <div className="text-muted-foreground">
                Storage do sistema
              </div>
            </CardFooter>
          </Card>

          {/* Performance Geral */}
          <Card className="@container/card">
            <CardHeader>
              <CardDescription>Performance</CardDescription>
              <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
                {Math.round((stats.totalDataPoints / 100) * 10)}%
              </CardTitle>
              <CardAction>
                <Badge variant="outline">
                  <IconChartBar className="size-3" />
                  Score
                </Badge>
              </CardAction>
            </CardHeader>
            <CardFooter className="flex-col items-start gap-1.5 text-sm">
              <div className="line-clamp-1 flex gap-2 font-medium">
                Baseado em dados reais <IconChartBar className="size-4" />
              </div>
              <div className="text-muted-foreground">
                Índice de atividade
              </div>
            </CardFooter>
          </Card>

        </div>
      </div>

    </div>
  )
}
