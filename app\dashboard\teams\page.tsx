'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Plus, Users, UserPlus, Crown, Settings } from 'lucide-react';
import { PlanLimitationAlert } from '@/components/upgrade-alert';
import { TeamStats } from '@/components/teams/team-stats';
import { TeamMembersTable } from '@/components/teams/team-members-table';
import { TeamSelector } from '@/components/teams/team-selector';
import { TeamFormModal } from '@/components/teams/team-form-modal';
import { TeamInviteModal } from '@/components/teams/team-invite-modal';
import { TeamMemberEditModal } from '@/components/teams/team-member-edit-modal';
import { TeamMemberRemoveModal } from '@/components/teams/team-member-remove-modal';
import { TeamMemberViewModal } from '@/components/teams/team-member-view-modal';
import { TeamUpgradeModal } from '@/components/teams/team-upgrade-modal';
import { TeamPreferencesModal } from '@/components/teams/team-preferences-modal';
import { useTeams, useTeamMembers } from '@/hooks/use-api';
import { useAuth } from '@/hooks/use-auth';
import { canAddTeamMember, canCreateTeam, hasTeamAccess } from '@/lib/plan-limits';
import type { TeamMembership } from '@/schemas/teams';

export default function TeamsPage() {
  const { user } = useAuth();

  // Modal states
  const [isTeamFormModalOpen, setIsTeamFormModalOpen] = useState(false);
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const [isEditMemberModalOpen, setIsEditMemberModalOpen] = useState(false);
  const [isRemoveMemberModalOpen, setIsRemoveMemberModalOpen] = useState(false);
  const [isUpgradeModalOpen, setIsUpgradeModalOpen] = useState(false);
  const [isPreferencesModalOpen, setIsPreferencesModalOpen] = useState(false);

  const [isViewMemberModalOpen, setIsViewMemberModalOpen] = useState(false);

  // Selected data
  const [selectedTeamId, setSelectedTeamId] = useState<string>('');
  const [selectedMember, setSelectedMember] = useState<TeamMembership | null>(null);
  const [teamFormMode, setTeamFormMode] = useState<'create' | 'edit'>('create');
  const [upgradeFeature, setUpgradeFeature] = useState<string>('');

  // Fetch teams and members data
  const { data: teams = [] } = useTeams();
  const { data: members = [], isLoading: membersLoading } = useTeamMembers(selectedTeamId);

  // Team limits and permissions
  const currentTeamsCount = teams.length;
  const teamCreationCheck = user ? canCreateTeam(user, currentTeamsCount) : { allowed: false };
  const hasTeamAccessCheck = user ? hasTeamAccess(user) : false;
  const currentTeam = teams.find(t => t.$id === selectedTeamId);
  const isOwner = true; // TODO: implementar verificação de permissões

  const handleExportClick = () => {
    // Exportar dados dos membros do time selecionado
    if (!selectedTeamId) return;
    // TODO: implementar exportação específica de membros do time
    console.log('Exportar membros do time:', selectedTeamId);
  };

  // Auto-select first team if none selected
  React.useEffect(() => {
    if (!selectedTeamId && teams.length > 0) {
      setSelectedTeamId(teams[0].$id);
    }
  }, [teams, selectedTeamId]);

  const handleCreateTeam = () => {
    if (!teamCreationCheck.allowed) {
      setUpgradeFeature('criação de times');
      setIsUpgradeModalOpen(true);
      return;
    }

    setTeamFormMode('create');
    setIsTeamFormModalOpen(true);
  };

  const handleInviteMember = () => {
    if (!user || !selectedTeamId) return;

    // Verificar se pode adicionar membros
    const currentMembersCount = members.length;
    const addMemberPermission = canAddTeamMember(user, currentMembersCount);

    if (!addMemberPermission.allowed) {
      setUpgradeFeature('convite de membros');
      setIsUpgradeModalOpen(true);
      return;
    }

    setIsInviteModalOpen(true);
  };

  const handleTeamPreferences = () => {
    setIsPreferencesModalOpen(true);
  };

  const handleEditMember = (member: TeamMembership) => {
    setSelectedMember(member);
    setIsEditMemberModalOpen(true);
  };

  const handleRemoveMember = (member: TeamMembership) => {
    setSelectedMember(member);
    setIsRemoveMemberModalOpen(true);
  };

  const handleViewMember = (member: TeamMembership) => {
    setSelectedMember(member);
    setIsViewMemberModalOpen(true);
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex flex-col gap-4 p-4 md:p-6 border-b md:flex-row md:items-center md:justify-between">
        <div className="min-w-0">
          <h1 className="text-xl md:text-2xl font-bold">Teams</h1>
          <p className="text-muted-foreground text-sm md:text-base">
            Gerencie sua equipe e colabore com outros membros.
          </p>
        </div>

        <div className="flex items-center gap-2 shrink-0">
          {/* Botão de Preferências - apenas para owners */}
          {selectedTeamId && isOwner && (
            <Button variant="outline" onClick={handleTeamPreferences} size="sm">
              <Settings className="mr-2 h-4 w-4" />
              Preferências
            </Button>
          )}

          <Button
            variant="outline"
            onClick={handleInviteMember}
            disabled={!selectedTeamId}
            size="sm"
          >
            <UserPlus className="mr-2 h-4 w-4" />
            Convidar Membro
          </Button>

          {/* Só mostrar botão de criar time se existem planos que permitem mais de 1 time */}
          {(teamCreationCheck.allowed || teamCreationCheck.planRequired) && (
            <Button
              onClick={handleCreateTeam}
              variant={teamCreationCheck.allowed ? "default" : "outline"}
              className={!teamCreationCheck.allowed ? "border-primary/30 text-primary hover:bg-primary/5 dark:border-primary/40 dark:text-primary dark:hover:bg-primary/10" : ""}
            >
              {teamCreationCheck.allowed ? (
                <>
                  <Plus className="mr-2 h-4 w-4" />
                  Novo Time
                </>
              ) : (
                <>
                  <Crown className="mr-2 h-4 w-4 text-primary" />
                  Upgrade Necessário
                </>
              )}
            </Button>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 p-4 md:p-6">
        {/* Plan limitation alert */}
        {!hasTeamAccessCheck && (
          <div className="mb-6">
            <PlanLimitationAlert
              feature="gerenciamento de times"
              requiredPlan={teamCreationCheck.planRequired}
              reason="Aprimore o plano para voltar a gerenciar o time."
            />
          </div>
        )}

        {/* Team Selector */}
        <div className="mb-6">
          <TeamSelector
            selectedTeamId={selectedTeamId}
            onTeamSelect={setSelectedTeamId}
          />
        </div>

        {/* Team Statistics */}
        <div className="mb-6">
          <TeamStats teamId={selectedTeamId} className="!px-0" />
        </div>

        {/* Team Members Table */}
        {selectedTeamId ? (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-lg font-semibold">
                  Membros {currentTeam ? `- ${currentTeam.name}` : ''}
                </h2>
                <p className="text-sm text-muted-foreground">
                  Gerencie os membros e suas permissões no time.
                </p>
              </div>
            </div>

            <TeamMembersTable
              teamId={selectedTeamId}
              data={members}
              isLoading={membersLoading}
              onEdit={handleEditMember}
              onRemove={handleRemoveMember}
              onView={handleViewMember}
              onExport={handleExportClick}
            />
          </div>
        ) : (
          <div className="text-center py-12">
            <Users className="mx-auto h-12 w-12 text-muted-foreground/50" />
            <h3 className="mt-4 text-lg font-semibold">
              {teams.length === 0 ? 'Nenhum time ainda' : 'Selecione um time'}
            </h3>
            <p className="mt-2 text-muted-foreground">
              {teams.length === 0
                ? 'Comece criando seu primeiro time para colaborar com outros membros.'
                : 'Selecione um time acima para gerenciar seus membros.'
              }
            </p>
            {teams.length === 0 && teamCreationCheck.allowed && (
              <Button
                className="mt-4"
                onClick={handleCreateTeam}
              >
                <Plus className="mr-2 h-4 w-4" />
                Criar primeiro time
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Modals */}
      <TeamFormModal
        open={isTeamFormModalOpen}
        onOpenChange={setIsTeamFormModalOpen}
        mode={teamFormMode}
      />

      <TeamInviteModal
        open={isInviteModalOpen}
        onOpenChange={setIsInviteModalOpen}
        teamId={selectedTeamId}
      />

      <TeamMemberEditModal
        open={isEditMemberModalOpen}
        onOpenChange={setIsEditMemberModalOpen}
        teamId={selectedTeamId}
        member={selectedMember}
      />

      <TeamMemberRemoveModal
        open={isRemoveMemberModalOpen}
        onOpenChange={setIsRemoveMemberModalOpen}
        teamId={selectedTeamId}
        member={selectedMember}
      />

      <TeamMemberViewModal
        open={isViewMemberModalOpen}
        onOpenChange={setIsViewMemberModalOpen}
        member={selectedMember}
      />

      <TeamUpgradeModal
        open={isUpgradeModalOpen}
        onOpenChange={setIsUpgradeModalOpen}
        feature={upgradeFeature}
        requiredPlan={teamCreationCheck.planRequired || undefined}
        reason={`Para usar ${upgradeFeature}, você precisa fazer upgrade do seu plano.`}
      />

      <TeamPreferencesModal
        open={isPreferencesModalOpen}
        onOpenChange={setIsPreferencesModalOpen}
        team={currentTeam || null}
      />


    </div>
  );
}
