import { useState } from 'react'
import {
  DownloadIcon,
  FileTextIcon,
  FileSpreadsheetIcon,
  FileIcon,
  LoaderIcon,
  CheckCircleIcon
} from 'lucide-react'
import { Button } from '../ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Label } from '../ui/label'
import { Checkbox } from '../ui/checkbox'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select'
import { Progress } from '../ui/progress'
import { toast } from 'sonner'

export interface ExportOptions {
  format: 'pdf' | 'excel' | 'csv' | 'json'
  includeCharts: boolean
  includeRawData: boolean
  includeFilters: boolean
  dateRange: boolean
  compression: boolean
}

interface ReportsExportProps {
  selectedReports?: string[]
  onExport?: (options: ExportOptions) => Promise<void>
}

export function ReportsExport({ selectedReports = [], onExport }: ReportsExportProps) {
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'pdf',
    includeCharts: true,
    includeRawData: false,
    includeFilters: true,
    dateRange: true,
    compression: false
  })

  const [isExporting, setIsExporting] = useState(false)
  const [exportProgress, setExportProgress] = useState(0)
  const [exportComplete, setExportComplete] = useState(false)

  const updateOption = <K extends keyof ExportOptions>(key: K, value: ExportOptions[K]) => {
    setExportOptions(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handleExport = async () => {
    try {
      setIsExporting(true)
      setExportProgress(0)
      setExportComplete(false)

      // Simular progresso de exportação
      const progressInterval = setInterval(() => {
        setExportProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return 90
          }
          return prev + 10
        })
      }, 200)

      // Executar exportação
      if (onExport) {
        await onExport(exportOptions)
      } else {
        // Simulação de exportação para demonstração
        await new Promise(resolve => setTimeout(resolve, 2000))

        // Simular download
        const fileName = `relatorio-${Date.now()}.${exportOptions.format}`
        const blob = new Blob(['Dados do relatório simulado'], { type: 'text/plain' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = fileName
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
      }

      clearInterval(progressInterval)
      setExportProgress(100)
      setExportComplete(true)

      toast.success('Relatório exportado com sucesso!')

      // Reset após 3 segundos
      setTimeout(() => {
        setIsExporting(false)
        setExportProgress(0)
        setExportComplete(false)
      }, 3000)

    } catch (error) {
      console.error('Erro ao exportar relatório:', error)
      toast.error('Erro ao exportar relatório')
      setIsExporting(false)
      setExportProgress(0)
    }
  }

  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'pdf':
        return <FileTextIcon className="size-4" />
      case 'excel':
        return <FileSpreadsheetIcon className="size-4" />
      case 'csv':
        return <FileSpreadsheetIcon className="size-4" />
      case 'json':
        return <FileIcon className="size-4" />
      default:
        return <FileIcon className="size-4" />
    }
  }

  const getFormatDescription = (format: string) => {
    switch (format) {
      case 'pdf':
        return 'Ideal para relatórios formatados e apresentações'
      case 'excel':
        return 'Perfeito para análise de dados e planilhas'
      case 'csv':
        return 'Formato simples para importação em outras ferramentas'
      case 'json':
        return 'Dados estruturados para integração com APIs'
      default:
        return ''
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <DownloadIcon className="size-5" />
          Exportar Relatórios
        </CardTitle>
        <CardDescription>
          {selectedReports.length > 0
            ? `${selectedReports.length} relatório(s) selecionado(s) para exportação`
            : 'Configure as opções de exportação para gerar seu relatório'
          }
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">

        {/* Formato de Exportação */}
        <div className="space-y-3">
          <Label className="text-base font-medium">Formato de Exportação</Label>
          <Select
            value={exportOptions.format}
            onValueChange={(value: ExportOptions['format']) => updateOption('format', value)}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="pdf">
                <div className="flex items-center gap-2">
                  <FileTextIcon className="size-4" />
                  PDF
                </div>
              </SelectItem>
              <SelectItem value="excel">
                <div className="flex items-center gap-2">
                  <FileSpreadsheetIcon className="size-4" />
                  Excel (.xlsx)
                </div>
              </SelectItem>
              <SelectItem value="csv">
                <div className="flex items-center gap-2">
                  <FileSpreadsheetIcon className="size-4" />
                  CSV
                </div>
              </SelectItem>
              <SelectItem value="json">
                <div className="flex items-center gap-2">
                  <FileIcon className="size-4" />
                  JSON
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
          <p className="text-sm text-muted-foreground">
            {getFormatDescription(exportOptions.format)}
          </p>
        </div>

        {/* Opções de Conteúdo */}
        <div className="space-y-3">
          <Label className="text-base font-medium">Conteúdo a Incluir</Label>
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="includeCharts"
                checked={exportOptions.includeCharts}
                onCheckedChange={(checked) => updateOption('includeCharts', !!checked)}
              />
              <Label htmlFor="includeCharts" className="text-sm font-normal">
                Incluir gráficos e visualizações
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="includeRawData"
                checked={exportOptions.includeRawData}
                onCheckedChange={(checked) => updateOption('includeRawData', !!checked)}
              />
              <Label htmlFor="includeRawData" className="text-sm font-normal">
                Incluir dados brutos (tabelas detalhadas)
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="includeFilters"
                checked={exportOptions.includeFilters}
                onCheckedChange={(checked) => updateOption('includeFilters', !!checked)}
              />
              <Label htmlFor="includeFilters" className="text-sm font-normal">
                Incluir filtros aplicados
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="dateRange"
                checked={exportOptions.dateRange}
                onCheckedChange={(checked) => updateOption('dateRange', !!checked)}
              />
              <Label htmlFor="dateRange" className="text-sm font-normal">
                Incluir período de análise
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="compression"
                checked={exportOptions.compression}
                onCheckedChange={(checked) => updateOption('compression', !!checked)}
              />
              <Label htmlFor="compression" className="text-sm font-normal">
                Compactar arquivo (ZIP)
              </Label>
            </div>
          </div>
        </div>

        {/* Progresso de Exportação */}
        {isExporting && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-sm">Progresso da Exportação</Label>
              <span className="text-sm text-muted-foreground">{exportProgress}%</span>
            </div>
            <Progress value={exportProgress} className="w-full" />
          </div>
        )}

        {/* Botão de Exportação */}
        <Button
          onClick={handleExport}
          disabled={isExporting}
          className="w-full"
          size="lg"
        >
          {isExporting ? (
            <>
              <LoaderIcon className="mr-2 size-4 animate-spin" />
              Exportando...
            </>
          ) : exportComplete ? (
            <>
              <CheckCircleIcon className="mr-2 size-4" />
              Exportação Concluída
            </>
          ) : (
            <>
              {getFormatIcon(exportOptions.format)}
              <span className="ml-2">Exportar como {exportOptions.format.toUpperCase()}</span>
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  )
}
