/**
 * Hooks para gerenciamento de permissões e cargos
 * Utiliza as preferências do time para armazenar dados
 */

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useAuth } from '../use-auth';
import { useTeamContext } from '../../contexts/team-context';
import * as teamsApi from '../../lib/appwrite/functions/teams';
import {
  createTeamRole,
  updateTeamRole,
  deleteTeamRole,
  assignRoleToMember,
  unassignRoleFromMember,
  initializeDefaultRoles,
} from '../../lib/permissions';
import type {
  CreateTeamRoleData,
  UpdateTeamRoleData,
  AssignRoleData,
  TeamPermissionPreferences,
} from '@/schemas/permissions';

/**
 * Hook para criar um novo cargo
 */
export function useCreateTeamRole() {
  const { user } = useAuth();
  const { currentTeamId } = useTeamContext();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateTeamRoleData) => {
      if (!user || !currentTeamId) {
        throw new Error('Usuário ou time não encontrado');
      }

      // Buscar preferências atuais do time
      const team = await teamsApi.getTeam(currentTeamId);
      const currentPrefs = (team.prefs as TeamPermissionPreferences) || {};

      // Criar novo cargo
      const updatedPrefs = createTeamRole(currentPrefs, data, user.$id);

      // Atualizar preferências do time
      await teamsApi.updateTeamPreferences(currentTeamId, updatedPrefs);

      return updatedPrefs;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teams'] });
      queryClient.invalidateQueries({ queryKey: ['team', currentTeamId] });
      toast.success('Cargo criado com sucesso');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erro ao criar cargo');
    },
  });
}

/**
 * Hook para atualizar um cargo existente
 */
export function useUpdateTeamRole() {
  const { user } = useAuth();
  const { currentTeamId } = useTeamContext();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ roleId, data }: { roleId: string; data: UpdateTeamRoleData }) => {
      if (!user || !currentTeamId) {
        throw new Error('Usuário ou time não encontrado');
      }

      // Buscar preferências atuais do time
      const team = await teamsApi.getTeam(currentTeamId);
      const currentPrefs = (team.prefs as TeamPermissionPreferences) || {};

      // Atualizar cargo
      const updatedPrefs = updateTeamRole(currentPrefs, roleId, data, user.$id);

      // Atualizar preferências do time
      await teamsApi.updateTeamPreferences(currentTeamId, updatedPrefs);

      return updatedPrefs;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teams'] });
      queryClient.invalidateQueries({ queryKey: ['team', currentTeamId] });
      toast.success('Cargo atualizado com sucesso');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erro ao atualizar cargo');
    },
  });
}

/**
 * Hook para excluir um cargo
 */
export function useDeleteTeamRole() {
  const { currentTeamId } = useTeamContext();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (roleId: string) => {
      if (!currentTeamId) {
        throw new Error('Time não encontrado');
      }

      // Buscar preferências atuais do time
      const team = await teamsApi.getTeam(currentTeamId);
      const currentPrefs = (team.prefs as TeamPermissionPreferences) || {};

      // Excluir cargo
      const updatedPrefs = deleteTeamRole(currentPrefs, roleId);

      // Atualizar preferências do time
      await teamsApi.updateTeamPreferences(currentTeamId, updatedPrefs);

      return updatedPrefs;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teams'] });
      queryClient.invalidateQueries({ queryKey: ['team', currentTeamId] });
      toast.success('Cargo excluído com sucesso');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erro ao excluir cargo');
    },
  });
}

/**
 * Hook para atribuir cargo a um membro
 */
export function useAssignRole() {
  const { user } = useAuth();
  const { currentTeamId } = useTeamContext();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: AssignRoleData) => {
      if (!user || !currentTeamId) {
        throw new Error('Usuário ou time não encontrado');
      }

      // Buscar preferências atuais do time
      const team = await teamsApi.getTeam(currentTeamId);
      const currentPrefs = (team.prefs as TeamPermissionPreferences) || {};

      // Atribuir cargo
      const updatedPrefs = assignRoleToMember(currentPrefs, data, user.$id);

      // Atualizar preferências do time
      await teamsApi.updateTeamPreferences(currentTeamId, updatedPrefs);

      return updatedPrefs;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teams'] });
      queryClient.invalidateQueries({ queryKey: ['team', currentTeamId] });
      queryClient.invalidateQueries({ queryKey: ['teams', currentTeamId, 'members'] });
      toast.success('Cargo atribuído com sucesso');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erro ao atribuir cargo');
    },
  });
}

/**
 * Hook para remover cargo de um membro
 */
export function useUnassignRole() {
  const { currentTeamId } = useTeamContext();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (userId: string) => {
      if (!currentTeamId) {
        throw new Error('Time não encontrado');
      }

      // Buscar preferências atuais do time
      const team = await teamsApi.getTeam(currentTeamId);
      const currentPrefs = (team.prefs as TeamPermissionPreferences) || {};

      // Remover cargo
      const updatedPrefs = unassignRoleFromMember(currentPrefs, userId);

      // Atualizar preferências do time
      await teamsApi.updateTeamPreferences(currentTeamId, updatedPrefs);

      return updatedPrefs;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teams'] });
      queryClient.invalidateQueries({ queryKey: ['team', currentTeamId] });
      queryClient.invalidateQueries({ queryKey: ['teams', currentTeamId, 'members'] });
      toast.success('Cargo removido com sucesso');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erro ao remover cargo');
    },
  });
}

/**
 * Hook para inicializar cargos padrão em um time novo
 */
export function useInitializeDefaultRoles() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (teamId: string) => {
      if (!user) {
        throw new Error('Usuário não encontrado');
      }

      // Buscar preferências atuais do time
      const team = await teamsApi.getTeam(teamId);
      const currentPrefs = (team.prefs as TeamPermissionPreferences) || {};

      // Verificar se já tem cargos configurados
      if (currentPrefs.roles && currentPrefs.roles.length > 0) {
        return currentPrefs; // Já inicializado
      }

      // Inicializar cargos padrão
      const updatedPrefs = initializeDefaultRoles(currentPrefs, user.$id);

      // Atualizar preferências do time
      await teamsApi.updateTeamPreferences(teamId, updatedPrefs);

      return updatedPrefs;
    },
    onSuccess: (_, teamId) => {
      queryClient.invalidateQueries({ queryKey: ['teams'] });
      queryClient.invalidateQueries({ queryKey: ['team', teamId] });
      toast.success('Cargos padrão configurados');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erro ao configurar cargos padrão');
    },
  });
}

/**
 * Hook para obter cargos de um time
 */
export function useTeamRoles(teamId?: string) {
  const { currentTeamId } = useTeamContext();
  const targetTeamId = teamId || currentTeamId;

  // Este hook pode ser implementado usando useQuery se necessário
  // Por enquanto, os dados vêm do contexto do time
  return {
    roles: [], // Implementar se necessário
    isLoading: false,
    error: null,
  };
}

/**
 * Hook para verificar se o usuário atual pode gerenciar permissões
 */
export function useCanManagePermissions() {
  const { permissionContext } = useTeamContext();

  // Apenas owners podem gerenciar permissões
  return permissionContext?.hasFullAccess || false;
}
