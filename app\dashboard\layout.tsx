'use client';

import { RouteGuard } from '@/components/route-guard';
import { AppSidebar } from "@/components/app-sidebar";
import { SiteHeader } from "@/components/site-header";
import { GeminiChatbox } from "@/components/chat/gemini-chatbox";
import {
  SidebarInset,
  SidebarProvider,
} from "@/components/ui/sidebar";
import { TeamProvider } from "@/contexts/team-context";

/**
 * Layout for protected dashboard pages
 * Requires authentication to access
 */
export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <RouteGuard requireAuth={true}>
      <TeamProvider>
        <SidebarProvider
          style={
            {
              "--sidebar-width": "calc(var(--spacing) * 72)",
              "--header-height": "calc(var(--spacing) * 12)",
            } as React.CSSProperties
          }
        >
          <AppSidebar variant="inset" />
          <SidebarInset>
            <SiteHeader />
            <div className="flex flex-1 flex-col">
              <div className="@container/main flex flex-1 flex-col gap-2">
                {children}
              </div>
            </div>
          </SidebarInset>

          {/* Chatbox do Gemini - disponível em todas as páginas */}
          <GeminiChatbox position="bottom-right" size="lg" />
        </SidebarProvider>
      </TeamProvider>
    </RouteGuard>
  );
}
