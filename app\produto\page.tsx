import type { <PERSON>ada<PERSON> } from "next";
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  ArrowLeft,
  Users,
  BarChart3,
  Calendar,
  MessageSquare,
  FileText,
  Zap,
  Shield,
  Clock,
  CheckCircle,
  Star,
  Play,
  Rocket,
  Code,
  Database,
  Smartphone
} from 'lucide-react';

export const metadata: Metadata = {
  title: "Produto - Template Appwrite Completo",
  description: "Conheça todos os recursos e funcionalidades do nosso template completo para desenvolvimento de aplicações modernas",
};

const features = [
  {
    icon: Users,
    title: "Autenticação Completa",
    description: "Sistema completo de login, registro, recuperação de senha e autenticação social",
    details: ["Login/Registro", "Recuperação de senha", "Verificação de email", "OAuth providers"]
  },
  {
    icon: BarChart3,
    title: "Dashboard Analytics",
    description: "Dashboard completo com gráficos, métricas e relatórios em tempo real",
    details: ["Gráficos interativos", "Métricas em tempo real", "Relatórios customizáveis", "Exportação de dados"]
  },
  {
    icon: Calendar,
    title: "Sistema de Calendário",
    description: "Calendário completo com eventos, agendamentos e notificações",
    details: ["Múltiplas visualizações", "Eventos recorrentes", "Notificações", "Integração com equipes"]
  },
  {
    icon: MessageSquare,
    title: "Chat em Tempo Real",
    description: "Sistema de chat moderno com mensagens instantâneas e notificações",
    details: ["Mensagens em tempo real", "Chat em equipe", "Emojis e reações", "Histórico de mensagens"]
  },
  {
    icon: FileText,
    title: "Gerenciamento de Documentos",
    description: "Upload, organização e compartilhamento de arquivos e documentos",
    details: ["Upload de arquivos", "Organização em pastas", "Compartilhamento", "Preview de documentos"]
  },
  {
    icon: Shield,
    title: "Segurança Avançada",
    description: "Implementação de melhores práticas de segurança e proteção de dados",
    details: ["Criptografia", "Controle de acesso", "Auditoria", "Backup automático"]
  }
];

const techStack = [
  { name: "React 19", description: "Framework frontend moderno" },
  { name: "TypeScript", description: "Tipagem estática" },
  { name: "Next.js 15", description: "Framework full-stack" },
  { name: "Appwrite", description: "Backend as a Service" },
  { name: "Tailwind CSS", description: "Framework CSS utilitário" },
  { name: "shadcn/ui", description: "Componentes UI modernos" },
  { name: "TanStack Query", description: "Gerenciamento de estado" },
  { name: "Framer Motion", description: "Animações fluidas" }
];

export default function ProductPage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Voltar
              </Link>
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Produto</h1>
              <p className="text-muted-foreground">Template completo para aplicações modernas</p>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12 max-w-6xl">
        {/* Hero Section */}
        <section className="mb-16 text-center">
          <Badge variant="outline" className="mb-4">
            <Rocket className="w-4 h-4 mr-2" />
            Template Completo
          </Badge>
          <h2 className="text-4xl font-bold mb-6">
            Tudo que você precisa para criar aplicações modernas
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
            Um template completo e pronto para produção com autenticação, dashboard, 
            chat em tempo real, calendário e muito mais. Economize semanas de desenvolvimento.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" asChild>
              <Link href="/register">
                <Play className="w-4 h-4 mr-2" />
                Testar Demo
              </Link>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link href="/#pricing">
                Ver Preços
              </Link>
            </Button>
          </div>
        </section>

        {/* Features Grid */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Recursos Principais</h2>
            <p className="text-lg text-muted-foreground">
              Funcionalidades completas e prontas para usar
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="h-full">
                <CardHeader>
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                    <feature.icon className="w-6 h-6 text-primary" />
                  </div>
                  <CardTitle className="text-lg">{feature.title}</CardTitle>
                  <CardDescription>{feature.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {feature.details.map((detail, detailIndex) => (
                      <li key={detailIndex} className="flex items-center gap-2 text-sm">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        {detail}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Tech Stack */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Stack Tecnológico</h2>
            <p className="text-lg text-muted-foreground">
              Tecnologias modernas e confiáveis
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {techStack.map((tech, index) => (
              <Card key={index} className="text-center">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <Code className="w-6 h-6 text-primary" />
                  </div>
                  <h4 className="font-semibold mb-2">{tech.name}</h4>
                  <p className="text-sm text-muted-foreground">{tech.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Benefits */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Por que escolher nosso template?</h2>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            <Card className="text-center">
              <CardHeader>
                <Clock className="w-12 h-12 text-primary mx-auto mb-4" />
                <CardTitle>Economize Tempo</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Economize semanas de desenvolvimento com um template completo e testado
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <Zap className="w-12 h-12 text-primary mx-auto mb-4" />
                <CardTitle>Alta Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Otimizado para velocidade e performance em produção
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <Star className="w-12 h-12 text-primary mx-auto mb-4" />
                <CardTitle>Qualidade Premium</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Código limpo, documentado e seguindo as melhores práticas
                </p>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* CTA Section */}
        <section className="text-center">
          <Card className="bg-primary/5 border-primary/20">
            <CardContent className="p-12">
              <h3 className="text-3xl font-bold mb-4">Pronto para começar?</h3>
              <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
                Junte-se a centenas de desenvolvedores que já estão usando nosso template 
                para criar aplicações incríveis.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" asChild>
                  <Link href="/register">
                    Começar Agora
                  </Link>
                </Button>
                <Button variant="outline" size="lg" asChild>
                  <Link href="/help">
                    Ver Documentação
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  );
}
