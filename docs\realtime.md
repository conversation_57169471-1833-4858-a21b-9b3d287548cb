# 📡 Sistema de Realtime do Appwrite

Sistema modular de tempo real que sincroniza automaticamente dados entre Appwrite, IndexedDB e React Query.

## 🏗️ Arquitetura

### Componentes Principais

1. **RealtimeManager** (`app/lib/realtime/index.ts`)
   - Gerencia conexão única com Appwrite Realtime
   - Coordena todos os módulos
   - Escuta canal geral do database

2. **Módulos Específicos** (`app/lib/realtime/modules/`)
   - `clients.ts` - Sincronização de clientes
   - `chat.ts` - Mensagens e chats de equipe
   - `kanban.ts` - Boards, colunas e tasks
   - `calendar.ts` - Eventos e categorias
   - `notifications.ts` - Notificações e logs de atividade

3. **Hooks** (`app/hooks/use-realtime.ts`)
   - `useRealtime()` - Hook principal
   - `useRealtimeCollection()` - Para collections específicas
   - `useRealtimeAuth()` - Para eventos de autenticação
   - `useRealtimeStatus()` - Status da conexão

4. **Provider** (`app/components/realtime/realtime-provider.tsx`)
   - Inicializa o sistema automaticamente
   - Conecta/desconecta baseado no usuário logado

## 🚀 Como Funciona

### Fluxo de Sincronização

1. **Evento no Appwrite** → Documento criado/atualizado/deletado
2. **Realtime Event** → Sistema recebe evento via Appwrite Realtime
3. **Módulo Específico** → Processa evento baseado na collection
4. **IndexedDB** → Atualiza cache local
5. **React Query** → Invalida/atualiza queries
6. **UI** → Interface atualizada automaticamente

### Canal Único

O sistema usa um canal geral para eficiência:
```typescript
`databases.${DATABASE_ID}.collections.*.documents`
```

Isso evita múltiplas conexões e centraliza o processamento.

## 📋 Uso Básico

### Automático (Recomendado)

O sistema funciona automaticamente após a configuração no `root.tsx`:

```typescript
// Já configurado no template
<RealtimeProvider>
  <App />
</RealtimeProvider>
```

### Manual

```typescript
import { useRealtime } from '@/hooks/use-realtime';

function MyComponent() {
  const { isConnected, connect, disconnect } = useRealtime();

  useEffect(() => {
    if (!isConnected) {
      connect();
    }
  }, [isConnected, connect]);

  return (
    <div>
      Status: {isConnected ? 'Conectado' : 'Desconectado'}
    </div>
  );
}
```

## 🔧 Hooks Disponíveis

### useRealtime()

Hook principal para controle da conexão:

```typescript
const {
  isConnected,
  isConnecting,
  error,
  connect,
  disconnect,
  subscribe,
  modules,
  getModuleStatus
} = useRealtime();
```

### useRealtimeCollection()

Para escutar eventos de uma collection específica:

```typescript
import { useRealtimeCollection } from '@/hooks/use-realtime';

function ClientsList() {
  const { lastEvent } = useRealtimeCollection(
    'clients',
    (event) => {
      console.log('Evento de cliente:', event);
      if (event.event.includes('create')) {
        toast.success('Novo cliente adicionado!');
      }
    }
  );

  return <div>Lista de clientes...</div>;
}
```

### useRealtimeStatus()

Para monitorar o status da conexão:

```typescript
const {
  isConnected,
  status,
  modules,
  getModuleStatus
} = useRealtimeStatus();
```

## 🔄 Módulos Disponíveis

### Clientes
- **Collection**: `clients`
- **Funcionalidades**: CRUD completo com cache local
- **Notificações**: Toast para criação/atualização/remoção

### Chat
- **Collections**: `chat_messages`, `team_chats`
- **Funcionalidades**: Mensagens em tempo real, deduplicação
- **Cache**: Por chat individual (`chat_${chatId}`)

### Kanban
- **Collections**: `kanban_boards`, `kanban_columns`, `kanban_tasks`
- **Funcionalidades**: Sincronização de boards, colunas e tasks
- **Relacionamentos**: Tasks invalidam queries do board pai

### Calendário
- **Collections**: `events`, `event_categories`
- **Funcionalidades**: Eventos e categorias em tempo real
- **Notificações**: Alertas para novos eventos

### Notificações
- **Collections**: `notifications`, `activity_logs`
- **Funcionalidades**: Notificações push, logs de atividade
- **Contador**: Atualização automática de não lidas

## 🛠️ Configuração

### Variáveis de Ambiente

```env
NEXT_PUBLIC_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
NEXT_PUBLIC_APPWRITE_PROJECT_ID=seu-project-id
NEXT_PUBLIC_APPWRITE_DATABASE_ID=main
```

### Habilitação/Desabilitação

```typescript
// Desabilitar realtime
<RealtimeProvider enabled={false}>
  <App />
</RealtimeProvider>

// Ou no hook
const { connect } = useRealtime({ enabled: false });
```

## 💬 Chat Real-time

### Hook de Chat
```typescript
import { useChatRealtime } from '@/hooks/api/use-chat';

function ChatComponent({ teamId }: { teamId: string }) {
  const {
    messages,
    isConnected,
    sendMessage,
    sendTypingIndicator,
    typingUsers,
    chatState
  } = useChatRealtime(teamId);

  const handleSendMessage = async () => {
    if (!message.trim()) return;

    try {
      await sendMessage({
        content: message.trim(),
        chatId,
        teamId,
        type: 'text'
      });

      setMessage('');
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
    }
  };

  return (
    <div>
      <div className="messages">
        {messages.map(msg => (
          <div key={msg.$id}>{msg.content}</div>
        ))}
      </div>

      {typingUsers.length > 0 && (
        <div className="typing-indicator">
          {typingUsers.join(', ')} está digitando...
        </div>
      )}

      <input
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        onKeyPress={(e) => {
          if (e.key === 'Enter') handleSendMessage();
        }}
      />
    </div>
  );
}
```

### Canais de Chat
```typescript
// Mensagens de um chat específico
const chatChannel = `databases.${DATABASE_ID}.collections.chat_messages.documents`;

// Chat de uma equipe
const teamChatChannel = `databases.${DATABASE_ID}.collections.team_chats.documents`;

// Filtrar por chat específico
const unsubscribe = subscribe([chatChannel], (message) => {
  if (message.payload.chatId === currentChatId) {
    // Processar mensagem do chat atual
    handleNewMessage(message.payload);
  }
});
```

## 🔔 Notificações Real-time

### Hook de Notificações
```typescript
import { useNotificationsRealtime } from '@/hooks/api/use-notifications';

function NotificationCenter() {
  const { notifications, unreadCount, markAsRead } = useNotificationsRealtime();

  return (
    <div>
      <Badge>{unreadCount}</Badge>

      {notifications.map(notification => (
        <div
          key={notification.$id}
          className={notification.isRead ? 'read' : 'unread'}
          onClick={() => markAsRead(notification.$id)}
        >
          <h4>{notification.title}</h4>
          <p>{notification.message}</p>
        </div>
      ))}
    </div>
  );
}
```

### Canais de Notificação
```typescript
// Notificações do usuário atual
const userNotificationChannel = `databases.${DATABASE_ID}.collections.notifications.documents`;

// Filtrar por usuário
const unsubscribe = subscribe([userNotificationChannel], (message) => {
  if (message.payload.userId === currentUserId) {
    // Nova notificação para o usuário atual
    showNotificationToast(message.payload);
  }
});
```

## 🔄 Hooks Especializados

### useDatabaseRealtime
Hook para mudanças em coleções específicas:

```typescript
import { useRealtime } from '@/hooks/use-realtime';

function ClientsList() {
  const { data: realtimeData } = useDatabaseRealtime('clients');

  useEffect(() => {
    if (realtimeData) {
      const { event, payload } = realtimeData;

      if (event.includes('create')) {
        toast.success('Novo cliente adicionado!');
      } else if (event.includes('update')) {
        toast.info('Cliente atualizado!');
      } else if (event.includes('delete')) {
        toast.warning('Cliente removido!');
      }
    }
  }, [realtimeData]);

  return <div>Lista de clientes...</div>;
}
```

### useAuthRealtime
Hook para eventos de autenticação:

```typescript
import { useRealtime } from '@/hooks/use-realtime';

function AuthMonitor() {
  const { authEvent } = useAuthRealtime();

  useEffect(() => {
    if (authEvent) {
      const { event } = authEvent;

      if (event.includes('sessions.create')) {
        console.log('Nova sessão criada');
      } else if (event.includes('sessions.delete')) {
        console.log('Sessão encerrada');
      }
    }
  }, [authEvent]);

  return null; // Componente de monitoramento
}
```

## 🔄 Integração com Cache

### Invalidação Automática
```typescript
import { useQueryClient } from '@tanstack/react-query';
import { useRealtime } from '@/hooks/use-realtime';

function useRealtimeInvalidation() {
  const queryClient = useQueryClient();
  const { isConnected } = useRealtime();

  useEffect(() => {
    // A invalidação agora é automática via RealtimeProvider
    // Não é necessário configurar manualmente
    // O sistema já gerencia as invalidações baseado nos módulos registrados

    console.log('Sistema de realtime ativo:', isConnected);
  }, [isConnected]);
}
```

## 🛠️ Mutations Otimistas

### Update Otimista com Real-time
```typescript
import { useOptimisticMutation } from '@/hooks/use-optimistic-mutations';

function useUpdateClientOptimistic() {
  return useOptimisticMutation({
    mutationFn: updateClient,
    collectionId: 'clients',
    // realtimeSync removido - agora é automático via RealtimeProvider

    // Update otimista
    onMutate: async (variables) => {
      // Cancelar queries em andamento
      await queryClient.cancelQueries({ queryKey: ['clients'] });

      // Snapshot do estado atual
      const previousClients = queryClient.getQueryData(['clients']);

      // Update otimista
      queryClient.setQueryData(['clients'], (old: Client[]) =>
        old.map(client =>
          client.$id === variables.id
            ? { ...client, ...variables.data }
            : client
        )
      );

      return { previousClients };
    },

    // Reverter em caso de erro
    onError: (err, variables, context) => {
      if (context?.previousClients) {
        queryClient.setQueryData(['clients'], context.previousClients);
      }
    },

    // Sincronizar com real-time
    onSettled: () => {
      // O real-time irá sincronizar automaticamente
      // quando outros usuários fizerem mudanças
    }
  });
}
```

## 📱 Indicadores de Status

### Connection Status
```typescript
function ConnectionStatus() {
  const { isConnected, isConnecting, error, status } = useRealtimeStatus();

  const getStatusColor = () => {
    switch (status) {
      case 'connected': return 'green';
      case 'connecting': return 'yellow';
      case 'error': return 'red';
      default: return 'gray';
    }
  };

  return (
    <div className="flex items-center gap-2">
      <div
        className={`w-2 h-2 rounded-full bg-${getStatusColor()}-500`}
      />
      <span className="text-sm">
        {isConnecting ? 'Conectando...' :
         isConnected ? 'Online' :
         error ? 'Erro de conexão' : 'Offline'}
      </span>
    </div>
  );
}
```

### Typing Indicator
```typescript
function TypingIndicator({ chatId }: { chatId: string }) {
  const { typingUsers } = useChatRealtime(chatId);

  if (typingUsers.length === 0) return null;

  return (
    <div className="text-sm text-muted-foreground italic">
      {typingUsers.length === 1
        ? `${typingUsers[0]} está digitando...`
        : `${typingUsers.slice(0, -1).join(', ')} e ${typingUsers[typingUsers.length - 1]} estão digitando...`
      }
    </div>
  );
}
```

## ⚠️ Error Handling

### Reconnection Logic
```typescript
const scheduleReconnect = useCallback(() => {
  if (reconnectAttemptsRef.current >= maxReconnectAttempts) {
    setError('Máximo de tentativas de reconexão atingido');
    setConnectionState('error');
    return;
  }

  reconnectAttemptsRef.current++;
  setConnectionState('reconnecting');

  reconnectTimeoutRef.current = setTimeout(() => {
    initializeConnection();
  }, reconnectInterval * reconnectAttemptsRef.current);
}, [initializeConnection, maxReconnectAttempts, reconnectInterval]);
```

### Error Boundaries
```typescript
function RealtimeErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary
      fallback={
        <div className="p-4 text-center">
          <p>Erro na conexão real-time</p>
          <Button onClick={() => window.location.reload()}>
            Recarregar
          </Button>
        </div>
      }
      onError={(error) => {
        console.error('Realtime error:', error);
        // Log para sistema de monitoramento
      }}
    >
      {children}
    </ErrorBoundary>
  );
}
```

## 🐛 Debug

### Status Visual (Desenvolvimento)

O componente `RealtimeStatus` mostra informações em desenvolvimento:
- Status da conexão (conectado/conectando/desconectado)
- Erros de conexão
- Número de módulos ativos

### Console Logs

O sistema gera logs detalhados:
```
🔗 Realtime conectado ao Appwrite
📡 Módulo clients registrado para collection clients
📡 Processando evento create para clients
✅ Cliente criado via realtime: cliente-id
```

### Hook de Debug

```typescript
import { useRealtimeDebug } from '@/components/realtime/realtime-provider';

function DebugComponent() {
  const debugInfo = useRealtimeDebug();

  return (
    <pre>{JSON.stringify(debugInfo, null, 2)}</pre>
  );
}
```

## ⚡ Performance

### Otimizações Implementadas

1. **Canal Único**: Uma conexão Appwrite Realtime para todas as collections
2. **Deduplicação**: Evita duplicatas em optimistic updates
3. **Cache Inteligente**: Atualiza IndexedDB e React Query
4. **Invalidação Seletiva**: Só invalida queries necessárias

### Boas Práticas

1. **Use o sistema automático** - Deixe o RealtimeProvider gerenciar
2. **Evite subscriptions manuais** - Use os módulos existentes
3. **Monitor performance** - Use o RealtimeStatus em desenvolvimento
4. **Cache local** - O sistema já gerencia IndexedDB automaticamente

## 🔧 Extensão

### Adicionando Novo Módulo

1. Criar arquivo em `app/lib/realtime/modules/minha-collection.ts`
2. Implementar interface `RealtimeModule`
3. Registrar no `RealtimeManager`

```typescript
// app/lib/realtime/modules/minha-collection.ts
export function createMinhaCollectionModule(queryClient): RealtimeModule {
  return {
    name: 'minha-collection',
    collection: COLLECTIONS.MINHA_COLLECTION,
    enabled: true,

    async handleCreate(event) {
      // Lógica para criação
    },

    async handleUpdate(event) {
      // Lógica para atualização
    },

    async handleDelete(event) {
      // Lógica para remoção
    }
  };
}
```

### Customização de Módulo

Cada módulo pode ser customizado para:
- Diferentes estratégias de cache
- Notificações específicas
- Validações de dados
- Transformações de payload

## 🚨 Troubleshooting

### Conexão Não Estabelecida
- Verificar variáveis de ambiente
- Confirmar configuração do Appwrite
- Checar console para erros

### Eventos Não Processados
- Verificar se módulo está registrado
- Confirmar collection ID no evento
- Checar logs do console

### Performance Lenta
- Verificar número de subscriptions ativas
- Monitorar tamanho do cache IndexedDB
- Usar RealtimeStatus para debug
