/**
 * Event Card Component
 * Card de evento para exibição no calendário
 */

import React from 'react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { 
  Clock, 
  MapPin, 
  Users, 
  AlertCircle,
  CheckCircle,
  XCircle,
  Pause,
  Calendar
} from 'lucide-react';
import { cn } from '../../lib/utils';
import { Badge } from '../ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '../ui/tooltip';
import type { Event, EventStatus, EventPriority } from '@/schemas/events';

// ============================================================================
// INTERFACES
// ============================================================================

interface EventCardProps {
  event: Event;
  compact?: boolean;
  className?: string;
  onClick?: () => void;
}

// ============================================================================
// HELPERS
// ============================================================================

const statusIcons = {
  agendado: Calendar,
  em_andamento: Clock,
  concluido: CheckCircle,
  cancelado: XCircle,
  adiado: Pause,
};

const statusColors = {
  agendado: 'text-blue-600',
  em_andamento: 'text-yellow-600',
  concluido: 'text-green-600',
  cancelado: 'text-red-600',
  adiado: 'text-gray-600',
};

const priorityColors = {
  baixa: 'border-l-green-500',
  media: 'border-l-yellow-500',
  alta: 'border-l-orange-500',
  critica: 'border-l-red-500',
};

const priorityBadgeColors = {
  baixa: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
  media: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
  alta: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300',
  critica: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
};

// ============================================================================
// COMPONENTE
// ============================================================================

export function EventCard({ event, compact = false, className, onClick }: EventCardProps) {
  const StatusIcon = statusIcons[event.status];
  
  // Formatação de tempo
  const timeFormat = event.allDay 
    ? 'Dia todo' 
    : `${format(new Date(event.startDate), 'HH:mm', { locale: ptBR })} - ${format(new Date(event.endDate), 'HH:mm', { locale: ptBR })}`;

  // Duração do evento
  const duration = React.useMemo(() => {
    if (event.allDay) return null;
    
    const start = new Date(event.startDate);
    const end = new Date(event.endDate);
    const diffMs = end.getTime() - start.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (diffHours > 0) {
      return diffMinutes > 0 ? `${diffHours}h ${diffMinutes}m` : `${diffHours}h`;
    }
    return `${diffMinutes}m`;
  }, [event.startDate, event.endDate, event.allDay]);

  // Versão compacta para visualização mensal
  if (compact) {
    return (
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            className={cn(
              'w-full p-1 rounded text-xs cursor-pointer transition-all hover:scale-105',
              'border-l-2 bg-white dark:bg-gray-800 shadow-sm',
              priorityColors[event.priority],
              event.status === 'cancelado' && 'opacity-60 line-through',
              className
            )}
            style={{ 
              backgroundColor: `${event.color}15`,
              borderLeftColor: event.color 
            }}
            onClick={onClick}
          >
            <div className="flex items-center gap-1 min-w-0">
              <StatusIcon className={cn('h-3 w-3 flex-shrink-0', statusColors[event.status])} />
              <span className="truncate font-medium">{event.title}</span>
            </div>
            
            {!event.allDay && (
              <div className="text-muted-foreground mt-0.5">
                {format(new Date(event.startDate), 'HH:mm', { locale: ptBR })}
              </div>
            )}
          </div>
        </TooltipTrigger>
        <TooltipContent side="top" className="max-w-xs">
          <div className="space-y-1">
            <div className="font-medium">{event.title}</div>
            <div className="text-sm text-muted-foreground">{timeFormat}</div>
            {event.location && (
              <div className="text-sm text-muted-foreground flex items-center gap-1">
                <MapPin className="h-3 w-3" />
                {event.location}
              </div>
            )}
            {event.description && (
              <div className="text-sm text-muted-foreground line-clamp-2">
                {event.description}
              </div>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    );
  }

  // Versão expandida para visualizações semanal e diária
  return (
    <div
      className={cn(
        'w-full p-2 rounded-md cursor-pointer transition-all hover:shadow-md',
        'border-l-4 bg-white dark:bg-gray-800 shadow-sm',
        priorityColors[event.priority],
        event.status === 'cancelado' && 'opacity-60',
        className
      )}
      style={{ 
        backgroundColor: `${event.color}10`,
        borderLeftColor: event.color 
      }}
      onClick={onClick}
    >
      {/* Header */}
      <div className="flex items-start justify-between gap-2 mb-1">
        <div className="flex items-center gap-1 min-w-0 flex-1">
          <StatusIcon className={cn('h-4 w-4 flex-shrink-0', statusColors[event.status])} />
          <h4 className={cn(
            'font-medium text-sm truncate',
            event.status === 'cancelado' && 'line-through'
          )}>
            {event.title}
          </h4>
        </div>
        
        {event.priority !== 'media' && (
          <Badge 
            variant="secondary" 
            className={cn('text-xs px-1 py-0', priorityBadgeColors[event.priority])}
          >
            {event.priority === 'baixa' ? 'Baixa' :
             event.priority === 'alta' ? 'Alta' : 'Crítica'}
          </Badge>
        )}
      </div>

      {/* Time */}
      <div className="flex items-center gap-1 text-xs text-muted-foreground mb-1">
        <Clock className="h-3 w-3" />
        <span>{timeFormat}</span>
        {duration && (
          <>
            <span>•</span>
            <span>{duration}</span>
          </>
        )}
      </div>

      {/* Location */}
      {event.location && (
        <div className="flex items-center gap-1 text-xs text-muted-foreground mb-1">
          <MapPin className="h-3 w-3" />
          <span className="truncate">{event.location}</span>
        </div>
      )}

      {/* Attendees */}
      {event.attendees && event.attendees.length > 0 && (
        <div className="flex items-center gap-1 text-xs text-muted-foreground mb-1">
          <Users className="h-3 w-3" />
          <span>{event.attendees.length} participante(s)</span>
        </div>
      )}

      {/* Description */}
      {event.description && (
        <div className="text-xs text-muted-foreground line-clamp-2 mt-1">
          {event.description}
        </div>
      )}

      {/* Category */}
      {event.category && (
        <div className="mt-2">
          <Badge 
            variant="outline" 
            className="text-xs"
            style={{ borderColor: event.color, color: event.color }}
          >
            {event.category}
          </Badge>
        </div>
      )}

      {/* Tags */}
      {event.tags && event.tags.length > 0 && (
        <div className="flex flex-wrap gap-1 mt-1">
          {event.tags.slice(0, 3).map((tag, index) => (
            <Badge key={index} variant="secondary" className="text-xs px-1 py-0">
              {tag}
            </Badge>
          ))}
          {event.tags.length > 3 && (
            <Badge variant="secondary" className="text-xs px-1 py-0">
              +{event.tags.length - 3}
            </Badge>
          )}
        </div>
      )}

      {/* Reminder indicator */}
      {event.reminderType !== 'none' && !event.reminderSent && (
        <div className="flex items-center gap-1 text-xs text-amber-600 mt-1">
          <AlertCircle className="h-3 w-3" />
          <span>Lembrete ativo</span>
        </div>
      )}

      {/* Recurrence indicator */}
      {event.recurrenceType !== 'none' && (
        <div className="flex items-center gap-1 text-xs text-blue-600 mt-1">
          <Calendar className="h-3 w-3" />
          <span>
            {event.recurrenceType === 'daily' ? 'Diário' :
             event.recurrenceType === 'weekly' ? 'Semanal' :
             event.recurrenceType === 'monthly' ? 'Mensal' : 'Anual'}
          </span>
        </div>
      )}
    </div>
  );
}
