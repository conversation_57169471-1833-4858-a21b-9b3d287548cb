/**
 * Activity Logging Functions
 * Centralized functions for logging activities to Appwrite database
 */

import { databases, account } from '../config';
import { DATABASE_ID, COLLECTIONS } from '../config';
import { Query, ID } from 'appwrite';
import type {
  Activity,
  CreateActivityData,
  ActivityFilters,
  ActivityQueryOptions,
  ActivityListResponse,
  ActivityStats,
  LogActivityParams,
  ActivityContext
} from '@/schemas/activities';
import { canLogActivities } from '../../activity-permissions';

// ============================================================================
// CORE ACTIVITY FUNCTIONS
// ============================================================================

/**
 * Create a new activity log entry
 */
export async function createActivity(data: CreateActivityData): Promise<Activity> {
  try {
    const user = await account.get();

    // Get client info for metadata
    const clientInfo = await getClientInfo();

    const activityData = {
      userId: user.$id,
      createdBy: user.$id,
      teamId: data.teamId,
      type: data.type,
      action: data.action,
      resource: data.resource,
      resourceId: data.resourceId,
      title: data.title,
      description: data.description,
      details: data.details ? JSON.stringify(data.details) : undefined,
      priority: data.priority || 'normal',
      tags: data.tags || [],
      category: data.category,
      ipAddress: clientInfo.ipAddress,
      userAgent: clientInfo.userAgent,
      metadata: data.metadata ? JSON.stringify(data.metadata) : undefined,
    };

    const response = await databases.createDocument(
      DATABASE_ID,
      COLLECTIONS.ACTIVITY_LOGS,
      ID.unique(),
      activityData
    );

    return response as Activity;
  } catch (error) {
    console.error('Error creating activity:', error);
    throw error;
  }
}

/**
 * Get activities with filtering and pagination
 */
export async function getActivities(options: ActivityQueryOptions = {}): Promise<ActivityListResponse> {
  try {
    const {
      limit = 50,
      offset = 0,
      orderBy = 'createdAt',
      orderDirection = 'desc',
      filters = {},
    } = options;

    // Build queries
    const queries = [
      Query.limit(limit),
      Query.offset(offset),
    ];

    // Add ordering
    if (orderDirection === 'desc') {
      queries.push(Query.orderDesc(orderBy));
    } else {
      queries.push(Query.orderAsc(orderBy));
    }

    // Add filters
    if (filters.type) {
      if (Array.isArray(filters.type)) {
        queries.push(Query.equal('type', filters.type));
      } else {
        queries.push(Query.equal('type', filters.type));
      }
    }

    if (filters.action) {
      if (Array.isArray(filters.action)) {
        queries.push(Query.equal('action', filters.action));
      } else {
        queries.push(Query.equal('action', filters.action));
      }
    }

    if (filters.resource) {
      queries.push(Query.equal('resource', filters.resource));
    }

    if (filters.userId) {
      queries.push(Query.equal('userId', filters.userId));
    }

    if (filters.teamId) {
      queries.push(Query.equal('teamId', filters.teamId));
    }

    if (filters.priority) {
      queries.push(Query.equal('priority', filters.priority));
    }

    if (filters.dateFrom) {
      queries.push(Query.greaterThanEqual('$createdAt', filters.dateFrom));
    }

    if (filters.dateTo) {
      queries.push(Query.lessThanEqual('$createdAt', filters.dateTo));
    }

    if (filters.search) {
      queries.push(Query.search('title', filters.search));
    }

    const response = await databases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.ACTIVITY_LOGS,
      queries
    );

    const activities = response.documents.map(doc => ({
      ...doc,
      details: doc.details ? JSON.parse(doc.details) : undefined,
      metadata: doc.metadata ? JSON.parse(doc.metadata) : undefined,
    })) as Activity[];

    return {
      activities,
      total: response.total,
      hasMore: response.total > offset + limit,
    };
  } catch (error) {
    console.error('Error getting activities:', error);
    throw error;
  }
}

/**
 * Get single activity by ID
 */
export async function getActivity(id: string): Promise<Activity> {
  try {
    const response = await databases.getDocument(
      DATABASE_ID,
      COLLECTIONS.ACTIVITY_LOGS,
      id
    );

    return {
      ...response,
      details: response.details ? JSON.parse(response.details) : undefined,
      metadata: response.metadata ? JSON.parse(response.metadata) : undefined,
    } as Activity;
  } catch (error) {
    console.error('Error getting activity:', error);
    throw error;
  }
}

/**
 * Get activity statistics
 */
export async function getActivityStats(filters: ActivityFilters = {}): Promise<ActivityStats> {
  try {
    // Get total count
    const totalResponse = await databases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.ACTIVITY_LOGS,
      [Query.limit(1)]
    );

    // Get recent activities (last 24 hours)
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);

    const recentResponse = await databases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.ACTIVITY_LOGS,
      [
        Query.greaterThanEqual('$createdAt', yesterday.toISOString()),
        Query.limit(1)
      ]
    );

    // Get today's activities
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const todayResponse = await databases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.ACTIVITY_LOGS,
      [
        Query.greaterThanEqual('$createdAt', today.toISOString()),
        Query.limit(1)
      ]
    );

    // Get this week's activities
    const weekStart = new Date();
    weekStart.setDate(weekStart.getDate() - weekStart.getDay());
    weekStart.setHours(0, 0, 0, 0);

    const weekResponse = await databases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.ACTIVITY_LOGS,
      [
        Query.greaterThanEqual('$createdAt', weekStart.toISOString()),
        Query.limit(1)
      ]
    );

    // Get this month's activities
    const monthStart = new Date();
    monthStart.setDate(1);
    monthStart.setHours(0, 0, 0, 0);

    const monthResponse = await databases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.ACTIVITY_LOGS,
      [
        Query.greaterThanEqual('$createdAt', monthStart.toISOString()),
        Query.limit(1)
      ]
    );

    return {
      total: totalResponse.total,
      byType: {} as Record<string, number>, // Would need separate queries for each type
      byAction: {} as Record<string, number>, // Would need separate queries for each action
      byPriority: {} as Record<string, number>, // Would need separate queries for each priority
      recentCount: recentResponse.total,
      todayCount: todayResponse.total,
      weekCount: weekResponse.total,
      monthCount: monthResponse.total,
    };
  } catch (error) {
    console.error('Error getting activity stats:', error);
    throw error;
  }
}

// ============================================================================
// ACTIVITY LOGGING HELPERS
// ============================================================================

/**
 * Check if current user has permission to log activities
 * Only users with enterprise plan or admin role can log activities
 */
async function checkCurrentUserCanLogActivities(): Promise<boolean> {
  try {
    const user = await account.get();
    return canLogActivities(user);
  } catch (error) {
    console.error('Error checking activity logging permissions:', error);
    return false;
  }
}

/**
 * Main activity logging function
 * Only logs activities if user has enterprise plan or admin role
 */
export async function logActivity(params: LogActivityParams): Promise<void> {
  try {
    // Check permissions first
    const hasPermission = await checkCurrentUserCanLogActivities();
    if (!hasPermission) {
      // Silently return without logging for users without permission
      return;
    }

    const title = params.title || generateActivityTitle(params.type, params.action, params.resource);

    await createActivity({
      ...params,
      title,
    });
  } catch (error) {
    console.error('Error logging activity:', error);
    // Don't throw error for logging failures to avoid breaking main functionality
  }
}

/**
 * Get client information for activity context
 */
async function getClientInfo(): Promise<{ ipAddress?: string; userAgent?: string }> {
  try {
    // In a real app, you might get this from a service or API
    return {
      ipAddress: undefined, // Would be set by server or service
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
    };
  } catch {
    return {};
  }
}

/**
 * Generate activity title based on type, action, and resource
 */
function generateActivityTitle(type: string, action: string, resource: string): string {
  const actionMap: Record<string, string> = {
    create: 'criou',
    update: 'atualizou',
    delete: 'excluiu',
    view: 'visualizou',
    login: 'fez login',
    logout: 'fez logout',
    invite: 'convidou',
    join: 'entrou',
    leave: 'saiu',
    upload: 'enviou',
    download: 'baixou',
    share: 'compartilhou',
    archive: 'arquivou',
    restore: 'restaurou',
    send: 'enviou',
    receive: 'recebeu',
    approve: 'aprovou',
    reject: 'rejeitou',
    export: 'exportou',
    import: 'importou',
  };

  const resourceMap: Record<string, string> = {
    client: 'cliente',
    team: 'equipe',
    chat: 'chat',
    file: 'arquivo',
    notification: 'notificação',
    user: 'usuário',
    system: 'sistema',
    document: 'documento',
    calendar: 'evento',
    preference: 'preferência',
  };

  const actionText = actionMap[action] || action;
  const resourceText = resourceMap[resource] || resource;

  return `${actionText} ${resourceText}`;
}
