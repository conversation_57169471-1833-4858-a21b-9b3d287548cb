/**
 * Simple Teams API Hooks
 * Direct Appwrite calls with React Query
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import {
  createTeam,
  getTeam,
  updateTeam,
  deleteTeam,
  listTeams,
  getTeamMembers,
  inviteToTeam,
  removeTeamMember,
  updateMemberRoles,
  getOrCreateTeamChat,
  getTeamChat,
  type Team,
  type TeamMembership,
  type CreateTeamData,
  type InviteMemberData
} from '../../lib/appwrite/functions/teams';
import { useAuth } from '../use-auth';

/**
 * Get all teams for current user
 */
export function useTeams() {
  return useQuery({
    queryKey: ['teams'],
    queryFn: async () => {
      const result = await listTeams();
      return result.teams as Team[];
    },
  });
}

/**
 * Get single team
 */
export function useTeam(teamId: string) {
  return useQuery({
    queryKey: ['teams', teamId],
    queryFn: async () => {
      const result = await getTeam(teamId);
      return result as Team;
    },
    enabled: !!teamId,
  });
}

/**
 * Get team members
 */
export function useTeamMembers(teamId: string) {
  return useQuery({
    queryKey: ['teams', teamId, 'members'],
    queryFn: async () => {
      const result = await getTeamMembers(teamId);
      return result.memberships as TeamMembership[];
    },
    enabled: !!teamId,
  });
}

/**
 * Create team
 */
export function useCreateTeam() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateTeamData) => {
      const result = await createTeam(data);
      return result as Team;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teams'] });
      toast.success('Time criado com sucesso');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erro ao criar time');
    },
  });
}

/**
 * Update team
 */
export function useUpdateTeam() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ teamId, name }: { teamId: string; name: string }) => {
      const result = await updateTeam(teamId, name);
      return result as Team;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teams'] });
      toast.success('Time atualizado com sucesso');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erro ao atualizar time');
    },
  });
}

/**
 * Delete team
 */
export function useDeleteTeam() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (teamId: string) => {
      await deleteTeam(teamId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teams'] });
      toast.success('Time excluído com sucesso');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erro ao excluir time');
    },
  });
}

/**
 * Invite member to team
 */
export function useInviteToTeam() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ teamId, data }: { teamId: string; data: InviteMemberData }) => {
      console.log('Enviando convite:', { teamId, email: data.email, roles: data.roles });
      const result = await inviteToTeam(teamId, data);
      return result as TeamMembership;
    },
    onSuccess: (_, { teamId }) => {
      queryClient.invalidateQueries({ queryKey: ['teams', teamId, 'members'] });
      toast.success('Convite enviado com sucesso');
    },
    onError: (error: any) => {
      console.error('Erro ao enviar convite:', error);

      // Tratamento específico de erros
      let errorMessage = 'Erro ao enviar convite';

      if (error.message?.includes('Phone authentication')) {
        errorMessage = 'Erro de configuração: Desabilite a autenticação por telefone no console Appwrite';
      } else if (error.message?.includes('budget cap')) {
        errorMessage = 'Limite de convites atingido. Verifique as configurações do projeto';
      } else if (error.message?.includes('rate limit')) {
        errorMessage = 'Muitos convites enviados. Tente novamente em alguns minutos';
      } else if (error.message?.includes('domain')) {
        errorMessage = 'Domínio não autorizado. Configure os domínios permitidos no console';
      } else if (error.message?.includes('permission')) {
        errorMessage = 'Você não tem permissão para convidar membros neste time';
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error(errorMessage);
    },
  });
}

/**
 * Remove team member
 */
export function useRemoveTeamMember() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ teamId, membershipId }: { teamId: string; membershipId: string }) => {
      await removeTeamMember(teamId, membershipId);
    },
    onSuccess: (_, { teamId }) => {
      queryClient.invalidateQueries({ queryKey: ['teams', teamId, 'members'] });
      toast.success('Membro removido com sucesso');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erro ao remover membro');
    },
  });
}

/**
 * Update member roles
 */
export function useUpdateMemberRoles() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ teamId, membershipId, roles }: { teamId: string; membershipId: string; roles: string[] }) => {
      const result = await updateMemberRoles(teamId, membershipId, roles);
      return result as TeamMembership;
    },
    onSuccess: (_, { teamId }) => {
      queryClient.invalidateQueries({ queryKey: ['teams', teamId, 'members'] });
      toast.success('Permissões atualizadas com sucesso');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erro ao atualizar permissões');
    },
  });
}

/**
 * Get or create team chat
 */
export function useGetOrCreateTeamChat() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ teamId, teamName }: { teamId: string; teamName: string }) => {
      if (!user) throw new Error('Usuário não autenticado');
      return await getOrCreateTeamChat(teamId, teamName, user.$id);
    },
    onSuccess: (chat, { teamId }) => {
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: ['team-chat', teamId] });
      queryClient.invalidateQueries({ queryKey: ['chat', chat.$id] });
      queryClient.invalidateQueries({ queryKey: ['team', teamId] });
      toast.success('Chat do time configurado!');
    },
    onError: (error) => {
      console.error('Erro ao configurar chat do time:', error);
      toast.error('Erro ao configurar chat do time');
    },
  });
}

/**
 * Get team chat by teamId
 */
export function useTeamChat(teamId: string) {
  return useQuery({
    queryKey: ['team-chat', teamId],
    queryFn: async () => {
      return await getTeamChat(teamId);
    },
    enabled: !!teamId,
  });
}
