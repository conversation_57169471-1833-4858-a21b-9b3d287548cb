// Script para configurar coleções no Appwrite e atualizar o .env
require('dotenv').config();
const fs = require('fs');
const { Client, Databases, ID, Permission, Role } = require('node-appwrite');
const { collections } = require('./const.cjs');

// Inicializar cliente Appwrite
const client = new Client();
client
  .setEndpoint(process.env.APPWRITE_ENDPOINT)
  .setProject(process.env.APPWRITE_PROJECT_ID)
  .setKey(process.env.APPWRITE_API_KEY);

const databases = new Databases(client);
const databaseId = process.env.APPWRITE_DATABASE_ID;

// Função para atualizar o arquivo .env
function updateEnvFile(key, value) {
  const envPath = '.env';
  let envContent = '';

  try {
    envContent = fs.readFileSync(envPath, 'utf8');
  } catch (error) {
    console.log('Arquivo .env não encontrado, criando novo...');
  }

  const lines = envContent.split('\n');
  let keyFound = false;

  for (let i = 0; i < lines.length; i++) {
    if (lines[i].startsWith(`${key}=`)) {
      lines[i] = `${key}=${value}`;
      keyFound = true;
      break;
    }
  }

  if (!keyFound) {
    lines.push(`${key}=${value}`);
  }

  fs.writeFileSync(envPath, lines.join('\n'));
  console.log(`✅ Arquivo .env atualizado: ${key}=${value}`);
}

// Função principal
async function setupCollections() {
  try {
    console.log('🚀 Iniciando configuração das coleções...');

    // Verificar configuração
    if (!process.env.APPWRITE_ENDPOINT || !process.env.APPWRITE_PROJECT_ID || !process.env.APPWRITE_API_KEY) {
      throw new Error('❌ Variáveis de ambiente do Appwrite não configuradas');
    }

    // Listar coleções existentes
    const existingCollections = await databases.listCollections(databaseId);
    const existingCollectionNames = existingCollections.collections.map(col => col.name);

    console.log('📋 Coleções existentes:', existingCollectionNames);

    // Mapear IDs das coleções existentes para o .env
    for (const collection of existingCollections.collections) {
      const matchingCollection = collections.find(col => col.name === collection.name);
      if (matchingCollection) {
        updateEnvFile(matchingCollection.envKey, collection.$id);
        console.log(`✅ Coleção ${collection.name} já existe com ID: ${collection.$id}`);
      }
    }

    // Criar coleções que não existem
    for (const collection of collections) {
      if (!existingCollectionNames.includes(collection.name)) {
        console.log(`🔨 Criando coleção: ${collection.name}`);

        // Definir permissões para a coleção
        const permissions = [
          Permission.read(Role.any()),
          Permission.create(Role.any()),
          Permission.update(Role.any()),
          Permission.delete(Role.any())
        ];

        const newCollection = await databases.createCollection(
          databaseId,
          ID.unique(),
          collection.name,
          permissions
        );

        updateEnvFile(collection.envKey, newCollection.$id);
        console.log(`✅ Coleção ${collection.name} criada com ID: ${newCollection.$id}`);
      }
    }

    console.log('🎉 Configuração das coleções concluída com sucesso!');
    console.log('📝 Execute "yarn setup:attributes" para criar os atributos');
  } catch (error) {
    console.error('❌ Erro ao configurar coleções:', error);
    process.exit(1);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  setupCollections();
}

module.exports = { setupCollections };
