import { useState } from 'react';
import {
  IconDownload,
  IconTrash,
  IconX,
  IconExternalLink,
  IconFile,
  IconZoomIn,
  IconZoomOut,
  IconRotateClockwise
} from '@tabler/icons-react';
import { Button } from '../ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle
} from '../ui/dialog';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { useDeleteFile, getFileDownload, getFileView } from '../../hooks/api/use-storage';
import {
  formatFileSize,
  getFileTypeIcon,
  getFileTypeColor,
  isImageFile,
  isPdfFile,
  canPreviewFile
} from '../../lib/file-utils';
import { toast } from 'sonner';

interface DocumentPreviewModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  file: any;
}

export function DocumentPreviewModal({
  open,
  onOpenChange,
  file
}: DocumentPreviewModalProps) {
  const [imageZoom, setImageZoom] = useState(100);
  const [imageRotation, setImageRotation] = useState(0);

  const deleteFileMutation = useDeleteFile();

  if (!file) return null;

  const FileIcon = getFileTypeIcon(file.mimeType);
  const fileColor = getFileTypeColor(file.mimeType);
  const isImage = isImageFile(file.mimeType);
  const isPdf = isPdfFile(file.mimeType);
  const canPreview = canPreviewFile(file.mimeType);

  const handleDownload = () => {
    const downloadUrl = getFileDownload(file.$id);
    const link = document.createElement('a');
    link.href = downloadUrl.toString();
    link.download = file.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    toast.success('Download iniciado');
  };

  const handleDelete = async () => {
    if (confirm(`Tem certeza que deseja excluir "${file.name}"?`)) {
      try {
        await deleteFileMutation.mutateAsync(file.$id);
        onOpenChange(false);
      } catch (error) {
        console.error('Erro ao excluir arquivo:', error);
      }
    }
  };

  const handleOpenInNewTab = () => {
    const viewUrl = getFileView(file.$id);
    window.open(viewUrl.toString(), '_blank');
  };

  const handleZoomIn = () => {
    setImageZoom(prev => Math.min(prev + 25, 300));
  };

  const handleZoomOut = () => {
    setImageZoom(prev => Math.max(prev - 25, 25));
  };

  const handleRotate = () => {
    setImageRotation(prev => (prev + 90) % 360);
  };

  const resetImageControls = () => {
    setImageZoom(100);
    setImageRotation(0);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FileIcon className={`h-6 w-6 ${fileColor}`} />
              <div>
                <DialogTitle className="text-left">{file.name}</DialogTitle>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span>{formatFileSize(file.sizeOriginal)}</span>
                  <span>•</span>
                  <Badge variant="outline" className="text-xs">
                    {file.mimeType?.split('/')[1]?.toUpperCase() || 'FILE'}
                  </Badge>
                  <span>•</span>
                  <span>{new Date(file.$createdAt).toLocaleDateString()}</span>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-2">
              {canPreview && (
                <Button variant="outline" size="sm" onClick={handleOpenInNewTab}>
                  <IconExternalLink className="h-4 w-4" />
                </Button>
              )}
              <Button variant="outline" size="sm" onClick={handleDownload}>
                <IconDownload className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={handleDelete}>
                <IconTrash className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        <Separator />

        {/* Controles para Imagem */}
        {isImage && (
          <div className="flex items-center justify-between py-2">
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handleZoomOut}>
                <IconZoomOut className="h-4 w-4" />
              </Button>
              <span className="text-sm text-muted-foreground min-w-[60px] text-center">
                {imageZoom}%
              </span>
              <Button variant="outline" size="sm" onClick={handleZoomIn}>
                <IconZoomIn className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={handleRotate}>
                <IconRotateClockwise className="h-4 w-4" />
              </Button>
            </div>
            <Button variant="outline" size="sm" onClick={resetImageControls}>
              Resetar
            </Button>
          </div>
        )}

        {/* Área de Preview */}
        <div className="flex-1 overflow-auto bg-muted/30 rounded-lg">
          {isImage ? (
            <div className="flex items-center justify-center min-h-[400px] p-4">
              <img
                src={getFileView(file.$id).toString()}
                alt={file.name}
                className="max-w-full max-h-full object-contain transition-transform"
                style={{
                  transform: `scale(${imageZoom / 100}) rotate(${imageRotation}deg)`
                }}
              />
            </div>
          ) : isPdf ? (
            <div className="h-[500px] w-full">
              <iframe
                src={getFileView(file.$id).toString()}
                className="w-full h-full border-0 rounded-lg"
                title={file.name}
              />
            </div>
          ) : canPreview ? (
            <div className="h-[500px] w-full">
              <iframe
                src={getFileView(file.$id).toString()}
                className="w-full h-full border-0 rounded-lg"
                title={file.name}
              />
            </div>
          ) : (
            <div className="flex items-center justify-center min-h-[400px] p-8">
              <div className="text-center">
                <FileIcon className={`h-16 w-16 ${fileColor} mx-auto mb-4`} />
                <h3 className="text-lg font-medium mb-2">
                  Visualização não disponível
                </h3>
                <p className="text-muted-foreground mb-4">
                  Este tipo de arquivo não pode ser visualizado no navegador.
                </p>
                <div className="flex gap-2 justify-center">
                  <Button onClick={handleDownload}>
                    <IconDownload className="mr-2 h-4 w-4" />
                    Download
                  </Button>
                  <Button variant="outline" onClick={handleOpenInNewTab}>
                    <IconExternalLink className="mr-2 h-4 w-4" />
                    Abrir em Nova Aba
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Informações do Arquivo */}
        <div className="border-t pt-4">
          <h4 className="font-medium mb-3">Informações do Arquivo</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Nome:</span>
              <p className="font-medium truncate" title={file.name}>
                {file.name}
              </p>
            </div>
            <div>
              <span className="text-muted-foreground">Tamanho:</span>
              <p className="font-medium">{formatFileSize(file.sizeOriginal)}</p>
            </div>
            <div>
              <span className="text-muted-foreground">Tipo:</span>
              <p className="font-medium">{file.mimeType}</p>
            </div>
            <div>
              <span className="text-muted-foreground">Criado em:</span>
              <p className="font-medium">
                {new Date(file.$createdAt).toLocaleDateString('pt-BR', {
                  day: '2-digit',
                  month: '2-digit',
                  year: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </p>
            </div>
          </div>

          {file.$updatedAt !== file.$createdAt && (
            <div className="mt-2 text-sm">
              <span className="text-muted-foreground">Atualizado em:</span>
              <span className="ml-2 font-medium">
                {new Date(file.$updatedAt).toLocaleDateString('pt-BR', {
                  day: '2-digit',
                  month: '2-digit',
                  year: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </span>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
