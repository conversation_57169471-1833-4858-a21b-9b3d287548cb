/**
 * Página de demonstração do Gemini File Processor
 * 
 * Esta página demonstra como usar a cloud function do Gemini para processar
 * arquivos de clientes e extrair dados estruturados.
 */

import { GeminiFileProcessorExample } from '@/components/examples/gemini-file-processor-example';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Gemini Demo - Template',
  description: 'Demonstração do processamento de arquivos com Gemini AI',
};

export default function GeminiDemoPage() {
  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Gemini File Processor</h1>
        <p className="text-muted-foreground mt-2">
          Demonstração do processamento de arquivos de clientes usando Google Gemini AI
        </p>
      </div>
      
      <GeminiFileProcessorExample />
    </div>
  );
}
