'use client';

/**
 * Activities Page
 * Comprehensive activity logging dashboard with filtering and search
 */

import { useState, useMemo } from 'react';
import { RefreshCwIcon, TrendingUpIcon, ActivityIcon, LockIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';

import { ActivityList } from '@/components/activities/activity-list';
import { ActivityFilters } from '@/components/activities/activity-filters';
import { ActivityDetailModal } from '@/components/activities/activity-detail-modal';
import { UpgradeAlert } from '@/components/upgrade-alert';
import {
  useInfiniteActivities,
  useActivityStats,
  useRecentActivities
} from '@/hooks/use-api';
import { useAuth } from '@/hooks/use-auth';
import { canViewActivities } from '@/lib/activity-permissions';
import type { Activity, ActivityFilters as ActivityFiltersType } from '@/schemas/activities';

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export default function ActivitiesPage() {
  const { user } = useAuth();
  const [filters, setFilters] = useState<ActivityFiltersType>({});
  const [selectedActivity, setSelectedActivity] = useState<Activity | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);

  // Check if user has permission to view activities
  const hasViewPermission = useMemo(() => canViewActivities(user), [user]);

  // Queries - only fetch if user has permission
  const {
    data: infiniteData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading: isLoadingActivities,
    refetch: refetchActivities,
  } = useInfiniteActivities(hasViewPermission ? filters : {});

  const { data: stats, isLoading: isLoadingStats, refetch: refetchStats } = useActivityStats(hasViewPermission ? {} : undefined);
  const { data: recentActivities, isLoading: isLoadingRecent } = useRecentActivities(hasViewPermission ? 5 : 0);

  // Flatten infinite query data
  const activities = useMemo(() => {
    return infiniteData?.pages.flatMap((page: any) => page.activities) || [];
  }, [infiniteData]);

  // Handlers
  const handleFiltersChange = (newFilters: ActivityFiltersType) => {
    setFilters(newFilters);
  };

  const handleClearFilters = () => {
    setFilters({});
  };

  const handleActivityClick = (activity: Activity) => {
    setSelectedActivity(activity);
    setIsDetailModalOpen(true);
  };

  const handleRefresh = async () => {
    await Promise.all([
      refetchActivities(),
      refetchStats(),
    ]);
  };

  const handleLoadMore = () => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex flex-col gap-4 p-4 md:p-6 border-b md:flex-row md:items-center md:justify-between">
        <div className="min-w-0">
          <h1 className="text-xl md:text-2xl font-bold">Atividades</h1>
          <p className="text-muted-foreground text-sm md:text-base">
            Acompanhe todas as atividades e eventos do sistema
          </p>
        </div>
        {hasViewPermission && (
          <Button onClick={handleRefresh} variant="outline" size="sm">
            <RefreshCwIcon className="h-4 w-4 mr-2" />
            Atualizar
          </Button>
        )}
      </div>

      {/* Content */}
      <div className="flex-1 p-4 md:p-6 space-y-6">

        {/* Permission Check */}
        {!hasViewPermission && (
          <UpgradeAlert
            title="Funcionalidade Premium"
            description="O log de atividades está disponível apenas para usuários com plano Enterprise."
            requiredPlan="enterprise"
            benefits={[
              'Histórico completo de atividades',
              'Filtros avançados por tipo e data',
              'Relatórios detalhados de auditoria',
              'Monitoramento em tempo real'
            ]}
            buttonText="Upgrade para Enterprise"
            buttonSize="default"
          />
        )}

        {/* Stats Cards */}
        {hasViewPermission && stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total</p>
                    <p className="text-2xl font-bold">{stats.total.toLocaleString()}</p>
                  </div>
                  <ActivityIcon className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Hoje</p>
                    <p className="text-2xl font-bold">{stats.todayCount.toLocaleString()}</p>
                  </div>
                  <TrendingUpIcon className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Esta Semana</p>
                    <p className="text-2xl font-bold">{stats.weekCount.toLocaleString()}</p>
                  </div>
                  <TrendingUpIcon className="h-8 w-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Este Mês</p>
                    <p className="text-2xl font-bold">{stats.monthCount.toLocaleString()}</p>
                  </div>
                  <TrendingUpIcon className="h-8 w-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Main Content */}
        {hasViewPermission && (
          <Tabs defaultValue="all" className="space-y-4">
            <TabsList>
              <TabsTrigger value="all">Todas as Atividades</TabsTrigger>
              <TabsTrigger value="recent">Recentes</TabsTrigger>
            </TabsList>

          <TabsContent value="all" className="space-y-4">
            {/* Filters */}
            <Card>
              <CardContent className="p-4">
                <ActivityFilters
                  filters={filters}
                  onFiltersChange={handleFiltersChange}
                  onClearFilters={handleClearFilters}
                  isLoading={isLoadingActivities}
                />
              </CardContent>
            </Card>

            {/* Activities List */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Lista de Atividades</span>
                  {activities.length > 0 && (
                    <Badge variant="secondary">
                      {activities.length} atividade{activities.length !== 1 ? 's' : ''}
                    </Badge>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ActivityList
                  activities={activities}
                  isLoading={isLoadingActivities}
                  onActivityClick={handleActivityClick}
                />

                {/* Load More Button */}
                {hasNextPage && (
                  <div className="mt-6 text-center">
                    <Button
                      onClick={handleLoadMore}
                      disabled={isFetchingNextPage}
                      variant="outline"
                    >
                      {isFetchingNextPage ? 'Carregando...' : 'Carregar Mais'}
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="recent" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Atividades Recentes</CardTitle>
                <p className="text-sm text-gray-600">
                  Últimas atividades registradas nas últimas 24 horas
                </p>
              </CardHeader>
              <CardContent>
                <ActivityList
                  activities={recentActivities || []}
                  isLoading={isLoadingRecent}
                  onActivityClick={handleActivityClick}
                  compact={true}
                />
              </CardContent>
            </Card>
          </TabsContent>
          </Tabs>
        )}

        {/* Activity Detail Modal */}
        {hasViewPermission && (
          <ActivityDetailModal
            activity={selectedActivity}
            isOpen={isDetailModalOpen}
            onClose={() => {
              setIsDetailModalOpen(false);
              setSelectedActivity(null);
            }}
          />
        )}
      </div>
    </div>
  );
}
