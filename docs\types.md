# 🏷️ Tipos TypeScript

Este documento explica a estrutura de tipos TypeScript do template, garantindo type safety completo em toda a aplicação.

## 📋 Visão Geral

O sistema de tipos é organizado em módulos específicos na pasta `app/types/`, cada um responsável por uma área da aplicação:

- **database.ts** - Tipos base e coleções do banco
- **clients.ts** - Gerenciamento de clientes com Zod
- **teams.ts** - Sistema de equipes (Appwrite nativo)
- **chat.ts** - Chat em tempo real com relacionamentos
- **activities.ts** - Logs de atividade
- **websocket.ts** - Real-time updates
- **auth.ts** - Autenticação e usuários

## 🗄️ Tipos Base do Banco

### BaseDocument
Interface base para todos os documentos do Appwrite:

```typescript
interface BaseDocument extends Models.Document {
  $id: string;
  $collectionId: string;
  $databaseId: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  isDeleted?: boolean; // Soft delete
}
```

### Coleções Principais
```typescript
type CollectionName =
  | 'public_profiles'
  | 'notifications'
  | 'activity_logs'
  | 'clients'
  | 'chat_messages'
  | 'team_chats';
```

### Mapeamento de Tipos
```typescript
interface DocumentTypeMap {
  public_profiles: PublicProfile;
  notifications: Notification;
  activity_logs: ActivityLog;
  clients: Client;
  chat_messages: ChatMessage;
  team_chats: TeamChat;
}
```

## 👥 Tipos de Clientes

### Enums e Status
```typescript
type ClientStatus = 'ativo' | 'inativo' | 'prospecto' | 'arquivado';
type ClientType = 'pessoa_fisica' | 'pessoa_juridica';
type ClientPriority = 'baixa' | 'media' | 'alta' | 'critica';
```

### Interface Principal
```typescript
interface Client extends BaseDocument {
  userId: string;
  teamId?: string;
  name: string;
  email?: string;
  phone?: string;
  document?: string;
  type: ClientType;
  company?: string;
  companyDocument?: string;
  status: ClientStatus;
  priority: ClientPriority;
  tags: string[];
  avatar?: string;
  address?: ClientAddress;
}
```

### Validação com Zod
O sistema usa Zod como fonte única de verdade:

```typescript
const clientSchema = z.object({
  name: z.string().min(2).max(200).trim(),
  email: z.string().email().optional(),
  type: clientTypeSchema,
  status: clientStatusSchema.default('ativo'),
  // ... outros campos
});

type Client = z.infer<typeof clientSchema>;
```

## 🏢 Tipos de Teams

### Team (Appwrite Nativo)
```typescript
interface Team extends Models.Team<Models.Preferences> {
  // Usa API nativa do Appwrite Teams
  // $id, $createdAt, $updatedAt, name, total
}

interface TeamMembership extends Models.Membership {
  // userId, userName, userEmail, teamId, roles, etc.
}
```

### Operações de Team
```typescript
interface CreateTeamData {
  name: string;
  roles?: string[];
}

interface InviteMemberData {
  email: string;
  roles: string[];
  url: string;
}
```

## 💬 Tipos de Chat

### Chat de Equipe
```typescript
interface TeamChat extends Models.Document {
  teamId: string;
  name: string;
  description?: string;
  isPrivate: boolean;
  isActive: boolean;
  members: string[]; // Array de user IDs
  lastActivity?: string;
  unreadCount: number;
  allowFileSharing: boolean;
  allowReactions: boolean;
  retentionDays: number;
  messages?: ChatMessage[]; // Relacionamento automático
}
```

### Mensagens
```typescript
interface ChatMessage extends Models.Document {
  chatId: string; // Relacionamento com TeamChat
  userId: string;
  userName: string;
  userAvatar?: string;
  content: string;
  type: MessageType;
  replyTo?: string;
  editedAt?: string;
  reactions: MessageReaction[];
  attachments: MessageAttachment[];
  metadata?: MessageMetadata;
}

type MessageType = 'text' | 'image' | 'file' | 'system' | 'voice';
```

### Chat com IA (Gemini)
```typescript
interface GeminiChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: string;
  status: 'sending' | 'sent' | 'error';
  metadata?: {
    model?: string;
    tokens?: number;
    processingTime?: number;
  };
}
```

## 📊 Tipos de Atividades

### Activity Log
```typescript
interface ActivityLog extends BaseDocument {
  userId: string;
  userName: string;
  type: ActivityType;
  action: string;
  description: string;
  metadata: ActivityMetadata;
  ipAddress?: string;
  userAgent?: string;
  severity: ActivitySeverity;
}

type ActivityType = 
  | 'auth' | 'client' | 'team' | 'chat' 
  | 'file' | 'system' | 'admin';

type ActivitySeverity = 'low' | 'medium' | 'high' | 'critical';
```

## 🔔 Tipos de Notificações

### Notification
```typescript
interface Notification extends BaseDocument {
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  isRead: boolean;
  priority: NotificationPriority;
  actionUrl?: string;
  actionText?: string;
  metadata?: NotificationMetadata;
  expiresAt?: string;
}

type NotificationType = 
  | 'info' | 'success' | 'warning' | 'error' 
  | 'team' | 'chat' | 'system';
```

## 🗂️ Tipos de Cache

### Cache Item
```typescript
interface CacheItem<T = any> {
  data: T;
  timestamp: number;
  ttl: number;
  queryKey: readonly unknown[];
}

interface CacheConfig {
  enabled: boolean;
  defaultTTL: number;
  dbName: string;
  version: number;
}
```

## 🌐 Tipos de Real-time

### WebSocket Events
```typescript
interface DatabaseRealtimeEvent {
  events: string[];
  channels: string[];
  timestamp: string;
  payload: {
    $id: string;
    $collectionId: string;
    $databaseId: string;
    $createdAt: string;
    $updatedAt: string;
    [key: string]: any;
  };
}

interface RealtimeMessage {
  type: 'connected' | 'message' | 'error' | 'close';
  data?: any;
  error?: string;
}
```

## 🔐 Tipos de Autenticação

### User Context
```typescript
interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

interface LoginCredentials {
  email: string;
  password: string;
}

interface RegisterData {
  email: string;
  password: string;
  name: string;
}
```

## 📝 Tipos de Formulários

### Query Options
```typescript
interface QueryOptions {
  limit?: number;
  offset?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
  filters?: Record<string, unknown>;
  search?: string;
}

interface PaginatedResponse<T> {
  documents: T[];
  total: number;
  limit: number;
  offset: number;
  hasMore: boolean;
}
```

## 🛠️ Padrões de Uso

### 1. Validação com Zod
```typescript
// Schema como fonte única de verdade
const schema = z.object({
  name: z.string().min(1),
  email: z.string().email(),
});

// Tipo derivado automaticamente
type FormData = z.infer<typeof schema>;

// Validação em runtime
const result = schema.safeParse(data);
```

### 2. Tipos Condicionais
```typescript
// Tipo baseado em operação
type CreateData<T> = Omit<T, '$id' | '$createdAt' | '$updatedAt'>;
type UpdateData<T> = Partial<Omit<T, '$id' | '$createdAt'>>;
```

### 3. Type Guards
```typescript
function isClient(doc: BaseDocument): doc is Client {
  return doc.$collectionId === 'clients';
}
```

## ✅ Boas Práticas

### 1. **Single Source of Truth**
- Use Zod schemas para validação e tipos
- Derive TypeScript types dos schemas
- Mantenha consistência com banco de dados

### 2. **Nomenclatura Consistente**
- Interfaces em PascalCase: `ClientData`
- Types em camelCase: `clientStatus`
- Enums descritivos: `'ativo' | 'inativo'`

### 3. **Extensibilidade**
- Use interfaces para objetos extensíveis
- Use types para unions e primitivos
- Adicione campos opcionais com `?`

### 4. **Documentação**
- Comente tipos complexos
- Use JSDoc para interfaces públicas
- Mantenha exemplos atualizados

---

**📖 Próximos Passos:**
- [database.md](./database.md) - Como usar os tipos com banco
- [hooks.md](./hooks.md) - Hooks tipados com React Query
- [components.md](./components.md) - Componentes type-safe
