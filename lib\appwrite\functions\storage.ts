import { ID } from 'appwrite';
import { storage, STORAGE_BUCKET_ID } from '../config';

/**
 * Simple Appwrite storage functions
 * Direct calls without unnecessary complexity
 */

/**
 * Upload a file
 */
export async function uploadFile(file: File, fileId?: string) {
  const result = await storage.createFile(
    STORAGE_BUCKET_ID,
    fileId || ID.unique(),
    file
  );

  return result;
}

/**
 * Get a file
 */
export async function getFile(fileId: string) {
  const result = await storage.getFile(STORAGE_BUCKET_ID, fileId);
  return result;
}

/**
 * Delete a file
 */
export async function deleteFile(fileId: string) {
  const result = await storage.deleteFile(STORAGE_BUCKET_ID, fileId);
  return result;
}

/**
 * List files
 */
export async function listFiles(queries?: string[]) {
  const result = await storage.listFiles(STORAGE_BUCKET_ID, queries);
  return result;
}

/**
 * Get file preview URL
 */
export function getFilePreview(
  fileId: string,
  width?: number,
  height?: number,
  gravity?: any,
  quality?: number,
  borderWidth?: number,
  borderColor?: string,
  borderRadius?: number,
  opacity?: number,
  rotation?: number,
  background?: string,
  output?: any
) {
  const url = storage.getFilePreview(
    STORAGE_BUCKET_ID,
    fileId,
    width,
    height,
    gravity,
    quality,
    borderWidth,
    borderColor,
    borderRadius,
    opacity,
    rotation,
    background,
    output
  );

  return url;
}

/**
 * Get file download URL
 */
export function getFileDownload(fileId: string) {
  const url = storage.getFileDownload(STORAGE_BUCKET_ID, fileId);
  return url;
}

/**
 * Get file view URL
 */
export function getFileView(fileId: string) {
  const url = storage.getFileView(STORAGE_BUCKET_ID, fileId);
  return url;
}

/**
 * Update file metadata
 */
export async function updateFile(fileId: string, name?: string, permissions?: string[]) {
  const result = await storage.updateFile(STORAGE_BUCKET_ID, fileId, name, permissions);
  return result;
}
