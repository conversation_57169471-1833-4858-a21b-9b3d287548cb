# 🔐 Configuração de Permissões das Cloud Functions

Este guia detalha como configurar corretamente as permissões de cada cloud function no console do Appwrite.

## 📋 Configurações por Função

### 🔧 Admin Functions

#### 1. **admin-users**
```json
{
  "execute": ["role:admin"],
  "scopes": ["users.read"],
  "timeout": 15,
  "runtime": "node-18.0"
}
```

**Como configurar:**
1. Acesse Functions > admin-users > Settings
2. Execute Access: `["role:admin"]`
3. Timeout: `15` segundos
4. Runtime: `node-18.0`

#### 2. **admin-user-management**
```json
{
  "execute": ["role:admin"],
  "scopes": ["users.read", "users.write"],
  "timeout": 30,
  "runtime": "node-18.0"
}
```

### 🏢 Team Functions

#### 3. **team-create**
```json
{
  "execute": ["users"],
  "scopes": ["teams.read", "teams.write"],
  "timeout": 30,
  "runtime": "node-18.0"
}
```

**Validação interna:** Verifica se usuário tem plano pago

#### 4. **team-list**
```json
{
  "execute": ["users"],
  "scopes": ["teams.read"],
  "timeout": 15,
  "runtime": "node-18.0"
}
```

#### 5. **team-get**
```json
{
  "execute": ["users"],
  "scopes": ["teams.read"],
  "timeout": 15,
  "runtime": "node-18.0"
}
```

**Validação interna:** Verifica se usuário é membro do time

#### 6. **team-update**
```json
{
  "execute": ["users"],
  "scopes": ["teams.read", "teams.write"],
  "timeout": 30,
  "runtime": "node-18.0"
}
```

**Validação interna:** Verifica se usuário é owner ou admin do time

#### 7. **team-delete**
```json
{
  "execute": ["users"],
  "scopes": ["teams.read", "teams.write"],
  "timeout": 30,
  "runtime": "node-18.0"
}
```

**Validação interna:** Verifica se usuário é owner do time

#### 8. **team-invite**
```json
{
  "execute": ["users"],
  "scopes": ["teams.read", "teams.write"],
  "timeout": 30,
  "runtime": "node-18.0"
}
```

**Validação interna:** Verifica se usuário é owner ou admin + limites de plano

#### 9. **team-remove-member**
```json
{
  "execute": ["users"],
  "scopes": ["teams.read", "teams.write"],
  "timeout": 30,
  "runtime": "node-18.0"
}
```

**Validação interna:** Verifica se usuário é owner ou admin

#### 10. **team-update-member**
```json
{
  "execute": ["users"],
  "scopes": ["teams.read", "teams.write"],
  "timeout": 30,
  "runtime": "node-18.0"
}
```

**Validação interna:** Verifica permissões por role

#### 11. **team-list-members**
```json
{
  "execute": ["users"],
  "scopes": ["teams.read"],
  "timeout": 15,
  "runtime": "node-18.0"
}
```

**Validação interna:** Verifica se usuário é membro do time

### 🔧 Utility Functions

#### 12. **utility-send-email**
```json
{
  "execute": ["users"],
  "scopes": ["functions.execute"],
  "timeout": 60,
  "runtime": "node-18.0"
}
```

**Nota:** Pode ser configurado como `["any"]` para emails de sistema

#### 13. **webhook-handler**
```json
{
  "execute": ["any"],
  "scopes": ["databases.write", "users.write"],
  "timeout": 60,
  "runtime": "node-18.0"
}
```

**Nota:** Deve ser `["any"]` para receber webhooks externos

## 🛠️ Como Configurar no Console

### 1. **Acessar Função**
1. Vá para o console do Appwrite
2. Acesse Functions
3. Clique na função desejada

### 2. **Configurar Execute Access**
1. Vá para Settings > Execute Access
2. Adicione as permissões conforme tabela acima
3. Clique em Update

### 3. **Configurar Variáveis de Ambiente**
Para todas as funções, adicione:
```env
APPWRITE_FUNCTION_ENDPOINT=https://cloud.appwrite.io/v1
APPWRITE_FUNCTION_PROJECT_ID=seu-project-id
APPWRITE_FUNCTION_API_KEY=sua-api-key
```

### 4. **Configurar Timeout**
- Admin functions: 15-30 segundos
- Team functions: 15-30 segundos
- Utility functions: 60 segundos
- Webhook functions: 60 segundos

## 🔐 Níveis de Segurança

### **Nível 1: Público**
- `["any"]` - Qualquer pessoa pode executar
- Usado apenas para webhooks externos

### **Nível 2: Usuários Autenticados**
- `["users"]` - Apenas usuários logados
- Validação adicional no código da função

### **Nível 3: Administradores**
- `["role:admin"]` - Apenas usuários com label admin
- Máxima segurança para funções administrativas

## ⚠️ Validações Internas

Mesmo com permissões do Appwrite, cada função tem validações internas:

### **Admin Functions**
- Verifica se usuário tem label "admin"
- Valida JWT e autenticação

### **Team Functions**
- Verifica membership no time
- Valida roles (owner/admin/member)
- Verifica limites de plano
- Valida dados de entrada

### **Utility Functions**
- Valida formato de dados
- Verifica autenticação quando necessário

## 🚨 Troubleshooting

### **Erro: "Execution not allowed"**
- Verifique Execute Access da função
- Confirme se usuário tem as permissões necessárias
- Para admin functions, verifique label "admin"

### **Erro: "Insufficient permissions"**
- Verifique scopes da API key
- Confirme permissões no projeto
- Verifique se API key não expirou

### **Erro: "Function timeout"**
- Aumente timeout da função
- Otimize código da função
- Verifique logs para identificar gargalos

## 📝 Checklist de Configuração

- [ ] Execute Access configurado
- [ ] Timeout apropriado definido
- [ ] Runtime node-18.0 selecionado
- [ ] Variáveis de ambiente configuradas
- [ ] API key com scopes necessários
- [ ] Função deployada e ativa
- [ ] Logs habilitados para debug
- [ ] Testado com usuário apropriado
