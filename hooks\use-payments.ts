import { useMutation } from '@tanstack/react-query';
import { toast } from 'sonner';
import { CLOUD_FUNCTIONS } from '../lib/appwrite/cloudfunctions/const';

/**
 * Hook para gerenciar pagamentos com Stripe
 * Integra com a cloud function de pagamentos
 */

export interface CreateCheckoutParams {
  planId: string;
  billingCycle: 'monthly' | 'yearly';
  successUrl?: string;
  cancelUrl?: string;
}

export interface CreatePortalParams {
  returnUrl?: string;
}

/**
 * Hook para criar sessão de checkout do Stripe
 */
export function useCreateCheckout() {
  return useMutation({
    mutationFn: async (params: CreateCheckoutParams) => {
      const cloudFunctionUrl = CLOUD_FUNCTIONS.STRIPE_PAYMENTS;
      
      if (!cloudFunctionUrl) {
        throw new Error('Cloud function de pagamentos não configurada');
      }

      const response = await fetch(cloudFunctionUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'create-checkout',
          ...params,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Erro ao criar sessão de pagamento');
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Erro ao criar sessão de pagamento');
      }

      return data;
    },
    onSuccess: (data) => {
      // Redirecionar para o Stripe Checkout
      if (data.url) {
        window.location.href = data.url;
      }
    },
    onError: (error) => {
      console.error('Erro ao criar checkout:', error);
      toast.error(error.message || 'Erro ao processar pagamento');
    },
  });
}

/**
 * Hook para criar sessão do portal do cliente
 */
export function useCreatePortal() {
  return useMutation({
    mutationFn: async (params: CreatePortalParams = {}) => {
      const cloudFunctionUrl = CLOUD_FUNCTIONS.STRIPE_PAYMENTS;
      
      if (!cloudFunctionUrl) {
        throw new Error('Cloud function de pagamentos não configurada');
      }

      const response = await fetch(cloudFunctionUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'create-portal',
          returnUrl: params.returnUrl,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Erro ao criar portal do cliente');
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Erro ao criar portal do cliente');
      }

      return data;
    },
    onSuccess: (data) => {
      // Redirecionar para o portal do Stripe
      if (data.url) {
        window.location.href = data.url;
      }
    },
    onError: (error) => {
      console.error('Erro ao criar portal:', error);
      toast.error(error.message || 'Erro ao acessar portal do cliente');
    },
  });
}

/**
 * Hook para processar upgrade de plano
 */
export function useUpgradePlan() {
  const createCheckout = useCreateCheckout();

  return useMutation({
    mutationFn: async (params: {
      planId: string;
      billingCycle?: 'monthly' | 'yearly';
      successUrl?: string;
      cancelUrl?: string;
    }) => {
      const { planId, billingCycle = 'monthly', successUrl, cancelUrl } = params;

      // Validar plano
      if (!['pro', 'enterprise'].includes(planId)) {
        throw new Error('Plano inválido');
      }

      // Criar URLs de retorno se não fornecidas
      const baseUrl = window.location.origin;
      const defaultSuccessUrl = successUrl || `${baseUrl}/dashboard/plans?success=true&plan=${planId}`;
      const defaultCancelUrl = cancelUrl || `${baseUrl}/dashboard/plans?canceled=true`;

      return createCheckout.mutateAsync({
        planId,
        billingCycle,
        successUrl: defaultSuccessUrl,
        cancelUrl: defaultCancelUrl,
      });
    },
    onMutate: () => {
      toast.loading('Redirecionando para pagamento...');
    },
    onError: (error) => {
      toast.dismiss();
      console.error('Erro no upgrade:', error);
      toast.error(error.message || 'Erro ao processar upgrade');
    },
  });
}

/**
 * Hook para gerenciar assinatura (portal do cliente)
 */
export function useManageSubscription() {
  const createPortal = useCreatePortal();

  return useMutation({
    mutationFn: async (returnUrl?: string) => {
      const baseUrl = window.location.origin;
      const defaultReturnUrl = returnUrl || `${baseUrl}/dashboard/plans`;

      return createPortal.mutateAsync({
        returnUrl: defaultReturnUrl,
      });
    },
    onMutate: () => {
      toast.loading('Redirecionando para gerenciar assinatura...');
    },
    onError: (error) => {
      toast.dismiss();
      console.error('Erro ao acessar portal:', error);
      toast.error(error.message || 'Erro ao acessar portal do cliente');
    },
  });
}

/**
 * Utilitários para pagamentos
 */
export const PaymentUtils = {
  /**
   * Verifica se a cloud function de pagamentos está configurada
   */
  isConfigured: (): boolean => {
    return Boolean(CLOUD_FUNCTIONS.STRIPE_PAYMENTS);
  },

  /**
   * Gera URLs de retorno para checkout
   */
  generateReturnUrls: (planId: string, baseUrl?: string) => {
    const base = baseUrl || window.location.origin;
    return {
      successUrl: `${base}/dashboard/plans?success=true&plan=${planId}`,
      cancelUrl: `${base}/dashboard/plans?canceled=true`,
    };
  },

  /**
   * Processa parâmetros de URL após retorno do Stripe
   */
  processReturnParams: () => {
    const urlParams = new URLSearchParams(window.location.search);
    const success = urlParams.get('success');
    const canceled = urlParams.get('canceled');
    const plan = urlParams.get('plan');

    if (success === 'true') {
      toast.success(
        plan 
          ? `Upgrade para o plano ${plan} realizado com sucesso!`
          : 'Pagamento realizado com sucesso!'
      );
      
      // Limpar parâmetros da URL
      const newUrl = window.location.pathname;
      window.history.replaceState({}, '', newUrl);
      
      return { type: 'success', plan };
    }

    if (canceled === 'true') {
      toast.info('Pagamento cancelado');
      
      // Limpar parâmetros da URL
      const newUrl = window.location.pathname;
      window.history.replaceState({}, '', newUrl);
      
      return { type: 'canceled' };
    }

    return null;
  },

  /**
   * Valida se um plano é válido para upgrade
   */
  isValidPlan: (planId: string): boolean => {
    return ['pro', 'enterprise'].includes(planId);
  },

  /**
   * Calcula desconto anual
   */
  calculateYearlyDiscount: (monthlyPrice: number): number => {
    const yearlyPrice = monthlyPrice * 10; // 2 meses grátis
    const totalMonthly = monthlyPrice * 12;
    return Math.round(((totalMonthly - yearlyPrice) / totalMonthly) * 100);
  },
};

/**
 * Hook para processar parâmetros de retorno do Stripe
 */
export function usePaymentReturn() {
  return {
    processReturn: PaymentUtils.processReturnParams,
  };
}

/**
 * Tipos para TypeScript
 */
export interface PaymentResponse {
  success: boolean;
  sessionId?: string;
  url?: string;
  error?: string;
  details?: string;
}

export interface PortalResponse {
  success: boolean;
  url?: string;
  error?: string;
  details?: string;
}

export type BillingCycle = 'monthly' | 'yearly';
export type PlanId = 'pro' | 'enterprise';

/**
 * Constantes para pagamentos
 */
export const PAYMENT_CONSTANTS = {
  VALID_PLANS: ['pro', 'enterprise'] as const,
  BILLING_CYCLES: ['monthly', 'yearly'] as const,
  YEARLY_DISCOUNT_MONTHS: 2, // 2 meses grátis no plano anual
} as const;
