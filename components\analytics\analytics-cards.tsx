/**
 * Componente de cards de analytics com dados reais
 */

import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import {
  IconUsers,
  IconUsersGroup,
  IconTrendingUp,
  IconTrendingDown,
  IconChartLine,
  IconBell,
  IconClock,
  IconCalendar,
  IconLayoutKanban,
  IconChecklist,
  IconFiles,
  IconActivity
} from "@tabler/icons-react";
import type { AnalyticsData } from '@/hooks/use-analytics';

interface AnalyticsCardsProps {
  data?: AnalyticsData;
}

export function AnalyticsCards({ data }: AnalyticsCardsProps) {
  if (!data) {
    return null;
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('pt-BR').format(value);
  };

  const formatPercentage = (value: number) => {
    const isPositive = value >= 0;
    return (
      <div className={`flex items-center gap-1 ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
        {isPositive ? (
          <IconTrendingUp className="h-4 w-4" />
        ) : (
          <IconTrendingDown className="h-4 w-4" />
        )}
        <span>{Math.abs(value)}%</span>
      </div>
    );
  };

  const cards = [
    {
      title: "Usuários Totais",
      value: formatNumber(data.totalUsers),
      total: formatNumber(data.activeUsers),
      growth: data.userGrowth,
      icon: IconUsers,
      description: "usuários únicos",
    },
    {
      title: "Clientes",
      value: formatNumber(data.totalClients),
      growth: data.clientGrowth,
      icon: IconUsersGroup,
      description: "clientes cadastrados",
    },
    {
      title: "Receita Total",
      value: formatCurrency(data.totalRevenue),
      growth: data.revenueGrowth,
      icon: IconTrendingUp,
      description: "receita acumulada",
    },
    {
      title: "Taxa de Conversão",
      value: `${data.conversionRate.toFixed(1)}%`,
      growth: 0, // Não temos dados históricos de conversão
      icon: IconChartLine,
      description: "clientes/usuários",
    },
  ];

  return (
    <div>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {cards.map((card, index) => {
          const Icon = card.icon;
          const isPositiveGrowth = card.growth >= 0;

          return (
            <Card key={index} className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {card.title}
                </CardTitle>
                <Icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="text-2xl font-bold">
                    {card.value}
                  </div>

                  {card.total && (
                    <div className="text-xs text-muted-foreground">
                      de {card.total} total
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <p className="text-xs text-muted-foreground">
                      {card.description}
                    </p>

                    <Badge
                      variant={isPositiveGrowth ? "default" : "destructive"}
                      className="text-xs"
                    >
                      {formatPercentage(card.growth)}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Cards de funcionalidades do sistema */}
      <div className="mt-6 grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Eventos
            </CardTitle>
            <IconCalendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-2xl font-bold">
                {formatNumber(data.totalEvents)}
              </div>
              <div className="flex items-center justify-between">
                <p className="text-xs text-muted-foreground">
                  eventos cadastrados
                </p>
                <Badge
                  variant={data.eventsGrowth >= 0 ? "default" : "destructive"}
                  className="text-xs"
                >
                  {formatPercentage(data.eventsGrowth)}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Boards Kanban
            </CardTitle>
            <IconLayoutKanban className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-2xl font-bold">
                {formatNumber(data.totalKanbanBoards)}
              </div>
              <div className="flex items-center justify-between">
                <p className="text-xs text-muted-foreground">
                  boards ativos: {data.activeBoards}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Tarefas
            </CardTitle>
            <IconChecklist className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-2xl font-bold">
                {formatNumber(data.totalKanbanTasks)}
              </div>
              <div className="flex items-center justify-between">
                <p className="text-xs text-muted-foreground">
                  concluídas: {data.completedTasks}
                </p>
                <Badge
                  variant={data.tasksGrowth >= 0 ? "default" : "destructive"}
                  className="text-xs"
                >
                  {formatPercentage(data.tasksGrowth)}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Documentos
            </CardTitle>
            <IconFiles className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-2xl font-bold">
                {formatNumber(data.totalDocuments)}
              </div>
              <p className="text-xs text-muted-foreground">
                arquivos armazenados
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Cards de métricas de atividade */}
      <div className="mt-6 grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Notificações
            </CardTitle>
            <IconBell className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatNumber(data.notificationsCount)}
            </div>
            <p className="text-xs text-muted-foreground">
              notificações ativas
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Tempo de Sessão
            </CardTitle>
            <IconClock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {data.averageSessionTime > 0 ? `${Math.round(data.averageSessionTime / 60)}min` : 'N/A'}
            </div>
            <p className="text-xs text-muted-foreground">
              tempo médio por sessão
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Usuários Ativos
            </CardTitle>
            <IconActivity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-1">
              <div className="text-lg font-bold">
                {formatNumber(data.activeUsers)}
              </div>
              <p className="text-xs text-muted-foreground">
                de {formatNumber(data.totalUsers)} total
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
