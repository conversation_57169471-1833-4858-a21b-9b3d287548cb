/**
 * Centralized Query Keys Factory
 * Provides consistent query key generation for all API operations
 * Following React Query best practices for cache invalidation
 */

// ============================================================================
// QUERY KEYS FACTORY
// ============================================================================

/**
 * Creates a standardized query key structure for any entity
 */
function createEntityQueryKeys<TFilters = Record<string, unknown>>(entityName: string) {
  return {
    all: [entityName] as const,
    lists: () => [entityName, 'list'] as const,
    list: (filters: TFilters) => [entityName, 'list', filters] as const,
    details: () => [entityName, 'detail'] as const,
    detail: (id: string) => [entityName, 'detail', id] as const,
    search: (term: string, filters?: TFilters) => [entityName, 'search', term, filters] as const,
    stats: () => [entityName, 'stats'] as const,
  };
}

/**
 * Creates query keys for user-specific entities
 */
function createUserEntityQueryKeys<TFilters = Record<string, unknown>>(entityName: string) {
  return {
    ...createEntityQueryKeys<TFilters>(entityName),
    forUser: (userId: string, filters?: TFilters) => [entityName, 'forUser', userId, filters] as const,
    byUser: (userId: string) => [entityName, 'byUser', userId] as const,
    unreadCount: (userId: string) => [entityName, 'unreadCount', userId] as const,
  };
}

/**
 * Creates query keys for team-specific entities
 */
function createTeamEntityQueryKeys<TFilters = Record<string, unknown>>(entityName: string) {
  return {
    ...createUserEntityQueryKeys<TFilters>(entityName),
    byTeam: (teamId: string) => [entityName, 'byTeam', teamId] as const,
    forTeam: (teamId: string, filters?: TFilters) => [entityName, 'forTeam', teamId, filters] as const,
  };
}

// ============================================================================
// ENTITY-SPECIFIC QUERY KEYS
// ============================================================================

export const queryKeys = {
  // Public Profiles
  publicProfiles: {
    ...createUserEntityQueryKeys('public_profiles'),
    byUserId: (userId: string) => ['public_profiles', 'byUserId', userId] as const,
  },

  // Notifications
  notifications: {
    ...createUserEntityQueryKeys('notifications'),
  },

  // Clients
  clients: {
    ...createTeamEntityQueryKeys('clients'),
    byStatus: (status: string) => ['clients', 'byStatus', status] as const,
  },

  // Activities
  activities: {
    ...createUserEntityQueryKeys('activities'),
    byType: (type: string) => ['activities', 'byType', type] as const,
    byAction: (action: string) => ['activities', 'byAction', action] as const,
    byResource: (resource: string) => ['activities', 'byResource', resource] as const,
    recent: () => ['activities', 'recent'] as const,
    stats: () => ['activities', 'stats'] as const,
    infinite: (filters: Record<string, unknown>) => ['activities', 'infinite', filters] as const,
  },

  // Teams (for future use if needed)
  teams: {
    ...createUserEntityQueryKeys('teams'),
  },

  // Events
  events: {
    ...createTeamEntityQueryKeys('events'),
    byDateRange: (userId: string, start: string, end: string, teamId?: string) =>
      ['events', 'byDateRange', userId, start, end, teamId] as const,
    upcoming: (userId: string, teamId?: string) =>
      ['events', 'upcoming', userId, teamId] as const,
    recurring: (parentId: string) =>
      ['events', 'recurring', parentId] as const,
    stats: (userId: string, teamId?: string) =>
      ['events', 'stats', userId, teamId] as const,
  },

  // Event Categories
  eventCategories: {
    ...createTeamEntityQueryKeys('event_categories'),
    defaults: (userId: string) =>
      ['event_categories', 'defaults', userId] as const,
  },
} as const;

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Helper to invalidate all queries for an entity
 */
export function getEntityInvalidationKeys(entityName: keyof typeof queryKeys) {
  return { queryKey: queryKeys[entityName].all };
}

/**
 * Helper to invalidate list queries for an entity
 */
export function getEntityListInvalidationKeys(entityName: keyof typeof queryKeys) {
  return { queryKey: queryKeys[entityName].lists() };
}

/**
 * Helper to invalidate detail queries for an entity
 */
export function getEntityDetailInvalidationKeys(entityName: keyof typeof queryKeys) {
  return { queryKey: queryKeys[entityName].details() };
}

/**
 * Type-safe query key generator for custom entities
 */
export function createCustomQueryKeys<TFilters = Record<string, unknown>>(
  entityName: string,
  type: 'basic' | 'user' | 'team' = 'basic'
) {
  switch (type) {
    case 'user':
      return createUserEntityQueryKeys<TFilters>(entityName);
    case 'team':
      return createTeamEntityQueryKeys<TFilters>(entityName);
    default:
      return createEntityQueryKeys<TFilters>(entityName);
  }
}
