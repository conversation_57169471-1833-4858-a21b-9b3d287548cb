/**
 * ColumnFormModal Component
 * Modal for creating and editing kanban columns
 */

import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useSnapshot } from 'valtio';
import { Palette, Hash } from 'lucide-react';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '../ui/form';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Button } from '../ui/button';
import { Switch } from '../ui/switch';

import { kanbanStore, kanbanActions } from '../../stores/kanban-store';
import { useAddColumn, useUpdateColumn } from '../../hooks/api/use-kanban';
import { useAuth } from '../../hooks/use-auth';
import { createColumnSchema, type CreateColumnData } from '../../schemas/kanban';

const columnColors = [
  '#3B82F6', // Blue
  '#10B981', // Green
  '#F59E0B', // Yellow
  '#EF4444', // Red
  '#8B5CF6', // Purple
  '#06B6D4', // Cyan
  '#F97316', // Orange
  '#84CC16', // Lime
  '#EC4899', // Pink
  '#6B7280', // Gray
  '#1F2937', // Dark Gray
  '#374151', // Slate
];

export function ColumnFormModal() {
  const { user } = useAuth();
  const snap = useSnapshot(kanbanStore);
  const createColumnMutation = useAddColumn();
  const updateColumnMutation = useUpdateColumn();

  const isOpen = snap.columnCreate.isOpen || snap.columnEdit.isOpen;
  const isEditing = snap.columnEdit.isOpen;
  const column = snap.columnEdit.column;
  const boardId = snap.columnCreate.boardId || snap.columnEdit.column?.boardId;

  const form = useForm({
    resolver: zodResolver(createColumnSchema),
    defaultValues: {
      userId: user?.$id || '',
      teamId: '',
      boardId: boardId || '',
      title: '',
      description: '',
      color: columnColors[0],
      isCollapsed: false,
      taskLimit: undefined,
      isArchived: false,
    },
  });

  // Reset form when modal opens/closes or column changes
  useEffect(() => {
    if (isOpen) {
      if (isEditing && column) {
        form.reset({
          userId: column.userId,
          teamId: column.teamId || '',
          boardId: column.boardId,
          title: column.title,
          description: column.description || '',
          color: column.color || columnColors[0],
          isCollapsed: column.isCollapsed,
          taskLimit: column.taskLimit,
          isArchived: column.isArchived,
        });
      } else {
        form.reset({
          userId: user?.$id || '',
          teamId: '',
          boardId: boardId || '',
          title: '',
          description: '',
          color: columnColors[0],
          isCollapsed: false,
          taskLimit: undefined,
          isArchived: false,
        });
      }
    }
  }, [isOpen, isEditing, column, form, user?.$id, boardId]);

  const onSubmit = async (data: CreateColumnData) => {
    if (!user) return;

    try {
      if (isEditing && column) {
        await updateColumnMutation.mutateAsync({
          boardId: column.boardId!,
          columnId: column.id,
          columnData: data,
          userId: user.$id,
        });
        kanbanActions.closeColumnEdit();
      } else {
        await createColumnMutation.mutateAsync({
          boardId: boardId!,
          columnData: data,
          userId: user.$id,
        });
        kanbanActions.closeColumnCreate();
      }
      form.reset();
    } catch (error) {
      console.error('Error submitting column:', error);
    }
  };

  const handleClose = () => {
    if (isEditing) {
      kanbanActions.closeColumnEdit();
    } else {
      kanbanActions.closeColumnCreate();
    }
    form.reset();
  };

  const isLoading = createColumnMutation.isPending || updateColumnMutation.isPending;
  const selectedColor = form.watch('color');

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Editar Coluna' : 'Nova Coluna'}
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? 'Atualize as configurações da coluna.'
              : 'Crie uma nova coluna para organizar suas tarefas.'
            }
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Title */}
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Título *</FormLabel>
                  <FormControl>
                    <Input placeholder="Digite o título da coluna..." {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Descrição</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Descreva o propósito da coluna..."
                      className="resize-none"
                      rows={2}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Color */}
            <FormField
              control={form.control}
              name="color"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Cor da Coluna</FormLabel>
                  <FormControl>
                    <div className="space-y-3">
                      <div className="grid grid-cols-6 gap-2">
                        {columnColors.map((color) => (
                          <button
                            key={color}
                            type="button"
                            className={`w-8 h-8 rounded-full border-2 transition-all ${
                              selectedColor === color
                                ? 'border-foreground scale-110'
                                : 'border-border hover:scale-105'
                            }`}
                            style={{ backgroundColor: color }}
                            onClick={() => field.onChange(color)}
                          />
                        ))}
                      </div>
                      <div className="flex items-center gap-2">
                        <Palette className="h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="#000000"
                          value={field.value}
                          onChange={field.onChange}
                          className="font-mono"
                        />
                      </div>
                    </div>
                  </FormControl>
                  <FormDescription>
                    Escolha uma cor ou digite um código hexadecimal
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Task Limit (WIP Limit) */}
            <FormField
              control={form.control}
              name="taskLimit"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Limite de Tarefas (WIP)</FormLabel>
                  <FormControl>
                    <div className="flex items-center gap-2">
                      <Hash className="h-4 w-4 text-muted-foreground" />
                      <Input
                        type="number"
                        min="1"
                        placeholder="Sem limite"
                        {...field}
                        onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                      />
                    </div>
                  </FormControl>
                  <FormDescription>
                    Limite máximo de tarefas nesta coluna (Work In Progress)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Collapsed State */}
            <FormField
              control={form.control}
              name="isCollapsed"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Coluna Recolhida</FormLabel>
                    <FormDescription>
                      Iniciar com a coluna minimizada
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {/* Form Actions */}
            <div className="flex justify-end gap-2 pt-4">
              <Button type="button" variant="outline" onClick={handleClose}>
                Cancelar
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Salvando...' : isEditing ? 'Atualizar' : 'Criar'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
