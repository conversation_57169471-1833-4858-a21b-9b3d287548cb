# 🔐 Sistema de Permissões

Este documento explica o sistema completo de permissões do template, incluindo permissões de teams, documentos e recursos do sistema.

## 📋 Visão Geral

O sistema de permissões é composto por três camadas:

1. **Permissões de Team** - Baseadas em roles/cargos customizáveis
2. **Permissões de Documentos** - Integração automática com Appwrite
3. **Permissões de Interface** - Controle de acesso a recursos da UI

## 🏗️ Arquitetura

### Estrutura de Arquivos

```
app/
├── lib/
│   ├── permissions.ts              # Sistema base de permissões
│   ├── team-permissions.ts         # Permissões específicas de teams
│   └── document-permissions.ts     # Permissões para documentos
├── hooks/
│   ├── use-document-permissions.ts # Hook para permissões de documentos
│   └── api/use-permissions.ts      # Hooks para gerenciar permissões
├── contexts/
│   └── team-context.tsx           # Contexto com permissões
└── types/
    └── permissions.ts             # Tipos de permissões
```

## 🎯 Tipos de Usuário

### Hierarquia de Roles

```typescript
type UserType = 'owner' | 'admin' | 'user' | 'guest';
```

#### Owner (Proprietário)
- **Acesso**: Total ao team
- **Pode**: Alterar preferências, deletar team, gerenciar todos os membros
- **Limitação**: Apenas um por team

#### Admin (Administrador)
- **Acesso**: CRUD completo + gerenciamento de membros
- **Pode**: Convidar/remover membros, criar/editar/deletar recursos
- **Limitação**: Não pode alterar preferências do team

#### User (Usuário)
- **Acesso**: Criar, visualizar e editar próprios recursos
- **Pode**: Trabalhar com recursos que criou ou tem permissão
- **Limitação**: Não pode gerenciar membros

#### Guest (Convidado)
- **Acesso**: Apenas visualização
- **Pode**: Ver recursos compartilhados
- **Limitação**: Não pode criar ou editar

## 📊 Recursos do Sistema

### Lista Completa de Recursos

```typescript
type SystemResource =
  | 'dashboard'      // Dashboard principal
  | 'analytics'      // Análises e métricas
  | 'calendar'       // Calendário e eventos
  | 'documents'      // Documentos gerais
  | 'kanban'         // Boards Kanban
  | 'clients'        // Gerenciamento de clientes
  | 'teams'          // Gerenciamento de teams
  | 'team_chat'      // Chat do team
  | 'activities'     // Logs de atividade
  | 'reports'        // Relatórios
  | 'preferences'    // Configurações do team
  | 'plans'          // Planos e assinaturas
  | 'help';          // Ajuda e suporte
```

### Ações por Recurso

```typescript
type ResourceAction = 'view' | 'create' | 'edit' | 'delete' | 'manage';
```

- **view**: Visualizar o recurso
- **create**: Criar novos itens
- **edit**: Editar itens existentes
- **delete**: Deletar itens
- **manage**: Acesso administrativo completo

## 🔧 Configuração de Permissões

### Permissões Padrão por Role

```typescript
const DEFAULT_PERMISSIONS: Record<UserType, SystemResource[]> = {
  owner: [
    'dashboard', 'analytics', 'calendar', 'documents', 'kanban',
    'clients', 'teams', 'team_chat', 'activities', 'reports',
    'preferences', 'plans', 'help'
  ],
  admin: [
    'dashboard', 'analytics', 'calendar', 'documents', 'kanban',
    'clients', 'teams', 'team_chat', 'activities', 'reports', 'help'
  ],
  user: [
    'dashboard', 'calendar', 'documents', 'kanban',
    'clients', 'team_chat', 'activities', 'help'
  ],
  guest: [
    'dashboard', 'team_chat', 'help'
  ]
};
```

### Ações Padrão por Role

```typescript
const DEFAULT_ACTIONS: Record<UserType, Record<SystemResource, ResourceAction[]>> = {
  owner: {
    // Acesso total a todos os recursos
    clients: ['view', 'create', 'edit', 'delete', 'manage'],
    teams: ['view', 'create', 'edit', 'delete', 'manage'],
    preferences: ['view', 'edit', 'manage']
  },
  admin: {
    // CRUD completo, sem manage em preferências
    clients: ['view', 'create', 'edit', 'delete'],
    teams: ['view', 'create', 'edit', 'delete'],
    preferences: ['view']
  },
  user: {
    // Criar e editar próprios itens
    clients: ['view', 'create', 'edit'],
    documents: ['view', 'create', 'edit'],
    kanban: ['view', 'create', 'edit']
  },
  guest: {
    // Apenas visualização
    dashboard: ['view'],
    team_chat: ['view'],
    help: ['view']
  }
};
```

## 🎨 Cargos Customizáveis

### Criar Cargo Customizado

```typescript
import { useCreateTeamRole } from '@/hooks/api/use-permissions';

function CreateCustomRole() {
  const createRole = useCreateTeamRole();

  const handleSubmit = async () => {
    await createRole.mutateAsync({
      teamId: 'team123',
      roleData: {
        name: 'Gerente de Vendas',
        description: 'Acesso total a clientes e relatórios de vendas',
        color: '#3b82f6',
        userType: 'user', // Role base
        permissions: [
          {
            resource: 'clients',
            actions: ['view', 'create', 'edit', 'delete']
          },
          {
            resource: 'reports',
            actions: ['view', 'create']
          },
          {
            resource: 'analytics',
            actions: ['view']
          }
        ],
        isDefault: false
      }
    });
  };
}
```

### Estrutura de Cargo Customizado

```typescript
interface TeamRole {
  id: string;
  name: string;
  description?: string;
  color?: string;
  userType: UserType; // Role base do Appwrite
  permissions: TeamPermission[];
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

interface TeamPermission {
  resource: SystemResource;
  actions: ResourceAction[];
}
```

## 📄 Permissões de Documentos

### Sistema Automático

O sistema aplica automaticamente permissões do Appwrite aos documentos:

```typescript
import { useDocumentPermissions } from '@/hooks/use-document-permissions';

function CreateDocument() {
  const { permissions, teamId } = useDocumentPermissions();

  const handleCreate = async (data: any) => {
    // Permissões aplicadas automaticamente
    await createDocument(COLLECTIONS.CLIENTS, data, {
      userId: user.$id,
      teamId,
      permissionContext,
      isPublic: false
    });
  };
}
```

### Lógica de Permissões de Documentos

1. **Criador**: Sempre tem acesso total
   ```typescript
   Permission.read(Role.user(creatorId))
   Permission.write(Role.user(creatorId))
   Permission.delete(Role.user(creatorId))
   ```

2. **Team**: Baseado nas permissões do cargo
   ```typescript
   // Se o cargo permite 'view' em 'documents'
   Permission.read(Role.team(teamId))

   // Se o cargo permite 'edit' em 'documents'
   Permission.write(Role.team(teamId))

   // Se o cargo permite 'delete' em 'documents'
   Permission.delete(Role.team(teamId))
   ```

3. **Público**: Se `isPublic: true`
   ```typescript
   Permission.read(Role.any())
   ```

### Verificar Acesso a Documentos

```typescript
import { useDocumentAccess } from '@/hooks/use-document-permissions';

function DocumentCard({ document }) {
  const { canRead, canWrite, canDelete } = useDocumentAccess(document.$permissions);

  return (
    <Card>
      {canRead && <DocumentContent document={document} />}

      <CardActions>
        {canWrite && (
          <Button onClick={() => editDocument(document)}>
            Editar
          </Button>
        )}
        {canDelete && (
          <Button variant="destructive" onClick={() => deleteDocument(document)}>
            Deletar
          </Button>
        )}
      </CardActions>
    </Card>
  );
}
```

## 🔍 Verificação de Permissões

### Hook Principal

```typescript
import { useHasPermission } from '@/contexts/team-context';

function MyComponent() {
  const hasPermission = useHasPermission();

  const canCreateClients = hasPermission('clients', 'create');
  const canManageTeams = hasPermission('teams', 'manage');
  const canViewAnalytics = hasPermission('analytics', 'view');

  return (
    <div>
      {canCreateClients && (
        <Button onClick={createClient}>Novo Cliente</Button>
      )}
      {canManageTeams && (
        <Button onClick={manageTeam}>Gerenciar Team</Button>
      )}
      {canViewAnalytics && (
        <AnalyticsWidget />
      )}
    </div>
  );
}
```

### Verificação de Acesso a Recursos

```typescript
import { useHasAccess } from '@/contexts/team-context';

function ProtectedComponent({ resource }: { resource: SystemResource }) {
  const hasAccess = useHasAccess(resource);

  if (!hasAccess) {
    return (
      <div className="text-center p-8">
        <h3>Acesso Negado</h3>
        <p>Você não tem permissão para acessar este recurso.</p>
      </div>
    );
  }

  return <ResourceContent />;
}
```

## 🛡️ Proteção de Rotas

### Componente de Proteção

```typescript
import { useHasAccess } from '@/contexts/team-context';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  resource: SystemResource;
  action?: ResourceAction;
  fallback?: string;
}

function ProtectedRoute({
  children,
  resource,
  action = 'view',
  fallback = '/dashboard'
}: ProtectedRouteProps) {
  const hasAccess = useHasAccess(resource);
  const hasPermission = useHasPermission();
  const router = useRouter();

  useEffect(() => {
    if (!hasAccess) {
      router.replace(fallback);
      return;
    }

    if (action !== 'view' && !hasPermission(resource, action)) {
      router.replace(fallback);
    }
  }, [hasAccess, hasPermission, resource, action, router, fallback]);

  if (!hasAccess || (action !== 'view' && !hasPermission(resource, action))) {
    return null; // ou um loading spinner
  }

  return <>{children}</>;
}
```

### Uso em Rotas (Next.js App Router)

```typescript
// app/(dashboard)/clients/page.tsx
import { ProtectedRoute } from '@/components/protected-route';
import { ClientsPage } from '@/components/clients-page';

export default function ClientsRoute() {
  return (
    <ProtectedRoute resource="clients">
      <ClientsPage />
    </ProtectedRoute>
  );
}

// app/(dashboard)/teams/page.tsx
export default function TeamsRoute() {
  return (
    <ProtectedRoute resource="teams" action="manage">
      <TeamsManagement />
    </ProtectedRoute>
  );
}

// app/(dashboard)/preferences/page.tsx
export default function PreferencesRoute() {
  return (
    <ProtectedRoute resource="preferences" action="edit">
      <TeamPreferences />
    </ProtectedRoute>
  );
}
```

## 🎛️ Gerenciamento de Permissões

### Atribuir Cargo a Membro

```typescript
import { useAssignRoleToMember } from '@/hooks/api/use-permissions';

function AssignRoleForm({ userId, teamId }: { userId: string; teamId: string }) {
  const assignRole = useAssignRoleToMember();
  const [selectedRoleId, setSelectedRoleId] = useState('');

  const handleAssign = async () => {
    await assignRole.mutateAsync({
      teamId,
      userId,
      roleId: selectedRoleId
    });
  };

  return (
    <form onSubmit={handleAssign}>
      <Select value={selectedRoleId} onValueChange={setSelectedRoleId}>
        <SelectItem value="admin">Administrador</SelectItem>
        <SelectItem value="user">Usuário</SelectItem>
        <SelectItem value="guest">Convidado</SelectItem>
        <SelectItem value="custom-role-1">Gerente de Vendas</SelectItem>
      </Select>

      <Button type="submit">Atribuir Cargo</Button>
    </form>
  );
}
```

### Editar Permissões de Cargo

```typescript
import { useUpdateTeamRole } from '@/hooks/api/use-permissions';

function EditRolePermissions({ role }: { role: TeamRole }) {
  const updateRole = useUpdateTeamRole();
  const [permissions, setPermissions] = useState(role.permissions);

  const handleSave = async () => {
    await updateRole.mutateAsync({
      teamId: role.teamId,
      roleId: role.id,
      updates: {
        permissions
      }
    });
  };

  return (
    <div>
      <h3>Editar Permissões - {role.name}</h3>

      {SYSTEM_RESOURCES.map(resource => (
        <div key={resource}>
          <h4>{resource}</h4>
          {RESOURCE_ACTIONS.map(action => (
            <Checkbox
              key={action}
              checked={permissions.some(p =>
                p.resource === resource && p.actions.includes(action)
              )}
              onCheckedChange={(checked) => {
                // Lógica para atualizar permissões
              }}
            >
              {action}
            </Checkbox>
          ))}
        </div>
      ))}

      <Button onClick={handleSave}>Salvar Alterações</Button>
    </div>
  );
}
```

## 🔄 Seleção de Team e Contexto

### Lógica de Seleção

O sistema determina o team ativo seguindo esta prioridade:

1. **selectedTeam** (localStorage) - Team selecionado pelo usuário
2. **Primeiro team** do usuário - Se não há seleção
3. **userId** como fallback - Se não há teams

```typescript
function getDocumentTeamId(userId: string, userTeams?: any[]): string {
  // Tentar obter team selecionado do localStorage
  const selectedTeamId = localStorage.getItem(`currentTeam_${userId}`);

  if (selectedTeamId) {
    return selectedTeamId;
  }

  // Se não há team selecionado, usar o primeiro team do usuário
  if (userTeams && userTeams.length > 0) {
    return userTeams[0].$id;
  }

  // Fallback: usar o ID do usuário
  return userId;
}
```

### Trocar de Team

```typescript
import { useTeamContext } from '@/contexts/team-context';

function TeamSwitcher() {
  const { teams, currentTeamId, switchTeam } = useTeamContext();

  return (
    <Select value={currentTeamId} onValueChange={switchTeam}>
      {teams.map(team => (
        <SelectItem key={team.$id} value={team.$id}>
          <div className="flex items-center gap-2">
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: team.prefs?.color || '#3b82f6' }}
            />
            {team.name}
          </div>
        </SelectItem>
      ))}
    </Select>
  );
}
```

## 🔍 Debug e Troubleshooting

### Debug de Permissões

```typescript
import { useTeamContext } from '@/contexts/team-context';

function PermissionsDebug() {
  const { permissionContext, currentUserMembership } = useTeamContext();

  console.log('Permission Context:', {
    userType: permissionContext?.userType,
    accessibleResources: permissionContext?.accessibleResources,
    permissions: permissionContext?.permissions,
    membership: currentUserMembership
  });

  return (
    <div className="p-4 bg-gray-100 rounded">
      <h3>Debug de Permissões</h3>
      <pre>{JSON.stringify(permissionContext, null, 2)}</pre>
    </div>
  );
}
```

### Problemas Comuns

1. **Permissões não aplicadas**
   - Verifique se o TeamProvider está configurado
   - Confirme que o usuário tem membership no team

2. **Documentos não acessíveis**
   - Verifique as permissões do Appwrite
   - Confirme que o team ID está correto

3. **Roles customizados não funcionam**
   - Verifique se o cargo foi salvo nas preferências do team
   - Confirme que o membro tem o cargo atribuído

## 📚 Exemplos Práticos

### Componente Completo com Permissões

```typescript
import { useTeamContext, useHasPermission } from '@/contexts/team-context';
import { useDocumentPermissions } from '@/hooks/use-document-permissions';

function ClientsManagement() {
  const { currentTeam } = useTeamContext();
  const hasPermission = useHasPermission();
  const { permissions } = useDocumentPermissions();

  const canCreate = hasPermission('clients', 'create');
  const canEdit = hasPermission('clients', 'edit');
  const canDelete = hasPermission('clients', 'delete');
  const canManage = hasPermission('clients', 'manage');

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1>Clientes - {currentTeam?.name}</h1>

        {canCreate && (
          <Button onClick={openCreateModal}>
            <Plus className="w-4 h-4 mr-2" />
            Novo Cliente
          </Button>
        )}
      </div>

      <ClientsList
        canEdit={canEdit}
        canDelete={canDelete}
        canManage={canManage}
      />
    </div>
  );
}
```

## 🎯 Próximos Passos

1. **Configure o sistema de permissões** no seu team
2. **Crie cargos customizados** conforme necessário
3. **Implemente verificações** nos componentes
4. **Teste com diferentes roles** para validar o sistema
5. **Configure permissões de documentos** automaticamente

Para mais informações, consulte:
- [teams.md](./teams.md) - Sistema de teams completo
- [hooks.md](./hooks.md) - Hooks disponíveis
- [types.md](./types.md) - Definições de tipos
