"use client";

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { ModernChat } from '../chat/modern-chat';
import type { ModernChatMessage } from '@/components/chat/modern-chat';
import { useAuth } from '../../hooks/use-auth';

/**
 * Demonstração das funcionalidades de editar e excluir mensagens
 */
export function ChatEditDeleteDemo() {
  const { user } = useAuth();
  const [messages, setMessages] = useState<ModernChatMessage[]>([
    {
      id: '1',
      content: 'Olá! Esta é uma mensagem que você pode editar ou excluir.',
      isUser: true,
      timestamp: new Date(Date.now() - 300000), // 5 minutos atrás
      userName: user?.name || 'Você',
      status: 'sent',
    },
    {
      id: '2',
      content: 'Esta é uma mensagem de outro usuário que você não pode editar.',
      isUser: false,
      timestamp: new Date(Date.now() - 240000), // 4 minutos atrás
      userName: 'Outro Usuário',
      status: 'sent',
    },
    {
      id: '3',
      content: 'Esta mensagem foi editada!',
      isUser: true,
      timestamp: new Date(Date.now() - 180000), // 3 minutos atrás
      userName: user?.name || 'Você',
      status: 'sent',
      edited: true,
      editedAt: new Date(Date.now() - 120000), // 2 minutos atrás
    },
  ]);

  const handleSendMessage = (content: string) => {
    const newMessage: ModernChatMessage = {
      id: `msg-${Date.now()}`,
      content,
      isUser: true,
      timestamp: new Date(),
      userName: user?.name || 'Você',
      status: 'sent',
    };
    setMessages(prev => [...prev, newMessage]);
  };

  const handleEditMessage = (messageId: string, content: string) => {
    setMessages(prev => prev.map(msg =>
      msg.id === messageId
        ? {
            ...msg,
            content,
            edited: true,
            editedAt: new Date()
          }
        : msg
    ));
  };

  const handleDeleteMessage = (messageId: string) => {
    // Simular soft delete: marcar como deletada em vez de remover
    setMessages(prev => prev.map(msg =>
      msg.id === messageId
        ? { ...msg, isDeleted: true } as ModernChatMessage & { isDeleted: boolean }
        : msg
    ).filter(msg => !(msg as any).isDeleted)); // Filtrar mensagens deletadas da exibição
  };

  const addSampleMessages = () => {
    const sampleMessages: ModernChatMessage[] = [
      {
        id: `sample-${Date.now()}-1`,
        content: 'Mensagem de exemplo que você pode editar 😊\nTeste quebra de linha!',
        isUser: true,
        timestamp: new Date(),
        userName: user?.name || 'Você',
        status: 'sent',
      },
      {
        id: `sample-${Date.now()}-2`,
        content: 'Outra mensagem de exemplo\nCom múltiplas linhas\nE emojis! 🚀✨',
        isUser: true,
        timestamp: new Date(),
        userName: user?.name || 'Você',
        status: 'sent',
      },
      {
        id: `sample-${Date.now()}-3`,
        content: 'Mensagem simples para testar edição',
        isUser: true,
        timestamp: new Date(),
        userName: user?.name || 'Você',
        status: 'sent',
      },
    ];
    setMessages(prev => [...prev, ...sampleMessages]);
  };

  const clearMessages = () => {
    setMessages([]);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Demo: Editar e Excluir Mensagens</CardTitle>
          <p className="text-sm text-muted-foreground">
            Teste as funcionalidades de edição e exclusão de mensagens.
            Apenas suas próprias mensagens podem ser editadas ou excluídas.
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={addSampleMessages} variant="outline">
              Adicionar Mensagens de Exemplo
            </Button>
            <Button onClick={clearMessages} variant="outline">
              Limpar Chat
            </Button>
          </div>

          <div className="border rounded-lg">
            <ModernChat
              messages={messages}
              onSendMessage={handleSendMessage}
              onEditMessage={handleEditMessage}
              onDeleteMessage={handleDeleteMessage}
              placeholder="Digite uma mensagem para testar..."
              height="500px"
              showFileUpload={false}
              showEmojiPicker={true}
            />
          </div>

          <div className="text-sm text-muted-foreground space-y-2">
            <h4 className="font-medium">Como usar:</h4>
            <ul className="list-disc list-inside space-y-1">
              <li>Passe o mouse sobre suas mensagens para ver o menu de ações</li>
              <li>Clique em "Editar" para modificar o conteúdo da mensagem</li>
              <li>Clique em "Excluir" para remover a mensagem permanentemente</li>
              <li>Use Enter para salvar edições ou Escape para cancelar</li>
              <li>Use Shift+Enter para quebrar linha durante a edição</li>
              <li>Clique no ícone de emoji para adicionar emojis durante a edição</li>
              <li>O textarea se ajusta automaticamente ao conteúdo</li>
              <li>Mensagens editadas mostram o indicador "(editada)" e timestamp</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
