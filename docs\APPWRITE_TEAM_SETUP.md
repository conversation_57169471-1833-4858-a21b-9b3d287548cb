# Configuração de Teams no Appwrite

## Problema Identificado

O erro "Phone authentication budget cap" ao convidar membros para teams indica uma configuração incorreta no console do Appwrite.

## Soluções Implementadas

### 1. Correção da Função `inviteToTeam`

A função `teams.createMembership` estava sendo chamada com parâmetros incorretos. Foi corrigida para:

```typescript
export async function inviteToTeam(teamId: string, data: InviteMemberData) {
  const result = await teams.createMembership(
    teamId,
    data.roles,
    data.email, // email
    undefined, // userId - will be set when user accepts
    undefined, // phone - not using phone invites
    data.url || `${window.location.origin}/dashboard/teams`, // url for redirect
    data.name || data.email.split('@')[0] // name
  );

  return result;
}
```

### 2. Configurações Necessárias no Console Appwrite

#### A. Configurar Domínios da Plataforma

1. Acesse o console Appwrite
2. Vá para **Settings** > **Platforms**
3. Adicione os domínios permitidos:
   - `http://localhost:3000` (desenvolvimento)
   - `https://seu-dominio.com` (produção)

#### B. Configurar Autenticação

1. Vá para **Auth** > **Settings**
2. **Desabilite** a autenticação por telefone se não estiver usando:
   - Phone (SMS) authentication: **OFF**
3. Configure apenas os métodos necessários:
   - Email/Password: **ON**
   - OAuth providers conforme necessário

#### C. Configurar Email Templates

1. Vá para **Auth** > **Templates**
2. Configure o template de convite para teams:
   - Subject: "Convite para participar do time"
   - Customize o template conforme necessário

### 3. Verificações de Segurança

#### A. Rate Limits
O endpoint `createMembership` tem rate limit de:
- 10 requests por 60 minutos por IP

#### B. Permissões
- Apenas membros com role "owner" podem convidar novos membros
- Verifique se o usuário atual tem as permissões necessárias

### 4. Debugging

Para debugar problemas de convites:

```typescript
// Adicione logs detalhados
console.log('Inviting member:', {
  teamId,
  email: data.email,
  roles: data.roles,
  url: data.url
});

try {
  const result = await inviteToTeam(teamId, data);
  console.log('Invite successful:', result);
} catch (error) {
  console.error('Invite failed:', error);
  // Verifique o erro específico
  if (error.message.includes('Phone authentication')) {
    console.error('Configuração de telefone incorreta no console');
  }
}
```

## Checklist de Configuração

- [ ] Domínios configurados na plataforma
- [ ] Autenticação por telefone desabilitada (se não usar)
- [ ] Templates de email configurados
- [ ] Permissões de team verificadas
- [ ] Rate limits respeitados
- [ ] URL de redirecionamento válida

## Próximos Passos

1. Verifique as configurações no console Appwrite
2. Teste o convite com um email válido
3. Monitore os logs para erros específicos
4. Configure templates de email personalizados se necessário

## Recursos Úteis

- [Documentação Teams](https://appwrite.io/docs/products/auth/teams)
- [Team Invites](https://appwrite.io/docs/products/auth/team-invites)
- [API Reference](https://appwrite.io/docs/references/cloud/client-web/teams#createMembership)
