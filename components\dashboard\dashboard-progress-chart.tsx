import { useMemo } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "../ui/card";
import { Badge } from "../ui/badge";
import { useAnalytics } from "../../hooks/use-analytics";
import { useClients } from "../../hooks/api/use-clients";
import { useEvents } from "../../hooks/api/use-events";
import { useBoards } from "../../hooks/api/use-kanban";
import { Skeleton } from "../ui/skeleton";
import { Target, TrendingUp, Users, Building, Calendar, CheckSquare } from "lucide-react";
import { Progress } from "../ui/progress";

interface ProgressItem {
  label: string;
  value: number;
  target: number;
  color: string;
  icon: React.ReactNode;
  description: string;
}

export function DashboardProgressChart() {
  const { data: analytics, isLoading } = useAnalytics();
  const { data: clients } = useClients();
  const { data: events } = useEvents();
  const { data: boards } = useBoards();

  // Gerar dados de progresso baseados em dados reais
  const progressData: ProgressItem[] = useMemo(() => {
    if (!analytics) return [];

    return [
      {
        label: "Usuários Ativos",
        value: analytics.activeUsers || 0,
        target: Math.max(analytics.totalUsers * 1.2, 100), // Meta 20% acima do total atual
        color: "hsl(var(--chart-1))",
        icon: <Users className="h-4 w-4" />,
        description: "Meta de usuários ativos mensais"
      },
      {
        label: "Clientes Cadastrados",
        value: analytics.totalClients || 0,
        target: Math.max(analytics.totalClients * 1.3, 50), // Meta 30% acima do atual
        color: "hsl(var(--chart-2))",
        icon: <Building className="h-4 w-4" />,
        description: "Meta de novos clientes"
      },
      {
        label: "Eventos Criados",
        value: analytics.totalEvents || 0,
        target: Math.max(analytics.totalEvents * 1.5, 20), // Meta 50% acima do atual
        color: "hsl(var(--chart-3))",
        icon: <Calendar className="h-4 w-4" />,
        description: "Meta de eventos mensais"
      },
      {
        label: "Tarefas Concluídas",
        value: analytics.completedTasks || 0,
        target: Math.max(analytics.totalKanbanTasks * 0.8, 10), // Meta 80% das tarefas totais
        color: "hsl(var(--chart-4))",
        icon: <CheckSquare className="h-4 w-4" />,
        description: "Meta de tarefas finalizadas"
      }
    ];
  }, [analytics]);

  // Calcular progresso geral
  const totalProgress = progressData.reduce((acc, item) => {
    const percentage = (item.value / item.target) * 100;
    return acc + Math.min(percentage, 100);
  }, 0) / progressData.length;

  if (isLoading) {
    return <Skeleton className="h-[400px] w-full" />;
  }

  const hasData = progressData.length > 0;

  if (!hasData) {
    return (
      <Card className="gap-5">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Progresso das Metas
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-60 text-muted-foreground">
            <div className="text-center">
              <p className="text-lg font-medium">Não há dados disponíveis</p>
              <p className="text-sm">Dados de progresso ainda não foram coletados</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="gap-5">
      <CardHeader>
        <div className="flex flex-wrap items-center justify-between gap-3">
          <div className="space-y-0.5">
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Progresso das Metas
            </CardTitle>
            <div className="flex items-start gap-2">
              <div className="font-semibold text-2xl">{totalProgress.toFixed(1)}%</div>
              <Badge className={`mt-1.5 border-none ${
                totalProgress >= 80
                  ? 'bg-emerald-500/24 text-emerald-500'
                  : totalProgress >= 60
                  ? 'bg-yellow-500/24 text-yellow-500'
                  : 'bg-red-500/24 text-red-500'
              }`}>
                <TrendingUp className="h-3 w-3 mr-1" />
                {totalProgress >= 80 ? 'Excelente' : totalProgress >= 60 ? 'Bom' : 'Atenção'}
              </Badge>
            </div>
            <div className="text-sm text-muted-foreground">
              Progresso geral das metas mensais
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="flex flex-col gap-6">
        {/* Barra de progresso geral */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="font-medium">Progresso Geral</span>
            <span className="text-muted-foreground">{totalProgress.toFixed(1)}%</span>
          </div>
          <Progress
            value={totalProgress}
            className="h-3"
          />
        </div>

        {/* Progresso individual por categoria */}
        <div className="grid gap-4">
          {progressData.map((item, index) => {
            const percentage = (item.value / item.target) * 100;
            const isCompleted = percentage >= 100;

            return (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div style={{ color: item.color }}>
                      {item.icon}
                    </div>
                    <div>
                      <div className="font-medium text-sm">{item.label}</div>
                      <div className="text-xs text-muted-foreground">{item.description}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium text-sm">
                      {item.value.toLocaleString('pt-BR')} / {item.target.toLocaleString('pt-BR')}
                    </div>
                    <div className={`text-xs ${
                      isCompleted ? 'text-emerald-600' : 'text-muted-foreground'
                    }`}>
                      {percentage.toFixed(1)}%
                    </div>
                  </div>
                </div>

                {/* Barra de progresso visual customizada */}
                <div className="flex gap-1 h-2">
                  <div
                    className="h-full rounded-sm transition-all duration-300"
                    style={{
                      backgroundColor: item.color,
                      width: `${Math.min(percentage, 100)}%`,
                      opacity: isCompleted ? 1 : 0.8
                    }}
                  />
                  <div
                    className="h-full bg-muted rounded-sm"
                    style={{ width: `${Math.max(100 - percentage, 0)}%` }}
                  />
                </div>
              </div>
            );
          })}
        </div>

        {/* Resumo das metas */}
        <div className="pt-4 border-t">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="font-semibold text-lg text-emerald-600">
                {progressData.filter(item => (item.value / item.target) >= 1).length}
              </div>
              <div className="text-xs text-muted-foreground">Metas Atingidas</div>
            </div>
            <div>
              <div className="font-semibold text-lg text-yellow-600">
                {progressData.filter(item => {
                  const perc = (item.value / item.target);
                  return perc >= 0.8 && perc < 1;
                }).length}
              </div>
              <div className="text-xs text-muted-foreground">Próximas da Meta</div>
            </div>
            <div>
              <div className="font-semibold text-lg text-red-600">
                {progressData.filter(item => (item.value / item.target) < 0.8).length}
              </div>
              <div className="text-xs text-muted-foreground">Precisam Atenção</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
