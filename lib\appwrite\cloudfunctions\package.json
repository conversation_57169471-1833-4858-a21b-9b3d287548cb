{"name": "appwrite-cloud-functions", "version": "1.0.0", "description": "Cloud functions for the template project", "type": "module", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["appwrite", "cloud-functions", "serverless", "teams", "authentication"], "author": "Template Project", "license": "MIT", "dependencies": {"node-appwrite": "^13.0.0", "@google/generative-ai": "^0.21.0"}, "engines": {"node": ">=18.0.0"}}