/**
 * PWA Configuration Manager
 * Handles PWA settings based on environment variables
 */

import { log } from './logger';

export interface PWAConfig {
  enabled: boolean;
}

/**
 * Get PWA configuration from environment variables
 */
export function getPWAConfig(): PWAConfig {
  const config: PWAConfig = {
    enabled: process.env.NEXT_PUBLIC_PWA_ENABLED === 'true',
  };

  log.pwa('PWA Configuration loaded', config as unknown as Record<string, unknown>);
  return config;
}

/**
 * Check if PWA is enabled
 */
export function isPWAEnabled(): boolean {
  return getPWAConfig().enabled;
}

/**
 * Check if PWA should be removed when disabled
 */
export function shouldRemovePWAOnDisable(): boolean {
  return true; // Always remove PWA when disabled for simplicity
}

/**
 * Remove PWA from browser
 */
export async function removePWA(): Promise<void> {
  try {
    log.pwa('Removing PWA from browser...');

    // Unregister all service workers
    if ('serviceWorker' in navigator) {
      const registrations = await navigator.serviceWorker.getRegistrations();

      for (const registration of registrations) {
        const result = await registration.unregister();
        log.pwa(`Service worker unregistered: ${result}`, { scope: registration.scope });
      }
    }

    // Clear PWA-related storage
    await clearPWAStorage();

    // Remove PWA manifest link
    removePWAManifest();

    log.pwa('PWA successfully removed from browser');
  } catch (error) {
    log.error('Failed to remove PWA', error instanceof Error ? error : undefined);
    throw error;
  }
}

/**
 * Clear PWA-related storage
 */
async function clearPWAStorage(): Promise<void> {
  try {
    // Clear cache storage
    if ('caches' in window) {
      const cacheNames = await caches.keys();
      await Promise.all(
        cacheNames.map(cacheName => {
          log.pwa(`Clearing cache: ${cacheName}`);
          return caches.delete(cacheName);
        })
      );
    }

    // Clear PWA-related localStorage items
    const pwaKeys = [
      'pwa-install-dismissed',
      'pwa-update-available',
      'pwa-last-update-check',
      'pwa-notification-permission',
    ];

    pwaKeys.forEach(key => {
      if (localStorage.getItem(key)) {
        localStorage.removeItem(key);
        log.pwa(`Cleared localStorage: ${key}`);
      }
    });

    // Clear PWA-related sessionStorage items
    const pwaSessionKeys = [
      'pwa-install-prompt-shown',
      'pwa-update-prompt-shown',
    ];

    pwaSessionKeys.forEach(key => {
      if (sessionStorage.getItem(key)) {
        sessionStorage.removeItem(key);
        log.pwa(`Cleared sessionStorage: ${key}`);
      }
    });

    log.pwa('PWA storage cleared successfully');
  } catch (error) {
    log.error('Failed to clear PWA storage', error instanceof Error ? error : undefined);
  }
}

/**
 * Remove PWA manifest link from document head
 */
function removePWAManifest(): void {
  try {
    const manifestLinks = document.querySelectorAll('link[rel="manifest"]');
    manifestLinks.forEach(link => {
      link.remove();
      log.pwa('PWA manifest link removed from document head');
    });

    // Remove PWA meta tags
    const pwaMetaTags = [
      'meta[name="theme-color"]',
      'meta[name="apple-mobile-web-app-capable"]',
      'meta[name="apple-mobile-web-app-status-bar-style"]',
      'meta[name="apple-mobile-web-app-title"]',
      'meta[name="application-name"]',
      'meta[name="msapplication-TileColor"]',
      'meta[name="msapplication-config"]',
    ];

    pwaMetaTags.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        element.remove();
        log.pwa(`PWA meta tag removed: ${selector}`);
      });
    });
  } catch (error) {
    log.error('Failed to remove PWA manifest', error instanceof Error ? error : undefined);
  }
}

/**
 * Initialize PWA based on configuration
 */
export async function initializePWA(): Promise<void> {
  const config = getPWAConfig();

  if (!config.enabled) {
    log.pwa('PWA is disabled in configuration');
    log.pwa('Removing existing PWA installation...');
    await removePWA();
    return;
  }

  log.pwa('PWA is enabled, initializing...');

  // PWA initialization will be handled by the usePWA hook
  // This function just validates the configuration
}

/**
 * Check if browser supports PWA features
 */
export function checkPWASupport(): {
  serviceWorker: boolean;
  manifest: boolean;
  notifications: boolean;
  installPrompt: boolean;
  cacheAPI: boolean;
} {
  const support = {
    serviceWorker: 'serviceWorker' in navigator,
    manifest: 'serviceWorker' in navigator && 'PushManager' in window,
    notifications: 'Notification' in window,
    installPrompt: 'BeforeInstallPromptEvent' in window || 'onbeforeinstallprompt' in window,
    cacheAPI: 'caches' in window,
  };

  log.pwa('PWA browser support check', support);
  return support;
}

/**
 * Get PWA installation status
 */
export function getPWAInstallationStatus(): {
  isInstalled: boolean;
  isStandalone: boolean;
  displayMode: string;
} {
  const isStandalone = window.matchMedia('(display-mode: standalone)').matches ||
                      (window.navigator as any).standalone === true;

  const displayMode = window.matchMedia('(display-mode: standalone)').matches ? 'standalone' :
                     window.matchMedia('(display-mode: minimal-ui)').matches ? 'minimal-ui' :
                     window.matchMedia('(display-mode: fullscreen)').matches ? 'fullscreen' :
                     'browser';

  const status = {
    isInstalled: isStandalone,
    isStandalone,
    displayMode,
  };

  log.pwa('PWA installation status', status);
  return status;
}

/**
 * Log PWA configuration on startup
 */
export function logPWAConfiguration(): void {
  const config = getPWAConfig();
  const support = checkPWASupport();
  const status = getPWAInstallationStatus();

  log.pwa('=== PWA Configuration Summary ===');
  log.pwa('Configuration:', config as unknown as Record<string, unknown>);
  log.pwa('Browser Support:', support);
  log.pwa('Installation Status:', status);
  log.pwa('================================');
}
