/**
 * Controller para Teams
 * Teams são gerenciados pelo Appwrite Teams service (não collection)
 * Este controller serve para invalidar queries quando necessário
 */

import { useEffect, useRef } from 'react';
import { subscribe } from 'valtio';
import { useQueryClient } from '@tanstack/react-query';
import { realtimeStore } from '../../lib/realtime/store';

export function useTeamsController() {
  const queryClient = useQueryClient();
  const unsubscribeRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    console.log('🎛️ Inicializando controller de teams...');

    // Subscribe do Valtio - função nativa, sem useEffect
    unsubscribeRef.current = subscribe(realtimeStore.teams, () => {
      const teams = realtimeStore.teams;

      if (teams.length === 0) return;
      
      console.log(`📝 Processando ${teams.length} teams do realtime...`);

      // Para teams, apenas invalidamos queries já que são gerenciados pelo Appwrite Teams
      teams.forEach(team => {
        console.log(`📝 Invalidando queries para team: ${team.$id}`);

        // Invalidar queries relacionadas a teams
        queryClient.invalidateQueries({ queryKey: ['teams'] });
        queryClient.invalidateQueries({ queryKey: ['team', team.$id] });
        queryClient.invalidateQueries({ queryKey: ['teamMembers', team.$id] });
        queryClient.invalidateQueries({ queryKey: ['team-chat', team.$id] });
        
        // Invalidar queries que dependem de teamId
        queryClient.invalidateQueries({ queryKey: ['clients'] });
        queryClient.invalidateQueries({ queryKey: ['events'] });
        queryClient.invalidateQueries({ queryKey: ['boards'] });
        queryClient.invalidateQueries({ queryKey: ['activities'] });
      });

    });

    return () => {
      console.log('🧹 Limpando controller de teams...');
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    };
  }, [queryClient]);

  return {};
}
