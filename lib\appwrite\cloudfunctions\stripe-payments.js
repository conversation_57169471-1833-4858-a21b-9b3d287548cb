/**
 * Cloud Function: Stripe Payments
 * 
 * Configuração de Permissões:
 * - Execute: users (usuários autenticados)
 * - Timeout: 30 segundos
 * - Runtime: Node.js 18.0
 * 
 * Validação Necessária:
 * - Verificar se o usuário está autenticado
 * - Validar dados do plano solicitado
 * - Verificar limites de upgrade baseado no plano atual
 * 
 * Dependências:
 * npm install node-appwrite stripe
 */

import { Client, Databases, Users, ID, Permission, Role } from 'node-appwrite';
import Strip<PERSON> from 'stripe';

// Configuração do Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

// Configuração do Appwrite
const client = new Client()
  .setEndpoint(process.env.APPWRITE_FUNCTION_API_ENDPOINT)
  .setProject(process.env.APPWRITE_FUNCTION_PROJECT_ID)
  .setKey(process.env.APPWRITE_FUNCTION_API_KEY);

const databases = new Databases(client);
const users = new Users(client);

// Configuração dos planos com preços do Stripe
const STRIPE_PLANS = {
  pro: {
    monthly: process.env.STRIPE_PRICE_PRO_MONTHLY,
    yearly: process.env.STRIPE_PRICE_PRO_YEARLY,
  },
  enterprise: {
    monthly: process.env.STRIPE_PRICE_ENTERPRISE_MONTHLY,
    yearly: process.env.STRIPE_PRICE_ENTERPRISE_YEARLY,
  }
};

export default async ({ req, res, log, error }) => {
  try {
    // Verificar método HTTP
    if (req.method !== 'POST') {
      return res.json({ error: 'Método não permitido' }, 405);
    }

    // Verificar autenticação
    const userId = req.headers['x-appwrite-user-id'];
    if (!userId) {
      return res.json({ error: 'Usuário não autenticado' }, 401);
    }

    // Parse do body
    const { action, planId, billingCycle, successUrl, cancelUrl } = JSON.parse(req.body);

    log(`Processando ação: ${action} para usuário: ${userId}`);

    switch (action) {
      case 'create-checkout':
        return await createCheckoutSession({
          userId,
          planId,
          billingCycle,
          successUrl,
          cancelUrl,
          req,
          res,
          log,
          error
        });

      case 'create-portal':
        return await createPortalSession({
          userId,
          returnUrl: successUrl,
          req,
          res,
          log,
          error
        });

      case 'webhook':
        return await handleWebhook({
          req,
          res,
          log,
          error
        });

      default:
        return res.json({ error: 'Ação não reconhecida' }, 400);
    }

  } catch (err) {
    error('Erro na função de pagamentos:', err);
    return res.json({ 
      error: 'Erro interno do servidor',
      details: err.message 
    }, 500);
  }
};

/**
 * Criar sessão de checkout do Stripe
 */
async function createCheckoutSession({ userId, planId, billingCycle, successUrl, cancelUrl, req, res, log, error }) {
  try {
    // Validar plano
    if (!STRIPE_PLANS[planId]) {
      return res.json({ error: 'Plano inválido' }, 400);
    }

    // Validar ciclo de cobrança
    if (!['monthly', 'yearly'].includes(billingCycle)) {
      return res.json({ error: 'Ciclo de cobrança inválido' }, 400);
    }

    // Obter preço do Stripe
    const priceId = STRIPE_PLANS[planId][billingCycle];
    if (!priceId) {
      return res.json({ error: 'Preço não configurado para este plano' }, 400);
    }

    // Obter dados do usuário
    const user = await users.get(userId);
    
    // Criar sessão de checkout
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: successUrl || `${req.scheme}://${req.headers.host}/dashboard/plans?success=true`,
      cancel_url: cancelUrl || `${req.scheme}://${req.headers.host}/dashboard/plans?canceled=true`,
      client_reference_id: userId,
      customer_email: user.email,
      metadata: {
        userId,
        planId,
        billingCycle,
      },
      subscription_data: {
        metadata: {
          userId,
          planId,
          billingCycle,
        },
      },
    });

    log(`Sessão de checkout criada: ${session.id} para usuário: ${userId}`);

    return res.json({
      success: true,
      sessionId: session.id,
      url: session.url,
    });

  } catch (err) {
    error('Erro ao criar sessão de checkout:', err);
    return res.json({ 
      error: 'Erro ao criar sessão de pagamento',
      details: err.message 
    }, 500);
  }
}

/**
 * Criar sessão do portal do cliente
 */
async function createPortalSession({ userId, returnUrl, req, res, log, error }) {
  try {
    // Buscar customer do Stripe baseado no userId
    const customers = await stripe.customers.list({
      metadata: { userId },
      limit: 1,
    });

    if (customers.data.length === 0) {
      return res.json({ error: 'Cliente não encontrado no Stripe' }, 404);
    }

    const customer = customers.data[0];

    // Criar sessão do portal
    const session = await stripe.billingPortal.sessions.create({
      customer: customer.id,
      return_url: returnUrl || `${req.scheme}://${req.headers.host}/dashboard/plans`,
    });

    log(`Sessão do portal criada para usuário: ${userId}`);

    return res.json({
      success: true,
      url: session.url,
    });

  } catch (err) {
    error('Erro ao criar sessão do portal:', err);
    return res.json({ 
      error: 'Erro ao criar portal do cliente',
      details: err.message 
    }, 500);
  }
}

/**
 * Processar webhooks do Stripe
 */
async function handleWebhook({ req, res, log, error }) {
  try {
    const sig = req.headers['stripe-signature'];
    const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

    let event;
    try {
      event = stripe.webhooks.constructEvent(req.bodyBinary, sig, endpointSecret);
    } catch (err) {
      error('Erro na verificação do webhook:', err);
      return res.json({ error: 'Webhook signature verification failed' }, 400);
    }

    log(`Processando evento do Stripe: ${event.type}`);

    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutCompleted(event.data.object, log, error);
        break;

      case 'customer.subscription.created':
      case 'customer.subscription.updated':
        await handleSubscriptionChange(event.data.object, log, error);
        break;

      case 'customer.subscription.deleted':
        await handleSubscriptionCanceled(event.data.object, log, error);
        break;

      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(event.data.object, log, error);
        break;

      case 'invoice.payment_failed':
        await handlePaymentFailed(event.data.object, log, error);
        break;

      default:
        log(`Evento não tratado: ${event.type}`);
    }

    return res.json({ received: true });

  } catch (err) {
    error('Erro ao processar webhook:', err);
    return res.json({ error: 'Erro ao processar webhook' }, 500);
  }
}

/**
 * Processar checkout completado
 */
async function handleCheckoutCompleted(session, log, error) {
  try {
    const userId = session.metadata.userId;
    const planId = session.metadata.planId;

    log(`Checkout completado para usuário: ${userId}, plano: ${planId}`);

    // Atualizar labels do usuário
    await updateUserPlan(userId, planId, log, error);

    // Criar registro de assinatura (opcional)
    await createSubscriptionRecord(session, log, error);

  } catch (err) {
    error('Erro ao processar checkout completado:', err);
  }
}

/**
 * Processar mudança de assinatura
 */
async function handleSubscriptionChange(subscription, log, error) {
  try {
    const userId = subscription.metadata.userId;
    const planId = subscription.metadata.planId;

    log(`Assinatura atualizada para usuário: ${userId}, plano: ${planId}`);

    await updateUserPlan(userId, planId, log, error);

  } catch (err) {
    error('Erro ao processar mudança de assinatura:', err);
  }
}

/**
 * Processar cancelamento de assinatura
 */
async function handleSubscriptionCanceled(subscription, log, error) {
  try {
    const userId = subscription.metadata.userId;

    log(`Assinatura cancelada para usuário: ${userId}`);

    await updateUserPlan(userId, 'free', log, error);

  } catch (err) {
    error('Erro ao processar cancelamento:', err);
  }
}

/**
 * Processar pagamento bem-sucedido
 */
async function handlePaymentSucceeded(invoice, log, error) {
  try {
    log(`Pagamento bem-sucedido: ${invoice.id}`);
    // Implementar lógica adicional se necessário
  } catch (err) {
    error('Erro ao processar pagamento bem-sucedido:', err);
  }
}

/**
 * Processar falha de pagamento
 */
async function handlePaymentFailed(invoice, log, error) {
  try {
    log(`Falha no pagamento: ${invoice.id}`);
    // Implementar lógica de notificação ou downgrade se necessário
  } catch (err) {
    error('Erro ao processar falha de pagamento:', err);
  }
}

/**
 * Atualizar plano do usuário
 */
async function updateUserPlan(userId, planId, log, error) {
  try {
    // Obter usuário atual
    const user = await users.get(userId);
    const currentLabels = user.labels || [];

    // Remover labels de planos antigos
    const filteredLabels = currentLabels.filter(label => 
      !['plan-free', 'plan-pro', 'plan-enterprise', 'free', 'pro', 'enterprise'].includes(label)
    );

    // Adicionar nova label do plano
    const newLabels = [...filteredLabels, `plan-${planId}`, planId];

    // Atualizar usuário
    await users.updateLabels(userId, newLabels);

    log(`Plano do usuário ${userId} atualizado para: ${planId}`);

  } catch (err) {
    error('Erro ao atualizar plano do usuário:', err);
    throw err;
  }
}

/**
 * Criar registro de assinatura
 */
async function createSubscriptionRecord(session, log, error) {
  try {
    const databaseId = process.env.APPWRITE_DATABASE_ID || 'main';
    const collectionId = process.env.APPWRITE_SUBSCRIPTIONS_COLLECTION_ID || 'subscriptions';

    await databases.createDocument(
      databaseId,
      collectionId,
      ID.unique(),
      {
        userId: session.metadata.userId,
        stripeSessionId: session.id,
        stripeCustomerId: session.customer,
        planId: session.metadata.planId,
        billingCycle: session.metadata.billingCycle,
        status: 'active',
        createdAt: new Date().toISOString(),
      },
      [
        Permission.read(Role.user(session.metadata.userId)),
        Permission.update(Role.user(session.metadata.userId)),
      ]
    );

    log(`Registro de assinatura criado para usuário: ${session.metadata.userId}`);

  } catch (err) {
    error('Erro ao criar registro de assinatura:', err);
    // Não falhar se não conseguir criar o registro
  }
}
