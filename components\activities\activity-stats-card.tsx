/**
 * Activity Stats Card Component
 * Displays activity statistics in a compact card format
 */

import React, { useMemo } from 'react';
import Link from 'next/link';
import { IconActivity, IconTrendingUp, IconEye, IconLock } from '@tabler/icons-react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Skeleton } from '../ui/skeleton';
import { Alert, AlertDescription } from '../ui/alert';
import { useActivityStats, useRecentActivities } from '../../hooks/use-api';
import { useAuth } from '../../hooks/use-auth';
import { canViewActivities } from '../../lib/activity-permissions';

// ============================================================================
// COMPONENTS
// ============================================================================

export function ActivityStatsCard() {
  const { user } = useAuth();
  const { data: stats, isLoading: isLoadingStats } = useActivityStats();
  const { data: recentActivities, isLoading: isLoadingRecent } = useRecentActivities(3);

  // Check if user has permission to view activities
  const hasViewPermission = useMemo(() => canViewActivities(user), [user]);

  if (!hasViewPermission) {
    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Atividades</CardTitle>
          <IconLock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <Alert>
            <IconLock className="h-4 w-4" />
            <AlertDescription className="text-xs">
              Disponível apenas no plano Enterprise
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (isLoadingStats) {
    return <ActivityStatsCardSkeleton />;
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">Atividades Recentes</CardTitle>
        <IconActivity className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Stats Summary */}
          {stats && (
            <div className="grid grid-cols-3 gap-2 text-center">
              <div>
                <div className="text-lg font-bold">{stats.todayCount}</div>
                <div className="text-xs text-muted-foreground">Hoje</div>
              </div>
              <div>
                <div className="text-lg font-bold">{stats.weekCount}</div>
                <div className="text-xs text-muted-foreground">Semana</div>
              </div>
              <div>
                <div className="text-lg font-bold">{stats.total}</div>
                <div className="text-xs text-muted-foreground">Total</div>
              </div>
            </div>
          )}

          {/* Recent Activities */}
          <div className="space-y-2">
            {isLoadingRecent ? (
              <div className="space-y-2">
                {Array.from({ length: 3 }).map((_, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <Skeleton className="h-2 w-2 rounded-full" />
                    <Skeleton className="h-3 flex-1" />
                  </div>
                ))}
              </div>
            ) : recentActivities && recentActivities.length > 0 ? (
              recentActivities.slice(0, 3).map((activity) => (
                <div key={activity.$id} className="flex items-center space-x-2 text-xs">
                  <div className={`h-2 w-2 rounded-full ${getActivityColor(activity.type)}`} />
                  <span className="flex-1 truncate">{activity.title}</span>
                  <Badge variant="outline" className="text-xs">
                    {getActivityTypeLabel(activity.type)}
                  </Badge>
                </div>
              ))
            ) : (
              <div className="text-xs text-muted-foreground text-center py-2">
                Nenhuma atividade recente
              </div>
            )}
          </div>

          {/* View All Button */}
          <Button asChild variant="outline" size="sm" className="w-full">
            <Link href="/dashboard/activities">
              <IconEye className="h-3 w-3 mr-2" />
              Ver Todas as Atividades
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

function ActivityStatsCardSkeleton() {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <Skeleton className="h-4 w-32" />
        <Skeleton className="h-4 w-4" />
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Stats Summary Skeleton */}
          <div className="grid grid-cols-3 gap-2 text-center">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index}>
                <Skeleton className="h-6 w-8 mx-auto mb-1" />
                <Skeleton className="h-3 w-12 mx-auto" />
              </div>
            ))}
          </div>

          {/* Recent Activities Skeleton */}
          <div className="space-y-2">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="flex items-center space-x-2">
                <Skeleton className="h-2 w-2 rounded-full" />
                <Skeleton className="h-3 flex-1" />
                <Skeleton className="h-4 w-12" />
              </div>
            ))}
          </div>

          {/* Button Skeleton */}
          <Skeleton className="h-8 w-full" />
        </div>
      </CardContent>
    </Card>
  );
}

// ============================================================================
// HELPERS
// ============================================================================

function getActivityColor(type: string): string {
  const colors: Record<string, string> = {
    auth: 'bg-blue-500',
    client: 'bg-green-500',
    team: 'bg-purple-500',
    chat: 'bg-orange-500',
    file: 'bg-indigo-500',
    system: 'bg-gray-500',
    admin: 'bg-red-500',
    notification: 'bg-yellow-500',
    preference: 'bg-slate-500',
    calendar: 'bg-cyan-500',
    document: 'bg-emerald-500',
  };
  return colors[type] || 'bg-gray-400';
}

function getActivityTypeLabel(type: string): string {
  const labels: Record<string, string> = {
    auth: 'Auth',
    client: 'Cliente',
    team: 'Team',
    chat: 'Chat',
    file: 'Arquivo',
    system: 'Sistema',
    admin: 'Admin',
    notification: 'Notif',
    preference: 'Config',
    calendar: 'Agenda',
    document: 'Doc',
  };
  return labels[type] || type;
}
