/**
 * Agenda View Component
 * Visualização de agenda do calendário
 */

import React, { useMemo } from 'react';
import { 
  format, 
  addDays,
  isToday,
  isSameDay,
  startOfDay,
  compareAsc
} from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { cn } from '../../lib/utils';
import { Badge } from '../ui/badge';
import { Card, CardContent } from '../ui/card';
import { CalendarIcon, Clock } from 'lucide-react';
import type { Event } from '@/schemas/events';

export interface AgendaViewProps {
  currentDate: Date;
  events: Event[];
  onEventSelect?: (event: Event) => void;
  className?: string;
}

const AGENDA_DAYS_TO_SHOW = 30;

export function AgendaView({
  currentDate,
  events,
  onEventSelect,
  className,
}: AgendaViewProps) {
  // Generate days to show
  const days = useMemo(() => {
    return Array.from({ length: AGENDA_DAYS_TO_SHOW }, (_, i) =>
      addDays(currentDate, i)
    );
  }, [currentDate]);

  // Group events by date and sort
  const eventsByDate = useMemo(() => {
    const grouped: Record<string, Event[]> = {};
    
    events.forEach(event => {
      const eventDate = new Date(event.startDate);
      const dateKey = format(eventDate, 'yyyy-MM-dd');
      
      if (!grouped[dateKey]) {
        grouped[dateKey] = [];
      }
      grouped[dateKey].push(event);
    });

    // Sort events within each day by start time
    Object.keys(grouped).forEach(dateKey => {
      grouped[dateKey].sort((a, b) => 
        compareAsc(new Date(a.startDate), new Date(b.startDate))
      );
    });

    return grouped;
  }, [events]);

  // Filter days that have events or are today/future
  const relevantDays = useMemo(() => {
    return days.filter(day => {
      const dateKey = format(day, 'yyyy-MM-dd');
      const hasEvents = eventsByDate[dateKey]?.length > 0;
      const isRelevant = isToday(day) || day >= startOfDay(new Date());
      return hasEvents || isRelevant;
    });
  }, [days, eventsByDate]);

  const handleEventClick = (event: Event) => {
    if (onEventSelect) {
      onEventSelect(event);
    }
  };

  const getEventTypeColor = (event: Event) => {
    // You can customize this based on event categories or types
    switch (event.category?.toLowerCase()) {
      case 'meeting':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'task':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'reminder':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'personal':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (relevantDays.length === 0) {
    return (
      <div className={cn('h-full flex items-center justify-center', className)}>
        <div className="text-center text-muted-foreground">
          <CalendarIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p className="text-lg font-medium mb-2">Nenhum evento encontrado</p>
          <p className="text-sm">
            Não há eventos nos próximos {AGENDA_DAYS_TO_SHOW} dias.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('h-full overflow-auto', className)}>
      <div className="space-y-4 p-4">
        {relevantDays.map((day) => {
          const dateKey = format(day, 'yyyy-MM-dd');
          const dayEvents = eventsByDate[dateKey] || [];
          const isDayToday = isToday(day);

          // Skip days without events unless it's today
          if (dayEvents.length === 0 && !isDayToday) {
            return null;
          }

          return (
            <Card key={dateKey} className={cn(
              'overflow-hidden',
              isDayToday && 'ring-2 ring-primary/20 bg-accent/5'
            )}>
              {/* Date header */}
              <div className={cn(
                'px-4 py-3 border-b bg-muted/30',
                isDayToday && 'bg-primary/10'
              )}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={cn(
                      'text-2xl font-bold',
                      isDayToday && 'text-primary'
                    )}>
                      {format(day, 'd')}
                    </div>
                    <div>
                      <div className={cn(
                        'font-medium',
                        isDayToday && 'text-primary'
                      )}>
                        {format(day, 'EEEE', { locale: ptBR })}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {format(day, "MMMM 'de' yyyy", { locale: ptBR })}
                      </div>
                    </div>
                  </div>
                  
                  {isDayToday && (
                    <Badge variant="default" className="text-xs">
                      Hoje
                    </Badge>
                  )}
                  
                  {dayEvents.length > 0 && (
                    <Badge variant="secondary" className="text-xs">
                      {dayEvents.length} evento{dayEvents.length !== 1 ? 's' : ''}
                    </Badge>
                  )}
                </div>
              </div>

              {/* Events list */}
              <CardContent className="p-0">
                {dayEvents.length === 0 ? (
                  <div className="p-4 text-center text-muted-foreground text-sm">
                    Nenhum evento agendado
                  </div>
                ) : (
                  <div className="divide-y">
                    {dayEvents.map((event) => (
                      <div
                        key={event.$id}
                        className="p-4 hover:bg-accent/50 cursor-pointer transition-colors"
                        onClick={() => handleEventClick(event)}
                      >
                        <div className="flex items-start gap-3">
                          <div className="flex-shrink-0 mt-1">
                            <Clock className="h-4 w-4 text-muted-foreground" />
                          </div>
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between gap-2">
                              <div className="flex-1">
                                <h4 className="font-medium text-sm leading-tight">
                                  {event.title}
                                </h4>
                                
                                {event.description && (
                                  <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                                    {event.description}
                                  </p>
                                )}
                              </div>
                              
                              {event.category && (
                                <Badge 
                                  variant="outline" 
                                  className={cn('text-xs', getEventTypeColor(event))}
                                >
                                  {event.category}
                                </Badge>
                              )}
                            </div>
                            
                            <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                              <span>
                                {format(new Date(event.startDate), 'HH:mm')} - 
                                {format(new Date(event.endDate), 'HH:mm')}
                              </span>
                              
                              {event.location && (
                                <span className="truncate">
                                  📍 {event.location}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}
