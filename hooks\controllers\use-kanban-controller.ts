/**
 * Controller para Kanban (Boards, Columns, Tasks)
 * Usa subscribe do Valtio - sem useEffect, useState, nada
 */

import { useEffect, useRef } from 'react';
import { subscribe } from 'valtio';
import { useQueryClient } from '@tanstack/react-query';
import { realtimeStore } from '../../lib/realtime/store';
import { saveToIndexedDB } from '../../lib/cache-sync';

export function useKanbanController() {
  const queryClient = useQueryClient();
  const unsubscribeRefs = useRef<{
    boards: (() => void) | null;
    columns: (() => void) | null;
    tasks: (() => void) | null;
  }>({
    boards: null,
    columns: null,
    tasks: null,
  });

  useEffect(() => {
    console.log('🎛️ Inicializando controller de kanban...');

    // Subscribe para Boards
    unsubscribeRefs.current.boards = subscribe(realtimeStore.boards, () => {
      const boards = realtimeStore.boards;

      if (boards.length === 0) return;

      console.log(`📝 Processando ${boards.length} boards do realtime...`);

      boards.forEach(board => {
        console.log(`📝 Atualizando board: ${board.$id}`);

        queryClient.setQueryData(['boards', board.userId], (oldData: any) => {
          if (!oldData) return [board];

          const exists = oldData.find((item: any) => item.$id === board.$id);
          if (exists) {
            return oldData.map((item: any) =>
              item.$id === board.$id ? board : item
            );
          } else {
            return [...oldData, board];
          }
        });

        // Invalidar queries relacionadas
        queryClient.invalidateQueries({ queryKey: ['boards'] });
        queryClient.invalidateQueries({ queryKey: ['boardWithData', board.$id] });

        saveToIndexedDB('kanban_boards', board, {
          collection: 'kanban_boards',
          userId: board.userId
        }).catch(error => {
          console.error(`❌ Erro ao salvar board ${board.$id} no IndexedDB:`, error);
        });
      });

    });

    // Subscribe para Columns
    unsubscribeRefs.current.columns = subscribe(realtimeStore.columns, () => {
      const columns = realtimeStore.columns;

      if (columns.length === 0) return;

      console.log(`📝 Processando ${columns.length} columns do realtime...`);

      columns.forEach(column => {
        console.log(`📝 Atualizando column: ${column.id}`);

        // Invalidar board pai
        queryClient.invalidateQueries({ queryKey: ['boardWithData', column.boardId] });
        queryClient.invalidateQueries({ queryKey: ['boards'] });

        // Salvar no IndexedDB apenas se tiver as propriedades necessárias
        if (column.id && column.createdAt) {
          const columnWithAppwriteFields = {
            ...column,
            $id: column.id,
            $updatedAt: column.updatedAt || column.createdAt
          };

          saveToIndexedDB('kanban_columns', columnWithAppwriteFields, {
            collection: 'kanban_columns',
            userId: column.userId
          }).catch(error => {
            console.error(`❌ Erro ao salvar column ${column.id} no IndexedDB:`, error);
          });
        }
      });

      realtimeStore.columns = [];
    });

    // Subscribe para Tasks
    unsubscribeRefs.current.tasks = subscribe(realtimeStore.tasks, () => {
      const tasks = realtimeStore.tasks;

      if (tasks.length === 0) return;

      console.log(`📝 Processando ${tasks.length} tasks do realtime...`);

      tasks.forEach(task => {
        console.log(`📝 Atualizando task: ${task.id}`);

        // Invalidar board pai
        queryClient.invalidateQueries({ queryKey: ['boardWithData', task.boardId] });
        queryClient.invalidateQueries({ queryKey: ['boards'] });

        // Salvar no IndexedDB apenas se tiver as propriedades necessárias
        if (task.id && task.createdAt) {
          const taskWithAppwriteFields = {
            ...task,
            $id: task.id,
            $updatedAt: task.updatedAt || task.createdAt
          };

          saveToIndexedDB('kanban_tasks', taskWithAppwriteFields, {
            collection: 'kanban_tasks',
            userId: task.userId
          }).catch(error => {
            console.error(`❌ Erro ao salvar task ${task.id} no IndexedDB:`, error);
          });
        }
      });

      realtimeStore.tasks = [];
    });

    return () => {
      console.log('🧹 Limpando controller de kanban...');
      Object.values(unsubscribeRefs.current).forEach(unsubscribe => {
        if (unsubscribe) {
          unsubscribe();
        }
      });
      unsubscribeRefs.current = {
        boards: null,
        columns: null,
        tasks: null,
      };
    };
  }, [queryClient]);

  return {};
}
