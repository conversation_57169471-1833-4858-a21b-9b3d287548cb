/**
 * Configuração dos planos de preços
 * Dados mockados para demonstração
 */

export interface PlanFeature {
  name: string;
  included: boolean;
}

export interface Plan {
  id: string;
  name: string;
  description: string;
  price: {
    monthly: number;
    yearly: number;
  };
  currency: string;
  period: {
    monthly: string;
    yearly: string;
  };
  features: PlanFeature[];
  popular?: boolean;
  buttonText: string;
  buttonVariant: 'default' | 'outline';
  limits: {
    teams: number;
    membersPerTeam: number;
    storage: string;
    apiCalls: number;
  };
}

export const PLANS: Plan[] = [
  {
    id: 'free',
    name: 'Gratuito',
    description: 'Perfeito para começar',
    price: {
      monthly: 0,
      yearly: 0,
    },
    currency: 'R$',
    period: {
      monthly: '/mês',
      yearly: '/ano',
    },
    features: [
      { name: 'Dashboard de Analytics Básico', included: true },
      { name: '5GB de Armazenamento na Nuvem', included: true },
      { name: 'Suporte por Email e Chat', included: true },
      { name: 'Acesso ao Fórum da Comunidade', included: true },
      { name: 'Acesso de Usuário Único', included: true },
      { name: 'Recursos de Segurança Padrão', included: true },
      { name: 'Criação de Times', included: false },
      { name: 'Templates Básicos', included: false },
      { name: 'Acesso ao App Mobile', included: false },
      { name: 'Relatórios Personalizados', included: false },
      { name: 'Integrações Avançadas', included: false },
      { name: 'Import/Export de Dados', included: false },
      { name: 'Processamento com IA', included: false },
    ],
    buttonText: 'Começar Grátis',
    buttonVariant: 'outline',
    limits: {
      teams: 0,
      membersPerTeam: 0,
      storage: '5GB',
      apiCalls: 1000,
    },
  },
  {
    id: 'pro',
    name: 'Profissional',
    description: 'Para equipes em crescimento',
    price: {
      monthly: 49,
      yearly: 490,
    },
    currency: 'R$',
    period: {
      monthly: '/mês',
      yearly: '/ano',
    },
    features: [
      { name: 'Tudo do plano Gratuito', included: true },
      { name: '50GB de Armazenamento na Nuvem', included: true },
      { name: 'Suporte Prioritário 24/7', included: true },
      { name: 'Acesso ao App Mobile', included: true },
      { name: 'Até 5 Usuários por Time', included: true },
      { name: '1 Time Incluso', included: true },
      { name: 'Templates Premium', included: true },
      { name: '10 Relatórios Personalizados/Mês', included: true },
      { name: 'Integrações Básicas', included: true },
      { name: 'Analytics Avançado', included: true },
      { name: 'Backup Automático', included: true },
      { name: 'Import/Export de Dados', included: true },
      { name: 'Processamento com IA', included: true },
    ],
    popular: true,
    buttonText: 'Começar Teste Grátis',
    buttonVariant: 'default',
    limits: {
      teams: 1,
      membersPerTeam: 5,
      storage: '50GB',
      apiCalls: 10000,
    },
  },
  {
    id: 'enterprise',
    name: 'Empresarial',
    description: 'Para grandes organizações',
    price: {
      monthly: 149,
      yearly: 1490,
    },
    currency: 'R$',
    period: {
      monthly: '/mês',
      yearly: '/ano',
    },
    features: [
      { name: 'Tudo do plano Profissional', included: true },
      { name: 'Armazenamento Ilimitado', included: true },
      { name: 'Suporte Dedicado', included: true },
      { name: 'Usuários Ilimitados', included: true },
      { name: 'Times Ilimitados', included: true },
      { name: 'Templates Personalizados', included: true },
      { name: 'Relatórios Ilimitados', included: true },
      { name: 'Todas as Integrações', included: true },
      { name: 'API Completa', included: true },
      { name: 'SSO e Segurança Avançada', included: true },
      { name: 'Gerente de Conta Dedicado', included: true },
      { name: 'Import/Export de Dados Avançado', included: true },
      { name: 'Processamento com IA Ilimitado', included: true },
    ],
    buttonText: 'Falar com Vendas',
    buttonVariant: 'outline',
    limits: {
      teams: -1, // -1 = ilimitado
      membersPerTeam: -1, // -1 = ilimitado
      storage: 'Ilimitado',
      apiCalls: -1,
    },
  },
];

export const PRICING_CONFIG = {
  title: 'Preços que Crescem com Você',
  subtitle: 'Escolha o plano perfeito para suas necessidades. Comece grátis e faça upgrade quando precisar.',
  currency: 'R$',
  billingToggle: {
    monthly: 'Mensal',
    yearly: 'Anual',
    yearlyDiscount: 'Economize 2 meses',
  },
  features: {
    title: 'Recursos Inclusos',
    allPlansInclude: 'Todos os planos incluem:',
    commonFeatures: [
      'SSL e Segurança',
      'Suporte Técnico',
      'Atualizações Automáticas',
      'Backup de Dados',
      'Monitoramento 24/7',
    ],
  },
  faq: {
    title: 'Perguntas Frequentes',
    items: [
      {
        question: 'Posso cancelar a qualquer momento?',
        answer: 'Sim, você pode cancelar sua assinatura a qualquer momento sem taxas de cancelamento.',
      },
      {
        question: 'Existe período de teste gratuito?',
        answer: 'Sim, oferecemos 14 dias de teste gratuito para todos os planos pagos.',
      },
      {
        question: 'Como funciona o suporte?',
        answer: 'Oferecemos suporte por email, chat e telefone dependendo do seu plano.',
      },
      {
        question: 'Posso fazer upgrade do meu plano?',
        answer: 'Sim, você pode fazer upgrade ou downgrade do seu plano a qualquer momento.',
      },
    ],
  },
};

/**
 * Utilitários para formatação de preços
 */
export const formatPrice = (price: number, currency: string = 'R$'): string => {
  if (price === 0) return 'Grátis';
  return `${currency}${price}`;
};

export const calculateYearlyDiscount = (monthly: number, yearly: number): number => {
  if (monthly === 0 || yearly === 0) return 0;
  const monthlyTotal = monthly * 12;
  return Math.round(((monthlyTotal - yearly) / monthlyTotal) * 100);
};
