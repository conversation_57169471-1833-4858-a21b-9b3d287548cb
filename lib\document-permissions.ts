/**
 * Sistema de Permissões para Documentos
 * Gerencia permissões do Appwrite para documentos baseado em times e usuários
 */

import { Permission, Role } from 'appwrite';
import type { UserPermissionContext } from '@/schemas/permissions';

// ============================================================================
// TIPOS
// ============================================================================

export interface DocumentPermissionOptions {
  userId: string;
  teamId?: string | null;
  permissionContext?: UserPermissionContext | null;
  isPublic?: boolean;
}

export interface DocumentPermissions {
  read: string[];
  write: string[];
  delete: string[];
}

// ============================================================================
// FUNÇÕES UTILITÁRIAS
// ============================================================================

/**
 * Obtém o team ID baseado no contexto do usuário
 * Prioridade: selectedTeam (localStorage) > primeiro team do usuário > userId como fallback
 */
export function getDocumentTeamId(userId: string, userTeams?: any[]): string {
  // Tentar obter team selecionado do localStorage
  const selectedTeamId = localStorage.getItem(`currentTeam_${userId}`);
  
  if (selectedTeamId) {
    return selectedTeamId;
  }
  
  // Se não há team selecionado, usar o primeiro team do usuário
  if (userTeams && userTeams.length > 0) {
    return userTeams[0].$id;
  }
  
  // Fallback: usar o ID do usuário
  return userId;
}

/**
 * Gera permissões do Appwrite para um documento
 * Criador tem acesso total, membros do time têm acesso baseado nas permissões do time
 */
export function generateDocumentPermissions(options: DocumentPermissionOptions): DocumentPermissions {
  const { userId, teamId, permissionContext, isPublic = false } = options;
  
  const permissions: DocumentPermissions = {
    read: [],
    write: [],
    delete: []
  };
  
  // Criador sempre tem acesso total
  permissions.read.push(Permission.read(Role.user(userId)));
  permissions.write.push(Permission.write(Role.user(userId)));
  permissions.delete.push(Permission.delete(Role.user(userId)));
  
  // Se é público, adicionar permissão de leitura para qualquer um
  if (isPublic) {
    permissions.read.push(Permission.read(Role.any()));
  }
  
  // Se há um team, adicionar permissões baseadas no contexto
  if (teamId && teamId !== userId && permissionContext) {
    const teamPermissions = getTeamDocumentPermissions(teamId, permissionContext);
    
    // Adicionar permissões do team
    permissions.read.push(...teamPermissions.read);
    permissions.write.push(...teamPermissions.write);
    permissions.delete.push(...teamPermissions.delete);
  }
  
  return permissions;
}

/**
 * Obtém permissões do team para documentos baseado no contexto de permissões
 */
function getTeamDocumentPermissions(teamId: string, permissionContext: UserPermissionContext): DocumentPermissions {
  const permissions: DocumentPermissions = {
    read: [],
    write: [],
    delete: []
  };
  
  // Verificar se o usuário tem permissão para documentos
  const documentPermission = permissionContext.permissions.find(p => p.resource === 'documents');
  
  if (!documentPermission) {
    return permissions; // Sem permissões para documentos
  }
  
  // Mapear ações para permissões do Appwrite
  if (documentPermission.actions.includes('view')) {
    permissions.read.push(Permission.read(Role.team(teamId)));
  }
  
  if (documentPermission.actions.includes('edit')) {
    permissions.write.push(Permission.write(Role.team(teamId)));
  }
  
  if (documentPermission.actions.includes('delete') || documentPermission.actions.includes('manage')) {
    permissions.delete.push(Permission.delete(Role.team(teamId)));
  }
  
  return permissions;
}

/**
 * Converte permissões para array de strings do Appwrite
 */
export function convertPermissionsToAppwrite(permissions: DocumentPermissions): string[] {
  return [
    ...permissions.read,
    ...permissions.write,
    ...permissions.delete
  ];
}

/**
 * Cria permissões completas para um documento
 * Função principal para ser usada na criação de documentos
 */
export function createDocumentPermissions(
  userId: string,
  teamId?: string | null,
  permissionContext?: UserPermissionContext | null,
  isPublic: boolean = false
): string[] {
  const permissions = generateDocumentPermissions({
    userId,
    teamId,
    permissionContext,
    isPublic
  });
  
  return convertPermissionsToAppwrite(permissions);
}

// ============================================================================
// VERIFICAÇÕES DE PERMISSÃO
// ============================================================================

/**
 * Verifica se um usuário pode acessar um documento
 */
export function canAccessDocument(
  documentPermissions: string[],
  userId: string,
  teamId?: string | null,
  action: 'read' | 'write' | 'delete' = 'read'
): boolean {
  // Verificar se é o criador do documento
  const userPermission = documentPermissions.find(p => 
    p.includes(`user:${userId}`) && p.includes(action)
  );
  
  if (userPermission) {
    return true;
  }
  
  // Verificar se tem permissão via team
  if (teamId) {
    const teamPermission = documentPermissions.find(p => 
      p.includes(`team:${teamId}`) && p.includes(action)
    );
    
    if (teamPermission) {
      return true;
    }
  }
  
  // Verificar se é público (apenas para leitura)
  if (action === 'read') {
    const publicPermission = documentPermissions.find(p => 
      p.includes('any') && p.includes('read')
    );
    
    if (publicPermission) {
      return true;
    }
  }
  
  return false;
}

/**
 * Verifica se um usuário pode editar um documento
 */
export function canEditDocument(
  documentPermissions: string[],
  userId: string,
  teamId?: string | null
): boolean {
  return canAccessDocument(documentPermissions, userId, teamId, 'write');
}

/**
 * Verifica se um usuário pode deletar um documento
 */
export function canDeleteDocument(
  documentPermissions: string[],
  userId: string,
  teamId?: string | null
): boolean {
  return canAccessDocument(documentPermissions, userId, teamId, 'delete');
}
