/**
 * Event Calendar Styles
 * Estilos para o novo sistema de calendário
 */

/* ============================================================================
   VARIÁVEIS CSS CUSTOMIZADAS
   ============================================================================ */

.event-calendar {
  --calendar-bg: hsl(var(--background));
  --calendar-border: hsl(var(--border));
  --calendar-accent: hsl(var(--accent));
  --calendar-primary: hsl(var(--primary));
  --calendar-muted: hsl(var(--muted));
  --calendar-foreground: hsl(var(--foreground));
  --calendar-muted-foreground: hsl(var(--muted-foreground));
}

/* ============================================================================
   LAYOUT PRINCIPAL
   ============================================================================ */

.event-calendar {
  background-color: var(--calendar-bg);
  font-family: inherit;
  height: 100%;
  overflow: hidden;
}

/* ============================================================================
   HEADER E NAVEGAÇÃO
   ============================================================================ */

.calendar-header {
  background-color: var(--calendar-bg);
  border-bottom: 1px solid var(--calendar-border);
  backdrop-filter: blur(8px);
  position: sticky;
  top: 0;
  z-index: 20;
}

.calendar-nav-button {
  transition: all 0.2s ease;
}

.calendar-nav-button:hover {
  background-color: var(--calendar-accent);
  transform: scale(1.05);
}

.calendar-title {
  font-weight: 600;
  color: var(--calendar-foreground);
  transition: color 0.2s ease;
}

/* ============================================================================
   VISUALIZAÇÃO MENSAL
   ============================================================================ */

.month-view {
  display: grid;
  grid-template-rows: auto 1fr;
  height: 100%;
}

.month-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background-color: var(--calendar-muted);
  border-bottom: 1px solid var(--calendar-border);
}

.month-weekday {
  padding: 12px 8px;
  text-align: center;
  font-weight: 600;
  font-size: 14px;
  color: var(--calendar-muted-foreground);
  border-right: 1px solid var(--calendar-border);
}

.month-weekday:last-child {
  border-right: none;
}

.month-grid {
  display: grid;
  grid-template-rows: repeat(6, 1fr);
  flex: 1;
}

.month-week {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  border-bottom: 1px solid var(--calendar-border);
}

.month-week:last-child {
  border-bottom: none;
}

.month-day {
  border-right: 1px solid var(--calendar-border);
  padding: 8px;
  min-height: 120px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;
}

.month-day:last-child {
  border-right: none;
}

.month-day:hover {
  background-color: var(--calendar-accent);
}

.month-day.today {
  background-color: hsl(var(--accent) / 0.2);
}

.month-day.other-month {
  background-color: var(--calendar-muted);
  color: var(--calendar-muted-foreground);
}

.month-day-number {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
}

.month-day-number.today {
  background-color: var(--calendar-primary);
  color: hsl(var(--primary-foreground));
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

/* ============================================================================
   EVENTOS NO CALENDÁRIO
   ============================================================================ */

.calendar-event {
  background-color: hsl(var(--primary) / 0.9);
  color: hsl(var(--primary-foreground));
  border: 1px solid hsl(var(--primary) / 0.2);
  border-radius: 4px;
  padding: 2px 6px;
  margin: 1px 0;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.calendar-event:hover {
  background-color: var(--calendar-primary);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.calendar-event.category-meeting {
  background-color: hsl(var(--blue-500) / 0.9);
  border-color: hsl(var(--blue-500) / 0.2);
}

.calendar-event.category-task {
  background-color: hsl(var(--green-500) / 0.9);
  border-color: hsl(var(--green-500) / 0.2);
}

.calendar-event.category-reminder {
  background-color: hsl(var(--yellow-500) / 0.9);
  border-color: hsl(var(--yellow-500) / 0.2);
}

.calendar-event.category-personal {
  background-color: hsl(var(--purple-500) / 0.9);
  border-color: hsl(var(--purple-500) / 0.2);
}

/* ============================================================================
   VISUALIZAÇÃO SEMANAL E DIÁRIA
   ============================================================================ */

.week-view,
.day-view {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.time-grid {
  flex: 1;
  overflow: auto;
  position: relative;
}

.time-slot {
  border-bottom: 1px solid hsl(var(--border) / 0.3);
  transition: background-color 0.2s ease;
}

.time-slot:hover {
  background-color: hsl(var(--accent) / 0.3);
}

.time-label {
  font-size: 12px;
  color: var(--calendar-muted-foreground);
  text-align: right;
  padding: 8px;
  border-right: 1px solid var(--calendar-border);
  background-color: hsl(var(--muted) / 0.3);
}

.positioned-event {
  position: absolute;
  background-color: hsl(var(--primary) / 0.9);
  color: hsl(var(--primary-foreground));
  border: 1px solid hsl(var(--primary) / 0.2);
  border-radius: 6px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.positioned-event:hover {
  background-color: var(--calendar-primary);
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 20;
}

/* ============================================================================
   VISUALIZAÇÃO DE AGENDA
   ============================================================================ */

.agenda-view {
  padding: 16px;
  overflow: auto;
}

.agenda-day-card {
  margin-bottom: 16px;
  border: 1px solid var(--calendar-border);
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.agenda-day-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.agenda-day-card.today {
  border-color: var(--calendar-primary);
  box-shadow: 0 0 0 1px hsl(var(--primary) / 0.2);
}

.agenda-day-header {
  background-color: hsl(var(--muted) / 0.3);
  padding: 16px;
  border-bottom: 1px solid var(--calendar-border);
}

.agenda-day-header.today {
  background-color: hsl(var(--primary) / 0.1);
}

.agenda-day-number {
  font-size: 24px;
  font-weight: bold;
  color: var(--calendar-foreground);
}

.agenda-day-number.today {
  color: var(--calendar-primary);
}

.agenda-event {
  padding: 16px;
  border-bottom: 1px solid hsl(var(--border) / 0.5);
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.agenda-event:last-child {
  border-bottom: none;
}

.agenda-event:hover {
  background-color: hsl(var(--accent) / 0.5);
}

/* ============================================================================
   INDICADOR DE TEMPO ATUAL
   ============================================================================ */

.current-time-indicator {
  position: absolute;
  left: 0;
  right: 0;
  z-index: 30;
  pointer-events: none;
}

.current-time-dot {
  width: 12px;
  height: 12px;
  background-color: hsl(var(--destructive));
  border: 2px solid white;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.current-time-line {
  height: 2px;
  background-color: hsl(var(--destructive));
  flex: 1;
}

/* ============================================================================
   BUSCA DE EVENTOS
   ============================================================================ */

.search-container {
  border-top: 1px solid var(--calendar-border);
  background-color: var(--calendar-bg);
}

.search-input {
  transition: all 0.2s ease;
}

.search-input:focus {
  box-shadow: 0 0 0 2px hsl(var(--ring));
}

.search-results-info {
  font-size: 12px;
  color: var(--calendar-muted-foreground);
  padding: 8px 0;
}

.search-clear-button {
  transition: all 0.2s ease;
}

.search-clear-button:hover {
  background-color: hsl(var(--destructive) / 0.1);
  color: hsl(var(--destructive));
}

/* ============================================================================
   RESPONSIVIDADE
   ============================================================================ */

@media (max-width: 768px) {
  .month-day {
    min-height: 80px;
    padding: 4px;
  }

  .month-day-number {
    font-size: 12px;
  }

  .calendar-event {
    font-size: 10px;
    padding: 1px 4px;
  }

  .time-label {
    font-size: 10px;
    padding: 4px;
    width: 48px;
  }

  .positioned-event {
    padding: 4px;
    font-size: 11px;
  }

  .agenda-day-header {
    padding: 12px;
  }

  .agenda-event {
    padding: 12px;
  }

  /* Day view mobile optimizations */
  .day-view .time-grid {
    font-size: 12px;
  }

  .day-view .time-slot {
    height: 60px;
  }

  .day-view .positioned-event {
    padding: 8px;
    font-size: 11px;
    min-height: 40px;
  }

  .day-view .current-time-indicator {
    left: 48px;
  }
}

@media (max-width: 480px) {
  .month-day {
    min-height: 60px;
    padding: 2px;
  }

  .calendar-event {
    font-size: 9px;
    padding: 1px 2px;
  }

  .month-weekday {
    padding: 8px 4px;
    font-size: 12px;
  }
}

/* ============================================================================
   ANIMAÇÕES
   ============================================================================ */

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.calendar-event,
.positioned-event,
.agenda-event {
  animation: fadeIn 0.3s ease;
}

/* ============================================================================
   MODAL DE EVENTOS
   ============================================================================ */

.event-modal {
  max-height: 90vh;
  overflow-y: auto;
}

.event-modal .category-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.event-modal .category-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 8px;
  border: 1px solid var(--calendar-border);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.event-modal .category-option:hover {
  background-color: var(--calendar-accent);
}

.event-modal .category-option.selected {
  border-color: var(--calendar-primary);
  background-color: hsl(var(--primary) / 0.1);
  color: var(--calendar-primary);
}

.event-modal .category-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.event-modal .form-error {
  color: hsl(var(--destructive));
  font-size: 12px;
  margin-top: 4px;
}

.event-modal .time-select {
  max-height: 200px;
  overflow-y: auto;
}

.event-modal .date-picker-trigger {
  justify-content: flex-start;
  text-align: left;
  font-weight: normal;
}

.event-modal .date-picker-trigger.placeholder {
  color: var(--calendar-muted-foreground);
}

/* ============================================================================
   ACESSIBILIDADE
   ============================================================================ */

.calendar-event:focus,
.positioned-event:focus,
.agenda-event:focus,
.month-day:focus {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
  .calendar-event,
  .positioned-event,
  .agenda-event,
  .month-day,
  .time-slot,
  .event-modal .category-option {
    transition: none;
    animation: none;
  }
}
