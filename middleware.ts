// middleware.ts
import { NextResponse, type NextRequest } from "next/server";
import { hasValidSession, getUserWithAdminCheck } from "@/actions/auth";

// Rotas protegidas que requerem autenticação
const protectedRoutes = ["/dashboard"];

// Rotas que requerem permissões de admin
const adminRoutes = ["/admin"];

// Rotas que devem ser acessíveis sem autenticação
const publicApiRoutes = [
  "/api/webhook/stripe",
  "/api/webhooks/mercadopago",
  "/api/create-checkout-session",
  "/api/pusher/auth"
];

/**
 * Verifica se a rota é uma API pública
 * @param pathname - Caminho da URL
 * @returns boolean
 */
function isPublicApiRoute(pathname: string): boolean {
  return publicApiRoutes.some(route => {
    if (route.endsWith("/*")) {
      const baseRoute = route.slice(0, -2);
      return pathname.startsWith(baseRoute);
    }
    return pathname.startsWith(route);
  });
}

/**
 * Verifica se a rota é protegida
 * @param pathname - Caminho da URL
 * @returns boolean
 */
function isProtectedRoute(pathname: string): boolean {
  return protectedRoutes.some(route => pathname.startsWith(route));
}

/**
 * Verifica se a rota requer permissões de admin
 * @param pathname - Caminho da URL
 * @returns boolean
 */
function isAdminRoute(pathname: string): boolean {
  return adminRoutes.some(route => pathname.startsWith(route));
}

/**
 * Middleware principal do Next.js (Server-side)
 * Gerencia autenticação e redirecionamentos de forma otimizada
 */
export async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;
  const searchParams = request.nextUrl.searchParams;

  // 1. Permitir acesso a APIs públicas sem autenticação
  if (isPublicApiRoute(pathname)) {
    return NextResponse.next();
  }

  // 2. Verificação rápida de sessão (sem fetch completo dos dados do usuário)
  const hasSession = await hasValidSession();

  // 3. Redirecionar usuários não autenticados tentando acessar rotas protegidas
  if (!hasSession && (isProtectedRoute(pathname) || isAdminRoute(pathname))) {
    const redirectUrl = new URL("/auth/login", request.url);
    redirectUrl.searchParams.set("redirectTo", pathname);
    return NextResponse.redirect(redirectUrl);
  }

  // 4. Para rotas de admin, precisamos verificar permissões (fetch completo apenas se necessário)
  if (hasSession && isAdminRoute(pathname)) {
    const { user, isAdmin } = await getUserWithAdminCheck();

    if (!user || !isAdmin) {
      return NextResponse.redirect(new URL("/dashboard", request.url));
    }
  }

  // 5. Redirecionar usuários autenticados tentando acessar rotas de autenticação
  if (hasSession && pathname.startsWith("/auth")) {
    const redirectTo = searchParams.get("redirectTo") || "/dashboard";
    return NextResponse.redirect(new URL(redirectTo, request.url));
  }

  // 6. Permitir acesso para todas as outras rotas
  return NextResponse.next();
}

/**
 * Configuração do middleware
 * Define quais rotas devem ser processadas pelo middleware
 */
export const config = {
  matcher: [
    "/dashboard/:path*",
    "/admin/:path*",
    "/auth/:path*",
    "/api/webhook/stripe/:path*",
    "/api/webhooks/mercadopago/:path*",
    "/api/create-checkout-session/:path*",
    "/api/socket/:path*"
  ],
};