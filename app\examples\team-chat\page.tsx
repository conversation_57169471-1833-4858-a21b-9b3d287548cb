/**
 * Página de exemplo para demonstrar o sistema de chat do time
 */

import { TeamChatDemo } from '@/components/examples/team-chat-demo';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Demo: Chat do Time | Template Appwrite',
  description: 'Demonstração do sistema de chat integrado com teams',
};

export default function TeamChatExamplePage() {
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Sistema de Chat do Time</h1>
          <p className="text-muted-foreground">
            Demonstração completa do sistema de chat integrado com teams, incluindo:
          </p>
          <ul className="list-disc list-inside mt-2 text-muted-foreground space-y-1">
            <li>Criação automática de chat nas preferências do time</li>
            <li>Integração com WebSocket para tempo real</li>
            <li>Envio e recebimento de mensagens</li>
            <li>Indicadores de status e atividade</li>
            <li>Interface responsiva e moderna</li>
          </ul>
        </div>

        <TeamChatDemo />

        <div className="mt-8 p-4 bg-muted rounded-lg">
          <h2 className="font-semibold mb-2">Como Funciona:</h2>
          <ol className="list-decimal list-inside space-y-1 text-sm">
            <li>Selecione um time da lista (você precisa estar em pelo menos um time)</li>
            <li>Se o time não tiver chat, clique em "Configurar Chat do Time"</li>
            <li>O sistema criará automaticamente um chat e salvará o ID nas preferências do time</li>
            <li>Você poderá enviar mensagens e ver atualizações em tempo real</li>
            <li>O chat fica disponível nas preferências do time para todos os membros</li>
          </ol>
        </div>
      </div>
    </div>
  );
}
