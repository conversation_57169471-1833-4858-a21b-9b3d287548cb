/**
 * Simple Storage API Hooks
 * Direct Appwrite calls with React Query
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import {
  uploadFile,
  getFile,
  deleteFile,
  listFiles,
  getFilePreview,
  getFileDownload,
  getFileView,
  updateFile
} from '../../lib/appwrite/functions/storage';

/**
 * Upload file
 */
export function useUploadFile() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ file, fileId }: { file: File; fileId?: string }) => {
      const result = await uploadFile(file, fileId);
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['files'] });
      toast.success('Arquivo enviado com sucesso');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erro ao enviar arquivo');
    },
  });
}

/**
 * Get file
 */
export function useFile(fileId: string) {
  return useQuery({
    queryKey: ['files', fileId],
    queryFn: async () => {
      const result = await getFile(fileId);
      return result;
    },
    enabled: !!fileId,
  });
}

/**
 * List files
 */
export function useFiles(queries?: string[]) {
  return useQuery({
    queryKey: ['files', queries],
    queryFn: async () => {
      const result = await listFiles(queries);
      return result;
    },
  });
}

/**
 * Delete file
 */
export function useDeleteFile() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (fileId: string) => {
      await deleteFile(fileId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['files'] });
      toast.success('Arquivo excluído com sucesso');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erro ao excluir arquivo');
    },
  });
}

/**
 * Update file
 */
export function useUpdateFile() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ fileId, name, permissions }: { fileId: string; name?: string; permissions?: string[] }) => {
      const result = await updateFile(fileId, name, permissions);
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['files'] });
      toast.success('Arquivo atualizado com sucesso');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erro ao atualizar arquivo');
    },
  });
}

// Helper functions for file URLs
export { getFilePreview, getFileDownload, getFileView };
