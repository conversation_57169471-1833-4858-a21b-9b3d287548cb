# Database Functions - Appwrite

Este documento explica como funciona o sistema de banco de dados do projeto usando Appwrite, baseado no arquivo `app/lib/appwrite/functions/database.ts`.

## Visão Geral

O sistema utiliza funções simples e diretas para interagir com o banco de dados Appwrite, implementando soft delete por padrão e controle de acesso baseado em usuário/equipe.

## Funcionalidades Principais

### Soft Delete
- **isDeleted**: Campo booleano (padrão: false) usado para soft delete
- **Soft Delete**: Marca documentos como deletados sem removê-los fisicamente
- **Hard Delete**: Remove permanentemente do banco (irreversível)
- **Restore**: Restaura documentos soft deleted

### Controle de Acesso
- Filtra documentos por `userId` ou `teamId`
- Usuários só veem seus próprios documentos ou da equipe

## Funções Base

### Criar Documento
```typescript
createDocument(collectionId: string, data: Record<string, any>, documentId?: string)
```

### Buscar Documento
```typescript
getDocument(collectionId: string, documentId: string)
// Automaticamente filtra documentos soft deleted
```

### Atualizar Documento
```typescript
updateDocument(collectionId: string, documentId: string, data: Record<string, any>)
```

### Deletar Documento
```typescript
deleteDocument(collectionId: string, documentId: string) // Soft delete por padrão
softDeleteDocument(collectionId: string, documentId: string) // Explícito
hardDeleteDocument(collectionId: string, documentId: string) // Permanente
```

### Restaurar Documento
```typescript
restoreDocument(collectionId: string, documentId: string)
```

### Listar Documentos
```typescript
listDocuments(collectionId: string) // Apenas ativos (isDeleted: false)
listDeletedDocuments(collectionId: string) // Apenas deletados (isDeleted: true)
listAllDocuments(collectionId: string) // Todos os documentos
```

## Como Implementar para Novas Coleções

Para criar ações para uma nova coleção, siga este padrão:

```typescript
export const minhaColecao = {
  create: (data: Record<string, any>) => createDocument(COLLECTIONS.MINHA_COLECAO, data),
  get: (id: string) => getDocument(COLLECTIONS.MINHA_COLECAO, id),
  update: (id: string, data: Record<string, any>) => updateDocument(COLLECTIONS.MINHA_COLECAO, id, data),
  delete: (id: string) => deleteDocument(COLLECTIONS.MINHA_COLECAO, id),
  softDelete: (id: string) => softDeleteDocument(COLLECTIONS.MINHA_COLECAO, id),
  hardDelete: (id: string) => hardDeleteDocument(COLLECTIONS.MINHA_COLECAO, id),
  restore: (id: string) => restoreDocument(COLLECTIONS.MINHA_COLECAO, id),
  list: () => listDocuments(COLLECTIONS.MINHA_COLECAO),
  listDeleted: () => listDeletedDocuments(COLLECTIONS.MINHA_COLECAO),
  listAll: () => listAllDocuments(COLLECTIONS.MINHA_COLECAO),
};
```

## Exemplos de Uso

### Clientes
```typescript
import { clients } from '@/lib/appwrite/functions/database';

// Criar cliente
const novoCliente = await clients.create({
  nome: 'João Silva',
  email: '<EMAIL>',
  telefone: '11999999999'
});

// Buscar cliente
const cliente = await clients.get('cliente-id');

// Atualizar cliente
await clients.update('cliente-id', {
  telefone: '11888888888'
});

// Soft delete (padrão)
await clients.delete('cliente-id');

// Hard delete (permanente)
await clients.hardDelete('cliente-id');

// Restaurar cliente
await clients.restore('cliente-id');

// Listar clientes ativos
const clientesAtivos = await clients.list();

// Listar clientes deletados
const clientesDeletados = await clients.listDeleted();

// Listar todos os clientes
const todosClientes = await clients.listAll();
```

## Coleções Disponíveis

O arquivo já implementa as seguintes coleções:
- `clients` - Clientes
- `publicProfiles` - Perfis públicos
- `notifications` - Notificações
- `activityLogs` - Logs de atividade
- `chatMessages` - Mensagens de chat
- `teamChats` - Chats de equipe

## Considerações Importantes

1. **Soft Delete por Padrão**: `delete()` usa soft delete. Use `hardDelete()` apenas quando necessário
2. **Filtros Automáticos**: Funções de listagem filtram automaticamente por usuário/equipe
3. **isDeleted**: Todas as coleções devem ter o campo `isDeleted` (boolean, padrão: false)
4. **Segurança**: O controle de acesso é feito automaticamente nas queries
5. **Performance**: Use `listDocuments()` para dados ativos, evite `listAll()` quando possível

## Estrutura de Dados

Todos os documentos devem incluir:
```typescript
{
  isDeleted: boolean; // Campo obrigatório para soft delete
  userId?: string;    // ID do usuário proprietário
  teamId?: string;    // ID da equipe (opcional)
  // ... outros campos específicos da coleção
}
```
