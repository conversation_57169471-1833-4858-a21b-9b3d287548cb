/**
 * KanbanTask Component
 * Individual task card with drag and drop functionality
 */

import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useSnapshot } from 'valtio';
import {
  Calendar,
  Clock,
  User,
  MessageSquare,
  Paperclip,
  MoreHorizontal,
  AlertTriangle,
  CheckCircle2
} from 'lucide-react';
import { formatDistanceToNow, isAfter, isBefore } from 'date-fns';
import { ptBR } from 'date-fns/locale';

import { Card, CardContent } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '../ui/dropdown-menu';

import { kanbanStore, kanbanActions } from '../../stores/kanban-store';
import { useUpdateTask } from '../../hooks/api/use-kanban';
import { useAuth } from '../../hooks/use-auth';
import { useIsMobile } from '../../hooks/use-mobile';
import { KanbanLabels } from './KanbanLabel';
import type { EmbeddedTask, EmbeddedLabel } from '@/schemas/kanban';

interface KanbanTaskProps {
  task: EmbeddedTask;
  columnId: string;
  labels?: EmbeddedLabel[]; // Labels do board para exibir nas tarefas
}

export function KanbanTask({ task, columnId, labels = [] }: KanbanTaskProps) {
  const snap = useSnapshot(kanbanStore);
  const { user } = useAuth();
  const updateTaskMutation = useUpdateTask();
  const isMobile = useIsMobile();

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: task.id,
    data: {
      type: 'task',
      task,
    },
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  // Priority colors
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critica':
        return 'border-l-red-500 bg-red-50 dark:bg-red-950/20';
      case 'alta':
        return 'border-l-orange-500 bg-orange-50 dark:bg-orange-950/20';
      case 'media':
        return 'border-l-yellow-500 bg-yellow-50 dark:bg-yellow-950/20';
      case 'baixa':
        return 'border-l-green-500 bg-green-50 dark:bg-green-950/20';
      default:
        return 'border-l-gray-500 bg-gray-50 dark:bg-gray-950/20';
    }
  };

  // Priority badge variant
  const getPriorityVariant = (priority: string) => {
    switch (priority) {
      case 'critica':
        return 'destructive';
      case 'alta':
        return 'secondary';
      case 'media':
        return 'outline';
      case 'baixa':
        return 'outline';
      default:
        return 'outline';
    }
  };

  // Check if task is overdue
  const isOverdue = task.dueDate &&
    isBefore(new Date(task.dueDate), new Date()) &&
    task.status !== 'done';

  // Check if task is due soon (within 24 hours)
  const isDueSoon = task.dueDate &&
    isAfter(new Date(task.dueDate), new Date()) &&
    isBefore(new Date(task.dueDate), new Date(Date.now() + 24 * 60 * 60 * 1000));

  // Get labels for this task
  const taskLabels = labels.filter(label => task.labelIds?.includes(label.id));

  // Check if task has cover image
  const hasCoverImage = task.coverImageUrl || task.coverColor;

  const handleTaskClick = (e: React.MouseEvent) => {
    // Don't open modal if clicking on interactive elements or during drag
    if ((e.target as HTMLElement).closest('button, [role="button"]') || isDragging) {
      return;
    }
    kanbanActions.openTaskView(task);
  };

  const handleDeleteTask = () => {
    if (!user) return;
    // TODO: Implementar delete task na estrutura otimizada
    console.log('Delete task not implemented yet');
  };

  const handleCompleteTask = () => {
    if (!user) return;
    // TODO: Implementar update task na estrutura otimizada
    console.log('Update task not implemented yet');
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`${isDragging ? 'opacity-50 rotate-1 scale-105 z-50' : ''} touch-none`}
      {...attributes}
    >
      <Card
        className={`
          cursor-pointer transition-all hover:shadow-md border-l-4 overflow-hidden
          ${getPriorityColor(task.priority)}
          ${snap.compactMode ? 'p-2' : ''}
          ${task.status === 'done' ? 'opacity-75' : ''}
          ${isDragging ? 'shadow-lg' : ''}
        `}
        onClick={handleTaskClick}
        {...listeners}
      >
        {/* Cover Image/Color */}
        {hasCoverImage && !snap.compactMode && (
          <div className="relative">
            {task.coverImageUrl ? (
              <img
                src={task.coverImageUrl}
                alt="Task cover"
                className="w-full h-24 object-cover"
              />
            ) : task.coverColor ? (
              <div
                className="w-full h-16"
                style={{ backgroundColor: task.coverColor }}
              />
            ) : null}
          </div>
        )}

        <CardContent className={`${snap.compactMode || isMobile ? 'p-3' : 'p-4'}`}>
          {/* Task Header */}
          <div className={`flex items-start justify-between gap-2 ${isMobile ? 'mb-1' : 'mb-2'}`}>
            <h4 className={`font-medium leading-tight ${snap.compactMode || isMobile ? 'text-sm' : ''} ${
              task.status === 'done' ? 'line-through text-muted-foreground' : ''
            }`}>
              {task.title}
            </h4>

            <div className="flex items-center gap-1">
              {/* Complete button */}
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={(e) => {
                  e.stopPropagation();
                  handleCompleteTask();
                }}
              >
                <CheckCircle2 className={`h-3 w-3 ${
                  task.status === 'done' ? 'text-green-600' : 'text-muted-foreground'
                }`} />
              </Button>

              {/* Task menu */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <MoreHorizontal className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => kanbanActions.openTaskEdit(task)}>
                    Editar
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => kanbanActions.openTaskView(task)}>
                    Ver Detalhes
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={handleDeleteTask}
                    className="text-destructive"
                  >
                    Excluir
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Task Labels */}
          {taskLabels.length > 0 && (
            <div className={`${isMobile ? 'mb-1' : 'mb-2'}`}>
              <KanbanLabels
                labels={taskLabels}
                maxVisible={snap.compactMode || isMobile ? 3 : 5}
                size={snap.compactMode || isMobile ? 'sm' : 'md'}
                showText={!snap.compactMode && !isMobile}
              />
            </div>
          )}

          {/* Task Description */}
          {task.description && !snap.compactMode && !isMobile && (
            <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
              {task.description}
            </p>
          )}

          {/* Task Tags */}
          {task.tags.length > 0 && (
            <div className={`flex flex-wrap gap-1 ${isMobile ? 'mb-2' : 'mb-3'}`}>
              {task.tags.slice(0, snap.compactMode || isMobile ? 2 : 4).map((tag, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {task.tags.length > (snap.compactMode || isMobile ? 2 : 4) && (
                <Badge variant="outline" className="text-xs">
                  +{task.tags.length - (snap.compactMode || isMobile ? 2 : 4)}
                </Badge>
              )}
            </div>
          )}

          {/* Task Metadata */}
          <div className={`space-y-${isMobile ? '1' : '2'}`}>
            {/* Priority and Status */}
            <div className="flex items-center gap-2">
              <Badge
                variant={getPriorityVariant(task.priority)}
                className="text-xs"
              >
                {isMobile ? task.priority.charAt(0).toUpperCase() : task.priority}
              </Badge>

              {task.status !== 'todo' && !isMobile && (
                <Badge variant="outline" className="text-xs">
                  {task.status.replace('_', ' ')}
                </Badge>
              )}
            </div>

            {/* Due Date */}
            {task.dueDate && (
              <div className={`flex items-center gap-1 text-xs ${
                isOverdue ? 'text-destructive' :
                isDueSoon ? 'text-orange-600' :
                'text-muted-foreground'
              }`}>
                {isOverdue && <AlertTriangle className="h-3 w-3" />}
                <Calendar className="h-3 w-3" />
                <span>
                  {formatDistanceToNow(new Date(task.dueDate), {
                    addSuffix: true,
                    locale: ptBR
                  })}
                </span>
              </div>
            )}

            {/* Assigned User */}
            {task.assignedTo && !isMobile && (
              <div className="flex items-center gap-2">
                <Avatar className="h-5 w-5">
                  <AvatarImage src={`/api/avatar/${task.assignedTo}`} />
                  <AvatarFallback className="text-xs">
                    {task.assignedTo.slice(0, 2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <span className="text-xs text-muted-foreground">
                  Atribuído
                </span>
              </div>
            )}

            {/* Time Tracking */}
            {(task.estimatedHours || task.actualHours) && !snap.compactMode && !isMobile && (
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <Clock className="h-3 w-3" />
                {task.actualHours && (
                  <span>{task.actualHours}h</span>
                )}
                {task.estimatedHours && task.actualHours && <span>/</span>}
                {task.estimatedHours && (
                  <span className={task.actualHours ? 'text-muted-foreground/70' : ''}>
                    {task.estimatedHours}h est.
                  </span>
                )}
              </div>
            )}
          </div>

          {/* Task Footer - Comments, Attachments, and Checklists */}
          {!snap.compactMode && !isMobile && (
            <div className="flex items-center justify-between mt-3 pt-2 border-t">
              <div className="flex items-center gap-3 text-xs text-muted-foreground">
                {/* Comments count */}
                {task.comments.length > 0 && (
                  <div className="flex items-center gap-1">
                    <MessageSquare className="h-3 w-3" />
                    <span>{task.comments.length}</span>
                  </div>
                )}

                {/* Attachments count */}
                {task.attachments.length > 0 && (
                  <div className="flex items-center gap-1">
                    <Paperclip className="h-3 w-3" />
                    <span>{task.attachments.length}</span>
                  </div>
                )}

                {/* Checklists progress */}
                {task.checklists.length > 0 && (
                  <div className="flex items-center gap-1">
                    <CheckCircle2 className="h-3 w-3" />
                    <span>{task.checklists.reduce((acc, checklist) => acc + checklist.items.filter(item => item.completed).length, 0)}/{task.checklists.reduce((acc, checklist) => acc + checklist.items.length, 0)}</span>
                  </div>
                )}
              </div>

              {/* Created date */}
              <span className="text-xs text-muted-foreground">
                {formatDistanceToNow(new Date(task.createdAt), {
                  addSuffix: true,
                  locale: ptBR
                })}
              </span>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
