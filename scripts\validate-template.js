#!/usr/bin/env node

/**
 * Script de validação do template
 * Verifica se todas as configurações estão corretas e não há dados mock em produção
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

// Cores para output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkFileExists(filePath) {
  return fs.existsSync(path.join(rootDir, filePath));
}

function readFile(filePath) {
  try {
    return fs.readFileSync(path.join(rootDir, filePath), 'utf8');
  } catch (error) {
    return null;
  }
}

function checkMockDataUsage() {
  log('\n📊 Verificando uso de dados mock...', 'blue');

  const issues = [];

  // Verificar se dados mock estão isolados
  if (!checkFileExists('app/data/dev/mock-clients.ts')) {
    issues.push('❌ mock-clients.ts não foi movido para app/data/dev/');
  } else {
    log('✅ mock-clients.ts está isolado em app/data/dev/', 'green');
  }

  if (!checkFileExists('app/data/dev/mock-events.ts')) {
    issues.push('❌ mock-events.ts não foi movido para app/data/dev/');
  } else {
    log('✅ mock-events.ts está isolado em app/data/dev/', 'green');
  }

  // Verificar se há referências a dados mock em produção
  const productionFiles = [
    'app/hooks/use-google-analytics.ts',
    'app/lib/google-analytics-api.ts',
    'app/components/testimonials-section.tsx',
    'app/components/stats-section.tsx'
  ];

  productionFiles.forEach(file => {
    const content = readFile(file);
    if (content) {
      // Verificar por padrões específicos de dados mock (excluindo comentários)
      const lines = content.split('\n');
      const codeLines = lines.filter(line =>
        !line.trim().startsWith('//') &&
        !line.trim().startsWith('*') &&
        !line.trim().startsWith('/*')
      );
      const codeContent = codeLines.join('\n');

      if (codeContent.includes('generateMockGAData(') ||
          codeContent.includes('mockData =') ||
          codeContent.includes('const fake') ||
          codeContent.includes('dummy =')) {
        issues.push(`❌ ${file} ainda contém referências a dados mock`);
      } else {
        log(`✅ ${file} livre de dados mock`, 'green');
      }
    }
  });

  return issues;
}

function checkEnvironmentConfig() {
  log('\n⚙️ Verificando configuração de ambiente...', 'blue');

  const issues = [];
  const envExample = readFile('.env.example');

  if (!envExample) {
    issues.push('❌ Arquivo .env.example não encontrado');
    return issues;
  }

  // Verificar se cloud functions foram atualizadas
  if (envExample.includes('NEXT_PUBLIC_CLOUDFUNCTION_ADMIN_USERS') ||
      envExample.includes('NEXT_PUBLIC_CLOUDFUNCTION_ADMIN_USER_MANAGEMENT')) {
    issues.push('❌ .env.example ainda contém referências a cloud functions antigas');
  } else {
    log('✅ .env.example atualizado com nova arquitetura', 'green');
  }

  // Verificar variáveis essenciais
  const requiredVars = [
    'NEXT_PUBLIC_APPWRITE_ENDPOINT',
    'NEXT_PUBLIC_APPWRITE_PROJECT_ID',
    'NEXT_PUBLIC_APPWRITE_DATABASE_ID'
  ];

  requiredVars.forEach(varName => {
    if (envExample.includes(varName)) {
      log(`✅ ${varName} presente no .env.example`, 'green');
    } else {
      issues.push(`❌ ${varName} ausente no .env.example`);
    }
  });

  return issues;
}

function checkLandingPageContent() {
  log('\n🎨 Verificando conteúdo da landing page...', 'blue');

  const issues = [];

  // Verificar testimonials
  const testimonialsContent = readFile('app/components/testimonials-section.tsx');
  if (testimonialsContent) {
    if (testimonialsContent.includes('Carlos Silva') ||
        testimonialsContent.includes('Ana Costa') ||
        testimonialsContent.includes('depoimentos')) {
      issues.push('❌ testimonials-section.tsx ainda contém depoimentos fictícios');
    } else {
      log('✅ testimonials-section.tsx livre de depoimentos fictícios', 'green');
    }
  }

  // Verificar stats
  const statsContent = readFile('app/components/stats-section.tsx');
  if (statsContent) {
    if (statsContent.includes('1,000+') ||
        statsContent.includes('R$ 2M+') ||
        statsContent.includes('TechCrunch 2024')) {
      issues.push('❌ stats-section.tsx ainda contém estatísticas infladas');
    } else {
      log('✅ stats-section.tsx livre de estatísticas infladas', 'green');
    }
  }

  return issues;
}

function checkDocumentation() {
  log('\n📚 Verificando documentação...', 'blue');

  const issues = [];

  // Verificar REPORTS.md
  const reportsDoc = readFile('docs/REPORTS.md');
  if (reportsDoc) {
    if (reportsDoc.includes('mockReportsData') ||
        reportsDoc.includes('dados simulados')) {
      issues.push('❌ docs/REPORTS.md ainda contém exemplos com dados mock');
    } else {
      log('✅ docs/REPORTS.md atualizado com dados reais', 'green');
    }
  }

  // Verificar se guia do GA real existe
  if (checkFileExists('docs/GOOGLE_ANALYTICS_REAL_SETUP.md')) {
    log('✅ Guia de configuração do Google Analytics real criado', 'green');
  } else {
    issues.push('❌ Guia de configuração do Google Analytics real não encontrado');
  }

  return issues;
}

function checkTypeScriptErrors() {
  log('\n🔍 Verificando erros de TypeScript...', 'blue');

  try {
    const { execSync } = require('child_process');
    const result = execSync('npx tsc --noEmit', {
      cwd: rootDir,
      stdio: 'pipe',
      encoding: 'utf8'
    });
    log('✅ Nenhum erro de TypeScript encontrado', 'green');
    return [];
  } catch (error) {
    // Se o comando falhou, verificar se é por erro de TypeScript ou outro problema
    if (error.status === 1 || error.status === 2) {
      // Erro de TypeScript
      return ['❌ Erros de TypeScript encontrados - execute `npx tsc --noEmit` para detalhes'];
    } else {
      // Outro tipo de erro (comando não encontrado, etc.)
      log('⚠️ Não foi possível verificar TypeScript - comando pode não estar disponível', 'yellow');
      return [];
    }
  }
}

function checkPackageJson() {
  log('\n📦 Verificando package.json...', 'blue');

  const issues = [];
  const packageJson = readFile('package.json');

  if (!packageJson) {
    issues.push('❌ package.json não encontrado');
    return issues;
  }

  const pkg = JSON.parse(packageJson);

  // Verificar scripts essenciais
  const requiredScripts = ['dev', 'build', 'setup', 'check'];
  requiredScripts.forEach(script => {
    if (pkg.scripts && pkg.scripts[script]) {
      log(`✅ Script "${script}" presente`, 'green');
    } else {
      issues.push(`❌ Script "${script}" ausente no package.json`);
    }
  });

  return issues;
}

function generateReport(allIssues) {
  log('\n📋 RELATÓRIO DE VALIDAÇÃO', 'bold');
  log('='.repeat(50), 'blue');

  if (allIssues.length === 0) {
    log('\n🎉 PARABÉNS! Template validado com sucesso!', 'green');
    log('✅ Todos os dados mock foram removidos', 'green');
    log('✅ Configurações estão corretas', 'green');
    log('✅ Documentação está atualizada', 'green');
    log('✅ Nenhum erro de TypeScript', 'green');
    log('\n🚀 Template pronto para produção!', 'bold');
  } else {
    log('\n⚠️ PROBLEMAS ENCONTRADOS:', 'yellow');
    allIssues.forEach(issue => {
      log(`  ${issue}`, 'red');
    });

    log('\n🔧 AÇÕES NECESSÁRIAS:', 'yellow');
    log('1. Corrija os problemas listados acima', 'yellow');
    log('2. Execute novamente: yarn validate', 'yellow');
    log('3. Verifique a documentação em docs/', 'yellow');
  }

  log('\n📊 ESTATÍSTICAS:', 'blue');
  log(`Total de verificações: ${6}`, 'blue');
  log(`Problemas encontrados: ${allIssues.length}`, allIssues.length > 0 ? 'red' : 'green');
  log(`Taxa de sucesso: ${Math.round(((6 * 10 - allIssues.length) / (6 * 10)) * 100)}%`, 'blue');
}

// Executar validações
async function main() {
  log('🔍 VALIDAÇÃO DO TEMPLATE APPWRITE', 'bold');
  log('Verificando se todos os dados mock foram removidos e configurações estão corretas...\n', 'blue');

  const allIssues = [
    ...checkMockDataUsage(),
    ...checkEnvironmentConfig(),
    ...checkLandingPageContent(),
    ...checkDocumentation(),
    ...checkTypeScriptErrors(),
    ...checkPackageJson()
  ];

  generateReport(allIssues);

  // Exit code para CI/CD
  process.exit(allIssues.length > 0 ? 1 : 0);
}

main().catch(error => {
  log(`❌ Erro durante validação: ${error.message}`, 'red');
  process.exit(1);
});
