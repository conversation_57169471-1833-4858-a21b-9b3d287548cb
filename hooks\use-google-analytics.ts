/**
 * Hook para Google Analytics 4
 * Fornece dados de analytics do Google Analytics para a aplicação
 */

import { useQuery } from '@tanstack/react-query';
import { useAuth } from './use-auth';
import { isGAConfigured } from '../lib/google-analytics';
import { fetchGoogleAnalyticsData, getGoogleAnalyticsConfig } from '../lib/google-analytics-api';

// Tipos para dados do Google Analytics
export interface GAMetrics {
  // Métricas básicas
  totalUsers: number;
  activeUsers: number;
  newUsers: number;
  sessions: number;
  pageviews: number;
  bounceRate: number;
  averageSessionDuration: number;

  // Métricas de engajamento
  engagementRate: number;
  engagedSessions: number;
  eventsPerSession: number;

  // Métricas de conversão
  conversions: number;
  conversionRate: number;

  // Dados temporais
  chartData: Array<{
    date: string;
    users: number;
    sessions: number;
    pageviews: number;
    events: number;
  }>;

  // Comparação com período anterior
  previousPeriod: {
    totalUsers: number;
    activeUsers: number;
    sessions: number;
    pageviews: number;
  };

  // Crescimento percentual
  growth: {
    users: number;
    sessions: number;
    pageviews: number;
    engagementRate: number;
  };
}

/**
 * REMOVIDO: generateMockGAData() - dados fictícios removidos
 * Agora usa apenas dados reais do Google Analytics via fetchGoogleAnalyticsData()
 */

/**
 * Hook principal para dados do Google Analytics
 */
export function useGoogleAnalytics() {
  const { user } = useAuth();
  const config = getGoogleAnalyticsConfig();

  return useQuery({
    queryKey: ['google-analytics', user?.$id, config.measurementId],
    queryFn: async () => {
      console.log('Buscando dados do Google Analytics...', config);

      // Usar a nova API que conecta com GA4 real
      return await fetchGoogleAnalyticsData();
    },
    enabled: !!user,
    staleTime: 1000 * 60 * 15, // 15 minutos
    refetchInterval: 1000 * 60 * 30, // Atualizar a cada 30 minutos
  });
}

/**
 * Hook para métricas específicas de usuários
 */
export function useUserMetrics() {
  const { data } = useGoogleAnalytics();

  return {
    totalUsers: data?.totalUsers || 0,
    activeUsers: data?.activeUsers || 0,
    newUsers: data?.newUsers || 0,
    userGrowth: data?.growth.users || 0,
  };
}

/**
 * Hook para métricas de sessões
 */
export function useSessionMetrics() {
  const { data } = useGoogleAnalytics();

  return {
    totalSessions: data?.sessions || 0,
    averageSessionDuration: data?.averageSessionDuration || 0,
    bounceRate: data?.bounceRate || 0,
    sessionGrowth: data?.growth.sessions || 0,
  };
}

/**
 * Hook para métricas de engajamento
 */
export function useEngagementMetrics() {
  const { data } = useGoogleAnalytics();

  return {
    engagementRate: data?.engagementRate || 0,
    engagedSessions: data?.engagedSessions || 0,
    eventsPerSession: data?.eventsPerSession || 0,
    pageviews: data?.pageviews || 0,
    pageviewGrowth: data?.growth.pageviews || 0,
  };
}

/**
 * Hook para dados de gráficos
 */
export function useAnalyticsChartData() {
  const { data } = useGoogleAnalytics();

  return {
    chartData: data?.chartData || [],
    isLoading: !data,
  };
}

/**
 * Hook para métricas de conversão
 */
export function useConversionMetrics() {
  const { data } = useGoogleAnalytics();

  return {
    conversions: data?.conversions || 0,
    conversionRate: data?.conversionRate || 0,
    totalSessions: data?.sessions || 0,
  };
}
