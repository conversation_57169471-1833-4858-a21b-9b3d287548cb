/**
 * Hook genérico para estratégia de cache simplificada
 * Implementa a nova estratégia: IndexedDB local-first sem cache de preferências
 */

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { isCacheEnabled } from '../lib/cache-config';
import {
  hasDataInIndexedDB,
  getFromIndexedDB,
  saveToIndexedDB,
  syncUpdatedDataFromServer
} from '../lib/cache-sync';

export interface SimplifiedCacheOptions<T> {
  queryKey: (string | undefined)[];
  collection: string;
  userId?: string;
  fetchFn: () => Promise<T[]>;
  enabled?: boolean;
  staleTime?: number;
  refetchOnWindowFocus?: boolean;
}

/**
 * Hook genérico que implementa a estratégia de cache simplificada
 */
export function useSimplifiedCache<T extends { $id: string; $updatedAt: string }>({
  queryKey,
  collection,
  userId,
  fetchFn,
  enabled = true,
  staleTime = 0,
  refetchOnWindowFocus = false
}: SimplifiedCacheOptions<T>) {
  const queryClient = useQueryClient();

  return useQuery({
    queryKey,
    queryFn: async () => {
      // Verificar se o cache está habilitado
      if (isCacheEnabled()) {
        // Verificar se IndexedDB tem dados
        const hasData = await hasDataInIndexedDB(collection, userId);

        if (hasData) {
          // Servir dados do IndexedDB e ignorar fetch
          const cachedData = await getFromIndexedDB<T>(collection, userId);
          console.log(`🗄️ Dados de ${collection} carregados do cache local`);

          // Sincronizar dados atualizados em background (não bloqueia o retorno)
          setTimeout(async () => {
            await syncUpdatedDataFromServer<T>(
              collection,
              userId,
              (activeData, deletedData) => {
                // Atualizar React Query cache com dados atualizados
                queryClient.setQueryData<T[]>(queryKey, (oldData) => {
                  if (!oldData) return activeData;

                  // Começar com dados existentes
                  let mergedData = [...oldData];

                  // Remover documentos que foram soft deleted
                  if (deletedData.length > 0) {
                    const deletedIds = deletedData.map(doc => doc.$id);
                    mergedData = mergedData.filter(item => !deletedIds.includes(item.$id));
                    console.log(`🗑️ Removidos ${deletedData.length} documentos deletados do cache React Query para ${collection}`);
                  }

                  // Merge dos dados ativos: substituir existentes e adicionar novos
                  activeData.forEach(updatedItem => {
                    const existingIndex = mergedData.findIndex(item => item.$id === updatedItem.$id);
                    if (existingIndex >= 0) {
                      mergedData[existingIndex] = updatedItem;
                    } else {
                      mergedData.push(updatedItem);
                    }
                  });

                  return mergedData;
                });
              }
            );
          }, 0);

          return cachedData;
        }
      }

      // Buscar do servidor (só se IndexedDB estiver vazio)
      console.log(`🌐 Buscando dados de ${collection} do servidor`);
      const serverData = await fetchFn();

      // Salvar no cache se habilitado
      if (isCacheEnabled() && serverData.length > 0) {
        await saveToIndexedDB(collection, serverData, {
          collection,
          userId
        });
      }

      return serverData;
    },
    enabled,
    staleTime: isCacheEnabled() ? staleTime : 5 * 60 * 1000,
    refetchOnWindowFocus,
  });
}

/**
 * Exemplo de uso do hook simplificado para clientes
 */
export function useClientsSimplified(userId?: string) {
  return useSimplifiedCache({
    queryKey: ['clients', userId],
    collection: 'clients',
    userId,
    fetchFn: async () => {
      const { clients } = await import('@/lib/appwrite/functions/database');
      const result = await clients.list();
      return result.documents as any[];
    },
    enabled: !!userId
  });
}

/**
 * Exemplo de uso do hook simplificado para eventos
 */
export function useEventsSimplified(userId?: string) {
  return useSimplifiedCache({
    queryKey: ['events', userId],
    collection: 'events',
    userId,
    fetchFn: async () => {
      // Implementar fetch de eventos aqui
      return [];
    },
    enabled: !!userId
  });
}

/**
 * Exemplo de uso do hook simplificado para notificações
 */
export function useNotificationsSimplified(userId?: string) {
  return useSimplifiedCache({
    queryKey: ['notifications', userId],
    collection: 'notifications',
    userId,
    fetchFn: async () => {
      // Implementar fetch de notificações aqui
      return [];
    },
    enabled: !!userId
  });
}
