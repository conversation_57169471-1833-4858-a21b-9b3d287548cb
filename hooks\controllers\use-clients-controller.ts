/**
 * Controller para Clientes
 * Usa subscribe do Valtio - sem useEffect, useState, nada
 */

import { useEffect, useRef } from 'react';
import { subscribe } from 'valtio';
import { useQueryClient } from '@tanstack/react-query';
import { realtimeStore } from '../../lib/realtime/store';
import { saveToIndexedDB } from '../../lib/cache-sync';

export function useClientsController() {
  const queryClient = useQueryClient();
  const unsubscribeRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    console.log('🎛️ Inicializando controller de clientes...');

    // Subscribe do Valtio - função nativa, sem useEffect
    unsubscribeRef.current = subscribe(realtimeStore.clients, () => {
      const clients = realtimeStore.clients;

      if (clients.length === 0) return;

      console.log(`📝 Processando ${clients.length} clientes do realtime...`);

      // Adicionar dados novos aos existentes no React Query
      clients.forEach(client => {
        console.log(`📝 Atualizando cliente: ${client.$id}`);

        queryClient.setQueryData(['clients', client.userId], (oldData: any) => {
          if (!oldData) return [client];

          const exists = oldData.find((item: any) => item.$id === client.$id);
          if (exists) {
            // Atualizar existente
            return oldData.map((item: any) =>
              item.$id === client.$id ? client : item
            );
          } else {
            // Adicionar novo
            return [...oldData, client];
          }
        });

        // Salvar no IndexedDB com tratamento de erro (sem await para não bloquear)
        saveToIndexedDB('clients', client, {
          collection: 'clients',
          userId: client.userId
        }).catch(error => {
          console.error(`❌ Erro ao salvar cliente ${client.$id} no IndexedDB:`, error);
          // Não interromper o fluxo, apenas logar o erro
        });
      });

  
    });

    return () => {
      console.log('🧹 Limpando controller de clientes...');
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    };
  }, [queryClient]);

  return {};
}
