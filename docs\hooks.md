# 🪝 Hooks Customizados

Este documento explica todos os hooks customizados do template, organizados por categoria e funcionalidade.

## 📋 Visão Geral

O template possui uma arquitetura de hooks bem estruturada:

- **API Hooks** - Integração com Appwrite + React Query
- **Auth Hooks** - Gerenciamento de autenticação
- **Real-time Hooks** - WebSocket e atualizações ao vivo
- **Cache Hooks** - Estratégia local-first
- **Utility Hooks** - Funcionalidades auxiliares

## 🏗️ Estrutura de Hooks

### Organização por Pasta
```
app/hooks/
├── api/                    # Hooks de API específicos
│   ├── use-clients.ts      # Gerenciamento de clientes
│   ├── use-teams.ts        # Sistema de equipes
│   ├── use-chat.ts         # Chat em tempo real
│   ├── use-notifications.ts # Notificações
│   └── use-activities.ts   # Logs de atividade
├── use-auth.ts             # Autenticação
├── use-websocket.ts        # WebSocket/Real-time
├── use-simplified-cache.ts # Cache local-first
└── use-api.ts              # Exportações centralizadas
```

## 🔐 Hooks de Autenticação

### useAuth
Hook principal para autenticação:

```typescript
import { useAuth } from '@/hooks/use-auth';

function MyComponent() {
  const {
    user,                    // Usuário atual
    isLoading,              // Estado de carregamento
    isAuthenticated,        // Status de autenticação
    login,                  // Função de login
    register,               // Função de registro
    logout,                 // Função de logout
    refreshUser,            // Atualizar dados do usuário
    updateProfile,          // Atualizar perfil
    deleteAccount           // Deletar conta
  } = useAuth();

  if (isLoading) return <Loading />;
  if (!isAuthenticated) return <LoginForm onSubmit={login} />;

  return <Dashboard user={user} onLogout={logout} />;
}
```

### useAuthActions
Hook para ações de autenticação:

```typescript
import { useAuthActions } from '@/hooks/use-auth';

function LoginForm() {
  const { login, register, logout } = useAuthActions();

  const handleLogin = async (credentials) => {
    try {
      await login(credentials);
      toast.success('Login realizado com sucesso!');
    } catch (error) {
      toast.error('Erro no login');
    }
  };
}
```

### useCurrentUser
Hook para obter usuário atual:

```typescript
import { useCurrentUser } from '@/hooks/use-auth';

function UserProfile() {
  const { user, isLoading } = useCurrentUser();

  if (isLoading) return <Skeleton />;
  if (!user) return <div>Usuário não encontrado</div>;

  return (
    <div>
      <h1>{user.name}</h1>
      <p>{user.email}</p>
    </div>
  );
}
```

## 👥 Hooks de Clientes

### useClients
Hook para listar clientes com cache local-first:

```typescript
import { useClients } from '@/hooks/api/use-clients';

function ClientsList() {
  const { data: clients, isLoading, error } = useClients();

  if (isLoading) return <Loading />;
  if (error) return <Error message={error.message} />;

  return (
    <div>
      {clients?.map(client => (
        <ClientCard key={client.$id} client={client} />
      ))}
    </div>
  );
}
```

### useClient
Hook para cliente específico:

```typescript
import { useClient } from '@/hooks/api/use-clients';

function ClientDetails({ clientId }: { clientId: string }) {
  const { data: client, isLoading } = useClient(clientId);

  if (isLoading) return <Skeleton />;
  if (!client) return <div>Cliente não encontrado</div>;

  return (
    <div>
      <h1>{client.name}</h1>
      <p>Status: {client.status}</p>
      <p>Prioridade: {client.priority}</p>
    </div>
  );
}
```

### useCreateClient
Hook para criar cliente:

```typescript
import { useCreateClient } from '@/hooks/api/use-clients';

function CreateClientForm() {
  const createClient = useCreateClient({
    onSuccess: (client) => {
      toast.success(`Cliente ${client.name} criado com sucesso!`);
      navigate(`/clients/${client.$id}`);
    },
    onError: (error) => {
      toast.error('Erro ao criar cliente');
    }
  });

  const handleSubmit = async (data: CreateClientData) => {
    await createClient.mutateAsync(data);
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* Formulário */}
      <Button
        type="submit"
        disabled={createClient.isPending}
      >
        {createClient.isPending ? 'Criando...' : 'Criar Cliente'}
      </Button>
    </form>
  );
}
```

### useUpdateClient
Hook para atualizar cliente:

```typescript
import { useUpdateClient } from '@/hooks/api/use-clients';

function EditClientForm({ client }: { client: Client }) {
  const updateClient = useUpdateClient({
    onSuccess: () => {
      toast.success('Cliente atualizado com sucesso!');
    }
  });

  const handleUpdate = async (data: UpdateClientData) => {
    await updateClient.mutateAsync({
      id: client.$id,
      data
    });
  };
}
```

## 🏢 Hooks de Teams

### useTeams
Hook para listar equipes:

```typescript
import { useTeams } from '@/hooks/api/use-teams';

function TeamsList() {
  const { data: teams, isLoading } = useTeams();

  return (
    <div>
      {teams?.map(team => (
        <TeamCard key={team.$id} team={team} />
      ))}
    </div>
  );
}
```

### useTeam
Hook para equipe específica:

```typescript
import { useTeam } from '@/hooks/api/use-teams';

function TeamDetails({ teamId }: { teamId: string }) {
  const { data: team, isLoading } = useTeam(teamId);

  if (isLoading) return <Skeleton />;

  return (
    <div>
      <h1>{team?.name}</h1>
      <p>Membros: {team?.total}</p>
    </div>
  );
}
```

### useTeamMembers
Hook para membros da equipe:

```typescript
import { useTeamMembers } from '@/hooks/api/use-teams';

function TeamMembers({ teamId }: { teamId: string }) {
  const { data: members, isLoading } = useTeamMembers(teamId);

  return (
    <div>
      <h2>Membros da Equipe</h2>
      {members?.map(member => (
        <MemberCard key={member.$id} member={member} />
      ))}
    </div>
  );
}
```

### useCreateTeam
Hook para criar equipe:

```typescript
import { useCreateTeam } from '@/hooks/api/use-teams';

function CreateTeamForm() {
  const createTeam = useCreateTeam({
    onSuccess: (team) => {
      toast.success(`Equipe ${team.name} criada!`);
    }
  });

  const handleSubmit = async (data: CreateTeamData) => {
    await createTeam.mutateAsync(data);
  };
}
```

## 💬 Hooks de Chat

### useChat
Hook principal para chat com mensagens:

```typescript
import { useChat } from '@/hooks/api/use-chat';

function ChatWindow({ chatId }: { chatId: string }) {
  const { data: chat, isLoading } = useChat(chatId);

  if (isLoading) return <Loading />;
  if (!chat) return <div>Chat não encontrado</div>;

  return (
    <div>
      <h1>{chat.name}</h1>
      <div className="messages">
        {chat.messages?.map(message => (
          <MessageBubble key={message.$id} message={message} />
        ))}
      </div>
    </div>
  );
}
```

### useChatRealtime
Hook para chat com real-time:

```typescript
import { useChatRealtime } from '@/hooks/api/use-chat';

function LiveChat({ teamId }: { teamId: string }) {
  const {
    messages,              // Mensagens em tempo real
    isConnected,          // Status da conexão
    sendMessage,          // Enviar mensagem
    sendTypingIndicator,  // Indicador de digitação
    typingUsers,          // Usuários digitando
    chatState             // Estado do chat
  } = useChatRealtime(teamId);

  const handleSendMessage = async (content: string) => {
    await sendMessage({
      content,
      chatId: chatState.currentChatId,
      teamId,
      type: 'text'
    });
  };

  return (
    <div>
      <div className="connection-status">
        {isConnected ? '🟢 Online' : '🔴 Offline'}
      </div>

      <div className="messages">
        {messages.map(message => (
          <MessageBubble key={message.$id} message={message} />
        ))}
      </div>

      {typingUsers.length > 0 && (
        <div className="typing-indicator">
          {typingUsers.join(', ')} está digitando...
        </div>
      )}

      <ChatInput onSend={handleSendMessage} />
    </div>
  );
}
```

### useSendMessage
Hook para enviar mensagens:

```typescript
import { useSendMessage } from '@/hooks/api/use-chat';

function MessageInput({ chatId }: { chatId: string }) {
  const [message, setMessage] = useState('');
  const sendMessage = useSendMessage({
    onSuccess: () => {
      setMessage('');
      toast.success('Mensagem enviada!');
    }
  });

  const handleSend = async () => {
    if (!message.trim()) return;

    await sendMessage.mutateAsync({
      content: message.trim(),
      chatId,
      type: 'text'
    });
  };
}
```

## 🔔 Hooks de Notificações

### useNotifications
Hook para listar notificações:

```typescript
import { useNotifications } from '@/hooks/api/use-notifications';

function NotificationCenter() {
  const { data: notifications, isLoading } = useNotifications();

  return (
    <div>
      <h2>Notificações</h2>
      {notifications?.map(notification => (
        <NotificationCard
          key={notification.$id}
          notification={notification}
        />
      ))}
    </div>
  );
}
```

### useUnreadNotificationCount
Hook para contador de não lidas:

```typescript
import { useUnreadNotificationCount } from '@/hooks/use-notifications-compat';

function NotificationBadge() {
  const unreadCount = useUnreadNotificationCount();

  if (unreadCount === 0) return null;

  return (
    <Badge variant="destructive">
      {unreadCount > 99 ? '99+' : unreadCount}
    </Badge>
  );
}
```

## ⚡ Hooks de Real-time

### useRealtime
Hook principal para Appwrite Realtime (sistema automático):

```typescript
import { useRealtime } from '@/hooks/use-realtime';

function RealtimeComponent() {
  const { isConnected, status, error } = useRealtime();

  // O sistema de realtime é automático via RealtimeProvider
  // Não é necessário configurar subscriptions manualmente

  return (
    <div>
      Status: {isConnected ? 'Conectado' : 'Desconectado'}
      {error && <p>Erro: {error}</p>}
    </div>
  );
}
```

### useRealtimeStatus
Hook para monitorar status do sistema de realtime:

```typescript
import { useRealtimeStatus } from '@/hooks/use-realtime';

function RealtimeMonitor() {
  const { isConnected, modules, getModuleStatus } = useRealtimeStatus();

  return (
    <div>
      <p>Conexão: {isConnected ? 'Ativa' : 'Inativa'}</p>
      <p>Módulos ativos: {modules.filter(m => getModuleStatus(m)).length}</p>
    </div>
  );
}
```

## 🗂️ Hooks de Cache

### useSimplifiedCache
Hook para estratégia local-first:

```typescript
import { useSimplifiedCache } from '@/hooks/use-simplified-cache';

function CachedDataComponent() {
  const { data, isLoading, error } = useSimplifiedCache({
    queryKey: ['my-data', userId],
    collection: 'my_collection',
    userId,
    fetchFn: () => fetchDataFromServer(),
    enabled: !!userId
  });

  // Dados servidos do IndexedDB primeiro,
  // depois sincronizados com servidor
}
```

## 📊 Hooks de Atividades

### useActivityLogger
Hook para registrar atividades:

```typescript
import { useActivityLogger } from '@/hooks/api/use-activities';

function ClientActions({ client }: { client: Client }) {
  const { logActivity } = useActivityLogger();

  const handleEdit = async () => {
    // Editar cliente...

    // Registrar atividade
    await logActivity({
      type: 'client',
      action: 'update',
      description: `Cliente ${client.name} foi atualizado`,
      metadata: { clientId: client.$id }
    });
  };
}
```

## 🛠️ Padrões de Uso

### 1. **Composição de Hooks**
```typescript
function ClientManagement() {
  const { user } = useAuth();
  const { data: clients } = useClients();
  const createClient = useCreateClient();
  const { isConnected } = useRealtimeStatus();

  // Combinar múltiplos hooks para funcionalidade completa
}
```

### 2. **Error Handling**
```typescript
function DataComponent() {
  const { data, isLoading, error } = useClients();

  if (isLoading) return <Loading />;
  if (error) return <ErrorBoundary error={error} />;

  return <DataDisplay data={data} />;
}
```

### 3. **Conditional Queries**
```typescript
function ConditionalData({ userId }: { userId?: string }) {
  const { data } = useClients({
    enabled: !!userId // Só executa se userId existir
  });
}
```

### 4. **Optimistic Updates**
```typescript
function OptimisticComponent() {
  const updateClient = useUpdateClient({
    onMutate: async (variables) => {
      // Update otimista na UI
      queryClient.setQueryData(['clients'], (old) =>
        old.map(client =>
          client.$id === variables.id
            ? { ...client, ...variables.data }
            : client
        )
      );
    }
  });
}
```

## ✅ Boas Práticas

### 1. **Nomenclatura Consistente**
```typescript
// ✅ Bom - nomes descritivos
const { data: clients, isLoading: clientsLoading } = useClients();

// ❌ Ruim - nomes genéricos
const { data, isLoading } = useClients();
```

### 2. **Dependency Arrays**
```typescript
// ✅ Bom - dependências explícitas
useEffect(() => {
  if (userId) {
    fetchData(userId);
  }
}, [userId]);

// ❌ Ruim - dependências faltando
useEffect(() => {
  fetchData(userId);
}, []);
```

### 3. **Error Boundaries**
```typescript
// ✅ Bom - tratamento de erro
function ComponentWithErrorHandling() {
  const { data, error } = useClients();

  if (error) {
    return <ErrorFallback error={error} />;
  }

  return <DataDisplay data={data} />;
}
```

---

**📖 Próximos Passos:**
- [types.md](./types.md) - Tipos usados nos hooks
- [cache.md](./cache.md) - Sistema de cache dos hooks
- [realtime.md](./realtime.md) - Real-time nos hooks
