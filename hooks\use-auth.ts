import { useAuth as useAuthContext } from '../contexts/auth-context';

/**
 * Custom hook for authentication
 * Re-exports the auth context hook for easier importing
 */
export const useAuth = useAuthContext;

/**
 * Hook to check if user is authenticated
 */
export function useIsAuthenticated() {
  const { isAuthenticated, isLoading } = useAuth();
  return { isAuthenticated, isLoading };
}

/**
 * Hook to get current user
 */
export function useCurrentUser() {
  const { user, isLoading } = useAuth();
  return { user, isLoading };
}

/**
 * Hook for authentication actions
 */
export function useAuthActions() {
  const { login, register, logout, refreshUser } = useAuth();
  return { login, register, logout, refreshUser };
}
