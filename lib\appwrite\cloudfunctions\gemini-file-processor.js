/**
 * Gemini File Processor Cloud Function
 * 
 * Processa arquivos de clientes (imagens, documentos) usando Google Gemini AI
 * e retorna dados estruturados/formatados.
 * 
 * Configuração de Permissões:
 * - Execute: ["users"] - Usuários autenticados podem usar
 * - Timeout: 60 segundos (processamento de arquivos pode demorar)
 * 
 * Validação Necessária:
 * - Verificar se usuário está autenticado
 * - Validar tipo de arquivo suportado
 * - Verificar tamanho do arquivo
 * - Rate limiting por usuário
 * 
 * Variáveis de Ambiente Necessárias:
 * - GEMINI_API_KEY: Chave da API do Google AI
 * - GEMINI_MODEL: Modelo a usar (padrão: gemini-1.5-flash)
 * - MAX_FILE_SIZE: Tamanho máximo do arquivo em bytes
 * - ALLOWED_MIME_TYPES: Tipos MIME permitidos
 */

import { GoogleGenerativeAI } from '@google/generative-ai';
import { Client, Databases, Storage, Users } from 'node-appwrite';

// Configuração do Gemini
const GEMINI_MODEL = process.env.GEMINI_MODEL || 'gemini-1.5-flash';
const MAX_FILE_SIZE = parseInt(process.env.MAX_FILE_SIZE || '10485760'); // 10MB padrão
const ALLOWED_MIME_TYPES = (process.env.ALLOWED_MIME_TYPES || 'image/jpeg,image/png,image/webp,application/pdf').split(',');

// Configuração do Appwrite
const client = new Client();
client
  .setEndpoint(process.env.APPWRITE_FUNCTION_ENDPOINT || 'https://cloud.appwrite.io/v1')
  .setProject(process.env.APPWRITE_FUNCTION_PROJECT_ID || '')
  .setKey(process.env.APPWRITE_FUNCTION_API_KEY || '');

const databases = new Databases(client);
const storage = new Storage(client);
const users = new Users(client);

/**
 * Valida se as variáveis de ambiente necessárias estão configuradas
 */
function validateEnvironment() {
  const required = ['GEMINI_API_KEY', 'APPWRITE_FUNCTION_PROJECT_ID'];
  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
}

/**
 * Valida o arquivo enviado
 */
function validateFile(file) {
  if (!file) {
    throw new Error('No file provided');
  }

  if (file.size > MAX_FILE_SIZE) {
    throw new Error(`File too large. Maximum size: ${MAX_FILE_SIZE} bytes`);
  }

  if (!ALLOWED_MIME_TYPES.includes(file.mimeType)) {
    throw new Error(`Unsupported file type. Allowed: ${ALLOWED_MIME_TYPES.join(', ')}`);
  }
}

/**
 * Converte arquivo para formato compatível com Gemini
 */
async function prepareFileForGemini(fileBuffer, mimeType) {
  return {
    inlineData: {
      data: fileBuffer.toString('base64'),
      mimeType: mimeType
    }
  };
}

/**
 * Processa arquivo com Gemini AI
 */
async function processWithGemini(fileData, prompt, mimeType) {
  try {
    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    const model = genAI.getGenerativeModel({ model: GEMINI_MODEL });

    const result = await model.generateContent([
      prompt,
      fileData
    ]);

    const response = await result.response;
    return response.text();
  } catch (error) {
    console.error('Gemini processing error:', error);
    throw new Error(`AI processing failed: ${error.message}`);
  }
}

/**
 * Prompts pré-definidos para diferentes tipos de processamento
 */
const PROCESSING_PROMPTS = {
  client_data: `
    Analise este documento/imagem e extraia as seguintes informações do cliente em formato JSON:
    {
      "name": "Nome completo do cliente",
      "email": "Email se disponível",
      "phone": "Telefone se disponível", 
      "address": "Endereço completo se disponível",
      "company": "Nome da empresa se aplicável",
      "document_type": "Tipo de documento (RG, CPF, CNPJ, etc.)",
      "document_number": "Número do documento se visível",
      "additional_info": "Outras informações relevantes"
    }
    
    Se alguma informação não estiver disponível, use null. Retorne apenas o JSON válido.
  `,
  
  invoice_data: `
    Analise esta fatura/nota fiscal e extraia as informações em formato JSON:
    {
      "invoice_number": "Número da fatura",
      "date": "Data da fatura",
      "due_date": "Data de vencimento",
      "total_amount": "Valor total",
      "currency": "Moeda",
      "items": [
        {
          "description": "Descrição do item",
          "quantity": "Quantidade",
          "unit_price": "Preço unitário",
          "total": "Total do item"
        }
      ],
      "client_info": {
        "name": "Nome do cliente",
        "address": "Endereço do cliente"
      }
    }
    
    Retorne apenas o JSON válido.
  `,
  
  general: `
    Analise este arquivo e extraia todas as informações relevantes de forma estruturada.
    Organize os dados em formato JSON com campos apropriados baseados no conteúdo encontrado.
    Seja preciso e detalhado. Retorne apenas o JSON válido.
  `
};

export default async ({ req, res, log, error }) => {
  try {
    // Validar ambiente
    validateEnvironment();

    // Verificar método HTTP
    if (req.method !== 'POST') {
      return res.json({ 
        success: false, 
        error: 'Method not allowed. Use POST.' 
      }, 405);
    }

    // Verificar autenticação
    const userId = req.headers['x-appwrite-user-id'];
    if (!userId) {
      return res.json({ 
        success: false, 
        error: 'Authentication required' 
      }, 401);
    }

    // Validar dados de entrada
    const { fileId, bucketId, processingType = 'general', customPrompt } = req.bodyJson;
    
    if (!fileId || !bucketId) {
      return res.json({ 
        success: false, 
        error: 'fileId and bucketId are required' 
      }, 400);
    }

    log(`Processing file ${fileId} for user ${userId}`);

    // Buscar arquivo no storage
    const file = await storage.getFile(bucketId, fileId);
    validateFile(file);

    // Baixar conteúdo do arquivo
    const fileBuffer = await storage.getFileDownload(bucketId, fileId);
    
    // Preparar arquivo para Gemini
    const fileData = await prepareFileForGemini(fileBuffer, file.mimeType);
    
    // Selecionar prompt
    const prompt = customPrompt || PROCESSING_PROMPTS[processingType] || PROCESSING_PROMPTS.general;
    
    // Processar com Gemini
    const result = await processWithGemini(fileData, prompt, file.mimeType);
    
    // Tentar parsear como JSON
    let parsedResult;
    try {
      parsedResult = JSON.parse(result);
    } catch (parseError) {
      // Se não for JSON válido, retornar como texto
      parsedResult = { raw_text: result };
    }

    log(`File processed successfully for user ${userId}`);

    return res.json({
      success: true,
      data: {
        file_id: fileId,
        processing_type: processingType,
        processed_data: parsedResult,
        file_info: {
          name: file.name,
          size: file.size,
          mimeType: file.mimeType
        },
        processed_at: new Date().toISOString()
      }
    }, 200);

  } catch (err) {
    error(`Gemini processing error: ${err.message}`);
    
    return res.json({
      success: false,
      error: err.message || 'Failed to process file',
      code: 'PROCESSING_ERROR'
    }, 500);
  }
};
