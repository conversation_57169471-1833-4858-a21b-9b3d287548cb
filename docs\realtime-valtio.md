# 📡 Sistema de Realtime com Valtio

Sistema simples e eficiente de realtime usando Valtio para gerenciar estado fora do ciclo de vida do React.

## 🏗️ Arquitetura

### Componentes Principais

1. **RealtimeStore** (`app/lib/realtime/index.ts`)
   - Store Valtio que gerencia estado global do realtime
   - Conexão única com Appwrite
   - Registro de módulos por collection

2. **Inicialização** (`app/lib/realtime/realtime-init.ts`)
   - Configuração de todos os módulos
   - Handlers para cada collection
   - Sincronização com IndexedDB e toasts

3. **Hooks Simples** (`app/hooks/use-realtime.ts`)
   - `useRealtimeStatus()` - Status da conexão
   - `useRealtimeConnection()` - Conecta/desconecta baseado no usuário
   - `useRealtimeCollection()` - Escuta eventos de uma collection
   - `useRealtimeQuerySync()` - Sincroniza React Query automaticamente

## 🚀 Como Usar

### 1. Configuração Inicial

O sistema é inicializado automaticamente no `RealtimeProvider`:

```tsx
// app/root.tsx ou layout principal
import { RealtimeProvider } from '@/components/realtime/realtime-provider';

export default function App() {
  return (
    <RealtimeProvider>
      {/* Sua aplicação */}
    </RealtimeProvider>
  );
}
```

### 2. Verificar Status

```tsx
import { useRealtimeStatus } from '@/hooks/use-realtime';

function StatusComponent() {
  const { isConnected, isConnecting, error, status } = useRealtimeStatus();

  return (
    <div>
      Status: {status} {/* 'connected' | 'connecting' | 'disconnected' */}
      {error && <span>Erro: {error}</span>}
    </div>
  );
}
```

### 3. Escutar Eventos de uma Collection

```tsx
import { useRealtimeCollection } from '@/hooks/use-realtime';

function ClientsList() {
  const { lastEvent } = useRealtimeCollection('clients', (event) => {
    console.log('Evento de cliente:', event);

    if (event.action === 'create') {
      toast.success(`Novo cliente: ${event.payload.name}`);
    }
  });

  return <div>Lista de clientes...</div>;
}
```

### 4. Usar Store Diretamente

```tsx
import { useSnapshot } from 'valtio';
import { realtimeStore } from '@/lib/realtime/store';

function DirectStoreUsage() {
  const snap = useSnapshot(realtimeStore);

  return (
    <div>
      <div>Conectado: {snap.isConnected ? 'Sim' : 'Não'}</div>
      <div>Último evento: {snap.lastEvent?.collection}</div>
      <div>Total de eventos: {snap.events.length}</div>
    </div>
  );
}
```

### 5. Subscribe Manual do Valtio

```tsx
import { useEffect } from 'react';
import { subscribe } from 'valtio';
import { realtimeStore } from '@/lib/realtime/store';

function ManualSubscribe() {
  useEffect(() => {
    const unsubscribe = subscribe(realtimeStore, () => {
      const event = realtimeStore.lastEvent;
      if (event?.collection === 'clients') {
        // Fazer algo específico para clientes
      }
    });

    return unsubscribe;
  }, []);

  return <div>Componente com subscribe manual</div>;
}
```

## 🔧 Configuração de Módulos

### Adicionar Nova Collection

1. **Registrar módulo** em `realtime-init.ts`:

```typescript
function setupNovaCollectionModule() {
  realtimeManager.registerModule('nova_collection', true, async (event) => {
    try {
      const item = event.payload;

      switch (event.action) {
        case 'create':
          await saveToIndexedDB('nova_collection', item, {
            collection: 'nova_collection',
            userId: item.userId
          });
          toast.success(`Novo item: ${item.name}`);
          break;

        case 'update':
          await saveToIndexedDB('nova_collection', item, {
            collection: 'nova_collection',
            userId: item.userId
          });
          break;

        case 'delete':
          await removeFromIndexedDB('nova_collection', item.$id);
          break;
      }
    } catch (error) {
      console.error('❌ Erro no módulo nova_collection:', error);
    }
  });
}
```

2. **Adicionar ao inicializador**:

```typescript
export function initializeRealtime() {
  // ... outros módulos
  setupNovaCollectionModule();
}
```

3. **Adicionar invalidação de queries** em `use-realtime.ts`:

```typescript
case 'nova_collection':
  queryClient.invalidateQueries({ queryKey: ['nova-collection'] });
  break;
```

## 📊 Debug e Monitoramento

### Hook de Debug

```tsx
import { useRealtimeDebug } from '@/hooks/use-realtime';

function DebugPanel() {
  const debug = useRealtimeDebug();

  return (
    <div>
      <div>Total de módulos: {debug.totalModules}</div>
      <div>Módulos ativos: {debug.activeModules}</div>
      <div>Eventos recentes: {debug.recentEvents.length}</div>

      {debug.recentEvents.map((event, i) => (
        <div key={i}>
          {event.action} em {event.collection}
        </div>
      ))}
    </div>
  );
}
```

### Componente de Status (Desenvolvimento)

```tsx
import { RealtimeStatus } from '@/components/realtime/realtime-provider';

// Adicionar em desenvolvimento
{import.meta.env.DEV && <RealtimeStatus />}
```

## 🔄 Fluxo de Dados

1. **Evento Appwrite** → Store Valtio atualizado
2. **Store Valtio** → Hooks React re-renderizam via `useSnapshot`
3. **Módulo específico** → IndexedDB atualizado + Toast mostrado
4. **Query Sync** → React Query invalidado automaticamente

## ✅ Vantagens

- **Simples**: Menos de 200 linhas de código total
- **Performático**: Valtio evita re-renders desnecessários
- **Modular**: Fácil adicionar/remover collections
- **Type-safe**: TypeScript em todos os pontos
- **Automático**: Sincronização transparente
- **Debug**: Ferramentas de debug integradas

## 🔧 Troubleshooting

### Realtime não conecta
- Verificar variáveis de ambiente do Appwrite
- Verificar se usuário está logado
- Verificar console para erros

### Eventos não chegam
- Verificar se módulo está registrado
- Verificar se collection existe no COLLECTIONS
- Verificar permissões no Appwrite

### Performance
- Store mantém apenas últimos 100 eventos
- Use `useRealtimeCollection` para escutar collections específicas
- Evite usar `useSnapshot` em componentes que re-renderizam muito
