/**
 * Gerenciador de Cargos e Permissões do Time
 * Permite criar, editar e gerenciar cargos customizados
 */

import { useState } from 'react';
import { Plus, Edit, Trash2, Users, Crown, Shield, Settings, User, Eye } from 'lucide-react';
import { <PERSON><PERSON> } from '../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../ui/table';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '../ui/alert-dialog';
import { useTeamContext } from '../../contexts/team-context';
import { useCanManagePermissions, useDeleteTeamRole } from '../../hooks/api/use-permissions';
import { TeamRoleFormModal } from './team-role-form-modal';
import { MemberRoleAssignModal } from './member-role-assign-modal';
import type { TeamRole, UserType } from '@/schemas/permissions';
import type { TeamPermissionPreferences } from '@/schemas/permissions';

interface TeamRolesManagerProps {
  teamId: string;
  teamPreferences: TeamPermissionPreferences;
}

export function TeamRolesManager({ teamId, teamPreferences }: TeamRolesManagerProps) {
  const [selectedRole, setSelectedRole] = useState<TeamRole | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);
  const [roleToDelete, setRoleToDelete] = useState<TeamRole | null>(null);

  const { teamMembers } = useTeamContext();
  const canManage = useCanManagePermissions();
  const deleteRoleMutation = useDeleteTeamRole();

  const roles = teamPreferences.roles || [];
  const memberRoles = teamPreferences.memberRoles || [];

  // Contar membros por cargo
  const getMemberCount = (roleId: string) => {
    return memberRoles.filter(mr => mr.roleId === roleId).length;
  };

  // Obter ícone do tipo de usuário
  const getUserTypeIcon = (userType: UserType) => {
    switch (userType) {
      case 'owner': return Crown;
      case 'admin': return Shield;
      case 'user': return User;
      case 'guest': return Eye;
      default: return Users;
    }
  };

  // Obter cor do badge baseado no tipo
  const getBadgeVariant = (userType: UserType) => {
    switch (userType) {
      case 'owner': return 'destructive';
      case 'admin': return 'default';
      case 'user': return 'secondary';
      case 'guest': return 'outline';
      default: return 'secondary';
    }
  };

  const handleEditRole = (role: TeamRole) => {
    setSelectedRole(role);
    setIsEditModalOpen(true);
  };

  const handleDeleteRole = (role: TeamRole) => {
    setRoleToDelete(role);
  };

  const confirmDeleteRole = async () => {
    if (!roleToDelete) return;

    try {
      await deleteRoleMutation.mutateAsync(roleToDelete.id);
      setRoleToDelete(null);
    } catch (error) {
      // Error já é tratado no hook
    }
  };

  const handleAssignRoles = () => {
    setIsAssignModalOpen(true);
  };

  if (!canManage) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Cargos e Permissões
          </CardTitle>
          <CardDescription>
            Apenas proprietários podem gerenciar cargos e permissões.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Cargos e Permissões
              </CardTitle>
              <CardDescription>
                Gerencie cargos customizados e suas permissões no sistema.
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button
                onClick={handleAssignRoles}
                variant="outline"
                size="sm"
              >
                <Users className="h-4 w-4 mr-2" />
                Atribuir Cargos
              </Button>
              <Button
                onClick={() => setIsCreateModalOpen(true)}
                size="sm"
              >
                <Plus className="h-4 w-4 mr-2" />
                Novo Cargo
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {roles.length === 0 ? (
            <div className="text-center py-8">
              <Shield className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">Nenhum cargo configurado</h3>
              <p className="text-muted-foreground mb-4">
                Crie cargos customizados para organizar as permissões da sua equipe.
              </p>
              <Button onClick={() => setIsCreateModalOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Criar primeiro cargo
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Cargo</TableHead>
                  <TableHead>Tipo</TableHead>
                  <TableHead>Membros</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {roles.map((role) => {
                  const Icon = getUserTypeIcon(role.userType);
                  const memberCount = getMemberCount(role.id);

                  return (
                    <TableRow key={role.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: role.color }}
                          />
                          <div>
                            <div className="font-medium">{role.name}</div>
                            {role.description && (
                              <div className="text-sm text-muted-foreground">
                                {role.description}
                              </div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getBadgeVariant(role.userType)}>
                          <Icon className="w-3 h-3 mr-1" />
                          {role.userType === 'owner' ? 'Proprietário' :
                           role.userType === 'admin' ? 'Administrador' :
                           role.userType === 'user' ? 'Usuário' : 'Convidado'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm">
                          {memberCount} {memberCount === 1 ? 'membro' : 'membros'}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Badge variant={role.isActive ? 'default' : 'secondary'}>
                            {role.isActive ? 'Ativo' : 'Inativo'}
                          </Badge>
                          {role.isDefault && (
                            <Badge variant="outline">Padrão</Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditRole(role)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteRole(role)}
                            disabled={memberCount > 0}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Modals */}
      <TeamRoleFormModal
        open={isCreateModalOpen}
        onOpenChange={setIsCreateModalOpen}
        mode="create"
        teamId={teamId}
      />

      <TeamRoleFormModal
        open={isEditModalOpen}
        onOpenChange={setIsEditModalOpen}
        mode="edit"
        teamId={teamId}
        role={selectedRole}
        onClose={() => setSelectedRole(null)}
      />

      <MemberRoleAssignModal
        open={isAssignModalOpen}
        onOpenChange={setIsAssignModalOpen}
        teamId={teamId}
        roles={roles}
        memberRoles={memberRoles}
        members={teamMembers}
      />

      {/* Confirmação de exclusão */}
      <AlertDialog open={!!roleToDelete} onOpenChange={() => setRoleToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Excluir cargo</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir o cargo "{roleToDelete?.name}"?
              Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteRole}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Excluir
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
