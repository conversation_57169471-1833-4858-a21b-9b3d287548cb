'use client';

/**
 * Componente de Debug para Realtime
 * Mostra status detalhado da conexão e configuração
 */

import { useAuth } from '../../hooks/use-auth';
import { useRealtimeStatus } from '../../hooks/use-realtime-status';
import { useSnapshot } from 'valtio';
import { realtimeStore } from '../../lib/realtime/store';
import { getAppwriteConfig } from '../../lib/appwrite/config';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { connect, disconnect } from '../../lib/realtime/realtime';
import { useCreateClient } from '../../hooks/api/use-clients';
import { toast } from 'sonner';

export function RealtimeDebug() {
  const { user, isLoading: authLoading } = useAuth();
  const { isConnected, isConnecting, error } = useRealtimeStatus();
  const storeSnapshot = useSnapshot(realtimeStore);
  const appwriteConfig = getAppwriteConfig();
  const createClientMutation = useCreateClient();

  const handleConnect = async () => {
    try {
      await connect();
    } catch (error) {
      console.error('Erro ao conectar:', error);
    }
  };

  const handleDisconnect = () => {
    disconnect();
  };

  const handleTestClient = async () => {
    if (!user) {
      toast.error('Usuário não logado');
      return;
    }

    try {
      const testClient = {
        type: 'pessoa_fisica' as const,
        name: `Cliente Teste ${Date.now()}`,
        userId: user?.$id || '',
        status: 'ativo' as const,
        priority: 'media' as const,
        tags: [],
        email: `teste${Date.now()}@example.com`,
        phone: '+55 11 99999-9999',
        company: 'Empresa Teste',
        notes: 'Cliente criado para teste do realtime',
      };

      await createClientMutation.mutateAsync(testClient);
      toast.success('Cliente teste criado! Verifique se aparece em tempo real.');
    } catch (error) {
      console.error('Erro ao criar cliente teste:', error);
      toast.error('Erro ao criar cliente teste');
    }
  };

  return (
    <div className="space-y-4 p-4">
      <Card>
        <CardHeader>
          <CardTitle>🔧 Debug: Sistema Realtime</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Status da Autenticação */}
          <div>
            <h3 className="font-semibold mb-2">👤 Autenticação</h3>
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <span>Carregando:</span>
                <Badge variant={authLoading ? 'secondary' : 'outline'}>
                  {authLoading ? 'Sim' : 'Não'}
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <span>Usuário:</span>
                <Badge variant={user ? 'default' : 'destructive'}>
                  {user ? `${user.name} (${user.$id})` : 'Não logado'}
                </Badge>
              </div>
            </div>
          </div>

          {/* Status da Conexão Realtime */}
          <div>
            <h3 className="font-semibold mb-2">📡 Conexão Realtime</h3>
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <span>Conectado:</span>
                <Badge variant={isConnected ? 'default' : 'destructive'}>
                  {isConnected ? 'Sim' : 'Não'}
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <span>Conectando:</span>
                <Badge variant={isConnecting ? 'secondary' : 'outline'}>
                  {isConnecting ? 'Sim' : 'Não'}
                </Badge>
              </div>
              {error && (
                <div className="flex items-center gap-2">
                  <span>Erro:</span>
                  <Badge variant="destructive">{error}</Badge>
                </div>
              )}
            </div>
          </div>

          {/* Configuração Appwrite */}
          <div>
            <h3 className="font-semibold mb-2">⚙️ Configuração Appwrite</h3>
            <div className="space-y-1 text-sm">
              <div>Endpoint: {appwriteConfig.endpoint || '❌ Não configurado'}</div>
              <div>Project ID: {appwriteConfig.projectId || '❌ Não configurado'}</div>
              <div>Database ID: {appwriteConfig.databaseId || '❌ Não configurado'}</div>
              <div className="flex items-center gap-2">
                <span>Status:</span>
                <Badge variant={appwriteConfig.isConfigured ? 'default' : 'destructive'}>
                  {appwriteConfig.isConfigured ? 'Configurado' : 'Incompleto'}
                </Badge>
              </div>
            </div>
          </div>

          {/* Store Valtio */}
          <div>
            <h3 className="font-semibold mb-2">🗄️ Store Valtio</h3>
            <div className="space-y-1 text-sm">
              <div>Clientes: {storeSnapshot.clients.length}</div>
              <div>Notificações: {storeSnapshot.notifications.length}</div>
              <div>Chats: {storeSnapshot.chats.length}</div>
              <div>Mensagens: {storeSnapshot.messages.length}</div>
            </div>
            {storeSnapshot.clients.length > 0 && (
              <div className="mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 rounded text-xs">
                <strong>Clientes pendentes:</strong>
                {storeSnapshot.clients.map(client => (
                  <div key={client.$id}>{client.name} ({client.$id})</div>
                ))}
              </div>
            )}
            {storeSnapshot.messages.length > 0 && (
              <div className="mt-2 p-2 bg-green-50 dark:bg-green-900/20 rounded text-xs">
                <strong>Mensagens pendentes:</strong>
                {storeSnapshot.messages.map(message => (
                  <div key={message.$id}>{message.content?.substring(0, 30)}... ({message.$id})</div>
                ))}
              </div>
            )}
          </div>

          {/* Controles */}
          <div className="flex gap-2 flex-wrap">
            <Button
              onClick={handleConnect}
              disabled={isConnected || isConnecting}
              size="sm"
            >
              Conectar
            </Button>
            <Button
              onClick={handleDisconnect}
              disabled={!isConnected}
              variant="outline"
              size="sm"
            >
              Desconectar
            </Button>
            <Button
              onClick={handleTestClient}
              disabled={!user || createClientMutation.isPending}
              variant="secondary"
              size="sm"
            >
              {createClientMutation.isPending ? 'Criando...' : 'Teste Cliente'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
