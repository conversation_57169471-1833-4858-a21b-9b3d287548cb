/**
 * Controller para Storage/Files
 * Files são gerenciados pelo Appwrite Storage service (não collection)
 * Este controller serve para invalidar queries quando necessário
 */

import { useEffect, useRef } from 'react';
import { subscribe } from 'valtio';
import { useQueryClient } from '@tanstack/react-query';
import { realtimeStore } from '../../lib/realtime/store';

export function useStorageController() {
  const queryClient = useQueryClient();
  const unsubscribeRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    console.log('🎛️ Inicializando controller de storage...');

    // Subscribe do Valtio - função nativa, sem useEffect
    unsubscribeRef.current = subscribe(realtimeStore.files, () => {
      const files = realtimeStore.files;

      if (files.length === 0) return;
      
      console.log(`📝 Processando ${files.length} files do realtime...`);

      // Para files, apenas invalidamos queries já que são gerenciados pelo Appwrite Storage
      files.forEach(file => {
        console.log(`📝 Invalidando queries para file: ${file.$id}`);

        // Invalidar queries relacionadas a files
        queryClient.invalidateQueries({ queryKey: ['files'] });
        queryClient.invalidateQueries({ queryKey: ['file', file.$id] });
        
        // Invalidar queries que podem usar files
        queryClient.invalidateQueries({ queryKey: ['clients'] }); // Se clientes têm avatars
        queryClient.invalidateQueries({ queryKey: ['publicProfiles'] }); // Se perfis têm avatars
      });


    });

    return () => {
      console.log('🧹 Limpando controller de storage...');
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    };
  }, [queryClient]);

  return {};
}
