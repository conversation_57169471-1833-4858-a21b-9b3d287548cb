'use client';

/**
 * Componente de input com suporte a menções (@)
 */

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Textarea } from '../ui/textarea';
import { Button } from '../ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '../ui/popover';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { ArrowUpIcon, Smile, Loader2 } from 'lucide-react';
import { cn } from '../../lib/utils';
import { extractMentions } from '../../lib/chat-utils';
import { EmojiPicker } from './emoji-picker';

interface User {
  $id: string;
  name: string;
  email?: string;
  prefs?: {
    avatar?: string;
  };
}

interface MentionInputProps {
  value: string;
  onChange: (value: string, mentions: string[]) => void;
  onSend?: () => void;
  onKeyDown?: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
  onEmojiSelect?: (emoji: string) => void;
  placeholder?: string;
  disabled?: boolean;
  isLoading?: boolean;
  className?: string;
  users?: User[];
  minHeight?: number;
  maxHeight?: number;
  showSendButton?: boolean;
  showEmojiPicker?: boolean;
}

export function MentionInput({
  value,
  onChange,
  onSend,
  onKeyDown,
  onEmojiSelect,
  placeholder = "Digite sua mensagem...",
  disabled = false,
  isLoading = false,
  className,
  users = [],
  minHeight = 60,
  maxHeight = 200,
  showSendButton = true,
  showEmojiPicker = true,
}: MentionInputProps) {
  const [showMentions, setShowMentions] = useState(false);
  const [mentionQuery, setMentionQuery] = useState('');
  const [mentionPosition, setMentionPosition] = useState({ start: 0, end: 0 });
  const [selectedMentionIndex, setSelectedMentionIndex] = useState(0);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(Math.max(textarea.scrollHeight, minHeight), maxHeight)}px`;
    }
  }, [value, minHeight, maxHeight]);

  // Filtrar usuários baseado na query de menção
  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(mentionQuery.toLowerCase())
  ).slice(0, 5); // Limitar a 5 resultados

  // Detectar menções no texto
  const detectMention = useCallback((text: string, cursorPosition: number) => {
    const beforeCursor = text.substring(0, cursorPosition);
    const mentionMatch = beforeCursor.match(/@(\w*)$/);

    if (mentionMatch) {
      const start = beforeCursor.lastIndexOf('@');
      const query = mentionMatch[1];

      setMentionQuery(query);
      setMentionPosition({ start, end: cursorPosition });
      setShowMentions(true);
      setSelectedMentionIndex(0);
    } else {
      setShowMentions(false);
      setMentionQuery('');
    }
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    const cursorPosition = e.target.selectionStart;

    // Detectar menções
    detectMention(newValue, cursorPosition);

    // Extrair menções do texto completo
    const mentions = extractMentions(newValue);
    const mentionIds = mentions.map(mention => {
      const user = users.find(u => u.name.toLowerCase() === mention.toLowerCase());
      return user?.$id;
    }).filter(Boolean) as string[];

    onChange(newValue, mentionIds);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (showMentions && filteredUsers.length > 0) {
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        setSelectedMentionIndex(prev =>
          prev < filteredUsers.length - 1 ? prev + 1 : 0
        );
        return;
      }

      if (e.key === 'ArrowUp') {
        e.preventDefault();
        setSelectedMentionIndex(prev =>
          prev > 0 ? prev - 1 : filteredUsers.length - 1
        );
        return;
      }

      if (e.key === 'Enter' || e.key === 'Tab') {
        e.preventDefault();
        selectMention(filteredUsers[selectedMentionIndex]);
        return;
      }

      if (e.key === 'Escape') {
        e.preventDefault();
        setShowMentions(false);
        return;
      }
    }

    onKeyDown?.(e);
  };

  const selectMention = (user: User) => {
    const beforeMention = value.substring(0, mentionPosition.start);
    const afterMention = value.substring(mentionPosition.end);
    const newValue = `${beforeMention}@${user.name} ${afterMention}`;

    // Extrair menções do novo texto
    const mentions = extractMentions(newValue);
    const mentionIds = mentions.map(mention => {
      const foundUser = users.find(u => u.name.toLowerCase() === mention.toLowerCase());
      return foundUser?.$id;
    }).filter(Boolean) as string[];

    onChange(newValue, mentionIds);
    setShowMentions(false);

    // Focar no textarea e posicionar cursor
    setTimeout(() => {
      if (textareaRef.current) {
        const newCursorPosition = beforeMention.length + user.name.length + 2; // +2 para @ e espaço
        textareaRef.current.focus();
        textareaRef.current.setSelectionRange(newCursorPosition, newCursorPosition);
      }
    }, 0);
  };

  const handleEmojiSelect = useCallback((emoji: string) => {
    if (onEmojiSelect) {
      onEmojiSelect(emoji);
    }
  }, [onEmojiSelect]);

  const handleSend = useCallback(() => {
    if (onSend && value.trim() && !disabled && !isLoading) {
      onSend();
    }
  }, [onSend, value, disabled, isLoading]);

  const canSend = value.trim().length > 0 && !disabled && !isLoading;

  return (
    <div className="relative">
      {/* Input Container */}
      <div className="relative bg-muted/50 rounded-xl border border-border/30">
        {/* Textarea */}
        <div className="overflow-y-auto">
          <Textarea
            ref={textareaRef}
            value={value}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={disabled}
            className={cn(
              "w-full px-4 py-3",
              "resize-none",
              "bg-transparent",
              "border-none",
              "text-foreground text-sm",
              "focus:outline-none",
              "focus-visible:ring-0 focus-visible:ring-offset-0",
              "placeholder:text-muted-foreground/60 placeholder:text-sm",
              className
            )}
            style={{
              overflow: "hidden",
              minHeight: `${minHeight}px`,
              maxHeight: `${maxHeight}px`,
            }}
          />
        </div>

        {/* Bottom Actions Bar */}
        <div className="flex items-center justify-between p-3">
          {/* Left side - Emoji Picker */}
          <div className="flex items-center gap-2">
            {showEmojiPicker && (
              <EmojiPicker
                onEmojiSelect={handleEmojiSelect}
                disabled={disabled}
              />
            )}
          </div>

          {/* Right side - Send Button */}
          <div className="flex items-center gap-2">
            {showSendButton && (
              <button
                type="button"
                onClick={handleSend}
                disabled={!canSend}
                className={cn(
                  "px-1.5 py-1.5 rounded-lg text-sm transition-colors border flex items-center justify-center",
                  canSend
                    ? "bg-primary text-primary-foreground border-primary hover:bg-primary/90"
                    : "text-muted-foreground border-border hover:border-border/80"
                )}
              >
                {isLoading ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <ArrowUpIcon className="w-4 h-4" />
                )}
                <span className="sr-only">Enviar</span>
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Popover de menções */}
      {showMentions && filteredUsers.length > 0 && (
        <div className="absolute bottom-full left-0 right-0 mb-2 z-50">
          <div className="bg-popover border rounded-md shadow-md p-1 max-h-40 overflow-y-auto">
            {filteredUsers.map((user, index) => (
              <div
                key={user.$id}
                className={cn(
                  "flex items-center gap-2 p-2 rounded cursor-pointer transition-colors",
                  index === selectedMentionIndex
                    ? "bg-accent text-accent-foreground"
                    : "hover:bg-accent/50"
                )}
                onClick={() => selectMention(user)}
              >
                <Avatar className="h-6 w-6">
                  <AvatarImage src={user.prefs?.avatar} />
                  <AvatarFallback className="text-xs">
                    {user.name.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>

                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium truncate">
                    {user.name}
                  </div>
                  {user.email && (
                    <div className="text-xs text-muted-foreground truncate">
                      {user.email}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
