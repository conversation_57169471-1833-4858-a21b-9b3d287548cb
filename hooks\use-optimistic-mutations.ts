/**
 * Hooks otimizados para mutações com cache inteligente e WebSocket real-time
 * Implementa optimistic updates e sincronização automática
 */

import { useMutation, useQueryClient } from '@tanstack/react-query';
import type { Models } from 'appwrite';
import { toast } from 'sonner';
import type { BaseDocument } from '@/schemas/database';
import { errorHandler } from '../lib/error-handler';
import { log } from '../lib/logger';
// Simple query keys factory to replace databaseKeys
const createQueryKeys = (collectionId: string) => ({
  documents: (collectionId: string) => ['database', 'collections', collectionId, 'documents'] as const,
  document: (collectionId: string, documentId: string) => ['database', 'collections', collectionId, 'documents', documentId] as const,
});
// Removido: import { useWebSocket } from './use-websocket';
// Removido: import type { WebSocketMessage } from '@/schemas/websocket';

// Type for paginated document list response
interface DocumentListResponse<T extends BaseDocument> {
  documents: T[];
  total: number;
}

export interface OptimisticMutationOptions<TData, TVariables> {
  collectionId: string;
  onSuccess?: (data: TData, variables: TVariables) => void;
  onError?: (error: Error, variables: TVariables) => void;
  showSuccessToast?: boolean;
  showErrorToast?: boolean;
  successMessage?: string;
  optimisticUpdate?: boolean;
  // Removido: realtimeSync - agora gerenciado pelo sistema de realtime automático
}

/**
 * Hook otimizado para criar documentos
 */
export function useOptimisticCreate<T extends BaseDocument = BaseDocument>(
  mutationFn: (data: Omit<T, keyof Models.Document>) => Promise<T>,
  options: OptimisticMutationOptions<T, Omit<T, keyof Models.Document>>
) {
  const queryClient = useQueryClient();
  // Removido: const { subscribe } = useWebSocket(); - agora usa sistema de realtime automático
  const queryKeys = createQueryKeys(options.collectionId);

  return useMutation({
    mutationFn: errorHandler.withErrorHandling(mutationFn, {
      operation: 'create',
      collection: options.collectionId,
    }),

    onMutate: async (newData) => {
      if (!options.optimisticUpdate) return;

      // Cancelar queries em andamento
      await queryClient.cancelQueries({
        queryKey: queryKeys.documents(options.collectionId),
      });

      // Snapshot do estado anterior
      const previousData = queryClient.getQueryData(
        queryKeys.documents(options.collectionId)
      );

      // Optimistic update
      const optimisticDocument = {
        ...newData,
        $id: `temp-${Date.now()}`,
        $collectionId: options.collectionId,
        $databaseId: process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
        $createdAt: new Date().toISOString(),
        $updatedAt: new Date().toISOString(),
        $permissions: [],
      } as unknown as T;

      queryClient.setQueryData(
        queryKeys.documents(options.collectionId),
        (old: DocumentListResponse<T> | undefined) => {
          if (!old) return { documents: [optimisticDocument], total: 1 };
          return {
            ...old,
            documents: [optimisticDocument, ...old.documents],
            total: old.total + 1,
          };
        }
      );

      log.database(`Optimistic create: ${optimisticDocument.$id}`, {
        operation: 'optimistic_create',
        collection: options.collectionId,
        documentId: optimisticDocument.$id
      });

      return { previousData, optimisticDocument };
    },

    onSuccess: (data, variables, context) => {
      // Substituir documento temporário pelo real
      if (options.optimisticUpdate && context?.optimisticDocument) {
        queryClient.setQueryData(
          queryKeys.documents(options.collectionId),
          (old: DocumentListResponse<T> | undefined) => {
            if (!old) return { documents: [data], total: 1 };
            return {
              ...old,
              documents: old.documents.map((doc: T) =>
                doc.$id === context.optimisticDocument.$id ? data : doc
              ),
            };
          }
        );
      } else {
        // Invalidar queries se não houver optimistic update
        queryClient.invalidateQueries({
          queryKey: queryKeys.documents(options.collectionId),
        });
      }

      // Adicionar documento individual ao cache
      queryClient.setQueryData(
        queryKeys.document(options.collectionId, data.$id),
        data
      );

      if (options.showSuccessToast) {
        toast.success(options.successMessage || 'Item criado com sucesso!');
      }

      log.success(`Document created successfully: ${data.$id}`, {
        operation: 'create_success',
        collection: options.collectionId,
        documentId: data.$id
      });
      options.onSuccess?.(data, variables);
    },

    onError: (error, variables, context) => {
      // Reverter optimistic update
      if (options.optimisticUpdate && context?.previousData) {
        queryClient.setQueryData(
          queryKeys.documents(options.collectionId),
          context.previousData
        );
      }

      log.error('Create mutation failed', error as Error, {
        operation: 'create_failed',
        collection: options.collectionId,
        variables,
      });

      options.onError?.(error as Error, variables);
    },

    onSettled: () => {
      // Sincronização realtime agora é automática via RealtimeProvider
      // Não é necessário configurar manualmente
    },
  });
}

/**
 * Hook otimizado para atualizar documentos
 */
export function useOptimisticUpdate<T extends BaseDocument = BaseDocument>(
  mutationFn: (data: { documentId: string; data: Partial<Omit<T, keyof Models.Document>> }) => Promise<T>,
  options: OptimisticMutationOptions<T, { documentId: string; data: Partial<Omit<T, keyof Models.Document>> }>
) {
  const queryClient = useQueryClient();
  // Removido: const { subscribe } = useWebSocket(); - agora usa sistema de realtime automático
  const queryKeys = createQueryKeys(options.collectionId);

  return useMutation({
    mutationFn: errorHandler.withErrorHandling(mutationFn, {
      operation: 'update',
      collection: options.collectionId,
    }),

    onMutate: async (variables) => {
      if (!options.optimisticUpdate) return;

      const { documentId, data } = variables;

      // Cancelar queries em andamento
      await queryClient.cancelQueries({
        queryKey: queryKeys.document(options.collectionId, documentId),
      });

      // Snapshot do estado anterior
      const previousDocument = queryClient.getQueryData(
        queryKeys.document(options.collectionId, documentId)
      );

      // Optimistic update do documento individual
      queryClient.setQueryData(
        queryKeys.document(options.collectionId, documentId),
        (old: T | undefined) => {
          if (!old) return old;
          return {
            ...old,
            ...data,
            $updatedAt: new Date().toISOString(),
          };
        }
      );

      // Optimistic update da lista de documentos
      queryClient.setQueryData(
        queryKeys.documents(options.collectionId),
        (old: DocumentListResponse<T> | undefined) => {
          if (!old) return old;
          return {
            ...old,
            documents: old.documents.map((doc: T) =>
              doc.$id === documentId
                ? { ...doc, ...data, $updatedAt: new Date().toISOString() }
                : doc
            ),
          };
        }
      );

      log.database(`Optimistic update: ${documentId}`, {
        operation: 'optimistic_update',
        collection: options.collectionId,
        documentId
      });

      return { previousDocument, documentId };
    },

    onSuccess: (data, variables) => {
      // Atualizar com dados reais do servidor
      queryClient.setQueryData(
        queryKeys.document(options.collectionId, data.$id),
        data
      );

      // Atualizar na lista também
      queryClient.setQueryData(
        queryKeys.documents(options.collectionId),
        (old: DocumentListResponse<T> | undefined) => {
          if (!old) return old;
          return {
            ...old,
            documents: old.documents.map((doc: T) =>
              doc.$id === data.$id ? data : doc
            ),
          };
        }
      );

      if (options.showSuccessToast) {
        toast.success(options.successMessage || 'Item atualizado com sucesso!');
      }

      log.success(`Document updated successfully: ${data.$id}`, {
        operation: 'update_success',
        collection: options.collectionId,
        documentId: data.$id
      });
      options.onSuccess?.(data, variables);
    },

    onError: (error, variables, context) => {
      // Reverter optimistic update
      if (options.optimisticUpdate && context?.previousDocument) {
        queryClient.setQueryData(
          queryKeys.document(options.collectionId, context.documentId),
          context.previousDocument
        );

        // Reverter na lista também
        queryClient.setQueryData(
          queryKeys.documents(options.collectionId),
          (old: DocumentListResponse<T> | undefined) => {
            if (!old) return old;
            return {
              ...old,
              documents: old.documents.map((doc: T) =>
                doc.$id === context.documentId ? context.previousDocument : doc
              ),
            };
          }
        );
      }

      log.error('Update mutation failed', error as Error, {
        operation: 'update_failed',
        collection: options.collectionId,
        documentId: variables.documentId,
      });

      options.onError?.(error as Error, variables);
    },

    onSettled: () => {
      // Sincronização realtime agora é automática via RealtimeProvider
      // Não é necessário configurar manualmente
    },
  });
}

/**
 * Hook otimizado para deletar documentos
 */
export function useOptimisticDelete(
  mutationFn: (documentId: string) => Promise<void>,
  options: OptimisticMutationOptions<void, string>
) {
  const queryClient = useQueryClient();
  // Removido: const { subscribe } = useWebSocket(); - agora usa sistema de realtime automático
  const queryKeys = createQueryKeys(options.collectionId);

  return useMutation({
    mutationFn: errorHandler.withErrorHandling(mutationFn, {
      operation: 'delete',
      collection: options.collectionId,
    }),

    onMutate: async (documentId) => {
      if (!options.optimisticUpdate) return;

      // Cancelar queries em andamento
      await queryClient.cancelQueries({
        queryKey: queryKeys.documents(options.collectionId),
      });

      // Snapshot do estado anterior
      const previousData = queryClient.getQueryData(
        queryKeys.documents(options.collectionId)
      );

      const previousDocument = queryClient.getQueryData(
        queryKeys.document(options.collectionId, documentId)
      );

      // Optimistic update - remover da lista
      queryClient.setQueryData(
        queryKeys.documents(options.collectionId),
        (old: DocumentListResponse<BaseDocument> | undefined) => {
          if (!old) return old;
          return {
            ...old,
            documents: old.documents.filter((doc: BaseDocument) => doc.$id !== documentId),
            total: Math.max(0, old.total - 1),
          };
        }
      );

      // Remover documento individual do cache
      queryClient.removeQueries({
        queryKey: queryKeys.document(options.collectionId, documentId),
      });

      log.database(`Optimistic delete: ${documentId}`, {
        operation: 'optimistic_delete',
        collection: options.collectionId,
        documentId
      });

      return { previousData, previousDocument, documentId };
    },

    onSuccess: (data, documentId) => {
      if (options.showSuccessToast) {
        toast.success(options.successMessage || 'Item removido com sucesso!');
      }

      log.success(`Document deleted successfully: ${documentId}`, {
        operation: 'delete_success',
        collection: options.collectionId,
        documentId
      });
      options.onSuccess?.(data, documentId);
    },

    onError: (error, documentId, context) => {
      // Reverter optimistic update
      if (options.optimisticUpdate && context?.previousData) {
        queryClient.setQueryData(
          queryKeys.documents(options.collectionId),
          context.previousData
        );

        // Restaurar documento individual se existia
        if (context.previousDocument) {
          queryClient.setQueryData(
            queryKeys.document(options.collectionId, documentId),
            context.previousDocument
          );
        }
      }

      log.error('Delete mutation failed', error as Error, {
        operation: 'delete_failed',
        collection: options.collectionId,
        documentId,
      });

      options.onError?.(error as Error, documentId);
    },

    onSettled: () => {
      // Sincronização realtime agora é automática via RealtimeProvider
      // Não é necessário configurar manualmente
    },
  });
}
