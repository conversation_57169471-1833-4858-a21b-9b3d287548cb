import type { Models } from 'appwrite';

/**
 * Team-related type definitions for Appwrite Teams (native)
 * Uses Appwrite's built-in Teams service
 */

// Appwrite native team types
export interface Team extends Models.Team<Models.Preferences> {
  // Appwrite Teams já tem: $id, $createdAt, $updatedAt, name, total
  // Podemos adicionar preferências customizadas via prefs
}

export interface TeamMembership extends Models.Membership {
  // Appwrite Membership já tem: $id, $createdAt, $updatedAt, userId, userName, userEmail, teamId, teamName, invited, joined, confirm, roles
}

// Roles específicas para o sistema
export type TeamRole = 'owner' | 'admin' | 'member';

// Ações disponíveis nos times
export type TeamAction =
  | 'update_team'
  | 'delete_team'
  | 'inNEXT_PUBLIC_member'
  | 'remove_member'
  | 'update_member_role'
  | 'update_own_profile'
  | 'view_team'
  | 'view_members';

// Tipos para criação e atualização
export interface CreateTeamData {
  teamId?: string; // opcional, será gerado se não fornecido
  name: string;
  roles?: string[]; // roles customizadas
}

export interface UpdateTeamData {
  name?: string;
}

// Preferências estendidas do team (armazenadas em team preferences)
export interface TeamPreferences {
  // Configurações básicas
  description?: string;
  color?: string;
  timezone?: string;

  // Configurações de notificação
  notifyOnMention?: boolean;
  notifyOnMessage?: boolean;
  notifyOnJoin?: boolean;
  emailNotifications?: boolean;

  // Configurações de chat
  allowGifs?: boolean;
  allowEmojis?: boolean;
  allowFileUploads?: boolean;

  // Configurações de privacidade
  isPublic?: boolean;
  allowInvites?: boolean;
  requireApproval?: boolean;

  // Sistema de permissões e cargos customizáveis
  roles?: import('./permissions').TeamRole[];
  memberRoles?: import('./permissions').TeamMemberRole[];
  permissionsVersion?: string;
  lastPermissionUpdate?: string;

  // Metadados
  updatedAt?: string;
}

export interface InviteMemberData {
  email: string;
  roles: string[];
  url?: string; // URL de redirecionamento após aceitar convite
  name?: string; // Nome do membro convidado
}

export interface UpdateMembershipData {
  roles: string[];
}

// Helper types for API responses (usando tipos nativos do Appwrite)
export interface TeamsListResponse {
  teams: Team[];
  total: number;
}

export interface MembershipsListResponse {
  memberships: TeamMembership[];
  total: number;
}
