/**
 * Gemini Chat Integration
 * Integração local com Google Gemini AI para chat contextual
 */

import { GoogleGenerativeAI } from '@google/generative-ai';

// Configurações do Gemini
const GEMINI_API_KEY = process.env.NEXT_PUBLIC_GEMINI_API_KEY || '';
const GEMINI_MODEL = process.env.NEXT_PUBLIC_GEMINI_MODEL || 'gemini-1.5-flash';
const GEMINI_TEMPERATURE = parseFloat(process.env.NEXT_PUBLIC_GEMINI_TEMPERATURE || '0.7');
const GEMINI_MAX_TOKENS = parseInt(process.env.NEXT_PUBLIC_GEMINI_MAX_TOKENS || '2048');

// Inicializar Gemini AI
let genAI: GoogleGenerativeAI | null = null;

function initializeGemini() {
  if (!GEMINI_API_KEY) {
    throw new Error('NEXT_PUBLIC_GEMINI_API_KEY não configurada. Adicione sua chave do Google AI no arquivo .env');
  }
  
  if (!genAI) {
    genAI = new GoogleGenerativeAI(GEMINI_API_KEY);
  }
  
  return genAI;
}

export interface GeminiChatOptions {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  includeContext?: boolean;
  context?: string;
  conversationHistory?: Array<{
    role: 'user' | 'assistant';
    content: string;
  }>;
}

export interface GeminiResponse {
  content: string;
  model: string;
  tokens?: number;
  processingTime: number;
  finishReason?: string;
}

/**
 * Enviar mensagem para o Gemini
 */
export async function sendMessageToGemini(
  message: string,
  options: GeminiChatOptions = {}
): Promise<GeminiResponse> {
  const startTime = Date.now();
  
  try {
    const ai = initializeGemini();
    const model = ai.getGenerativeModel({
      model: options.model || GEMINI_MODEL,
      generationConfig: {
        temperature: options.temperature || GEMINI_TEMPERATURE,
        maxOutputTokens: options.maxTokens || GEMINI_MAX_TOKENS,
      },
    });

    // Construir prompt com contexto e histórico
    let fullPrompt = '';
    
    // Adicionar contexto do projeto se solicitado
    if (options.includeContext && options.context) {
      fullPrompt += `CONTEXTO DO PROJETO:\n${options.context}\n\n`;
    }
    
    // Adicionar histórico da conversa
    if (options.conversationHistory && options.conversationHistory.length > 0) {
      fullPrompt += 'HISTÓRICO DA CONVERSA:\n';
      options.conversationHistory.slice(-5).forEach(msg => {
        fullPrompt += `${msg.role === 'user' ? 'Usuário' : 'Assistente'}: ${msg.content}\n`;
      });
      fullPrompt += '\n';
    }
    
    // Adicionar mensagem atual
    fullPrompt += `PERGUNTA ATUAL:\n${message}`;

    // Gerar resposta
    const result = await model.generateContent(fullPrompt);
    const response = await result.response;
    const text = response.text();
    
    const processingTime = Date.now() - startTime;
    
    return {
      content: text,
      model: options.model || GEMINI_MODEL,
      processingTime,
      finishReason: response.candidates?.[0]?.finishReason,
    };
    
  } catch (error) {
    console.error('Erro ao comunicar com Gemini:', error);
    
    // Tratamento de erros específicos
    if (error instanceof Error) {
      if (error.message.includes('API_KEY')) {
        throw new Error('Chave da API do Gemini inválida. Verifique sua configuração.');
      }
      if (error.message.includes('QUOTA')) {
        throw new Error('Cota da API do Gemini excedida. Tente novamente mais tarde.');
      }
      if (error.message.includes('SAFETY')) {
        throw new Error('Conteúdo bloqueado por políticas de segurança do Gemini.');
      }
    }
    
    throw new Error(`Erro na comunicação com Gemini: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
  }
}

/**
 * Verificar se o Gemini está configurado
 */
export function isGeminiConfigured(): boolean {
  return !!GEMINI_API_KEY;
}

/**
 * Obter configurações atuais do Gemini
 */
export function getGeminiConfig() {
  return {
    apiKey: GEMINI_API_KEY ? '***' + GEMINI_API_KEY.slice(-4) : 'Não configurada',
    model: GEMINI_MODEL,
    temperature: GEMINI_TEMPERATURE,
    maxTokens: GEMINI_MAX_TOKENS,
    isConfigured: isGeminiConfigured(),
  };
}

/**
 * Testar conexão com Gemini
 */
export async function testGeminiConnection(): Promise<boolean> {
  try {
    await sendMessageToGemini('Teste de conexão. Responda apenas "OK".', {
      includeContext: false,
      maxTokens: 10,
    });
    return true;
  } catch (error) {
    console.error('Teste de conexão com Gemini falhou:', error);
    return false;
  }
}
