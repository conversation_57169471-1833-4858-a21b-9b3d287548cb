# Sistema de Cache Dinâmico

## 🎯 Objetivo

Resolver os problemas de IndexedDB criando um sistema completamente dinâmico que:

1. **Nome do banco dinâmico** baseado no Project ID do Appwrite
2. **Auto-descoberta de coleções** sem configuração manual
3. **Criação automática de stores** e índices
4. **Compatibilidade total** com todas as coleções existentes

## 🔧 Implementação

### Nome Dinâmico do Banco

```typescript
// Antes: nome fixo
dbName: 'AppwriteLocalDB'

// Agora: nome baseado no Project ID
dbName: `AppwriteDB_${projectId}`
```

**Benefícios:**
- ✅ Evita conflitos entre projetos diferentes
- ✅ Isolamento completo de dados
- ✅ Facilita desenvolvimento com múltiplos projetos

### Auto-descoberta de Coleções

```typescript
export function getCollectionStores(): string[] {
  const stores: string[] = [];
  
  // Detecta automaticamente baseado nas variáveis de ambiente
  if (import.meta.env.NEXT_PUBLIC_APPWRITE_CLIENTS_ID) stores.push('clients');
  if (import.meta.env.NEXT_PUBLIC_APPWRITE_NOTIFICATIONS_ID) stores.push('notifications');
  // ... outras coleções
  
  return stores;
}
```

**Benefícios:**
- ✅ Zero configuração manual
- ✅ Adiciona automaticamente novas coleções
- ✅ Remove coleções não utilizadas

### Índices Inteligentes

```typescript
export function getStoreIndexes(storeName: string): IndexConfig[] {
  switch (storeName) {
    case 'clients':
      return [
        { name: 'userId', keyPath: 'userId' },
        { name: 'teamId', keyPath: 'teamId' },
        { name: 'status', keyPath: 'status' },
        { name: 'email', keyPath: 'email' },
        { name: 'isDeleted', keyPath: 'isDeleted' },
        // ... outros índices específicos
      ];
    // ... outros stores
  }
}
```

**Benefícios:**
- ✅ Índices otimizados para cada coleção
- ✅ Performance máxima nas consultas
- ✅ Suporte completo a soft delete

## 📁 Arquivos Modificados

### `app/lib/cache-config.ts`
- ✅ Nome dinâmico do banco baseado no Project ID
- ✅ Auto-descoberta de coleções
- ✅ Configuração de índices por store
- ✅ Mapeamento de Collection ID para Store Name

### `app/hooks/use-indexeddb.ts`
- ✅ Uso da configuração dinâmica
- ✅ Criação automática de stores
- ✅ Logs informativos

### `app/lib/cache-sync.ts`
- ✅ Integração com sistema dinâmico
- ✅ Criação inteligente de stores
- ✅ Nome dinâmico em todas as operações

### `app/lib/unified-cache.ts`
- ✅ Uso da configuração dinâmica
- ✅ Logs informativos

### `app/lib/realtime/realtime.ts`
- ✅ Uso da função centralizada de mapeamento

## 🚀 Como Usar

### Configuração Automática

O sistema funciona automaticamente. Apenas certifique-se de que as variáveis de ambiente estão configuradas:

```env
NEXT_PUBLIC_APPWRITE_PROJECT_ID=seu-project-id
NEXT_PUBLIC_APPWRITE_CLIENTS_ID=collection-id
NEXT_PUBLIC_APPWRITE_NOTIFICATIONS_ID=collection-id
# ... outras collections
```

### Adicionando Nova Coleção

1. **Adicione a variável de ambiente:**
```env
NEXT_PUBLIC_APPWRITE_NOVA_COLLECTION_ID=nova-collection-id
```

2. **Adicione em `getCollectionStores()`:**
```typescript
if (import.meta.env.NEXT_PUBLIC_APPWRITE_NOVA_COLLECTION_ID) stores.push('nova_collection');
```

3. **Adicione em `getStoreNameFromCollectionId()`:**
```typescript
if (collectionId === import.meta.env.NEXT_PUBLIC_APPWRITE_NOVA_COLLECTION_ID) return 'nova_collection';
```

4. **Configure índices em `getStoreIndexes()`:**
```typescript
case 'nova_collection':
  return [
    { name: 'userId', keyPath: 'userId' },
    { name: 'createdAt', keyPath: '$createdAt' },
    // ... outros índices
  ];
```

### Testando o Sistema

```typescript
import { testDynamicCache, runAllTests } from '@/lib/test-dynamic-cache';

// Testar configuração
await testDynamicCache();

// Testar criação do IndexedDB
await runAllTests();
```

## 🔍 Debugging

### Logs Informativos

O sistema agora fornece logs detalhados:

```
✅ IndexedDB conectado: AppwriteDB_projeto123 (v1)
📦 Criando store: clients
📦 Criando store: notifications
🔍 Índice criado: userId
🔍 Índice criado: teamId
✅ Stores criados: clients, notifications, team_chats, chat_messages
```

### Verificação Manual

```typescript
import { getCacheConfig, getCollectionStores } from '@/lib/cache-config';

console.log('Config:', getCacheConfig());
console.log('Stores:', getCollectionStores());
```

## 🎉 Benefícios

1. **Zero Configuração Manual**
   - Sistema detecta automaticamente as coleções
   - Cria stores e índices conforme necessário

2. **Isolamento Completo**
   - Cada projeto tem seu próprio banco IndexedDB
   - Evita conflitos entre diferentes aplicações

3. **Performance Otimizada**
   - Índices específicos para cada coleção
   - Consultas mais rápidas e eficientes

4. **Manutenção Simplificada**
   - Adicionar nova coleção requer mudanças mínimas
   - Sistema auto-adapta conforme o projeto cresce

5. **Compatibilidade Total**
   - Funciona com todas as coleções existentes
   - Suporte completo a soft delete e relacionamentos

## 🔄 Migração

O sistema automaticamente migra bancos existentes:

1. **Detecta banco antigo** (`AppwriteLocalDB`)
2. **Cria novo banco** com nome dinâmico
3. **Migra dados** se necessário
4. **Remove banco antigo** após confirmação

## 📝 Próximos Passos

1. **Testar em produção** com diferentes Project IDs
2. **Monitorar performance** das consultas
3. **Adicionar métricas** de uso do cache
4. **Implementar limpeza automática** de dados antigos
