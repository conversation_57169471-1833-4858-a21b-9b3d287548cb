import { BellIcon, X, Info, CheckCircle, AlertTriangle, AlertCircle } from "lucide-react"
import { Badge } from "./ui/badge"
import { Button } from "./ui/button"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "./ui/popover"
import { useNotifications } from "../hooks/use-api"
import { cn } from "../lib/utils"
import Link from 'next/link'

function getNotificationIcon(type: string) {
  switch (type) {
    case 'success':
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    case 'warning':
      return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    case 'error':
      return <AlertCircle className="h-4 w-4 text-red-500" />;
    default:
      return <Info className="h-4 w-4 text-blue-500" />;
  }
}

function formatTimestamp(timestamp: string) {
  const date = new Date(timestamp);
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) return 'Agora mesmo';
  if (diffInMinutes < 60) return `${diffInMinutes} min atrás`;

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `${diffInHours}h atrás`;

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) return `${diffInDays}d atrás`;

  return date.toLocaleDateString('pt-BR');
}

export default function NotificationComponent() {
  const {
    notifications,
    unreadCount,
    isLoading,
    markAsRead,
    markAllAsRead,
    removeNotification
  } = useNotifications();

  if (isLoading) {
    return (
      <Button
        size="icon"
        variant="outline"
        disabled
        aria-label="Carregando notificações"
      >
        <BellIcon size={16} aria-hidden="true" />
      </Button>
    );
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          size="icon"
          variant="outline"
          className="relative"
          aria-label="Abrir notificações"
        >
          <BellIcon size={16} aria-hidden="true" />
          {unreadCount > 0 && (
            <Badge className="absolute -top-2 left-full min-w-5 -translate-x-1/2 px-1">
              {unreadCount > 99 ? "99+" : unreadCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-1">
        <div className="flex items-baseline justify-between gap-4 px-3 py-2">
          <div className="text-sm font-semibold">Notificações</div>
          {unreadCount > 0 && (
            <button
              className="text-xs font-medium hover:underline"
              onClick={markAllAsRead}
            >
              Marcar todas como lidas
            </button>
          )}
        </div>
        <div
          role="separator"
          aria-orientation="horizontal"
          className="bg-border -mx-1 my-1 h-px"
        />

        {notifications.length === 0 ? (
          <div className="px-3 py-8 text-center text-sm text-muted-foreground">
            Nenhuma notificação
          </div>
        ) : (
          notifications.slice(0, 5).map((notification: any) => (
            <div
              key={notification.id}
              className={cn(
                "hover:bg-accent rounded-md px-3 py-2 text-sm transition-colors",
                !notification.read && "bg-muted/50"
              )}
            >
              <div className="relative flex items-start gap-3 pe-8">
                <div className="mt-1">
                  {getNotificationIcon(notification.type)}
                </div>
                <div className="flex-1 space-y-1">
                  <button
                    className="text-foreground/80 text-left w-full"
                    onClick={() => markAsRead(notification.id)}
                  >
                    <div className="font-medium text-foreground">
                      {notification.title}
                    </div>
                    <div className="text-muted-foreground text-xs mt-1">
                      {notification.message}
                    </div>
                  </button>
                  <div className="text-muted-foreground text-xs">
                    {formatTimestamp(notification.timestamp)}
                  </div>
                </div>

                <div className="absolute top-1 right-0 flex items-center gap-1">
                  {!notification.read && (
                    <div className="h-2 w-2 bg-blue-500 rounded-full" />
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 hover:bg-destructive/10"
                    onClick={() => removeNotification(notification.id)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </div>
          ))
        )}

        {notifications.length > 0 && (
          <>
            <div
              role="separator"
              aria-orientation="horizontal"
              className="bg-border -mx-1 my-1 h-px"
            />
            <div className="px-3 py-2">
              <Button
                asChild
                variant="ghost"
                size="sm"
                className="w-full justify-center text-xs"
              >
                <Link href="/dashboard/notifications">
                  Ver todas as notificações
                </Link>
              </Button>
            </div>
          </>
        )}
      </PopoverContent>
    </Popover>
  );
}
