/**
 * Activity List Component
 * Displays a list of activities with filtering and search
 */

import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import {
  IconUser,
  IconUsers,
  IconMessage,
  IconFile,
  IconSettings,
  IconBell,
  IconCalendar,
  IconFolder,
  IconShield,
  IconSettings2,
  IconLogin,
  IconLogout,
  IconPlus,
  IconEdit,
  IconTrash,
  IconEye,
  IconUpload,
  IconDownload,
  IconShare,
  IconArchive,
  IconRestore,
  IconSend,
  IconCheck,
  IconX,
  IconFileExport,
  IconFileImport,
} from '@tabler/icons-react';
import { Badge } from '../ui/badge';
import { Card, CardContent } from '../ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { Button } from '../ui/button';
import { Skeleton } from '../ui/skeleton';
import type { Activity, ActivityType, ActivityAction } from '@/schemas/activities';

// ============================================================================
// ACTIVITY DISPLAY CONFIGURATION
// ============================================================================

const ACTIVITY_TYPE_CONFIG: Record<ActivityType, { icon: React.ComponentType<any>; color: string; bgColor: string; label: string }> = {
  auth: { icon: IconLogin, color: 'text-blue-600', bgColor: 'bg-blue-100', label: 'Autenticação' },
  client: { icon: IconUser, color: 'text-green-600', bgColor: 'bg-green-100', label: 'Cliente' },
  team: { icon: IconUsers, color: 'text-purple-600', bgColor: 'bg-purple-100', label: 'Equipe' },
  chat: { icon: IconMessage, color: 'text-orange-600', bgColor: 'bg-orange-100', label: 'Chat' },
  file: { icon: IconFile, color: 'text-indigo-600', bgColor: 'bg-indigo-100', label: 'Arquivo' },
  system: { icon: IconSettings2, color: 'text-gray-600', bgColor: 'bg-gray-100', label: 'Sistema' },
  admin: { icon: IconShield, color: 'text-red-600', bgColor: 'bg-red-100', label: 'Admin' },
  notification: { icon: IconBell, color: 'text-yellow-600', bgColor: 'bg-yellow-100', label: 'Notificação' },
  preference: { icon: IconSettings, color: 'text-slate-600', bgColor: 'bg-slate-100', label: 'Preferência' },
  calendar: { icon: IconCalendar, color: 'text-cyan-600', bgColor: 'bg-cyan-100', label: 'Calendário' },
  document: { icon: IconFolder, color: 'text-emerald-600', bgColor: 'bg-emerald-100', label: 'Documento' },
};

const ACTIVITY_ACTION_CONFIG: Record<ActivityAction, { icon: React.ComponentType<any>; label: string; pastTense: string }> = {
  create: { icon: IconPlus, label: 'Criar', pastTense: 'criou' },
  update: { icon: IconEdit, label: 'Atualizar', pastTense: 'atualizou' },
  delete: { icon: IconTrash, label: 'Excluir', pastTense: 'excluiu' },
  view: { icon: IconEye, label: 'Visualizar', pastTense: 'visualizou' },
  login: { icon: IconLogin, label: 'Login', pastTense: 'fez login' },
  logout: { icon: IconLogout, label: 'Logout', pastTense: 'fez logout' },
  invite: { icon: IconSend, label: 'Convidar', pastTense: 'convidou' },
  join: { icon: IconPlus, label: 'Entrar', pastTense: 'entrou' },
  leave: { icon: IconX, label: 'Sair', pastTense: 'saiu' },
  upload: { icon: IconUpload, label: 'Enviar', pastTense: 'enviou' },
  download: { icon: IconDownload, label: 'Baixar', pastTense: 'baixou' },
  share: { icon: IconShare, label: 'Compartilhar', pastTense: 'compartilhou' },
  archive: { icon: IconArchive, label: 'Arquivar', pastTense: 'arquivou' },
  restore: { icon: IconRestore, label: 'Restaurar', pastTense: 'restaurou' },
  send: { icon: IconSend, label: 'Enviar', pastTense: 'enviou' },
  receive: { icon: IconDownload, label: 'Receber', pastTense: 'recebeu' },
  approve: { icon: IconCheck, label: 'Aprovar', pastTense: 'aprovou' },
  reject: { icon: IconX, label: 'Rejeitar', pastTense: 'rejeitou' },
  export: { icon: IconFileExport, label: 'Exportar', pastTense: 'exportou' },
  import: { icon: IconFileImport, label: 'Importar', pastTense: 'importou' },
};

const PRIORITY_CONFIG = {
  low: { color: 'text-gray-500', bgColor: 'bg-gray-100', label: 'Baixa' },
  normal: { color: 'text-blue-500', bgColor: 'bg-blue-100', label: 'Normal' },
  high: { color: 'text-orange-500', bgColor: 'bg-orange-100', label: 'Alta' },
  critical: { color: 'text-red-500', bgColor: 'bg-red-100', label: 'Crítica' },
};

// ============================================================================
// COMPONENTS
// ============================================================================

interface ActivityListProps {
  activities: Activity[];
  isLoading?: boolean;
  onActivityClick?: (activity: Activity) => void;
  showUserInfo?: boolean;
  compact?: boolean;
}

export function ActivityList({
  activities,
  isLoading = false,
  onActivityClick,
  showUserInfo = true,
  compact = false
}: ActivityListProps) {
  if (isLoading) {
    return <ActivityListSkeleton />;
  }

  if (activities.length === 0) {
    return (
      <div className="text-center py-8">
        <IconBell className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhuma atividade</h3>
        <p className="mt-1 text-sm text-gray-500">
          Não há atividades registradas ainda.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {activities.map((activity) => (
        <ActivityItem
          key={activity.$id}
          activity={activity}
          onClick={onActivityClick}
          showUserInfo={showUserInfo}
          compact={compact}
        />
      ))}
    </div>
  );
}

interface ActivityItemProps {
  activity: Activity;
  onClick?: (activity: Activity) => void;
  showUserInfo?: boolean;
  compact?: boolean;
}

function ActivityItem({ activity, onClick, showUserInfo = true, compact = false }: ActivityItemProps) {
  const typeConfig = ACTIVITY_TYPE_CONFIG[activity.type];
  const actionConfig = ACTIVITY_ACTION_CONFIG[activity.action];
  const priorityConfig = PRIORITY_CONFIG[activity.priority];

  const TypeIcon = typeConfig.icon;
  const ActionIcon = actionConfig.icon;

  const handleClick = () => {
    if (onClick) {
      onClick(activity);
    }
  };

  return (
    <Card
      className={`transition-colors hover:bg-gray-50 ${onClick ? 'cursor-pointer' : ''}`}
      onClick={handleClick}
    >
      <CardContent className={compact ? 'p-3' : 'p-4'}>
        <div className="flex items-start space-x-3">
          {/* Activity Icon */}
          <div className={`flex-shrink-0 p-2 rounded-full ${typeConfig.bgColor}`}>
            <TypeIcon className={`h-4 w-4 ${typeConfig.color}`} />
          </div>

          {/* Activity Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <h4 className="text-sm font-medium text-gray-900 truncate">
                  {activity.title}
                </h4>
                <Badge variant="secondary" className="text-xs">
                  {typeConfig.label}
                </Badge>
                {activity.priority !== 'normal' && (
                  <Badge
                    variant="outline"
                    className={`text-xs ${priorityConfig.color} border-current`}
                  >
                    {priorityConfig.label}
                  </Badge>
                )}
              </div>
              <time className="text-xs text-gray-500 flex-shrink-0">
                {formatDistanceToNow(new Date(activity.$createdAt), {
                  addSuffix: true,
                  locale: ptBR,
                })}
              </time>
            </div>

            {activity.description && !compact && (
              <p className="mt-1 text-sm text-gray-600 line-clamp-2">
                {activity.description}
              </p>
            )}

            {/* Tags */}
            {activity.tags && activity.tags.length > 0 && !compact && (
              <div className="mt-2 flex flex-wrap gap-1">
                {activity.tags.slice(0, 3).map((tag, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
                {activity.tags.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{activity.tags.length - 3}
                  </Badge>
                )}
              </div>
            )}

            {/* User Info */}
            {showUserInfo && !compact && (
              <div className="mt-2 flex items-center space-x-2 text-xs text-gray-500">
                <Avatar className="h-4 w-4">
                  <AvatarFallback className="text-xs">
                    {activity.userId.slice(0, 2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <span>ID: {activity.userId.slice(-8)}</span>
                {activity.teamId && (
                  <>
                    <span>•</span>
                    <span>Team: {activity.teamId.slice(-8)}</span>
                  </>
                )}
              </div>
            )}
          </div>

          {/* Action Icon */}
          <div className="flex-shrink-0">
            <ActionIcon className="h-4 w-4 text-gray-400" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function ActivityListSkeleton() {
  return (
    <div className="space-y-3">
      {Array.from({ length: 5 }).map((_, index) => (
        <Card key={index}>
          <CardContent className="p-4">
            <div className="flex items-start space-x-3">
              <Skeleton className="h-8 w-8 rounded-full" />
              <div className="flex-1 space-y-2">
                <div className="flex items-center justify-between">
                  <Skeleton className="h-4 w-48" />
                  <Skeleton className="h-3 w-16" />
                </div>
                <Skeleton className="h-3 w-full" />
                <Skeleton className="h-3 w-3/4" />
              </div>
              <Skeleton className="h-4 w-4" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
