/**
 * Activity API Hooks
 * React Query hooks for activity logging with local-first caching
 */

import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useAuth } from '../use-auth';
import { useIndexedDB } from '../use-indexeddb';
import { isCacheEnabled } from '../../lib/cache-config';
import { syncAfterMutation } from '../../lib/cache-sync';
import type {
  Activity,
  CreateActivityData,
  ActivityFilters,
  ActivityQueryOptions,
  ActivityListResponse,
  ActivityStats,
  LogActivityParams
} from '@/schemas/activities';
import {
  getActivities,
  getActivity,
  createActivity,
  getActivityStats,
  logActivity
} from '../../lib/appwrite/functions/activities';

// ============================================================================
// QUERY KEYS
// ============================================================================

export const activityKeys = {
  all: ['activities'] as const,
  lists: () => [...activityKeys.all, 'list'] as const,
  list: (filters: ActivityFilters) => [...activityKeys.lists(), filters] as const,
  details: () => [...activityKeys.all, 'detail'] as const,
  detail: (id: string) => [...activityKeys.details(), id] as const,
  stats: () => [...activityKeys.all, 'stats'] as const,
  infinite: (filters: ActivityFilters) => [...activityKeys.all, 'infinite', filters] as const,
};

// ============================================================================
// QUERY HOOKS
// ============================================================================

/**
 * Get activities with local-first strategy
 */
export function useActivities(options: ActivityQueryOptions = {}) {
  const { user } = useAuth();
  const { getCache, setCache } = useIndexedDB();

  return useQuery({
    queryKey: activityKeys.list(options.filters || {}),
    queryFn: async (): Promise<Activity[]> => {
      if (!user?.$id) return [];

      // Check cache first if enabled
      if (isCacheEnabled()) {
        console.log('🔍 Verificando cache local para activities');
        const cachedData = await getCache<Activity>('activities', user.$id);

        if (cachedData.length > 0) {
          console.log(`📦 Dados encontrados no cache: ${cachedData.length} activities`);
          return cachedData;
        }
      }

      // Fetch from server
      console.log('🌐 Buscando activities do servidor');
      const result = await getActivities(options);
      const activitiesData = result.activities;

      // Save to cache if enabled
      if (isCacheEnabled() && activitiesData.length > 0) {
        await setCache('activities', activitiesData, user.$id);
      }

      return activitiesData;
    },
    enabled: !!user?.$id && options.filters !== undefined,
    staleTime: isCacheEnabled() ? 0 : 5 * 60 * 1000,
    refetchOnWindowFocus: false,
  });
}

/**
 * Get activities with infinite scroll
 */
export function useInfiniteActivities(filters: ActivityFilters = {}) {
  const { user } = useAuth();
  const hasFilters = Object.keys(filters).length > 0;

  return useInfiniteQuery({
    queryKey: activityKeys.infinite(filters),
    queryFn: async ({ pageParam = 0 }): Promise<ActivityListResponse> => {
      if (!user?.$id) {
        return { activities: [], total: 0, hasMore: false };
      }

      return await getActivities({
        filters,
        limit: 20,
        offset: pageParam as number,
        orderBy: 'createdAt',
        orderDirection: 'desc',
      });
    },
    initialPageParam: 0,
    getNextPageParam: (lastPage: ActivityListResponse, allPages: ActivityListResponse[]) => {
      const totalLoaded = allPages.reduce((sum, page) => sum + page.activities.length, 0);
      return lastPage.hasMore ? totalLoaded : undefined;
    },
    enabled: !!user?.$id && hasFilters,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

/**
 * Get single activity
 */
export function useActivity(id: string) {
  const { user } = useAuth();

  return useQuery({
    queryKey: activityKeys.detail(id),
    queryFn: () => getActivity(id),
    enabled: !!user?.$id && !!id,
    staleTime: 5 * 60 * 1000,
  });
}

/**
 * Get activity statistics
 */
export function useActivityStats(filters?: ActivityFilters) {
  const { user } = useAuth();

  return useQuery({
    queryKey: activityKeys.stats(),
    queryFn: () => getActivityStats(filters || {}),
    enabled: !!user?.$id && filters !== undefined,
    staleTime: 5 * 60 * 1000,
    refetchInterval: 5 * 60 * 1000, // Refresh every 5 minutes
  });
}

// ============================================================================
// MUTATION HOOKS
// ============================================================================

/**
 * Create activity mutation
 */
export function useCreateActivity() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const { addToCache } = useIndexedDB();

  return useMutation({
    mutationFn: (data: CreateActivityData) => createActivity(data),
    onSuccess: async (newActivity) => {
      // Update cache
      if (isCacheEnabled() && user?.$id) {
        await addToCache('activities', newActivity);
      }

      // Invalidate and refetch queries
      await queryClient.invalidateQueries({ queryKey: activityKeys.all });

      // Sync cache after mutation
      await syncAfterMutation('activities', 'create', newActivity, user?.$id);

      toast.success('Atividade registrada com sucesso');
    },
    onError: (error) => {
      console.error('Error creating activity:', error);
      toast.error('Erro ao registrar atividade');
    },
  });
}

/**
 * Log activity mutation (simplified)
 */
export function useLogActivity() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: LogActivityParams) => logActivity(params),
    onSuccess: async () => {
      // Invalidate queries to refresh data
      await queryClient.invalidateQueries({ queryKey: activityKeys.all });

      // Sync cache
      if (user?.$id) {
        await syncAfterMutation('activities', 'create', 'logged', user.$id);
      }
    },
    onError: (error) => {
      console.error('Error logging activity:', error);
      // Don't show error toast for logging failures
    },
  });
}

// ============================================================================
// SPECIALIZED HOOKS
// ============================================================================

/**
 * Get recent activities (last 24 hours)
 */
export function useRecentActivities(limit: number = 10) {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);

  return useActivities({
    filters: limit > 0 ? {
      dateFrom: yesterday.toISOString(),
    } : undefined,
    limit,
    orderBy: 'createdAt',
    orderDirection: 'desc',
  });
}

/**
 * Get activities by type
 */
export function useActivitiesByType(type: string) {
  return useActivities({
    filters: { type: type as any },
    orderBy: 'createdAt',
    orderDirection: 'desc',
  });
}

/**
 * Get user activities
 */
export function useUserActivities(userId: string) {
  return useActivities({
    filters: { userId },
    orderBy: 'createdAt',
    orderDirection: 'desc',
  });
}

/**
 * Get team activities
 */
export function useTeamActivities(teamId: string) {
  return useActivities({
    filters: { teamId },
    orderBy: 'createdAt',
    orderDirection: 'desc',
  });
}

/**
 * Search activities
 */
export function useSearchActivities(searchTerm: string) {
  return useActivities({
    filters: { search: searchTerm },
    orderBy: 'createdAt',
    orderDirection: 'desc',
  });
}

// ============================================================================
// ACTIVITY LOGGER HOOK
// ============================================================================

/**
 * Activity logger hook for easy integration
 */
export function useActivityLogger() {
  const logActivityMutation = useLogActivity();

  const log = async (params: LogActivityParams) => {
    try {
      await logActivityMutation.mutateAsync(params);
    } catch (error) {
      console.error('Failed to log activity:', error);
    }
  };

  const logAuth = async (action: 'login' | 'logout', details?: Record<string, unknown>) => {
    await log({
      type: 'auth',
      action,
      resource: 'user',
      details,
      priority: 'normal',
    });
  };

  const logClient = async (action: string, clientId: string, details?: Record<string, unknown>) => {
    await log({
      type: 'client',
      action: action as any,
      resource: 'client',
      resourceId: clientId,
      details,
      priority: 'normal',
    });
  };

  const logTeam = async (action: string, teamId: string, details?: Record<string, unknown>) => {
    await log({
      type: 'team',
      action: action as any,
      resource: 'team',
      resourceId: teamId,
      details,
      priority: 'normal',
    });
  };

  const logFile = async (action: string, fileId: string, details?: Record<string, unknown>) => {
    await log({
      type: 'file',
      action: action as any,
      resource: 'file',
      resourceId: fileId,
      details,
      priority: 'normal',
    });
  };

  const logSystem = async (action: string, details?: Record<string, unknown>) => {
    await log({
      type: 'system',
      action: action as any,
      resource: 'system',
      details,
      priority: 'high',
    });
  };

  return {
    log,
    logAuth,
    logClient,
    logTeam,
    logFile,
    logSystem,
    isLogging: logActivityMutation.isPending,
  };
}
