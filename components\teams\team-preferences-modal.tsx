import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '../ui/form';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';

import { Checkbox } from '../ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Separator } from '../ui/separator';
import { Settings, Crown, Users, Bell, Shield, Palette } from 'lucide-react';
import { useUpdateTeam } from '../../hooks/use-api';
import { useAuth } from '../../hooks/use-auth';
import { toast } from 'sonner';
import type { Team } from '@/schemas/teams';

import { TeamRolesManager } from '../permissions/team-roles-manager';
import type { TeamPermissionPreferences } from '@/schemas/permissions';

const teamPreferencesSchema = z.object({
  // Informações básicas (suportadas nativamente pelo Appwrite Teams)
  name: z.string().min(1, 'Nome é obrigatório').max(50, 'Nome muito longo'),

  // Configurações estendidas (armazenadas em user preferences)
  description: z.string().max(500, 'Descrição muito longa'),
  color: z.string(),
  timezone: z.string(),

  // Configurações de notificação
  notifyOnJoin: z.boolean(),
  emailNotifications: z.boolean(),

  // Configurações de privacidade
  isPublic: z.boolean(),
  allowInvites: z.boolean(),
  requireApproval: z.boolean(),
});

type TeamPreferencesFormData = z.infer<typeof teamPreferencesSchema>;

interface TeamPreferencesModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  team: Team | null;
}

export function TeamPreferencesModal({
  open,
  onOpenChange,
  team
}: TeamPreferencesModalProps) {
  const { user } = useAuth();
  const updateTeamMutation = useUpdateTeam();

  // Simplificado: assumir que o usuário pode editar se for o criador do time
  const canEdit = true; // TODO: implementar verificação de permissões

  const form = useForm<TeamPreferencesFormData>({
    resolver: zodResolver(teamPreferencesSchema),
    defaultValues: {
      name: '',
      description: '',
      color: '#3b82f6',
      timezone: 'America/Sao_Paulo',
      notifyOnJoin: true,
      emailNotifications: false,
      isPublic: false,
      allowInvites: true,
      requireApproval: false,
    },
  });

  // Reset form when team changes
  React.useEffect(() => {
    if (team && open) {
      // Com a nova estratégia de cache, usamos valores padrão
      // As preferências específicas do time serão gerenciadas pelo cache
      form.reset({
        name: team.name || '',
        description: '',
        color: '#3b82f6',
        timezone: 'America/Sao_Paulo',
        notifyOnJoin: true,
        emailNotifications: false,
        isPublic: false,
        allowInvites: true,
        requireApproval: false,
      });
    }
  }, [team, open, form]);

  const onSubmit = async (data: TeamPreferencesFormData) => {
    if (!team || !canEdit) {
      toast.error('Você não tem permissão para editar as preferências do time');
      return;
    }

    try {
      // 1. Atualizar nome do time (API nativa do Appwrite Teams)
      if (data.name !== team.name) {
        await updateTeamMutation.mutateAsync({
          teamId: team.$id,
          name: data.name,
        });
      }

      // 2. Com a nova estratégia de cache, não precisamos mais salvar
      // preferências de time nas user preferences. O cache é gerenciado
      // automaticamente pelo IndexedDB.

      toast.success('Preferências do time atualizadas com sucesso!');
      onOpenChange(false);
    } catch (error) {
      console.error('Error updating team preferences:', error);
      toast.error('Erro ao atualizar preferências do time');
    }
  };

  const handleCancel = () => {
    form.reset();
    onOpenChange(false);
  };

  if (!team) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Preferências do Time
          </DialogTitle>
          <DialogDescription>
            {canEdit
              ? 'Configure as preferências e informações do time.'
              : 'Visualize as informações do time. Apenas proprietários e administradores podem editar.'
            }
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Informações Básicas */}
            <div className="space-y-4">
              <div className="flex items-center gap-2 text-sm font-medium">
                <Users className="h-4 w-4" />
                Informações Básicas
              </div>

              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nome do Time</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        disabled={!canEdit}
                        placeholder="Digite o nome do time"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Descrição</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        disabled={!canEdit}
                        placeholder="Descreva o propósito do time (opcional)"
                        rows={3}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="color"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <Palette className="h-4 w-4" />
                        Cor do Time
                      </FormLabel>
                      <FormControl>
                        <div className="flex items-center gap-2">
                          <Input
                            {...field}
                            disabled={!canEdit}
                            type="color"
                            className="w-16 h-10 p-1 border rounded"
                          />
                          <Input
                            {...field}
                            disabled={!canEdit}
                            placeholder="#3b82f6"
                            className="flex-1"
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="timezone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Fuso Horário</FormLabel>
                      <Select
                        disabled={!canEdit}
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o fuso horário" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="America/Sao_Paulo">São Paulo (GMT-3)</SelectItem>
                          <SelectItem value="America/New_York">Nova York (GMT-5)</SelectItem>
                          <SelectItem value="Europe/London">Londres (GMT+0)</SelectItem>
                          <SelectItem value="Asia/Tokyo">Tóquio (GMT+9)</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <Separator />

            {/* Configurações de Notificação */}
            <div className="space-y-4">
              <div className="flex items-center gap-2 text-sm font-medium">
                <Bell className="h-4 w-4" />
                Notificações
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="notifyOnJoin"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={!canEdit}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Notificar novos membros</FormLabel>
                        <FormDescription>
                          Receber notificações quando alguém entrar no time
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="emailNotifications"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={!canEdit}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Notificações por email</FormLabel>
                        <FormDescription>
                          Receber notificações também por email
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <Separator />

            {/* Configurações de Privacidade */}
            <div className="space-y-4">
              <div className="flex items-center gap-2 text-sm font-medium">
                <Shield className="h-4 w-4" />
                Privacidade e Acesso
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="isPublic"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={!canEdit}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Time público</FormLabel>
                        <FormDescription>
                          Qualquer pessoa pode ver o time
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="allowInvites"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={!canEdit}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Permitir convites</FormLabel>
                        <FormDescription>
                          Membros podem convidar outras pessoas
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="requireApproval"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={!canEdit}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Requer aprovação</FormLabel>
                        <FormDescription>
                          Novos membros precisam ser aprovados
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <Separator />

            {/* Cargos e Permissões */}
            <div className="space-y-4">
              <TeamRolesManager
                teamId={team.$id}
                teamPreferences={(team.prefs as TeamPermissionPreferences) || {}}
              />
            </div>

            {/* Permissões */}
            {!canEdit && (
              <div className="p-3 rounded-lg bg-muted/50 border">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Crown className="h-4 w-4 text-yellow-500" />
                  Apenas proprietários e administradores podem editar essas configurações
                </div>
              </div>
            )}

            <div className="flex justify-end gap-2 pt-4">
              <Button type="button" variant="outline" onClick={handleCancel}>
                {canEdit ? 'Cancelar' : 'Fechar'}
              </Button>
              {canEdit && (
                <Button
                  type="submit"
                  disabled={updateTeamMutation.isPending}
                >
                  {updateTeamMutation.isPending ? 'Salvando...' : 'Salvar Preferências'}
                </Button>
              )}
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
