// Script principal para configuração completa do banco de dados
require('dotenv').config();

const { setupCollections } = require('./setup-collections.cjs');
const { setupAttributes } = require('./setup-attributes.cjs');

async function setupDatabase() {
  try {
    console.log('🚀 Iniciando configuração completa do banco de dados...\n');
    console.log(process.env.APPWRITE_ENDPOINT)
    console.log(process.env.APPWRITE_PROJECT_ID)
    console.log(process.env.APPWRITE_API_KEY)
    // Verificar configuração
    if (!process.env.APPWRITE_ENDPOINT || !process.env.APPWRITE_PROJECT_ID || !process.env.APPWRITE_API_KEY) {
      throw new Error('❌ Variáveis de ambiente do Appwrite não configuradas');
    }

    console.log('📋 ETAPAS DA CONFIGURAÇÃO:');
    console.log('   1. Criar coleções');
    console.log('   2. Criar atributos e índices');
    console.log('   3. Configuração concluída\n');

    // Etapa 1: Criar coleções
    console.log('🔄 ETAPA 1: Configurando coleções...');
    await setupCollections();
    console.log('✅ Coleções configuradas com sucesso!\n');

    // Aguardar um momento entre etapas
    console.log('⏳ Aguardando 3 segundos antes da próxima etapa...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Etapa 2: Criar atributos
    console.log('🔄 ETAPA 2: Configurando atributos e índices...');
    await setupAttributes();
    console.log('✅ Atributos e índices configurados com sucesso!\n');

    console.log('🎉 CONFIGURAÇÃO BÁSICA CONCLUÍDA!');
    console.log('\n📝 PRÓXIMOS PASSOS OBRIGATÓRIOS:');
    console.log('   ⚠️  Execute "yarn setup:relationships" para configurar relacionamentos chat-mensagens');
    console.log('\n📝 PRÓXIMOS PASSOS OPCIONAIS:');
    console.log('   - Execute "yarn seed" para popular com dados fictícios');
    console.log('   - Verifique o arquivo .env para os IDs das coleções');
    console.log('\n💡 LEMBRE-SE:');
    console.log('   - Relacionamentos são gerenciados separadamente em setup-relationships.cjs');
    console.log('   - Para funcionalidade completa de chat, execute setup:relationships');

  } catch (error) {
    console.error('❌ Erro na configuração do banco de dados:', error);
    process.exit(1);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  setupDatabase();
}

module.exports = { setupDatabase };
