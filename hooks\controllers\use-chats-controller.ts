/**
 * Controller para Chats
 * Usa subscribe do Valtio - sem useEffect, useState, nada
 */

import { useEffect, useRef } from 'react';
import { subscribe } from 'valtio';
import { useQueryClient } from '@tanstack/react-query';
import { realtimeStore } from '../../lib/realtime/store';
import { saveToIndexedDB } from '../../lib/cache-sync';

export function useChatsController() {
  const queryClient = useQueryClient();
  const unsubscribeRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    console.log('🎛️ Inicializando controller de chats...');

    // Subscribe do Valtio para mudanças no store
    unsubscribeRef.current = subscribe(realtimeStore, () => {
      const chats = realtimeStore.chats;

      if (chats.length === 0) return;

      console.log(`📦 Processando ${chats.length} chats do realtime...`);

      // Adicionar dados novos aos existentes no React Query
      chats.forEach(chat => {
        console.log(`📝 Atualizando chat: ${chat.$id}`);

        queryClient.setQueryData(['team-chats'], (oldData: any) => {
          if (!oldData) return [chat];

          const exists = oldData.find((item: any) => item.$id === chat.$id);
          if (exists) {
            // Atualizar existente
            return oldData.map((item: any) =>
              item.$id === chat.$id ? chat : item
            );
          } else {
            // Adicionar novo
            return [...oldData, chat];
          }
        });

        // Salvar no IndexedDB
        saveToIndexedDB('team_chats', chat, {
          collection: 'team_chats',
          userId: chat.teamId // TeamChat usa teamId
        });
      });

  
    });

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    };
  }, [queryClient]);

  return {
    unsubscribe: () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    }
  };
}
