/**
 * Google Analytics 4 Configuration
 * Integração com React GA4 para tracking de eventos e métricas
 */

import ReactGA from 'react-ga4';

// Configuração do Google Analytics
const GA_MEASUREMENT_ID = process.env.NEXT_PUBLIC_GA4_MEASUREMENT_ID;

/**
 * <PERSON><PERSON><PERSON>zar Google Analytics
 */
export function initializeGA() {
  if (!GA_MEASUREMENT_ID) {
    console.warn('Google Analytics Measurement ID não configurado');
    return;
  }

  ReactGA.initialize(GA_MEASUREMENT_ID, {
    testMode: process.env.NODE_ENV === "development", // Modo de teste em desenvolvimento
    gtagOptions: {
      debug_mode: process.env.NODE_ENV === "development",
    },
  });

  console.log('Google Analytics inicializado:', GA_MEASUREMENT_ID);
}

/**
 * Verificar se GA está configurado
 */
export function isGAConfigured(): boolean {
  return !!GA_MEASUREMENT_ID;
}

/**
 * Enviar pageview para o GA
 */
export function sendPageview(path: string, title?: string) {
  if (!isGAConfigured()) return;

  ReactGA.send({
    hitType: 'pageview',
    page: path,
    title: title || document.title,
  });
}

/**
 * Enviar evento customizado para o GA
 */
export function sendEvent(action: string, category: string, label?: string, value?: number) {
  if (!isGAConfigured()) return;

  ReactGA.event({
    action,
    category,
    label,
    value,
  });
}

/**
 * Definir propriedades do usuário
 */
export function setUserProperties(properties: Record<string, any>) {
  if (!isGAConfigured()) return;

  ReactGA.set(properties);
}

/**
 * Definir ID do usuário para tracking
 */
export function setUserId(userId: string) {
  if (!isGAConfigured()) return;

  ReactGA.set({ user_id: userId });
}

/**
 * Tipos de eventos personalizados para o template
 */
export const GAEvents = {
  // Autenticação
  LOGIN: 'login',
  LOGOUT: 'logout',
  SIGNUP: 'sign_up',
  
  // Clientes
  CLIENT_CREATE: 'client_create',
  CLIENT_UPDATE: 'client_update',
  CLIENT_DELETE: 'client_delete',
  CLIENT_VIEW: 'client_view',
  
  // Eventos
  EVENT_CREATE: 'event_create',
  EVENT_UPDATE: 'event_update',
  EVENT_DELETE: 'event_delete',
  
  // Kanban
  KANBAN_CREATE: 'kanban_create',
  KANBAN_UPDATE: 'kanban_update',
  TASK_CREATE: 'task_create',
  TASK_COMPLETE: 'task_complete',
  
  // Documentos
  DOCUMENT_UPLOAD: 'document_upload',
  DOCUMENT_DOWNLOAD: 'document_download',
  DOCUMENT_DELETE: 'document_delete',
  
  // Dashboard
  DASHBOARD_VIEW: 'dashboard_view',
  ANALYTICS_VIEW: 'analytics_view',
  
  // Teams
  TEAM_CREATE: 'team_create',
  TEAM_JOIN: 'team_join',
  TEAM_LEAVE: 'team_leave',
} as const;

/**
 * Categorias de eventos
 */
export const GACategories = {
  AUTH: 'Authentication',
  CLIENT: 'Client Management',
  EVENT: 'Event Management',
  KANBAN: 'Kanban Management',
  DOCUMENT: 'Document Management',
  DASHBOARD: 'Dashboard',
  TEAM: 'Team Management',
  NAVIGATION: 'Navigation',
} as const;

/**
 * Helper para enviar eventos específicos do template
 */
export const trackEvent = {
  // Autenticação
  login: (method: string) => sendEvent(GAEvents.LOGIN, GACategories.AUTH, method),
  logout: () => sendEvent(GAEvents.LOGOUT, GACategories.AUTH),
  signup: (method: string) => sendEvent(GAEvents.SIGNUP, GACategories.AUTH, method),
  
  // Clientes
  clientCreate: () => sendEvent(GAEvents.CLIENT_CREATE, GACategories.CLIENT),
  clientUpdate: (clientId: string) => sendEvent(GAEvents.CLIENT_UPDATE, GACategories.CLIENT, clientId),
  clientDelete: (clientId: string) => sendEvent(GAEvents.CLIENT_DELETE, GACategories.CLIENT, clientId),
  clientView: (clientId: string) => sendEvent(GAEvents.CLIENT_VIEW, GACategories.CLIENT, clientId),
  
  // Eventos
  eventCreate: () => sendEvent(GAEvents.EVENT_CREATE, GACategories.EVENT),
  eventUpdate: (eventId: string) => sendEvent(GAEvents.EVENT_UPDATE, GACategories.EVENT, eventId),
  eventDelete: (eventId: string) => sendEvent(GAEvents.EVENT_DELETE, GACategories.EVENT, eventId),
  
  // Kanban
  kanbanCreate: () => sendEvent(GAEvents.KANBAN_CREATE, GACategories.KANBAN),
  kanbanUpdate: (boardId: string) => sendEvent(GAEvents.KANBAN_UPDATE, GACategories.KANBAN, boardId),
  taskCreate: (boardId: string) => sendEvent(GAEvents.TASK_CREATE, GACategories.KANBAN, boardId),
  taskComplete: (taskId: string) => sendEvent(GAEvents.TASK_COMPLETE, GACategories.KANBAN, taskId),
  
  // Documentos
  documentUpload: (fileType: string) => sendEvent(GAEvents.DOCUMENT_UPLOAD, GACategories.DOCUMENT, fileType),
  documentDownload: (fileId: string) => sendEvent(GAEvents.DOCUMENT_DOWNLOAD, GACategories.DOCUMENT, fileId),
  documentDelete: (fileId: string) => sendEvent(GAEvents.DOCUMENT_DELETE, GACategories.DOCUMENT, fileId),
  
  // Dashboard
  dashboardView: () => sendEvent(GAEvents.DASHBOARD_VIEW, GACategories.DASHBOARD),
  analyticsView: () => sendEvent(GAEvents.ANALYTICS_VIEW, GACategories.DASHBOARD),
  
  // Teams
  teamCreate: () => sendEvent(GAEvents.TEAM_CREATE, GACategories.TEAM),
  teamJoin: (teamId: string) => sendEvent(GAEvents.TEAM_JOIN, GACategories.TEAM, teamId),
  teamLeave: (teamId: string) => sendEvent(GAEvents.TEAM_LEAVE, GACategories.TEAM, teamId),
  
  // Navegação
  pageView: (path: string, title?: string) => sendPageview(path, title),
};
