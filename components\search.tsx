import * as React from "react"
import { useRouter } from 'next/navigation'
import {
  ArrowUpRightIcon,
  SearchIcon,
  LayoutDashboard,
  UserIcon,
  SettingsIcon,
  BarChart3Icon,
  HelpCircleIcon,
  Calendar,
  Folder,
  Activity,
  Bell,
  MessageSquare,

  Plus,
  Users,
  CalendarPlus,
  Grid3X3,
} from "lucide-react"

import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
  CommandShortcut,
} from "./ui/command"
import { useIsMobile } from "../hooks/use-mobile"

// Dados das páginas disponíveis
const pages = [
  {
    title: "Dashboard",
    url: "/dashboard",
    icon: LayoutDashboard,
    description: "Visão geral e métricas principais"
  },
  {
    title: "Analytics",
    url: "/dashboard/analytics",
    icon: BarChart3Icon,
    description: "Análises e métricas detalhadas"
  },
  {
    title: "Clientes",
    url: "/dashboard/clients",
    icon: UserIcon,
    description: "Gerenciar clientes"
  },
  {
    title: "Teams",
    url: "/dashboard/teams",
    icon: Users,
    description: "Gerenciar equipes"
  },
  {
    title: "Team Chat",
    url: "/dashboard/team-chat",
    icon: MessageSquare,
    description: "Chat da equipe"
  },
  {
    title: "Calendar",
    url: "/dashboard/calendar",
    icon: Calendar,
    description: "Calendário e eventos"
  },
  {
    title: "Kanban",
    url: "/dashboard/kanban",
    icon: Grid3X3,
    description: "Boards e tarefas"
  },
  {
    title: "Documents",
    url: "/dashboard/documents",
    icon: Folder,
    description: "Gerenciar documentos"
  },
  {
    title: "Activities",
    url: "/dashboard/activities",
    icon: Activity,
    description: "Log de atividades"
  },
  {
    title: "Notifications",
    url: "/dashboard/notifications",
    icon: Bell,
    description: "Central de notificações"
  },
  {
    title: "Relatórios",
    url: "/dashboard/reports",
    icon: BarChart3Icon,
    description: "Relatórios e análises detalhadas"
  },
  {
    title: "Planos",
    url: "/dashboard/plans",
    icon: BarChart3Icon,
    description: "Planos e assinaturas"
  },
  {
    title: "Preferências",
    url: "/dashboard/preferences",
    icon: SettingsIcon,
    description: "Configurações pessoais e da conta"
  },
  {
    title: "Ajuda",
    url: "/dashboard/help",
    icon: HelpCircleIcon,
    description: "Central de ajuda e suporte"
  },
]

// Comandos rápidos disponíveis
const quickCommands = [
  {
    title: "Novo cliente",
    action: () => console.log("Criar novo cliente"),
    icon: Plus,
    shortcut: "⌘N"
  },
  {
    title: "Nova equipe",
    action: () => console.log("Criar nova equipe"),
    icon: Users,
    shortcut: "⌘T"
  },
  {
    title: "Novo evento",
    action: () => console.log("Criar novo evento"),
    icon: CalendarPlus,
    shortcut: "⌘E"
  },
  {
    title: "Novo board",
    action: () => console.log("Criar novo board"),
    icon: Grid3X3,
    shortcut: "⌘B"
  },
]

export default function SearchComponent() {
  const [open, setOpen] = React.useState(false)
  const router = useRouter()
  const isMobile = useIsMobile()

  React.useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === "k" && (e.metaKey || e.ctrlKey)) {
        e.preventDefault()
        setOpen((open) => !open)
      }
    }

    document.addEventListener("keydown", down)
    return () => document.removeEventListener("keydown", down)
  }, [])

  const handlePageSelect = (url: string) => {
    setOpen(false)
    router.push(url)
  }

  const handleCommandSelect = (action: () => void) => {
    setOpen(false)
    action()
  }

  return (
    <>
      <button
        className={`border-input bg-background text-foreground placeholder:text-muted-foreground/70 focus-visible:border-ring focus-visible:ring-ring/50 inline-flex h-9 rounded-md border text-sm shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] ${
          isMobile ? 'w-9 px-0 justify-center' : 'w-fit px-3 py-2'
        }`}
        onClick={() => setOpen(true)}
      >
        {isMobile ? (
          <SearchIcon
            className="text-muted-foreground/80"
            size={16}
            aria-hidden="true"
          />
        ) : (
          <>
            <span className="flex grow items-center">
              <SearchIcon
                className="text-muted-foreground/80 -ms-1 me-3"
                size={16}
                aria-hidden="true"
              />
              <span className="text-muted-foreground/70 font-normal">Pesquisar</span>
            </span>
            <kbd className="bg-background text-muted-foreground/70 ms-12 -me-1 inline-flex h-5 max-h-full items-center rounded border px-1 font-[inherit] text-[0.625rem] font-medium">
              ⌘K
            </kbd>
          </>
        )}
      </button>
      <CommandDialog open={open} onOpenChange={setOpen}>
        <CommandInput placeholder="Digite um comando ou pesquise páginas..." />
        <CommandList>
          <CommandEmpty>Nenhum resultado encontrado.</CommandEmpty>

          <CommandGroup heading="Páginas">
            {pages.map((page) => (
              <CommandItem
                key={page.url}
                onSelect={() => handlePageSelect(page.url)}
                className="cursor-pointer"
              >
                <page.icon
                  size={16}
                  className="opacity-60"
                  aria-hidden="true"
                />
                <div className="flex flex-col">
                  <span>{page.title}</span>
                  <span className="text-xs text-muted-foreground">{page.description}</span>
                </div>
                <ArrowUpRightIcon size={12} className="ml-auto opacity-60" />
              </CommandItem>
            ))}
          </CommandGroup>

          <CommandSeparator />

          <CommandGroup heading="Comandos Rápidos">
            {quickCommands.map((command) => (
              <CommandItem
                key={command.title}
                onSelect={() => handleCommandSelect(command.action)}
                className="cursor-pointer"
              >
                <command.icon
                  size={16}
                  className="opacity-60"
                  aria-hidden="true"
                />
                <span>{command.title}</span>
                <CommandShortcut className="justify-center">{command.shortcut}</CommandShortcut>
              </CommandItem>
            ))}
          </CommandGroup>
        </CommandList>
      </CommandDialog>
    </>
  )
}
