/**
 * Event Types and Schemas
 * Sistema completo de calendário com eventos, categorias e recorrência
 */

import { z } from 'zod';

// ============================================================================
// ENUMS E CONSTANTES
// ============================================================================

export const EVENT_TYPES = ['meeting', 'task', 'reminder', 'appointment', 'deadline', 'personal', 'work', 'other'] as const;
export const EVENT_PRIORITIES = ['baixa', 'media', 'alta', 'critica'] as const;
export const EVENT_STATUS = ['agendado', 'em_andamento', 'concluido', 'cancelado', 'adiado'] as const;
export const RECURRENCE_TYPES = ['none', 'daily', 'weekly', 'monthly', 'yearly'] as const;
export const REMINDER_TYPES = ['none', '5min', '15min', '30min', '1hour', '2hours', '1day', '1week'] as const;

// Cores padrão para categorias
export const EVENT_COLORS = [
  '#3B82F6', // blue
  '#EF4444', // red
  '#10B981', // green
  '#F59E0B', // yellow
  '#8B5CF6', // purple
  '#F97316', // orange
  '#06B6D4', // cyan
  '#84CC16', // lime
  '#EC4899', // pink
  '#6B7280', // gray
] as const;

// ============================================================================
// SCHEMAS PRINCIPAIS
// ============================================================================

/**
 * Schema principal do evento
 */
export const eventSchema = z.object({
  // Campos de auditoria Appwrite
  $id: z.string(),
  $createdAt: z.string(),
  $updatedAt: z.string(),

  // Ownership e auditoria
  userId: z.string().min(1, 'User ID é obrigatório'),
  createdBy: z.string().min(1, 'Created by é obrigatório'),
  updatedBy: z.string().optional(),
  teamId: z.string().optional(),

  // Informações básicas do evento
  title: z.string().min(1, 'Título é obrigatório').max(200, 'Título muito longo'),
  description: z.string().max(1000, 'Descrição muito longa').optional(),

  // Data e hora
  startDate: z.string(), // ISO string
  endDate: z.string(), // ISO string
  allDay: z.boolean().default(false),
  timezone: z.string().default('America/Sao_Paulo'),

  // Localização
  location: z.string().max(300, 'Localização muito longa').optional(),
  locationUrl: z.string().url('URL inválida').optional(),

  // Classificação
  type: z.enum(EVENT_TYPES).default('other'),
  priority: z.enum(EVENT_PRIORITIES).default('media'),
  status: z.enum(EVENT_STATUS).default('agendado'),

  // Categoria e cor
  category: z.string().max(50, 'Categoria muito longa').optional(),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Cor deve ser um hex válido').default('#3B82F6'),

  // Recorrência
  recurrenceType: z.enum(RECURRENCE_TYPES).default('none'),
  recurrenceInterval: z.number().min(1).max(365).default(1), // A cada X dias/semanas/meses
  recurrenceEndDate: z.string().optional(), // ISO string
  recurrenceCount: z.number().min(1).max(1000).optional(), // Número de ocorrências
  recurrenceWeekdays: z.array(z.number().min(0).max(6)).optional(), // 0=domingo, 6=sábado
  recurrenceMonthDay: z.number().min(1).max(31).optional(), // Dia do mês
  parentEventId: z.string().optional(), // Para eventos recorrentes

  // Lembretes
  reminderType: z.enum(REMINDER_TYPES).default('none'),
  customReminderMinutes: z.number().min(0).max(10080).optional(), // Até 1 semana em minutos
  reminderSent: z.boolean().default(false),

  // Participantes e compartilhamento
  attendees: z.array(z.string()).default([]), // Array de user IDs
  isPublic: z.boolean().default(false),
  allowGuestInvites: z.boolean().default(false),

  // Anexos e links
  attachments: z.array(z.string()).default([]), // URLs dos arquivos
  links: z.array(z.object({
    title: z.string().max(100),
    url: z.string().url(),
  })).default([]),

  // Metadata adicional
  tags: z.array(z.string().max(30)).default([]),
  notes: z.string().max(2000).optional(),

  // Soft delete
  isDeleted: z.boolean().default(false),

  // Campos calculados (não persistidos)
  isRecurring: z.boolean().optional(),
  nextOccurrence: z.string().optional(),
});

/**
 * Schema para formulários (criação/edição)
 */
export const eventFormSchema = eventSchema.omit({
  $id: true,
  $createdAt: true,
  $updatedAt: true,
  createdBy: true,
  updatedBy: true,
  reminderSent: true,
  isRecurring: true,
  nextOccurrence: true,
}).extend({
  // Campos específicos do formulário
  startTime: z.string().optional(), // HH:mm format
  endTime: z.string().optional(), // HH:mm format

  // Validações condicionais
}).refine((data) => {
  // Validar que endDate >= startDate
  const start = new Date(data.startDate);
  const end = new Date(data.endDate);
  return end >= start;
}, {
  message: 'Data de fim deve ser posterior à data de início',
  path: ['endDate'],
}).refine((data) => {
  // Se tem recorrência, deve ter fim ou contagem
  if (data.recurrenceType !== 'none') {
    return data.recurrenceEndDate || data.recurrenceCount;
  }
  return true;
}, {
  message: 'Eventos recorrentes devem ter data de fim ou número de ocorrências',
  path: ['recurrenceEndDate'],
});

/**
 * Schema para atualizações
 */
export const updateEventSchema = z.object({
  $id: z.string(),
  updatedBy: z.string(),
  // Campos opcionais do evento
  title: z.string().min(1).max(200).optional(),
  description: z.string().max(1000).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  allDay: z.boolean().optional(),
  timezone: z.string().optional(),
  location: z.string().max(300).optional(),
  locationUrl: z.string().url().optional(),
  type: z.enum(EVENT_TYPES).optional(),
  priority: z.enum(EVENT_PRIORITIES).optional(),
  status: z.enum(EVENT_STATUS).optional(),
  category: z.string().max(50).optional(),
  color: z.string().regex(/^#[0-9A-F]{6}$/i).optional(),
  recurrenceType: z.enum(RECURRENCE_TYPES).optional(),
  recurrenceInterval: z.number().min(1).max(365).optional(),
  recurrenceEndDate: z.string().optional(),
  recurrenceCount: z.number().min(1).max(1000).optional(),
  recurrenceWeekdays: z.array(z.number().min(0).max(6)).optional(),
  recurrenceMonthDay: z.number().min(1).max(31).optional(),
  parentEventId: z.string().optional(),
  reminderType: z.enum(REMINDER_TYPES).optional(),
  customReminderMinutes: z.number().min(0).max(10080).optional(),
  attendees: z.array(z.string()).optional(),
  isPublic: z.boolean().optional(),
  allowGuestInvites: z.boolean().optional(),
  attachments: z.array(z.string()).optional(),
  links: z.array(z.object({
    title: z.string().max(100),
    url: z.string().url(),
  })).optional(),
  tags: z.array(z.string().max(30)).optional(),
  notes: z.string().max(2000).optional(),
});

/**
 * Schema para categoria de evento
 */
export const eventCategorySchema = z.object({
  $id: z.string(),
  $createdAt: z.string(),
  $updatedAt: z.string(),

  userId: z.string(),
  teamId: z.string().optional(),

  name: z.string().min(1, 'Nome é obrigatório').max(50, 'Nome muito longo'),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Cor deve ser um hex válido'),
  description: z.string().max(200).optional(),
  isDefault: z.boolean().default(false),
  sortOrder: z.number().default(0),

  // Soft delete
  isDeleted: z.boolean().default(false),
});

export const eventCategoryFormSchema = eventCategorySchema.omit({
  $id: true,
  $createdAt: true,
  $updatedAt: true,
});

// ============================================================================
// TIPOS DERIVADOS
// ============================================================================

export type Event = z.infer<typeof eventSchema>;
export type EventFormData = z.infer<typeof eventFormSchema>;
export type UpdateEventData = z.infer<typeof updateEventSchema>;
export type CreateEventData = Omit<EventFormData, 'userId' | 'teamId'>;

export type EventCategory = z.infer<typeof eventCategorySchema>;
export type EventCategoryFormData = z.infer<typeof eventCategoryFormSchema>;

export type EventType = typeof EVENT_TYPES[number];
export type EventPriority = typeof EVENT_PRIORITIES[number];
export type EventStatus = typeof EVENT_STATUS[number];
export type RecurrenceType = typeof RECURRENCE_TYPES[number];
export type ReminderType = typeof REMINDER_TYPES[number];

// ============================================================================
// INTERFACES AUXILIARES
// ============================================================================

/**
 * Interface para visualização do calendário
 */
export interface CalendarEvent extends Event {
  // Campos calculados para o calendário
  start: Date;
  end: Date;
  resource?: any;
  isDraggable?: boolean;
  isResizable?: boolean;
}

/**
 * Interface para filtros do calendário
 */
export interface EventFilters {
  search?: string;
  categories?: string[];
  types?: EventType[];
  priorities?: EventPriority[];
  status?: EventStatus[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  attendees?: string[];
  tags?: string[];
  teamId?: string;
}

/**
 * Interface para configurações de visualização
 */
export interface CalendarViewSettings {
  view: 'month' | 'week' | 'day' | 'agenda';
  startDate: Date;
  showWeekends: boolean;
  showAllDay: boolean;
  timeSlotDuration: number; // em minutos
  businessHours: {
    start: string; // HH:mm
    end: string; // HH:mm
  };
  defaultReminder: ReminderType;
  defaultDuration: number; // em minutos
}

/**
 * Interface para estatísticas de eventos
 */
export interface EventStats {
  total: number;
  byStatus: Record<EventStatus, number>;
  byType: Record<EventType, number>;
  byPriority: Record<EventPriority, number>;
  upcoming: number;
  overdue: number;
  thisWeek: number;
  thisMonth: number;
}

/**
 * Interface para exportação
 */
export interface EventExportOptions {
  format: 'ical' | 'csv' | 'json';
  dateRange?: {
    start: Date;
    end: Date;
  };
  includeRecurring: boolean;
  includeCompleted: boolean;
  categories?: string[];
}

/**
 * Interface para notificação de evento
 */
export interface EventNotification {
  eventId: string;
  userId: string;
  type: 'reminder' | 'invitation' | 'update' | 'cancellation';
  scheduledFor: Date;
  sent: boolean;
  title: string;
  message: string;
  actionUrl?: string;
}
