/**
 * Hook de Integração com React Query
 * Sistema de cache unificado transparente
 */

import { useQuery, useQueryClient, type UseQueryOptions } from '@tanstack/react-query';
import { useCallback } from 'react';
import {
  getCache,
  setCache,
  deleteCache,
} from '../lib/unified-cache';
import { isCacheEnabled } from '../lib/cache-config';

// ============================================================================
// TIPOS
// ============================================================================

export interface UnifiedCacheOptions<T> extends Omit<UseQueryOptions<T>, 'queryKey' | 'queryFn'> {
  queryKey: readonly unknown[];
  queryFn: () => Promise<T>;
  cacheTTL?: number;
  enableLocalCache?: boolean;
  onCacheHit?: (data: T) => void;
  onCacheMiss?: () => void;
  onCacheInvalidated?: (reason: string) => void;
}

export interface UnifiedCacheResult<T> {
  data: T | undefined;
  isLoading: boolean;
  error: Error | null;
  refetch: () => void;
  isCacheHit: boolean;
  invalidateCache: () => Promise<void>;
  clearLocalCache: () => Promise<void>;
}

// ============================================================================
// HOOK PRINCIPAL
// ============================================================================

/**
 * Hook unificado que integra React Query com cache local IndexedDB
 */
export function useUnifiedCache<T>(
  options: UnifiedCacheOptions<T>
): UnifiedCacheResult<T> {
  const {
    queryKey,
    queryFn,
    cacheTTL,
    enableLocalCache = true,
    onCacheHit,
    onCacheMiss,
    onCacheInvalidated,
    ...reactQueryOptions
  } = options;

  const queryClient = useQueryClient();
  const shouldUseCache = isCacheEnabled() && enableLocalCache;

  // ============================================================================
  // REACT QUERY COM CACHE LOCAL
  // ============================================================================

  const query = useQuery({
    queryKey,
    queryFn: async (): Promise<T> => {
      // Tentar cache local primeiro (local-first)
      if (shouldUseCache) {
        const cachedData = await getCache<T>(queryKey);

        if (cachedData !== null) {
          onCacheHit?.(cachedData);

          // Buscar dados frescos em background para validação
          queryFn().then(async (freshData) => {
            // Sempre atualizar cache com dados frescos
            await setCache(queryKey, freshData, cacheTTL);
          }).catch(() => {
            // Erro na validação em background - manter dados do cache
          });

          return cachedData;
        } else {
          onCacheMiss?.();
        }
      }

      // Buscar dados do servidor
      const data = await queryFn();

      // Salvar no cache local
      if (shouldUseCache) {
        await setCache(queryKey, data, cacheTTL);
      }

      return data;
    },
    // Configurações para local-first
    staleTime: shouldUseCache ? 0 : (reactQueryOptions.staleTime ?? 5 * 60 * 1000),
    gcTime: shouldUseCache ? 30 * 60 * 1000 : (reactQueryOptions.gcTime ?? 10 * 60 * 1000),
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    ...reactQueryOptions,
  });

  // ============================================================================
  // FUNÇÕES DE CONTROLE
  // ============================================================================

  const invalidateCache = useCallback(async () => {
    // Invalidar cache local
    await deleteCache(queryKey);

    // Invalidar React Query
    queryClient.invalidateQueries({ queryKey });
  }, [queryKey, queryClient]);

  const clearLocalCache = useCallback(async () => {
    await deleteCache(queryKey);
  }, [queryKey]);

  // ============================================================================
  // DETECÇÃO DE CACHE HIT
  // ============================================================================

  const isCacheHit = query.dataUpdatedAt === query.dataUpdatedAt &&
                     query.isFetched &&
                     !query.isFetching;

  // ============================================================================
  // RETORNO
  // ============================================================================

  return {
    data: query.data,
    isLoading: query.isLoading,
    error: query.error,
    refetch: query.refetch,
    isCacheHit,
    invalidateCache,
    clearLocalCache,
  };
}

// ============================================================================
// HOOKS ESPECIALIZADOS
// ============================================================================

/**
 * Hook para invalidação universal de cache
 */
export function useCacheInvalidation() {
  const queryClient = useQueryClient();

  const invalidateAll = useCallback(async () => {
    // Limpar todo cache local
    const { clearCache } = await import('@/lib/unified-cache');
    await clearCache();

    // Invalidar todas as queries do React Query
    queryClient.invalidateQueries();
  }, [queryClient]);

  const invalidateByPattern = useCallback(async (pattern: string) => {
    // Invalidar queries que correspondem ao padrão
    queryClient.invalidateQueries({
      predicate: (query) => {
        const key = JSON.stringify(query.queryKey);
        return key.includes(pattern);
      },
    });

    // Limpar cache local correspondente
    const { getCacheKeys, deleteCache } = await import('@/lib/unified-cache');
    const keys = await getCacheKeys();

    for (const key of keys) {
      if (key.includes(pattern)) {
        try {
          const queryKey = JSON.parse(key) as readonly unknown[];
          await deleteCache(queryKey);
        } catch (error) {
          // Ignorar erros de parsing
        }
      }
    }
  }, [queryClient]);

  return {
    invalidateAll,
    invalidateByPattern,
  };
}

/**
 * Hook para estatísticas do cache
 */
export function useCacheStats() {
  const { data: stats, refetch } = useQuery({
    queryKey: ['cache-stats'],
    queryFn: async () => {
      const { getCacheStats } = await import('@/lib/unified-cache');
      return await getCacheStats();
    },
    refetchInterval: 30000, // Atualizar a cada 30 segundos
  });

  const cleanup = useCallback(async () => {
    const { cleanupExpiredCache } = await import('@/lib/unified-cache');
    const cleanedCount = await cleanupExpiredCache();
    await refetch();
    return cleanedCount;
  }, [refetch]);

  return {
    stats,
    cleanup,
    refetch,
  };
}

// ============================================================================
// UTILITÁRIOS
// ============================================================================

/**
 * Hook para verificar se cache está habilitado
 */
export function useIsCacheEnabled() {
  return isCacheEnabled();
}

/**
 * Hook para controle manual do cache
 */
export function useManualCache() {
  const queryClient = useQueryClient();

  const setManualCache = useCallback(async <T>(
    queryKey: readonly unknown[],
    data: T,
    ttl?: number
  ) => {
    // Salvar no cache local
    await setCache(queryKey, data, ttl);

    // Atualizar React Query
    queryClient.setQueryData(queryKey, data);
  }, [queryClient]);

  const getManualCache = useCallback(async <T>(
    queryKey: readonly unknown[]
  ): Promise<T | null> => {
    return await getCache<T>(queryKey);
  }, []);

  const deleteManualCache = useCallback(async (
    queryKey: readonly unknown[]
  ) => {
    // Deletar do cache local
    await deleteCache(queryKey);

    // Remover do React Query
    queryClient.removeQueries({ queryKey });
  }, [queryClient]);

  return {
    setCache: setManualCache,
    getCache: getManualCache,
    deleteCache: deleteManualCache,
  };
}

// ============================================================================
// HOOK DE FALLBACK PARA COMPATIBILIDADE
// ============================================================================

/**
 * Hook de fallback que funciona sem cache local
 * Mantém a mesma interface para compatibilidade
 */
export function useUnifiedCacheFallback<T>(
  options: UnifiedCacheOptions<T>
): UnifiedCacheResult<T> {
  const { queryKey, queryFn, ...reactQueryOptions } = options;
  const queryClient = useQueryClient();

  const query = useQuery({
    queryKey,
    queryFn,
    ...reactQueryOptions,
  });

  const invalidateCache = useCallback(async () => {
    queryClient.invalidateQueries({ queryKey });
  }, [queryKey, queryClient]);

  const clearLocalCache = useCallback(async () => {
    // No-op para fallback
  }, []);

  return {
    data: query.data,
    isLoading: query.isLoading,
    error: query.error,
    refetch: query.refetch,
    isCacheHit: false, // Sempre false no fallback
    invalidateCache,
    clearLocalCache,
  };
}
