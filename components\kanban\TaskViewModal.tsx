/**
 * TaskViewModal Component
 * Modal para visualizar detalhes da tarefa com comentários
 */

import React, { useState } from 'react';
import { useSnapshot } from 'valtio';
import { formatDistanceToNow, format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import {
  Calendar,
  Clock,
  User,
  MessageSquare,
  Paperclip,
  Edit,
  Trash2,
  CheckCircle2,
  AlertTriangle,
  Tag,
  MoreHorizontal,
  Send,
  X
} from 'lucide-react';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Separator } from '../ui/separator';
import { Textarea } from '../ui/textarea';
import { ScrollArea } from '../ui/scroll-area';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '../ui/dropdown-menu';

import { kanbanStore, kanbanActions } from '../../stores/kanban-store';
import { useDeleteTask, useUpdateTask } from '../../hooks/api/use-kanban';
import { useAuth } from '../../hooks/use-auth';
import { useIsMobile } from '../../hooks/use-mobile';
import { KanbanLabels } from './KanbanLabel';
import type { Task, Label, Comment, Attachment, Checklist } from '@/schemas/kanban';

interface TaskViewModalProps {
  labels?: Label[];
}

export function TaskViewModal({
  labels = []
}: TaskViewModalProps = {}) {
  const { user } = useAuth();
  const snap = useSnapshot(kanbanStore);
  const task = snap.taskView.task;
  const isOpen = snap.taskView.isOpen;
  const deleteTaskMutation = useDeleteTask();
  const updateTaskMutation = useUpdateTask();
  const isMobile = useIsMobile();

  if (!task) return null;

  const handleClose = () => {
    kanbanActions.closeTaskView();
  };

  const handleEdit = () => {
    kanbanActions.closeTaskView();
    kanbanActions.openTaskEdit({
      ...task,
      tags: [...task.tags], // Create mutable copy
      labelIds: [...task.labelIds] // Create mutable copy
    } as any);
  };

  const handleDelete = () => {
    if (!user) return;

    deleteTaskMutation.mutate({
      boardId: task.boardId!,
      taskId: task.id,
      userId: user.$id,
    });
    handleClose();
  };

  const handleCompleteTask = () => {
    if (!user) return;

    updateTaskMutation.mutate({
      boardId: task.boardId!,
      taskId: task.id,
      taskData: {
        status: task.status === 'done' ? 'todo' : 'done',
        completedAt: task.status === 'done' ? undefined : new Date().toISOString(),
      },
      userId: user.$id,
    });
  };



  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critica':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'alta':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'media':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'baixa':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'todo':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'review':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'done':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'todo':
        return 'A Fazer';
      case 'in_progress':
        return 'Em Progresso';
      case 'review':
        return 'Em Revisão';
      case 'done':
        return 'Concluído';
      default:
        return status;
    }
  };

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case 'critica':
        return 'Crítica';
      case 'alta':
        return 'Alta';
      case 'media':
        return 'Média';
      case 'baixa':
        return 'Baixa';
      default:
        return priority;
    }
  };

  // Check if task is overdue
  const isOverdue = task.dueDate &&
    new Date(task.dueDate) < new Date() &&
    task.status !== 'done';

  // Check if task is due soon (within 24 hours)
  const isDueSoon = task.dueDate &&
    new Date(task.dueDate) > new Date() &&
    new Date(task.dueDate) < new Date(Date.now() + 24 * 60 * 60 * 1000);

  // Get labels for this task
  const taskLabels = labels.filter(label => task.labelIds?.includes(label.id));

  // Para estrutura otimizada, checklists estão embarcados na task
  const taskChecklists: any[] = [];

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <DialogTitle className="text-xl font-semibold mb-2 pr-8">
                {task.title}
              </DialogTitle>

              {/* Labels */}
              {taskLabels.length > 0 && (
                <div className="mb-3">
                  <KanbanLabels
                    labels={taskLabels}
                    maxVisible={10}
                    size="md"
                    showText={true}
                  />
                </div>
              )}

              <div className="flex items-center gap-2 flex-wrap">
                <Badge className={getStatusColor(task.status)}>
                  {getStatusLabel(task.status)}
                </Badge>
                <Badge className={getPriorityColor(task.priority)}>
                  {getPriorityLabel(task.priority)}
                </Badge>
                {isOverdue && (
                  <Badge variant="destructive" className="flex items-center gap-1">
                    <AlertTriangle className="h-3 w-3" />
                    Atrasada
                  </Badge>
                )}
                {isDueSoon && (
                  <Badge variant="outline" className="flex items-center gap-1 text-orange-600">
                    <Clock className="h-3 w-3" />
                    Vence em breve
                  </Badge>
                )}
              </div>
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={handleEdit}>
                  <Edit className="h-4 w-4 mr-2" />
                  Editar
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleCompleteTask}>
                  <CheckCircle2 className="h-4 w-4 mr-2" />
                  {task.status === 'done' ? 'Marcar como pendente' : 'Marcar como concluída'}
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={handleDelete}
                  className="text-destructive"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Excluir
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <div className={`grid gap-6 h-full ${isMobile ? 'grid-cols-1' : 'grid-cols-3'}`}>
            {/* Main Content */}
            <div className={`${isMobile ? 'col-span-1' : 'col-span-2'} space-y-6 overflow-y-auto`}>
              {/* Description */}
              {task.description && (
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium">Descrição</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground whitespace-pre-wrap">
                      {task.description}
                    </p>
                  </CardContent>
                </Card>
              )}

              {/* Funcionalidades futuras: Checklists, Attachments, Comments */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium">Recursos Avançados</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Checklists, anexos e comentários serão implementados na estrutura otimizada.
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-4 overflow-y-auto">
              {/* Task Details */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium">Detalhes</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {/* Assigned User */}
                  {task.assignedTo && (
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <div className="flex items-center gap-2">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={`/api/avatar/${task.assignedTo}`} />
                          <AvatarFallback className="text-xs">
                            {task.assignedTo.slice(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-sm">Atribuído</span>
                      </div>
                    </div>
                  )}

                  {/* Due Date */}
                  {task.dueDate && (
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <div className="flex flex-col">
                        <span className="text-sm">
                          {format(new Date(task.dueDate), 'dd/MM/yyyy', { locale: ptBR })}
                        </span>
                        <span className={`text-xs ${
                          isOverdue ? 'text-destructive' :
                          isDueSoon ? 'text-orange-600' :
                          'text-muted-foreground'
                        }`}>
                          {formatDistanceToNow(new Date(task.dueDate), {
                            addSuffix: true,
                            locale: ptBR
                          })}
                        </span>
                      </div>
                    </div>
                  )}

                  {/* Time Tracking */}
                  {(task.estimatedHours || task.actualHours) && (
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <div className="flex flex-col">
                        {task.actualHours && (
                          <span className="text-sm">{task.actualHours}h trabalhadas</span>
                        )}
                        {task.estimatedHours && (
                          <span className="text-xs text-muted-foreground">
                            {task.estimatedHours}h estimadas
                          </span>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Tags */}
                  {task.tags && task.tags.length > 0 && (
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Tag className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">Tags</span>
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {task.tags.map((tag, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Created/Updated */}
                  <Separator />
                  <div className="space-y-2 text-xs text-muted-foreground">
                    <div>
                      Criada {formatDistanceToNow(new Date(task.createdAt), {
                        addSuffix: true,
                        locale: ptBR
                      })}
                    </div>
                    {task.updatedAt !== task.createdAt && (
                      <div>
                        Atualizada {formatDistanceToNow(new Date(task.updatedAt!), {
                          addSuffix: true,
                          locale: ptBR
                        })}
                      </div>
                    )}
                    {task.completedAt && (
                      <div>
                        Concluída {formatDistanceToNow(new Date(task.completedAt), {
                          addSuffix: true,
                          locale: ptBR
                        })}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
