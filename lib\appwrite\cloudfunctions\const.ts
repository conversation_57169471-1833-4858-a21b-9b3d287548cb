/**
 * Cloud Functions Configuration Constants
 * Centralized configuration for all Appwrite cloud function URLs and settings
 *
 * This file allows the application to function gracefully when URLs are not configured
 * by providing fallback behavior and proper error handling.
 */

/**
 * Cloud Functions Configuration
 *
 * Configuração das URLs das cloud functions do Appwrite.
 * Apenas funções essenciais que precisam ser server-side.
 *
 * IMPORTANTE: Configure as URLs no arquivo .env do projeto:
 * NEXT_PUBLIC_CLOUDFUNCTION_[NOME]=https://cloud.appwrite.io/v1/functions/[function-id]/executions
 */

// Environment-based cloud function URLs
// APENAS para funcionalidades que requerem dados sensíveis/segurança
export const CLOUD_FUNCTIONS = {
  // Admin Functions - Operações administrativas sensíveis
  ADMIN_USERS: process.env.NEXT_PUBLIC_CLOUDFUNCTION_ADMIN_USERS || '',
  ADMIN_USER_MANAGEMENT: process.env.NEXT_PUBLIC_CLOUDFUNCTION_ADMIN_USER_MANAGEMENT || '',

  // AI Functions - Processamento com chaves API sensíveis
  GEMINI_FILE_PROCESSOR: process.env.NEXT_PUBLIC_CLOUDFUNCTION_GEMINI_FILE_PROCESSOR || '',
  GEMINI_DATA_PROCESSOR: process.env.NEXT_PUBLIC_CLOUDFUNCTION_GEMINI_DATA_PROCESSOR || '',

  // Payment Functions - Chaves secretas de pagamento
  STRIPE_PAYMENTS: process.env.NEXT_PUBLIC_CLOUDFUNCTION_STRIPE_PAYMENTS || '',
} as const;

// Function execution configuration
export const FUNCTION_CONFIG = {
  // Default timeout for function execution (in milliseconds)
  DEFAULT_TIMEOUT: 30000,

  // Retry configuration
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000,

  // Fallback behavior
  ENABLE_FALLBACKS: true,
  SHOW_FALLBACK_MESSAGES: process.env.NODE_ENV === "development",
} as const;

// Function categories for organization
// APENAS funções essenciais que requerem processamento server-side
export const FUNCTION_CATEGORIES = {
  ADMIN: ['ADMIN_USERS', 'ADMIN_USER_MANAGEMENT'],
  AI: ['GEMINI_FILE_PROCESSOR', 'GEMINI_DATA_PROCESSOR'],
  PAYMENT: ['STRIPE_PAYMENTS'],
} as const;

/**
 * Check if a cloud function is configured
 */
export function isFunctionConfigured(functionName: keyof typeof CLOUD_FUNCTIONS): boolean {
  return Boolean(CLOUD_FUNCTIONS[functionName]);
}

/**
 * Get cloud function URL with validation
 */
export function getFunctionUrl(functionName: keyof typeof CLOUD_FUNCTIONS): string | null {
  const url = CLOUD_FUNCTIONS[functionName];
  return url || null;
}

/**
 * Check if any functions in a category are configured
 */
export function isCategoryConfigured(category: keyof typeof FUNCTION_CATEGORIES): boolean {
  const functions = FUNCTION_CATEGORIES[category];
  return functions.some(fn => isFunctionConfigured(fn as keyof typeof CLOUD_FUNCTIONS));
}

/**
 * Get all configured functions
 */
export function getConfiguredFunctions(): Array<keyof typeof CLOUD_FUNCTIONS> {
  return Object.keys(CLOUD_FUNCTIONS).filter(fn =>
    isFunctionConfigured(fn as keyof typeof CLOUD_FUNCTIONS)
  ) as Array<keyof typeof CLOUD_FUNCTIONS>;
}

/**
 * Get configuration status for debugging
 */
export function getFunctionConfigStatus() {
  const configured = getConfiguredFunctions();
  const total = Object.keys(CLOUD_FUNCTIONS).length;

  return {
    configured: configured.length,
    total,
    percentage: Math.round((configured.length / total) * 100),
    functions: configured,
    missing: Object.keys(CLOUD_FUNCTIONS).filter(fn =>
      !isFunctionConfigured(fn as keyof typeof CLOUD_FUNCTIONS)
    ),
  };
}

// Log configuration status in development
if (process.env.NODE_ENV === "development") {
  import('@/lib/logger').then(({ log }) => {
    const status = getFunctionConfigStatus();
    log.function('Cloud Functions Configuration', {
      configured: `${status.configured}/${status.total} (${status.percentage}%)`,
      functions: status.functions.length > 0 ? '✅ ' + status.functions.join(', ') : '❌ None configured',
      missing: status.missing.length > 0 ? '⚠️ ' + status.missing.join(', ') : '✅ All configured',
    });
  });
}

// Export types for TypeScript
export type CloudFunctionName = keyof typeof CLOUD_FUNCTIONS;
export type FunctionCategory = keyof typeof FUNCTION_CATEGORIES;
