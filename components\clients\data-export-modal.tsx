/**
 * Enhanced modal for data export with improved PDF limitations and configuration
 * Supports multiple formats with format-specific settings and controls
 */

import { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { <PERSON><PERSON> } from '../ui/button';
import { Label } from '../ui/label';
import { Switch } from '../ui/switch';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '../ui/alert';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Checkbox } from '../ui/checkbox';
import { Input } from '../ui/input';
import { Slider } from '../ui/slider';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import {
  Ta<PERSON>,
  Ta<PERSON>Content,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from '../ui/tabs';
import {
  Download,
  FileText,
  FileSpreadsheet,
  Code,
  Calendar,
  Filter,
  Crown,
  CheckCircle,
  Loader2,
  ExternalLink,
  AlertTriangle,
  Settings,
  Eye,
  Info,
} from 'lucide-react';
import { useEnhancedExport } from '../../hooks/use-enhanced-export';
import { useExportConfig } from '../../hooks/use-export-config';
import { useClients } from '../../hooks/use-api';
import { useTeams } from '../../hooks/use-api';
import { DatePickerWithRange } from '../ui/date-range-picker';
import { addDays } from 'date-fns';
import type { DateRange } from 'react-day-picker';
import { toast } from 'sonner';
import { EXPORT_FORMATS, PDF_LIMITATIONS, type ExportFormat } from '../../schemas/export';

interface DataExportModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  collection?: string;
  defaultFilters?: Record<string, unknown>;
}

const STATUS_OPTIONS = [
  { value: 'ativo', label: 'Ativo' },
  { value: 'inativo', label: 'Inativo' },
  { value: 'prospecto', label: 'Prospecto' },
  { value: 'arquivado', label: 'Arquivado' },
];

const PRIORITY_OPTIONS = [
  { value: 'baixa', label: 'Baixa' },
  { value: 'media', label: 'Média' },
  { value: 'alta', label: 'Alta' },
  { value: 'critica', label: 'Crítica' },
];

const FORMAT_ICONS = {
  csv: FileText,
  excel: FileSpreadsheet,
  json: Code,
  pdf: FileText,
} as const;

export function DataExportModal({
  open,
  onOpenChange,
  collection = 'clients',
  defaultFilters = {}
}: DataExportModalProps) {
  // Get data for the collection
  const { data: allClients = [] } = useClients();
  const { data: allTeams = [] } = useTeams();
  const allData = collection === 'teams' ? allTeams : allClients;

  // Date range filter
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: addDays(new Date(), -30),
    to: new Date(),
  });

  // Status and priority filters (for clients only)
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([]);
  const [selectedPriorities, setSelectedPriorities] = useState<string[]>([]);

  // Enhanced export hooks
  const {
    isExporting,
    exportResult,
    error,
    canUseExport,
    exportData,
    reset,
  } = useEnhancedExport({ collection });

  const {
    config,
    availableFields,
    updateFormat,
    updateSelectedFields,
    toggleField,
    toggleIncludeAllFields,
    updatePDFSettings,
    updateSettings,
    generatePreview,
    validateConfig,
    getFilteredFields,
    getFormatInfo,
    isFieldSelected,
    isFieldRequired,
    isFieldPDFSupported,
  } = useExportConfig({
    collection,
    totalRecords: allData.length,
    data: allData,
  });

  // Generate preview when config changes
  const [preview, setPreview] = useState(() => generatePreview());

  useEffect(() => {
    setPreview(generatePreview());
  }, [generatePreview]);

  // Filter data based on selected filters
  const getFilteredData = () => {
    let filteredData = [...allData];

    // Apply date range filter
    if (dateRange?.from && dateRange?.to) {
      filteredData = filteredData.filter(item => {
        const itemDate = new Date(item.$createdAt);
        return itemDate >= dateRange.from! && itemDate <= dateRange.to!;
      });
    }

    // Apply status filter (clients only)
    if (collection === 'clients' && selectedStatuses.length > 0) {
      filteredData = filteredData.filter(item =>
        selectedStatuses.includes((item as any).status)
      );
    }

    // Apply priority filter (clients only)
    if (collection === 'clients' && selectedPriorities.length > 0) {
      filteredData = filteredData.filter(item =>
        selectedPriorities.includes((item as any).priority)
      );
    }

    return filteredData;
  };

  const handleExport = async () => {
    const validation = validateConfig();
    if (!validation.isValid) {
      validation.errors.forEach(error => toast.error(error));
      return;
    }

    const filteredData = getFilteredData();

    // Update config with date range
    const finalConfig = {
      ...config,
      dateRange: dateRange ? {
        start: dateRange.from!.toISOString(),
        end: dateRange.to!.toISOString(),
      } : undefined,
      filters: {
        ...defaultFilters,
        status: selectedStatuses,
        priority: selectedPriorities,
      },
    };

    await exportData(filteredData, finalConfig);
  };

  const handleClose = () => {
    reset();
    onOpenChange(false);
  };

  // Show upgrade message if user doesn't have export access
  if (!canUseExport) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Crown className="h-5 w-5 text-yellow-500" />
              Acesso Restrito
            </DialogTitle>
            <DialogDescription>
              Você não tem permissão para exportar dados
            </DialogDescription>
          </DialogHeader>

          <Card className="border-yellow-200 bg-yellow-50">
            <CardHeader>
              <CardTitle className="text-lg">Funcionalidade Restrita</CardTitle>
              <CardDescription>
                Entre em contato com o administrador para obter acesso
              </CardDescription>
            </CardHeader>
          </Card>

          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)} className="flex-1">
              Fechar
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export de Dados - {collection === 'teams' ? 'Times' : 'Clientes'}
          </DialogTitle>
          <DialogDescription>
            Configure e exporte dados em diferentes formatos com controles avançados
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="format" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="format">Formato</TabsTrigger>
            <TabsTrigger value="fields">Campos</TabsTrigger>
            <TabsTrigger value="filters">Filtros</TabsTrigger>
            <TabsTrigger value="preview">Prévia</TabsTrigger>
          </TabsList>

          {/* Format Selection Tab */}
          <TabsContent value="format" className="space-y-4">
            <div className="space-y-3">
              <Label>Formato do Arquivo</Label>
              <div className="grid grid-cols-2 gap-3">
                {EXPORT_FORMATS.map((format) => {
                  const Icon = FORMAT_ICONS[format.value];
                  const isSelected = config.format === format.value;
                  const isPDF = format.value === 'pdf';

                  return (
                    <Card
                      key={format.value}
                      className={`cursor-pointer transition-colors ${
                        isSelected
                          ? 'border-primary bg-primary/5'
                          : 'hover:border-primary/50'
                      }`}
                      onClick={() => updateFormat(format.value)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center gap-3">
                          <Icon className="h-8 w-8 text-primary" />
                          <div className="flex-1">
                            <p className="font-medium">{format.label}</p>
                            <p className="text-xs text-muted-foreground">
                              {format.description}
                            </p>
                            {isPDF && (
                              <p className="text-xs text-orange-600 mt-1">
                                Limitado a {PDF_LIMITATIONS.DEFAULT_MAX_ROWS} registros
                              </p>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </div>

            {/* Format-specific settings */}
            {config.format === 'pdf' && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Configurações PDF
                  </CardTitle>
                  <CardDescription>
                    Configure limitações específicas para exportação PDF
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Máximo de Registros</Label>
                      <div className="space-y-2">
                        <Slider
                          value={[config.pdfSettings?.maxRows || PDF_LIMITATIONS.DEFAULT_MAX_ROWS]}
                          onValueChange={([value]) => updatePDFSettings({ maxRows: value })}
                          max={PDF_LIMITATIONS.MAX_ROWS_LIMIT}
                          min={10}
                          step={10}
                          className="w-full"
                        />
                        <div className="flex justify-between text-xs text-muted-foreground">
                          <span>10</span>
                          <span>{config.pdfSettings?.maxRows || PDF_LIMITATIONS.DEFAULT_MAX_ROWS}</span>
                          <span>{PDF_LIMITATIONS.MAX_ROWS_LIMIT}</span>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Máximo de Colunas</Label>
                      <div className="space-y-2">
                        <Slider
                          value={[config.pdfSettings?.maxColumns || PDF_LIMITATIONS.DEFAULT_MAX_COLUMNS]}
                          onValueChange={([value]) => updatePDFSettings({ maxColumns: value })}
                          max={PDF_LIMITATIONS.MAX_COLUMNS_LIMIT}
                          min={3}
                          step={1}
                          className="w-full"
                        />
                        <div className="flex justify-between text-xs text-muted-foreground">
                          <span>3</span>
                          <span>{config.pdfSettings?.maxColumns || PDF_LIMITATIONS.DEFAULT_MAX_COLUMNS}</span>
                          <span>{PDF_LIMITATIONS.MAX_COLUMNS_LIMIT}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Orientação</Label>
                      <Select
                        value={config.pdfSettings?.orientation || 'portrait'}
                        onValueChange={(value: 'portrait' | 'landscape') =>
                          updatePDFSettings({ orientation: value })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="portrait">Retrato</SelectItem>
                          <SelectItem value="landscape">Paisagem</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Tamanho da Fonte</Label>
                      <Select
                        value={config.pdfSettings?.fontSize || 'medium'}
                        onValueChange={(value: 'small' | 'medium' | 'large') =>
                          updatePDFSettings({ fontSize: value })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="small">Pequena</SelectItem>
                          <SelectItem value="medium">Média</SelectItem>
                          <SelectItem value="large">Grande</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Fields Selection Tab */}
          <TabsContent value="fields" className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label>Campos para Export</Label>
                <div className="flex items-center gap-2">
                  <Switch
                    checked={config.includeAllFields}
                    onCheckedChange={toggleIncludeAllFields}
                  />
                  <Label className="text-sm">Incluir todos os campos</Label>
                </div>
              </div>

              {config.format === 'pdf' && (
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    Alguns campos podem não ser suportados em PDF ou serem limitados devido ao espaço disponível.
                    Campos não suportados: {getFilteredFields().filter(f => !f.pdfSupported).map(f => f.label).join(', ') || 'Nenhum'}
                  </AlertDescription>
                </Alert>
              )}

              <div className="grid grid-cols-2 gap-2 max-h-60 overflow-y-auto border rounded-lg p-4">
                {availableFields.map((field) => {
                  const isSelected = isFieldSelected(field.id);
                  const isRequired = isFieldRequired(field.id);
                  const isPDFSupported = isFieldPDFSupported(field.id);
                  const isDisabled = config.format === 'pdf' && !isPDFSupported;

                  return (
                    <div key={field.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={field.id}
                        checked={isSelected}
                        onCheckedChange={(checked) => toggleField(field.id)}
                        disabled={isRequired || isDisabled}
                      />
                      <Label
                        htmlFor={field.id}
                        className={`text-sm flex-1 ${
                          isRequired ? 'font-medium' : ''
                        } ${isDisabled ? 'text-muted-foreground line-through' : ''}`}
                      >
                        {field.label}
                        {isRequired && <span className="text-red-500 ml-1">*</span>}
                        {config.format === 'pdf' && !isPDFSupported && (
                          <span className="text-orange-500 ml-1 text-xs">(não suportado em PDF)</span>
                        )}
                      </Label>
                    </div>
                  );
                })}
              </div>

              <div className="text-sm text-muted-foreground">
                {config.selectedFields.length} de {availableFields.length} campos selecionados
                {config.format === 'pdf' && (
                  <span className="text-orange-600 ml-2">
                    (máximo {config.pdfSettings?.maxColumns || PDF_LIMITATIONS.DEFAULT_MAX_COLUMNS} para PDF)
                  </span>
                )}
              </div>
            </div>
          </TabsContent>

          {/* Filters Tab */}
          <TabsContent value="filters" className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                <Label>Filtros de Dados</Label>
              </div>

              {/* Período */}
              <div className="space-y-2">
                <Label className="text-sm">Período</Label>
                <DatePickerWithRange
                  date={dateRange}
                  onDateChange={setDateRange}
                  placeholder="Selecionar período"
                />
              </div>

              {/* Status - apenas para clientes */}
              {collection === 'clients' && (
                <div className="space-y-2">
                  <Label className="text-sm">Status</Label>
                  <div className="flex flex-wrap gap-2">
                    {STATUS_OPTIONS.map((status) => (
                      <Badge
                        key={status.value}
                        variant={selectedStatuses.includes(status.value) ? "default" : "outline"}
                        className="cursor-pointer"
                        onClick={() => {
                          setSelectedStatuses(prev =>
                            prev.includes(status.value)
                              ? prev.filter(s => s !== status.value)
                              : [...prev, status.value]
                          );
                        }}
                      >
                        {status.label}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Prioridade - apenas para clientes */}
              {collection === 'clients' && (
                <div className="space-y-2">
                  <Label className="text-sm">Prioridade</Label>
                  <div className="flex flex-wrap gap-2">
                    {PRIORITY_OPTIONS.map((priority) => (
                      <Badge
                        key={priority.value}
                        variant={selectedPriorities.includes(priority.value) ? "default" : "outline"}
                        className="cursor-pointer"
                        onClick={() => {
                          setSelectedPriorities(prev =>
                            prev.includes(priority.value)
                              ? prev.filter(p => p !== priority.value)
                              : [...prev, priority.value]
                          );
                        }}
                      >
                        {priority.label}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Opções Avançadas */}
              <div className="space-y-3">
                <Label>Opções Avançadas</Label>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-sm">Incluir Metadados</Label>
                    <p className="text-xs text-muted-foreground">
                      Adicionar informações de criação e atualização
                    </p>
                  </div>
                  <Switch
                    checked={config.includeMetadata}
                    onCheckedChange={(checked) =>
                      updateSettings({ includeMetadata: checked })
                    }
                  />
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Preview Tab */}
          <TabsContent value="preview" className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                <Label>Prévia da Exportação</Label>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Resumo da Exportação</CardTitle>
                  <CardDescription>
                    Verifique os dados que serão exportados antes de prosseguir
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium">Total de Registros</Label>
                      <p className="text-2xl font-bold">{preview.totalRecords}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Registros a Exportar</Label>
                      <p className="text-2xl font-bold text-primary">{preview.recordsToExport}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Campos Selecionados</Label>
                      <p className="text-2xl font-bold">{preview.fieldsToExport.length}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Tamanho Estimado</Label>
                      <p className="text-2xl font-bold">{preview.estimatedFileSize}</p>
                    </div>
                  </div>

                  {preview.warnings.length > 0 && (
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-orange-600">Avisos</Label>
                      {preview.warnings.map((warning, index) => (
                        <Alert key={index} className="border-orange-200 bg-orange-50">
                          <AlertTriangle className="h-4 w-4 text-orange-600" />
                          <AlertDescription className="text-orange-800">
                            {warning}
                          </AlertDescription>
                        </Alert>
                      ))}
                    </div>
                  )}

                  {preview.limitations?.suggestedAlternatives && preview.limitations.suggestedAlternatives.length > 0 && (
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Formatos Alternativos Sugeridos</Label>
                      <div className="flex gap-2">
                        {preview.limitations.suggestedAlternatives.map((format) => {
                          const formatInfo = getFormatInfo(format);
                          return (
                            <Button
                              key={format}
                              variant="outline"
                              size="sm"
                              onClick={() => updateFormat(format)}
                            >
                              {formatInfo?.label}
                            </Button>
                          );
                        })}
                      </div>
                    </div>
                  )}

                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Campos a Exportar</Label>
                    <div className="flex flex-wrap gap-1 max-h-20 overflow-y-auto">
                      {preview.fieldsToExport.map((fieldId) => {
                        const field = availableFields.find(f => f.id === fieldId);
                        return (
                          <Badge key={fieldId} variant="secondary" className="text-xs">
                            {field?.label || fieldId}
                          </Badge>
                        );
                      })}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* Resultado do Export */}
        {exportResult && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between">
              <div>
                <p>Export concluído! Arquivo: {exportResult.fileName}</p>
                {exportResult.warnings && exportResult.warnings.length > 0 && (
                  <p className="text-sm text-orange-600 mt-1">
                    {exportResult.warnings.join(', ')}
                  </p>
                )}
              </div>
            </AlertDescription>
          </Alert>
        )}

        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Botões */}
        <div className="flex gap-2 pt-4">
          <Button variant="outline" onClick={handleClose} className="flex-1">
            Cancelar
          </Button>
          <Button
            onClick={handleExport}
            disabled={isExporting || config.selectedFields.length === 0}
            className="flex-1"
          >
            {isExporting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Exportando...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Exportar {preview.recordsToExport} registros
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
