/**
 * Sistema de Logging Simplificado
 * Mantém todas as funcionalidades essenciais com muito menos complexidade
 */

// ============================================================================
// TIPOS E CONFIGURAÇÃO
// ============================================================================

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';
export type LogType =
  | 'info' | 'success' | 'warning' | 'error' | 'debug' | 'working'
  | 'cache' | 'api' | 'database' | 'auth' | 'team' | 'notification'
  | 'function' | 'performance' | 'realtime' | 'pwa' | 'network' | 'system';

interface LogEntry {
  level: LogLevel;
  type: LogType;
  message: string;
  timestamp: string;
  context?: Record<string, unknown>;
  error?: Error;
}

// Configuração centralizada para cada tipo de log
const LOG_CONFIG: Record<LogType, { icon: string; level: LogLevel; color?: string }> = {
  // Logs básicos
  info: { icon: 'ℹ️', level: 'info' },
  success: { icon: '✅', level: 'info' },
  warning: { icon: '⚠️', level: 'warn' },
  error: { icon: '❌', level: 'error' },
  debug: { icon: '🐛', level: 'debug' },
  working: { icon: '⚙️', level: 'info' },

  // Logs específicos por contexto
  cache: { icon: '💾', level: 'info' },
  api: { icon: '🌐', level: 'info' },
  database: { icon: '🗄️', level: 'info' },
  auth: { icon: '🔐', level: 'info' },
  team: { icon: '👥', level: 'info' },
  notification: { icon: '🔔', level: 'info' },
  function: { icon: '⚡', level: 'info' },
  performance: { icon: '📊', level: 'info' },
  realtime: { icon: '🔄', level: 'debug' },
  pwa: { icon: '📱', level: 'info' },
  network: { icon: '🌐', level: 'debug' },
  system: { icon: '🖥️', level: 'info' },
};

// Configuração por ambiente
const ENV_CONFIG = {
  development: {
    minLevel: 'debug' as LogLevel,
    enableConsole: true,
    enableStorage: true,
    enableIcons: true,
    maxStorageEntries: 1000,
  },
  production: {
    minLevel: 'info' as LogLevel,
    enableConsole: true,
    enableStorage: true,
    enableIcons: false,
    maxStorageEntries: 500,
  },
  test: {
    minLevel: 'error' as LogLevel,
    enableConsole: false,
    enableStorage: false,
    enableIcons: false,
    maxStorageEntries: 0,
  },
};

// ============================================================================
// UTILITÁRIOS
// ============================================================================

const LOG_LEVELS: Record<LogLevel, number> = {
  debug: 0,
  info: 1,
  warn: 2,
  error: 3,
};

// Detectar ambiente atual
function getCurrentEnv(): keyof typeof ENV_CONFIG {
  if (typeof window === 'undefined') return 'production';
  if (process.env.MODE === 'test') return 'test';
  if (process.env.NODE_ENV === "development") return 'development';
  return 'production';
}

const currentConfig = ENV_CONFIG[getCurrentEnv()];

// Storage simples para logs
let logStorage: LogEntry[] = [];

// Rate limiting simples
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT_WINDOW = 1000; // 1 segundo
const RATE_LIMIT_MAX = 10; // máximo 10 logs por segundo por tipo

function canLog(type: LogType): boolean {
  const now = Date.now();
  const key = type;
  const existing = rateLimitMap.get(key);

  if (!existing || now >= existing.resetTime) {
    rateLimitMap.set(key, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return true;
  }

  if (existing.count >= RATE_LIMIT_MAX) {
    return false;
  }

  existing.count++;
  return true;
}

// Sanitizar dados sensíveis
function sanitizeContext(context?: Record<string, unknown>): Record<string, unknown> {
  if (!context) return {};

  const sanitized = { ...context };
  const sensitiveFields = ['password', 'token', 'secret', 'key', 'authorization', 'cookie'];

  sensitiveFields.forEach(field => {
    if (field in sanitized) {
      sanitized[field] = '[REDACTED]';
    }
  });

  return sanitized;
}

// Verificar se deve logar baseado no nível
function shouldLog(level: LogLevel): boolean {
  return LOG_LEVELS[level] >= LOG_LEVELS[currentConfig.minLevel];
}

// ============================================================================
// FUNÇÃO PRINCIPAL DE LOG
// ============================================================================

function writeLog(
  type: LogType,
  message: string,
  context?: Record<string, unknown>,
  error?: Error
): void {
  const config = LOG_CONFIG[type];
  const level = config.level;

  // Verificar se deve logar
  if (!shouldLog(level)) return;

  // Rate limiting
  if (!canLog(type)) return;

  // Criar entrada de log
  const entry: LogEntry = {
    level,
    type,
    message,
    timestamp: new Date().toISOString(),
    context: sanitizeContext(context),
    error,
  };

  // Log no console
  if (currentConfig.enableConsole) {
    logToConsole(entry);
  }

  // Armazenar localmente
  if (currentConfig.enableStorage) {
    logToStorage(entry);
  }
}

function logToConsole(entry: LogEntry): void {
  const config = LOG_CONFIG[entry.type];
  const timestamp = new Date(entry.timestamp).toLocaleTimeString('pt-BR');
  const icon = currentConfig.enableIcons ? config.icon : '';
  const prefix = `${icon} [${timestamp}] ${entry.type.toUpperCase()}:`;
  const message = `${prefix} ${entry.message}`;

  const args: unknown[] = [message];
  if (entry.context && Object.keys(entry.context).length > 0) {
    args.push(entry.context);
  }
  if (entry.error) {
    args.push(entry.error);
  }

  // Usar método apropriado do console
  switch (entry.level) {
    case 'debug':
      console.debug(...args);
      break;
    case 'info':
      console.info(...args);
      break;
    case 'warn':
      console.warn(...args);
      break;
    case 'error':
      console.error(...args);
      break;
  }
}

function logToStorage(entry: LogEntry): void {
  logStorage.push(entry);

  // Manter apenas as últimas entradas
  if (logStorage.length > currentConfig.maxStorageEntries) {
    logStorage = logStorage.slice(-currentConfig.maxStorageEntries);
  }
}

// ============================================================================
// API PÚBLICA SIMPLIFICADA
// ============================================================================

/**
 * Logger principal com API limpa
 */
export const log = {
  // Logs básicos
  info: (message: string, context?: Record<string, unknown>) =>
    writeLog('info', message, context),

  success: (message: string, context?: Record<string, unknown>) =>
    writeLog('success', message, context),

  warn: (message: string, context?: Record<string, unknown>) =>
    writeLog('warning', message, context),

  error: (message: string, error?: Error | unknown, context?: Record<string, unknown>) => {
    const errorObj = error instanceof Error ? error : undefined;
    const finalContext = error instanceof Error ? context : { ...context, error };
    writeLog('error', message, finalContext, errorObj);
  },

  debug: (message: string, context?: Record<string, unknown>) =>
    writeLog('debug', message, context),

  working: (message: string, context?: Record<string, unknown>) =>
    writeLog('working', message, context),

  // Logs específicos por contexto
  cache: (message: string, data?: unknown) =>
    writeLog('cache', message, { data }),

  api: (message: string, context?: Record<string, unknown>) =>
    writeLog('api', message, context),

  database: (message: string, context?: Record<string, unknown>) =>
    writeLog('database', message, context),

  auth: (message: string, context?: Record<string, unknown>) =>
    writeLog('auth', message, context),

  team: (message: string, context?: Record<string, unknown>) =>
    writeLog('team', message, context),

  notification: (message: string, context?: Record<string, unknown>) =>
    writeLog('notification', message, context),

  function: (message: string, context?: Record<string, unknown>) =>
    writeLog('function', message, context),

  performance: (operation: string, duration: number, context?: Record<string, unknown>) => {
    const icon = duration > 1000 ? '🐌' : duration > 500 ? '⏱️' : '⚡';
    writeLog('performance', `${icon} ${operation} (${duration}ms)`, { duration, ...context });
  },

  realtime: (message: string, context?: Record<string, unknown>) =>
    writeLog('realtime', message, context),

  pwa: (message: string, context?: Record<string, unknown>) =>
    writeLog('pwa', message, context),

  network: (message: string, context?: Record<string, unknown>) =>
    writeLog('network', message, context),

  system: (message: string, context?: Record<string, unknown>) =>
    writeLog('system', message, context),

  // Utilitários
  getLogs: (type?: LogType, limit?: number): LogEntry[] => {
    let logs = logStorage;

    if (type) {
      logs = logs.filter(entry => entry.type === type);
    }

    if (limit) {
      logs = logs.slice(-limit);
    }

    return logs;
  },

  clearLogs: (): void => {
    logStorage = [];
  },

  exportLogs: (): string => {
    return JSON.stringify(logStorage, null, 2);
  },
};

// Função de conveniência para timing automático
export async function withTiming<T>(
  operation: string,
  fn: () => Promise<T>,
  context?: Record<string, unknown>
): Promise<T> {
  const startTime = Date.now();
  log.working(`Starting ${operation}`, context);

  try {
    const result = await fn();
    const duration = Date.now() - startTime;
    log.performance(operation, duration, context);
    return result;
  } catch (error) {
    const duration = Date.now() - startTime;
    log.error(`Failed ${operation} after ${duration}ms`, error, context);
    throw error;
  }
}

// Export para compatibilidade com código existente
export { log as logger };
export default log;
