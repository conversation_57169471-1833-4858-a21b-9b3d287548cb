/**
 * Users API Hooks
 * Simple hooks for user data
 */

import { useQuery } from '@tanstack/react-query';
import { useAuth } from '../use-auth';
import { useAllUsers } from './use-admin';
import type { Models } from 'appwrite';

export interface User extends Models.User<Models.Preferences> {}

/**
 * Get users - uses admin hook if available, otherwise returns empty array
 * This is a simplified version for the kanban filters
 */
export function useUsers() {
  const { user } = useAuth();
  const { data: adminUsers, isLoading, error } = useAllUsers(undefined, 100, false); // Don't auto-enable

  return useQuery({
    queryKey: ['users', user?.$id],
    queryFn: async (): Promise<User[]> => {
      // If we have admin users data, return it
      if (adminUsers && adminUsers.length > 0) {
        return adminUsers.map(adminUser => ({
          $id: adminUser.$id,
          name: adminUser.name,
          email: adminUser.email,
          status: true,
          labels: [],
          $createdAt: adminUser.$createdAt,
          $updatedAt: adminUser.$updatedAt,
          registration: adminUser.$createdAt,
          emailVerification: true,
          phoneVerification: false,
          prefs: {},
        } as unknown as User));
      }

      // For non-admin users, return just the current user
      if (user) {
        return [user as User];
      }

      return [];
    },
    enabled: !!user?.$id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

/**
 * Get user by ID
 */
export function useUser(userId: string) {
  const { data: users } = useUsers();

  return useQuery({
    queryKey: ['user', userId],
    queryFn: async (): Promise<User | null> => {
      if (!users || !userId) return null;
      return users.find(user => user.$id === userId) || null;
    },
    enabled: !!userId && !!users,
    staleTime: 5 * 60 * 1000,
  });
}

/**
 * Get current user data
 */
export function useCurrentUser() {
  const { user } = useAuth();
  return {
    data: user as User | null,
    isLoading: false,
    error: null,
  };
}
