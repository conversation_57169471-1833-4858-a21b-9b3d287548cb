/**
 * Componente para gerenciar cache em caso de problemas
 * Permite limpeza manual quando há erros persistentes do IndexedDB
 */

import { useState } from 'react';
import { Button } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { AlertTriangle, Trash2, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';
import { clearCacheManually } from '../lib/cache-sync';

interface CacheManagerProps {
  onCacheCleared?: () => void;
}

export function CacheManager({ onCacheCleared }: CacheManagerProps) {
  const [isClearing, setIsClearing] = useState(false);

  const handleClearCache = async () => {
    if (!confirm('Tem certeza que deseja limpar todo o cache? Isso irá remover todos os dados salvos localmente.')) {
      return;
    }

    setIsClearing(true);
    
    try {
      await clearCacheManually();
      toast.success('Cache limpo com sucesso! A página será recarregada.');
      
      // Notificar componente pai
      onCacheCleared?.();
      
      // Recarregar página após um delay
      setTimeout(() => {
        window.location.reload();
      }, 1000);
      
    } catch (error) {
      console.error('Erro ao limpar cache:', error);
      toast.error('Erro ao limpar cache. Tente recarregar a página.');
    } finally {
      setIsClearing(false);
    }
  };

  return (
    <Card className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-orange-800 dark:text-orange-200">
          <AlertTriangle className="h-5 w-5" />
          Gerenciador de Cache
        </CardTitle>
        <CardDescription className="text-orange-700 dark:text-orange-300">
          Use esta opção se estiver enfrentando problemas persistentes com o cache local.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-sm text-orange-700 dark:text-orange-300">
          <p className="mb-2">
            <strong>Quando usar:</strong>
          </p>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li>Erros de versão do IndexedDB</li>
            <li>Dados não carregando corretamente</li>
            <li>Problemas de sincronização persistentes</li>
            <li>Mensagens de erro relacionadas ao cache</li>
          </ul>
        </div>
        
        <div className="flex gap-2">
          <Button
            onClick={handleClearCache}
            disabled={isClearing}
            variant="destructive"
            size="sm"
            className="flex items-center gap-2"
          >
            {isClearing ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <Trash2 className="h-4 w-4" />
            )}
            {isClearing ? 'Limpando...' : 'Limpar Cache'}
          </Button>
          
          <Button
            onClick={() => window.location.reload()}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Recarregar Página
          </Button>
        </div>
        
        <div className="text-xs text-orange-600 dark:text-orange-400">
          ⚠️ Limpar o cache irá remover todos os dados salvos localmente e recarregar a página.
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Hook para detectar erros de cache e mostrar opções de recuperação
 */
export function useCacheErrorDetection() {
  const [hasError, setHasError] = useState(false);
  const [errorCount, setErrorCount] = useState(0);

  const reportError = (error: any) => {
    console.error('Cache error detected:', error);
    
    // Verificar se é um erro relacionado ao cache
    const isCacheError = (
      error?.name === 'VersionError' ||
      error?.message?.includes('IndexedDB') ||
      error?.message?.includes('version') ||
      error?.message?.includes('database')
    );

    if (isCacheError) {
      setErrorCount(prev => prev + 1);
      
      // Se temos muitos erros, sugerir limpeza
      if (errorCount >= 2) {
        setHasError(true);
        toast.error('Problemas detectados no cache. Considere limpar o cache.', {
          duration: 10000,
        });
      }
    }
  };

  const clearError = () => {
    setHasError(false);
    setErrorCount(0);
  };

  return {
    hasError,
    errorCount,
    reportError,
    clearError,
  };
}
