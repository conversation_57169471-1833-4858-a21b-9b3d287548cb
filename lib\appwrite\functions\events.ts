/**
 * Event Functions for Appwrite
 * CRUD operations para eventos e categorias
 */

import { ID, Query } from 'appwrite';
import { databases, COLLECTIONS, DATABASE_ID } from '../config';
import type {
  Event,
  EventFormData,
  UpdateEventData,
  EventCategory,
  EventCategoryFormData,
  EventFilters
} from '@/schemas/events';

// ============================================================================
// EVENT FUNCTIONS
// ============================================================================

/**
 * Listar todos os eventos do usuário
 */
export async function listEvents(userId: string, teamId?: string) {
  const queries = [
    Query.equal('userId', userId),
    Query.equal('isDeleted', false), // Filtrar eventos não deletados
    Query.orderDesc('startDate'),
    Query.limit(1000)
  ];

  if (teamId) {
    queries.push(Query.equal('teamId', teamId));
  }

  return await databases.listDocuments(
    DATABASE_ID,
    COLLECTIONS.EVENTS,
    queries
  );
}

/**
 * Buscar eventos por filtros
 */
export async function searchEvents(userId: string, filters: EventFilters) {
  const queries = [
    Query.equal('userId', userId),
    Query.equal('isDeleted', false), // Filtrar eventos não deletados
    Query.limit(1000)
  ];

  // Filtro por texto
  if (filters.search) {
    queries.push(Query.search('title', filters.search));
  }

  // Filtro por categoria
  if (filters.categories && filters.categories.length > 0) {
    queries.push(Query.equal('category', filters.categories));
  }

  // Filtro por tipo
  if (filters.types && filters.types.length > 0) {
    queries.push(Query.equal('type', filters.types));
  }

  // Filtro por status
  if (filters.status && filters.status.length > 0) {
    queries.push(Query.equal('status', filters.status));
  }

  // Filtro por prioridade
  if (filters.priorities && filters.priorities.length > 0) {
    queries.push(Query.equal('priority', filters.priorities));
  }

  // Filtro por intervalo de datas
  if (filters.dateRange) {
    queries.push(Query.greaterThanEqual('startDate', filters.dateRange.start.toISOString()));
    queries.push(Query.lessThanEqual('endDate', filters.dateRange.end.toISOString()));
  }

  // Filtro por participantes
  if (filters.attendees && filters.attendees.length > 0) {
    queries.push(Query.equal('attendees', filters.attendees));
  }

  // Filtro por tags
  if (filters.tags && filters.tags.length > 0) {
    queries.push(Query.equal('tags', filters.tags));
  }

  // Filtro por team
  if (filters.teamId) {
    queries.push(Query.equal('teamId', filters.teamId));
  }

  queries.push(Query.orderDesc('startDate'));

  return await databases.listDocuments(
    DATABASE_ID,
    COLLECTIONS.EVENTS,
    queries
  );
}

/**
 * Buscar eventos por intervalo de datas
 */
export async function getEventsByDateRange(
  userId: string,
  startDate: Date,
  endDate: Date,
  teamId?: string
) {
  const queries = [
    Query.equal('userId', userId),
    Query.equal('isDeleted', false), // Filtrar eventos não deletados
    Query.greaterThanEqual('startDate', startDate.toISOString()),
    Query.lessThanEqual('endDate', endDate.toISOString()),
    Query.orderAsc('startDate'),
    Query.limit(1000)
  ];

  if (teamId) {
    queries.push(Query.equal('teamId', teamId));
  }

  return await databases.listDocuments(
    DATABASE_ID,
    COLLECTIONS.EVENTS,
    queries
  );
}

/**
 * Obter evento por ID
 */
export async function getEvent(eventId: string) {
  return await databases.getDocument(
    DATABASE_ID,
    COLLECTIONS.EVENTS,
    eventId
  );
}

/**
 * Criar novo evento
 */
export async function createEvent(
  data: EventFormData & { userId: string; createdBy: string },
  options?: import('./database').CreateDocumentOptions
) {
  // Processar dados para o formato do Appwrite
  const eventData = {
    ...data,
    // Converter arrays de objetos para JSON strings se necessário
    links: data.links ? JSON.stringify(data.links) : undefined,
    // Converter weekdays para array de strings
    recurrenceWeekdays: data.recurrenceWeekdays?.map(day => day.toString()),
  };

  // Usar a função createDocument com permissões se options foram fornecidas
  if (options) {
    const { createDocument } = await import('./database');
    return await createDocument(COLLECTIONS.EVENTS, eventData, options, ID.unique());
  }

  // Fallback para método antigo
  return await databases.createDocument(
    DATABASE_ID,
    COLLECTIONS.EVENTS,
    ID.unique(),
    eventData
  );
}

/**
 * Atualizar evento
 */
export async function updateEvent(eventId: string, data: Partial<UpdateEventData>) {
  // Processar dados para o formato do Appwrite
  const eventData = {
    ...data,
    // Converter arrays de objetos para JSON strings se necessário
    links: data.links ? JSON.stringify(data.links) : undefined,
    // Converter weekdays para array de strings
    recurrenceWeekdays: data.recurrenceWeekdays?.map((day: number) => day.toString()),
  };

  return await databases.updateDocument(
    DATABASE_ID,
    COLLECTIONS.EVENTS,
    eventId,
    eventData
  );
}

/**
 * Deletar evento
 */
export async function deleteEvent(eventId: string) {
  return await databases.deleteDocument(
    DATABASE_ID,
    COLLECTIONS.EVENTS,
    eventId
  );
}

/**
 * Buscar eventos recorrentes filhos
 */
export async function getRecurringEvents(parentEventId: string) {
  return await databases.listDocuments(
    DATABASE_ID,
    COLLECTIONS.EVENTS,
    [
      Query.equal('parentEventId', parentEventId),
      Query.equal('isDeleted', false), // Filtrar eventos não deletados
      Query.orderAsc('startDate'),
      Query.limit(1000)
    ]
  );
}

/**
 * Buscar eventos próximos (próximos 7 dias)
 */
export async function getUpcomingEvents(userId: string, teamId?: string) {
  const now = new Date();
  const nextWeek = new Date();
  nextWeek.setDate(now.getDate() + 7);

  const queries = [
    Query.equal('userId', userId),
    Query.equal('isDeleted', false), // Filtrar eventos não deletados
    Query.greaterThanEqual('startDate', now.toISOString()),
    Query.lessThanEqual('startDate', nextWeek.toISOString()),
    Query.notEqual('status', 'cancelado'),
    Query.notEqual('status', 'concluido'),
    Query.orderAsc('startDate'),
    Query.limit(50)
  ];

  if (teamId) {
    queries.push(Query.equal('teamId', teamId));
  }

  return await databases.listDocuments(
    DATABASE_ID,
    COLLECTIONS.EVENTS,
    queries
  );
}

/**
 * Marcar lembrete como enviado
 */
export async function markReminderSent(eventId: string) {
  return await databases.updateDocument(
    DATABASE_ID,
    COLLECTIONS.EVENTS,
    eventId,
    { reminderSent: true }
  );
}

// ============================================================================
// EVENT CATEGORY FUNCTIONS
// ============================================================================

/**
 * Listar categorias do usuário
 */
export async function listEventCategories(userId: string, teamId?: string) {
  const queries = [
    Query.equal('userId', userId),
    Query.equal('isDeleted', false), // Filtrar categorias não deletadas
    Query.orderAsc('sortOrder'),
    Query.orderAsc('name'),
    Query.limit(100)
  ];

  if (teamId) {
    queries.push(Query.equal('teamId', teamId));
  }

  return await databases.listDocuments(
    DATABASE_ID,
    COLLECTIONS.EVENT_CATEGORIES,
    queries
  );
}

/**
 * Obter categoria por ID
 */
export async function getEventCategory(categoryId: string) {
  return await databases.getDocument(
    DATABASE_ID,
    COLLECTIONS.EVENT_CATEGORIES,
    categoryId
  );
}

/**
 * Criar nova categoria
 */
export async function createEventCategory(data: EventCategoryFormData & { userId: string }) {
  return await databases.createDocument(
    DATABASE_ID,
    COLLECTIONS.EVENT_CATEGORIES,
    ID.unique(),
    data
  );
}

/**
 * Atualizar categoria
 */
export async function updateEventCategory(categoryId: string, data: Partial<EventCategoryFormData>) {
  return await databases.updateDocument(
    DATABASE_ID,
    COLLECTIONS.EVENT_CATEGORIES,
    categoryId,
    data
  );
}

/**
 * Deletar categoria
 */
export async function deleteEventCategory(categoryId: string) {
  return await databases.deleteDocument(
    DATABASE_ID,
    COLLECTIONS.EVENT_CATEGORIES,
    categoryId
  );
}

/**
 * Buscar categorias padrão
 */
export async function getDefaultEventCategories(userId: string) {
  return await databases.listDocuments(
    DATABASE_ID,
    COLLECTIONS.EVENT_CATEGORIES,
    [
      Query.equal('userId', userId),
      Query.equal('isDeleted', false), // Filtrar categorias não deletadas
      Query.equal('isDefault', true),
      Query.orderAsc('sortOrder'),
      Query.limit(10)
    ]
  );
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Contar eventos por status
 */
export async function getEventStats(userId: string, teamId?: string) {
  const queries = [
    Query.equal('userId', userId),
    Query.equal('isDeleted', false) // Filtrar eventos não deletados
  ];

  if (teamId) {
    queries.push(Query.equal('teamId', teamId));
  }

  // Buscar todos os eventos para calcular estatísticas
  const response = await databases.listDocuments(
    DATABASE_ID,
    COLLECTIONS.EVENTS,
    [...queries, Query.limit(5000)]
  );

  const events = response.documents as unknown as Event[];
  const now = new Date();

  const stats = {
    total: events.length,
    byStatus: {
      agendado: 0,
      em_andamento: 0,
      concluido: 0,
      cancelado: 0,
      adiado: 0,
    },
    byType: {
      meeting: 0,
      task: 0,
      reminder: 0,
      appointment: 0,
      deadline: 0,
      personal: 0,
      work: 0,
      other: 0,
    },
    byPriority: {
      baixa: 0,
      media: 0,
      alta: 0,
      critica: 0,
    },
    upcoming: 0,
    overdue: 0,
    thisWeek: 0,
    thisMonth: 0,
  };

  const weekStart = new Date(now);
  weekStart.setDate(now.getDate() - now.getDay());
  const weekEnd = new Date(weekStart);
  weekEnd.setDate(weekStart.getDate() + 6);

  const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
  const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);

  events.forEach(event => {
    const eventStart = new Date(event.startDate);

    // Contar por status
    stats.byStatus[event.status]++;

    // Contar por tipo
    stats.byType[event.type]++;

    // Contar por prioridade
    stats.byPriority[event.priority]++;

    // Eventos futuros
    if (eventStart > now && event.status === 'agendado') {
      stats.upcoming++;
    }

    // Eventos atrasados
    if (eventStart < now && event.status === 'agendado') {
      stats.overdue++;
    }

    // Esta semana
    if (eventStart >= weekStart && eventStart <= weekEnd) {
      stats.thisWeek++;
    }

    // Este mês
    if (eventStart >= monthStart && eventStart <= monthEnd) {
      stats.thisMonth++;
    }
  });

  return stats;
}
