/**
 * Modal para import de dados de clientes
 * Suporta upload de arquivos e processamento com IA Gemini
 */

import { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { <PERSON><PERSON> } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Switch } from '../ui/switch';
import { Progress } from '../ui/progress';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '../ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import {
  Upload,
  FileText,
  Image,
  FileSpreadsheet,
  Sparkles,
  AlertTriangle,
  CheckCircle,
  Crown,
  X,
} from 'lucide-react';
import { useDataImportExport, type ImportOptions } from '../../hooks/use-data-import-export';
import { cn } from '../../lib/utils';

interface DataImportModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onImportComplete?: (result: unknown) => void;
}

const FILE_TYPES = [
  { value: 'csv', label: 'CSV', icon: FileText, accept: '.csv' },
  { value: 'excel', label: 'Excel', icon: FileSpreadsheet, accept: '.xlsx,.xls' },
  { value: 'pdf', label: 'PDF', icon: FileText, accept: '.pdf' },
  { value: 'image', label: 'Imagem', icon: Image, accept: '.jpg,.jpeg,.png,.webp' },
] as const;

export function DataImportModal({ open, onOpenChange, onImportComplete }: DataImportModalProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [importOptions, setImportOptions] = useState<ImportOptions>({
    fileType: 'csv',
    useAI: true,
    targetCollection: 'clients',
    customPrompt: '',
  });
  const [step, setStep] = useState<'upload' | 'preview' | 'complete'>('upload');

  const {
    isImporting,
    uploadProgress,
    importResult,
    error,
    canUseFeatures,
    canUseAI,
    upgradeInfo,
    importData,
    validateImportFile,
    reset,
  } = useDataImportExport();

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);

      // Auto-detectar tipo do arquivo
      if (file.type.includes('csv')) {
        setImportOptions(prev => ({ ...prev, fileType: 'csv' }));
      } else if (file.type.includes('excel') || file.type.includes('spreadsheet')) {
        setImportOptions(prev => ({ ...prev, fileType: 'excel' }));
      } else if (file.type.includes('pdf')) {
        setImportOptions(prev => ({ ...prev, fileType: 'pdf' }));
      } else if (file.type.includes('image')) {
        setImportOptions(prev => ({ ...prev, fileType: 'image' }));
      }
    }
  };

  const handleImport = async () => {
    if (!selectedFile) return;

    const result = await importData(selectedFile, importOptions);
    if (result) {
      setStep('preview');
      onImportComplete?.(result);
    }
  };

  const handleClose = () => {
    setSelectedFile(null);
    setStep('upload');
    reset();
    onOpenChange(false);
  };

  const getFileTypeIcon = (type: string) => {
    const fileType = FILE_TYPES.find(ft => ft.value === type);
    return fileType?.icon || FileText;
  };

  // Se o usuário não tem acesso, mostrar upgrade
  if (!canUseFeatures && upgradeInfo) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Crown className="h-5 w-5 text-yellow-500" />
              Upgrade Necessário
            </DialogTitle>
            <DialogDescription>
              {upgradeInfo.message}
            </DialogDescription>
          </DialogHeader>

          <Card className="border-yellow-200 bg-yellow-50">
            <CardHeader>
              <CardTitle className="text-lg">Plano {upgradeInfo.requiredPlan}</CardTitle>
              <CardDescription>
                Desbloqueie o import/export de dados e processamento com IA
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {upgradeInfo.benefits?.slice(0, 3).map((benefit: string, index: number) => (
                  <li key={index} className="flex items-center gap-2 text-sm">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    {benefit}
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)} className="flex-1">
              Cancelar
            </Button>
            <Button className="flex-1 bg-yellow-500 hover:bg-yellow-600">
              Fazer Upgrade
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Import de Dados
          </DialogTitle>
          <DialogDescription>
            Importe dados de clientes de arquivos CSV, Excel, PDF ou imagens usando IA
          </DialogDescription>
        </DialogHeader>

        <Tabs value={step} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="upload" disabled={step !== 'upload'}>
              1. Upload
            </TabsTrigger>
            <TabsTrigger value="preview" disabled={step !== 'preview'}>
              2. Preview
            </TabsTrigger>
            <TabsTrigger value="complete" disabled={step !== 'complete'}>
              3. Concluído
            </TabsTrigger>
          </TabsList>

          <TabsContent value="upload" className="space-y-4">
            {/* Upload de Arquivo */}
            <div className="space-y-4">
              <div>
                <Label htmlFor="file-upload">Selecionar Arquivo</Label>
                <Input
                  id="file-upload"
                  type="file"
                  accept={FILE_TYPES.map(ft => ft.accept).join(',')}
                  onChange={handleFileSelect}
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Suportados: CSV, Excel, PDF, Imagens (máx. 10MB)
                </p>
              </div>

              {selectedFile && (
                <Card>
                  <CardContent className="pt-4">
                    <div className="flex items-center gap-3">
                      {(() => {
                        const Icon = getFileTypeIcon(importOptions.fileType);
                        return <Icon className="h-8 w-8 text-blue-500" />;
                      })()}
                      <div className="flex-1">
                        <p className="font-medium">{selectedFile.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </div>
                      <Badge variant="outline">
                        {importOptions.fileType.toUpperCase()}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Opções de Import */}
              <div className="space-y-4">
                <div>
                  <Label htmlFor="file-type">Tipo de Arquivo</Label>
                  <Select
                    value={importOptions.fileType}
                    onValueChange={(value: ImportOptions['fileType']) =>
                      setImportOptions(prev => ({ ...prev, fileType: value }))
                    }
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {FILE_TYPES.map((type) => {
                        const Icon = type.icon;
                        return (
                          <SelectItem key={type.value} value={type.value}>
                            <div className="flex items-center gap-2">
                              <Icon className="h-4 w-4" />
                              {type.label}
                            </div>
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="flex items-center gap-2">
                      <Sparkles className="h-4 w-4 text-purple-500" />
                      Usar IA para Processamento
                    </Label>
                    <p className="text-xs text-muted-foreground">
                      Gemini AI irá extrair e estruturar os dados automaticamente
                    </p>
                  </div>
                  <Switch
                    checked={importOptions.useAI && canUseAI}
                    onCheckedChange={(checked) =>
                      setImportOptions(prev => ({ ...prev, useAI: checked }))
                    }
                    disabled={!canUseAI}
                  />
                </div>

                {!canUseAI && (
                  <Alert>
                    <Crown className="h-4 w-4" />
                    <AlertDescription>
                      Processamento com IA disponível apenas para planos pagos
                    </AlertDescription>
                  </Alert>
                )}

                {importOptions.useAI && canUseAI && (
                  <div>
                    <Label htmlFor="custom-prompt">Prompt Personalizado (Opcional)</Label>
                    <Textarea
                      id="custom-prompt"
                      placeholder="Ex: Extrair nome, email, telefone e empresa dos dados..."
                      value={importOptions.customPrompt}
                      onChange={(e) =>
                        setImportOptions(prev => ({ ...prev, customPrompt: e.target.value }))
                      }
                      className="mt-1"
                      rows={3}
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Descreva como a IA deve processar os dados
                    </p>
                  </div>
                )}
              </div>

              {error && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {isImporting && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Processando arquivo...</span>
                    <span>{uploadProgress}%</span>
                  </div>
                  <Progress value={uploadProgress} />
                </div>
              )}
            </div>

            <div className="flex gap-2 pt-4">
              <Button variant="outline" onClick={handleClose} className="flex-1">
                Cancelar
              </Button>
              <Button
                onClick={handleImport}
                disabled={!selectedFile || isImporting}
                className="flex-1"
              >
                {isImporting ? 'Processando...' : 'Importar Dados'}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="preview" className="space-y-4">
            {importResult && (
              <div className="space-y-4">
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    {importResult.processedRecords} registros processados com sucesso!
                  </AlertDescription>
                </Alert>

                {importResult.errors.length > 0 && (
                  <Alert variant="destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      {importResult.errors.length} erro(s) encontrado(s)
                    </AlertDescription>
                  </Alert>
                )}

                {importResult.previewData && importResult.previewData.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Preview dos Dados</CardTitle>
                      <CardDescription>
                        Primeiros registros importados
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 max-h-40 overflow-y-auto">
                        {importResult.previewData.slice(0, 5).map((record, index) => (
                          <div key={index} className="p-2 bg-muted rounded text-sm">
                            <pre>{JSON.stringify(record, null, 2)}</pre>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}

                <div className="flex gap-2">
                  <Button variant="outline" onClick={handleClose} className="flex-1">
                    Fechar
                  </Button>
                  <Button onClick={() => setStep('upload')} className="flex-1">
                    Importar Mais
                  </Button>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
