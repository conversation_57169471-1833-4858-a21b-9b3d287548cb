import * as React from "react"
import {
  IconDashboard,
  IconHelp,
  IconSettings,
  IconCreditCard,
  IconUsersGroup,
  IconChartBar,
  IconMessage,
  IconChartLine,
  IconCalendar,
  IconFolder,
  IconActivity,
  IconLayoutKanban,
} from "@tabler/icons-react"
import Link from 'next/link'


import { NavMain } from "./nav-main"
import { NavSecondary } from "./nav-secondary"
import { NavUser } from "./nav-user"
import { LogoIcon } from "./logo"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "./ui/sidebar"
import { useAuth } from "../hooks/use-auth"
import { usePermissions } from "../contexts/team-context"
import { getAccessibleSidebarItems } from "../lib/permissions"

const data = {
  navMain: [
    {
      title: "Dashboard",
      url: "/dashboard",
      icon: IconDashboard,
      resource: "dashboard" as const,
    },
    {
      title: "Analytics",
      url: "/dashboard/analytics",
      icon: IconChartLine,
      resource: "analytics" as const,
    },
    {
      title: "Calendário",
      url: "/dashboard/calendar",
      icon: IconCalendar,
      resource: "calendar" as const,
    },
    {
      title: "Documentos",
      url: "/dashboard/documents",
      icon: IconFolder,
      resource: "documents" as const,
    },
    {
      title: "Kanban",
      url: "/dashboard/kanban",
      icon: IconLayoutKanban,
      resource: "kanban" as const,
    },
    {
      title: "Clientes",
      url: "/dashboard/clients",
      icon: IconUsersGroup,
      resource: "clients" as const,
    },
    {
      title: "Teams",
      url: "/dashboard/teams",
      icon: IconUsersGroup,
      resource: "teams" as const,
    },
    {
      title: "Chat de Time",
      url: "/dashboard/team-chat",
      icon: IconMessage,
      resource: "team_chat" as const,
    },
    {
      title: "Atividades",
      url: "/dashboard/activities",
      icon: IconActivity,
      resource: "activities" as const,
    },
  ],

  navSecondary: [
    {
      title: "Preferências",
      url: "/dashboard/preferences",
      icon: IconSettings,
      resource: "preferences" as const,
    },
    {
      title: "Planos",
      url: "/dashboard/plans",
      icon: IconCreditCard,
      resource: "plans" as const,
    },
    {
      title: "Ajuda",
      url: "/dashboard/help",
      icon: IconHelp,
      resource: "help" as const,
    },
  ],

}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { user } = useAuth()
  const { context: permissionContext } = usePermissions()

  const userData = {
    name: user?.name || "Usuário",
    email: user?.email || "<EMAIL>",
    avatar: user?.prefs?.avatar || undefined,
  }

  // Filtrar itens da sidebar baseado nas permissões
  const accessibleMainItems = permissionContext
    ? getAccessibleSidebarItems(permissionContext, data.navMain)
    : data.navMain;

  const accessibleSecondaryItems = permissionContext
    ? getAccessibleSidebarItems(permissionContext, data.navSecondary)
    : data.navSecondary;

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="data-[slot=sidebar-menu-button]:!p-1.5"
            >
              <Link href="/dashboard">
                <LogoIcon className="!size-5" uniColor />
                <span className="text-base font-semibold">Codexa</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={accessibleMainItems} />
        <NavSecondary items={accessibleSecondaryItems} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={userData} />
      </SidebarFooter>
    </Sidebar>
  )
}
