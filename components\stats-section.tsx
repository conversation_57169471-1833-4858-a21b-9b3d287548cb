import React from 'react';
import { Card, CardContent } from './ui/card';
import { Badge } from './ui/badge';
import {
  Users,
  Rocket,
  Clock,
  DollarSign,
  Code,
  Star,
  TrendingUp,
  Shield
} from 'lucide-react';

// Estatísticas baseadas em funcionalidades reais do template
const realTemplateStats = [
  {
    icon: Code,
    value: '50K+',
    label: 'Linhas de Código',
    description: 'Código TypeScript pronto e testado',
    color: 'bg-blue-500'
  },
  {
    icon: Users,
    value: '15+',
    label: 'Componentes UI',
    description: 'Componentes shadcn/ui customizados',
    color: 'bg-green-500'
  },
  {
    icon: Rocket,
    value: '30+',
    label: 'Hooks Customizados',
    description: 'React Query hooks otimizados',
    color: 'bg-purple-500'
  },
  {
    icon: Shield,
    value: '100%',
    label: 'TypeScript',
    description: 'Type-safe em todo o projeto',
    color: 'bg-emerald-500'
  },
  {
    icon: Star,
    value: '10+',
    label: 'Páginas Prontas',
    description: 'Dashboard, auth, teams e mais',
    color: 'bg-orange-500'
  },
  {
    icon: Clock,
    value: '5+',
    label: 'Integrações',
    description: 'Appwrite, PWA, Analytics, Chat',
    color: 'bg-yellow-500'
  },
  {
    icon: TrendingUp,
    value: '20+',
    label: 'Schemas DB',
    description: 'Banco de dados estruturado',
    color: 'bg-cyan-500'
  },
  {
    icon: DollarSign,
    value: '3',
    label: 'Planos Incluídos',
    description: 'Sistema de assinatura completo',
    color: 'bg-red-500'
  }
];

// Funcionalidades técnicas reais do template
const technicalFeatures = [
  {
    title: 'React Router v7',
    description: 'Roteamento moderno e otimizado',
    year: '2024'
  },
  {
    title: 'Appwrite Backend',
    description: 'Database, Auth e Storage',
    year: '2024'
  },
  {
    title: 'PWA Configurado',
    description: 'Progressive Web App completo',
    year: '2024'
  },
  {
    title: 'Cache Local-First',
    description: 'IndexedDB + React Query',
    year: '2024'
  }
];

export function StatsSection() {
  return (
    <section className="py-16 md:py-24 bg-muted/30">
      <div className="container mx-auto px-4 max-w-7xl">
        {/* Header */}
        <div className="text-center mb-16">
          <Badge variant="outline" className="mb-4">
            Especificações Técnicas
          </Badge>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
            O que está incluído
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
            Um template completo e profissional com todas as funcionalidades
            essenciais para acelerar o desenvolvimento do seu SaaS.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {realTemplateStats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <Card key={index} className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                <CardContent className="p-6 text-center">
                  <div className={`w-16 h-16 ${stat.color} rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}>
                    <Icon className="w-8 h-8 text-white" />
                  </div>
                  <div className="text-3xl md:text-4xl font-bold text-primary mb-2">
                    {stat.value}
                  </div>
                  <div className="font-semibold mb-2">
                    {stat.label}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {stat.description}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Technical Features */}
        <div className="bg-background rounded-lg p-8 border">
          <h3 className="text-2xl font-bold text-center mb-8">
            Tecnologias Principais
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {technicalFeatures.map((feature, index) => (
              <div key={index} className="text-center group">
                <div className="bg-primary/10 rounded-lg p-6 group-hover:bg-primary/20 transition-colors duration-300">
                  <div className="text-lg font-bold mb-2">
                    {feature.title}
                  </div>
                  <div className="text-sm text-muted-foreground mb-2">
                    {feature.description}
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {feature.year}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Template Benefits */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="text-4xl font-bold text-primary mb-2">📚</div>
            <div className="font-semibold mb-1">Documentação Completa</div>
            <div className="text-sm text-muted-foreground">
              Guias detalhados e exemplos práticos
            </div>
          </div>
          <div className="text-center">
            <div className="text-4xl font-bold text-primary mb-2">🔧</div>
            <div className="font-semibold mb-1">Pronto para Produção</div>
            <div className="text-sm text-muted-foreground">
              Código testado e otimizado
            </div>
          </div>
          <div className="text-center">
            <div className="text-4xl font-bold text-primary mb-2">⚡</div>
            <div className="font-semibold mb-1">Desenvolvimento Rápido</div>
            <div className="text-sm text-muted-foreground">
              Acelere seu time-to-market
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
