/**
 * Calendar State Store
 * Gerencia estado global do calendário usando Valtio
 */

import { proxy } from 'valtio';
import type { 
  EventFilters, 
  CalendarViewSettings, 
  EventType, 
  EventPriority, 
  EventStatus,
  ReminderType 
} from '@/schemas/events';

// ============================================================================
// INTERFACES
// ============================================================================

export interface CalendarState {
  // Configurações de visualização
  view: CalendarViewSettings;

  // Filtros ativos
  filters: EventFilters;

  // Estado da interface
  ui: {
    sidebarOpen: boolean;
    miniCalendarOpen: boolean;
    filtersOpen: boolean;
    loading: boolean;
    selectedDate: Date;
    selectedEventId: string | null;
    draggedEventId: string | null;
    isCreatingEvent: boolean;
    isEditingEvent: boolean;
  };

  // Configurações do usuário
  preferences: {
    defaultView: 'month' | 'week' | 'day' | 'agenda';
    defaultDuration: number; // em minutos
    defaultReminder: ReminderType;
    workingHours: {
      start: string; // HH:mm
      end: string; // HH:mm
    };
    weekStartsOn: 0 | 1; // 0 = domingo, 1 = segunda
    timeFormat: '12h' | '24h';
    dateFormat: 'dd/MM/yyyy' | 'MM/dd/yyyy' | 'yyyy-MM-dd';
    showWeekends: boolean;
    showDeclinedEvents: boolean;
    autoRefresh: boolean;
    refreshInterval: number; // em minutos
    notifications: {
      enabled: boolean;
      sound: boolean;
      desktop: boolean;
      email: boolean;
    };
  };

  // Cache de dados
  cache: {
    lastRefresh: Date | null;
    eventsCount: number;
    categoriesCount: number;
  };
}

// ============================================================================
// STORE
// ============================================================================

/**
 * Store reativo para estado do calendário
 */
export const calendarStore = proxy<CalendarState>({
  view: {
    view: 'month',
    startDate: new Date(),
    showWeekends: true,
    showAllDay: true,
    timeSlotDuration: 30,
    businessHours: {
      start: '09:00',
      end: '18:00',
    },
    defaultReminder: 'none',
    defaultDuration: 60,
  },

  filters: {
    search: '',
    categories: [],
    types: [],
    priorities: [],
    status: [],
    attendees: [],
    tags: [],
  },

  ui: {
    sidebarOpen: true,
    miniCalendarOpen: true,
    filtersOpen: false,
    loading: false,
    selectedDate: new Date(),
    selectedEventId: null,
    draggedEventId: null,
    isCreatingEvent: false,
    isEditingEvent: false,
  },

  preferences: {
    defaultView: 'month',
    defaultDuration: 60,
    defaultReminder: '15min',
    workingHours: {
      start: '09:00',
      end: '18:00',
    },
    weekStartsOn: 1, // Segunda-feira
    timeFormat: '24h',
    dateFormat: 'dd/MM/yyyy',
    showWeekends: true,
    showDeclinedEvents: false,
    autoRefresh: true,
    refreshInterval: 5,
    notifications: {
      enabled: true,
      sound: true,
      desktop: true,
      email: false,
    },
  },

  cache: {
    lastRefresh: null,
    eventsCount: 0,
    categoriesCount: 0,
  },
});

// ============================================================================
// ACTIONS
// ============================================================================

/**
 * Actions para manipular o estado do calendário
 */
export const calendarActions = {
  // View actions
  setView: (view: 'month' | 'week' | 'day' | 'agenda') => {
    calendarStore.view.view = view;
  },

  setStartDate: (date: Date) => {
    calendarStore.view.startDate = date;
    calendarStore.ui.selectedDate = date;
  },

  navigateToDate: (date: Date) => {
    calendarStore.view.startDate = date;
    calendarStore.ui.selectedDate = date;
  },

  navigateToToday: () => {
    const today = new Date();
    calendarStore.view.startDate = today;
    calendarStore.ui.selectedDate = today;
  },

  navigatePrevious: () => {
    const current = calendarStore.view.startDate;
    const view = calendarStore.view.view;
    
    let newDate: Date;
    switch (view) {
      case 'day':
        newDate = new Date(current);
        newDate.setDate(current.getDate() - 1);
        break;
      case 'week':
        newDate = new Date(current);
        newDate.setDate(current.getDate() - 7);
        break;
      case 'month':
        newDate = new Date(current);
        newDate.setMonth(current.getMonth() - 1);
        break;
      default:
        newDate = current;
    }
    
    calendarStore.view.startDate = newDate;
    calendarStore.ui.selectedDate = newDate;
  },

  navigateNext: () => {
    const current = calendarStore.view.startDate;
    const view = calendarStore.view.view;
    
    let newDate: Date;
    switch (view) {
      case 'day':
        newDate = new Date(current);
        newDate.setDate(current.getDate() + 1);
        break;
      case 'week':
        newDate = new Date(current);
        newDate.setDate(current.getDate() + 7);
        break;
      case 'month':
        newDate = new Date(current);
        newDate.setMonth(current.getMonth() + 1);
        break;
      default:
        newDate = current;
    }
    
    calendarStore.view.startDate = newDate;
    calendarStore.ui.selectedDate = newDate;
  },

  // Filter actions
  setSearchFilter: (search: string) => {
    calendarStore.filters.search = search;
  },

  setCategoryFilter: (categories: string[]) => {
    calendarStore.filters.categories = categories;
  },

  setTypeFilter: (types: EventType[]) => {
    calendarStore.filters.types = types;
  },

  setPriorityFilter: (priorities: EventPriority[]) => {
    calendarStore.filters.priorities = priorities;
  },

  setStatusFilter: (status: EventStatus[]) => {
    calendarStore.filters.status = status;
  },

  setDateRangeFilter: (start: Date, end: Date) => {
    calendarStore.filters.dateRange = { start, end };
  },

  clearFilters: () => {
    calendarStore.filters = {
      search: '',
      categories: [],
      types: [],
      priorities: [],
      status: [],
      attendees: [],
      tags: [],
    };
  },

  // UI actions
  toggleSidebar: () => {
    calendarStore.ui.sidebarOpen = !calendarStore.ui.sidebarOpen;
  },

  setSidebarOpen: (open: boolean) => {
    calendarStore.ui.sidebarOpen = open;
  },

  toggleMiniCalendar: () => {
    calendarStore.ui.miniCalendarOpen = !calendarStore.ui.miniCalendarOpen;
  },

  toggleFilters: () => {
    calendarStore.ui.filtersOpen = !calendarStore.ui.filtersOpen;
  },

  setLoading: (loading: boolean) => {
    calendarStore.ui.loading = loading;
  },

  selectEvent: (eventId: string | null) => {
    calendarStore.ui.selectedEventId = eventId;
  },

  setDraggedEvent: (eventId: string | null) => {
    calendarStore.ui.draggedEventId = eventId;
  },

  setCreatingEvent: (creating: boolean) => {
    calendarStore.ui.isCreatingEvent = creating;
  },

  setEditingEvent: (editing: boolean) => {
    calendarStore.ui.isEditingEvent = editing;
  },

  // Preferences actions
  updatePreferences: (preferences: Partial<CalendarState['preferences']>) => {
    Object.assign(calendarStore.preferences, preferences);
  },

  setDefaultView: (view: 'month' | 'week' | 'day' | 'agenda') => {
    calendarStore.preferences.defaultView = view;
  },

  setWorkingHours: (start: string, end: string) => {
    calendarStore.preferences.workingHours = { start, end };
  },

  setTimeFormat: (format: '12h' | '24h') => {
    calendarStore.preferences.timeFormat = format;
  },

  setDateFormat: (format: 'dd/MM/yyyy' | 'MM/dd/yyyy' | 'yyyy-MM-dd') => {
    calendarStore.preferences.dateFormat = format;
  },

  toggleWeekends: () => {
    calendarStore.preferences.showWeekends = !calendarStore.preferences.showWeekends;
    calendarStore.view.showWeekends = calendarStore.preferences.showWeekends;
  },

  // Cache actions
  updateCache: (data: Partial<CalendarState['cache']>) => {
    Object.assign(calendarStore.cache, data);
  },

  refreshCache: () => {
    calendarStore.cache.lastRefresh = new Date();
  },

  // Reset actions
  resetView: () => {
    calendarStore.view = {
      view: calendarStore.preferences.defaultView,
      startDate: new Date(),
      showWeekends: calendarStore.preferences.showWeekends,
      showAllDay: true,
      timeSlotDuration: 30,
      businessHours: calendarStore.preferences.workingHours,
      defaultReminder: calendarStore.preferences.defaultReminder,
      defaultDuration: calendarStore.preferences.defaultDuration,
    };
  },

  resetFilters: () => {
    calendarStore.filters = {
      search: '',
      categories: [],
      types: [],
      priorities: [],
      status: [],
      attendees: [],
      tags: [],
    };
  },

  resetUI: () => {
    calendarStore.ui = {
      sidebarOpen: true,
      miniCalendarOpen: true,
      filtersOpen: false,
      loading: false,
      selectedDate: new Date(),
      selectedEventId: null,
      draggedEventId: null,
      isCreatingEvent: false,
      isEditingEvent: false,
    };
  },
};

// ============================================================================
// HOOKS
// ============================================================================

/**
 * Hook para usar o store de estado do calendário
 */
export function useCalendarState() {
  return {
    store: calendarStore,
    actions: calendarActions,
  };
}
