import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '../ui/dialog';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Switch } from '../ui/switch';
import { Label } from '../ui/label';
import { Crown, Sparkles, ArrowRight, X, CreditCard, Settings } from 'lucide-react';
import { useAuth } from '../../hooks/use-auth';
import { getUserPlan } from '../../lib/plan-limits';
import { PLANS } from '../../lib/plans';
import { useUpgradePlan, useManageSubscription, PaymentUtils } from '../../hooks/use-payments';

interface TeamUpgradeModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  feature: string;
  requiredPlan?: string;
  reason?: string;
}

export function TeamUpgradeModal({
  open,
  onOpenChange,
  feature,
  requiredPlan = 'pro',
  reason
}: TeamUpgradeModalProps) {
  const router = useRouter();
  const { user } = useAuth();
  const [isYearly, setIsYearly] = useState(false);

  const upgradePlan = useUpgradePlan();
  const manageSubscription = useManageSubscription();

  const currentPlan = getUserPlan(user?.labels || []);
  const targetPlan = PLANS.find(p => p.id === requiredPlan);
  const currentPlanData = PLANS.find(p => p.id === currentPlan);

  // Verificar se já tem um plano pago (pode gerenciar assinatura)
  const hasPaidPlan = currentPlan !== 'free';

  // Verificar se pagamentos estão configurados
  const paymentsConfigured = PaymentUtils.isConfigured();

  const handleUpgrade = () => {
    if (!paymentsConfigured) {
      // Fallback para página de planos se pagamentos não configurados
      onOpenChange(false);
      router.push('/dashboard/plans');
      return;
    }

    if (hasPaidPlan) {
      // Se já tem plano pago, abrir portal de gerenciamento
      manageSubscription.mutate(window.location.href);
    } else {
      // Fazer upgrade direto
      upgradePlan.mutate({
        planId: requiredPlan,
        billingCycle: isYearly ? 'yearly' : 'monthly',
      });
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  // Calcular preços
  const monthlyPrice = targetPlan?.price.monthly || 0;
  const yearlyPrice = targetPlan?.price.yearly || 0;
  const yearlyDiscount = PaymentUtils.calculateYearlyDiscount(monthlyPrice);
  const displayPrice = isYearly ? Math.round(yearlyPrice / 12) : monthlyPrice;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader className="text-center space-y-4">
          <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-primary/10 border border-primary/20">
            <Crown className="h-8 w-8 text-primary" />
          </div>

          <div className="space-y-2">
            <DialogTitle className="text-xl font-bold">
              Upgrade Necessário
            </DialogTitle>
            <DialogDescription className="text-base">
              {reason || `Para usar ${feature}, você precisa fazer upgrade do seu plano.`}
            </DialogDescription>
          </div>
        </DialogHeader>

        <div className="space-y-4">
          {/* Plano Atual vs Necessário */}
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-4 rounded-lg border bg-muted/50">
              <div className="text-sm text-muted-foreground mb-1">Plano Atual</div>
              <div className="font-semibold">{currentPlanData?.name || 'Gratuito'}</div>
              <Badge variant="outline" className="mt-2">
                {typeof currentPlanData?.price === 'object'
                  ? `R$ ${currentPlanData.price.monthly}`
                  : currentPlanData?.price || 'R$ 0'}/mês
              </Badge>
            </div>

            <div className="text-center p-4 rounded-lg border bg-primary/5 border-primary/20">
              <div className="text-sm text-muted-foreground mb-1">Necessário</div>
              <div className="font-semibold flex items-center justify-center gap-1">
                <Sparkles className="h-4 w-4 text-primary" />
                {targetPlan?.name || 'Pro'}
              </div>
              <Badge className="mt-2 bg-primary text-primary-foreground">
                R$ {displayPrice}/mês
                {isYearly && (
                  <span className="ml-1 text-xs opacity-90">
                    ({yearlyDiscount}% off)
                  </span>
                )}
              </Badge>
            </div>
          </div>

          {/* Toggle de Cobrança (apenas se não tem plano pago) */}
          {!hasPaidPlan && paymentsConfigured && (
            <div className="flex items-center justify-center space-x-3 p-3 bg-muted/30 rounded-lg">
              <Label htmlFor="billing-toggle" className={`text-sm ${!isYearly ? 'font-medium' : ''}`}>
                Mensal
              </Label>
              <Switch
                id="billing-toggle"
                checked={isYearly}
                onCheckedChange={setIsYearly}
              />
              <Label htmlFor="billing-toggle" className={`text-sm ${isYearly ? 'font-medium' : ''}`}>
                Anual
                <Badge variant="secondary" className="ml-1 text-xs">
                  {yearlyDiscount}% OFF
                </Badge>
              </Label>
            </div>
          )}

          {/* Benefícios do Upgrade */}
          {targetPlan && (
            <div className="space-y-2">
              <div className="text-sm font-medium text-center">
                O que você ganha com o upgrade:
              </div>
              <div className="grid grid-cols-1 gap-2 text-sm">
                {targetPlan.features.slice(0, 3).map((feature, index) => (
                  <div key={index} className="flex items-center gap-2 text-muted-foreground">
                    <div className="h-1.5 w-1.5 rounded-full bg-primary" />
                    {feature.name}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="flex-col sm:flex-col gap-2">
          <Button
            onClick={handleUpgrade}
            disabled={upgradePlan.isPending || manageSubscription.isPending}
            className="w-full"
          >
            {upgradePlan.isPending || manageSubscription.isPending ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                Processando...
              </>
            ) : hasPaidPlan ? (
              <>
                <Settings className="mr-2 h-4 w-4" />
                Gerenciar Assinatura
                <ArrowRight className="ml-2 h-4 w-4" />
              </>
            ) : (
              <>
                <CreditCard className="mr-2 h-4 w-4" />
                {paymentsConfigured ? 'Fazer Upgrade Agora' : 'Ver Planos'}
                <ArrowRight className="ml-2 h-4 w-4" />
              </>
            )}
          </Button>

          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={upgradePlan.isPending || manageSubscription.isPending}
            className="w-full"
          >
            Cancelar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
