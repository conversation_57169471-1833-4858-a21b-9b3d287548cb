'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

/**
 * Redirect page from documents to files
 * Maintains compatibility with old routes
 */
export default function DocumentsRedirectPage() {
  const router = useRouter();

  useEffect(() => {
    router.replace('/dashboard/files');
  }, [router]);

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-muted-foreground">Redirecionando para arquivos...</p>
      </div>
    </div>
  );
}
