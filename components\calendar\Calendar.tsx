/**
 * Calendar Component
 * Componente principal do sistema de calendário
 */

import React, { useMemo, useCallback } from 'react';
import { Calendar as BigCalendar, dateFnsLocalizer, Views, type View } from 'react-big-calendar';
import { format, parse, startOfWeek, getDay } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useSnapshot } from 'valtio';
import { cn } from '../../lib/utils';
import { useCalendarState } from '../../lib/stores/calendar-state';
import { useCalendarModals } from '../../lib/stores/calendar-modals';
import { useEvents, useCreateEvent, useUpdateEvent, useDeleteEvent } from '../../hooks/api/use-events';
import type { Event, CalendarEvent } from '@/schemas/events';
import { CalendarHeader } from './CalendarHeader';
import { EventCard } from './EventCard';
import { CalendarSidebar } from './CalendarSidebar';
import { EventModal } from './EventModal';
import { Button } from '../ui/button';
import { Card } from '../ui/card';
import { Skeleton } from '../ui/skeleton';
import { toast } from 'sonner';
import { useAuth } from '../../hooks/use-auth';

// Configurar localizer para date-fns
const localizer = dateFnsLocalizer({
  format,
  parse,
  startOfWeek: (date: Date) => startOfWeek(date, { weekStartsOn: 1 }), // Segunda-feira
  getDay,
  locales: {
    'pt-BR': ptBR,
  },
});

// ============================================================================
// INTERFACES
// ============================================================================

interface CalendarProps {
  className?: string;
  teamId?: string;
}

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

export function Calendar({ className, teamId }: CalendarProps) {
  const { user } = useAuth();
  const { store: calendarState, actions: calendarActions } = useCalendarState();
  const { store: modalsState, actions: modalsActions } = useCalendarModals();
  const calendarSnapshot = useSnapshot(calendarState);
  const modalsSnapshot = useSnapshot(modalsState);

  // Buscar eventos
  const { data: events = [], isLoading, error } = useEvents(teamId);

  // Mutation hooks
  const createEventMutation = useCreateEvent({
    onSuccess: () => {
      toast.success('Evento criado com sucesso!');
    }
  });

  const updateEventMutation = useUpdateEvent({
    onSuccess: () => {
      toast.success('Evento atualizado com sucesso!');
    }
  });

  const deleteEventMutation = useDeleteEvent({
    onSuccess: () => {
      toast.success('Evento excluído com sucesso!');
    }
  });

  // Converter eventos para formato do BigCalendar
  const calendarEvents = useMemo((): CalendarEvent[] => {
    return events.map((event: Event) => ({
      ...event,
      start: new Date(event.startDate),
      end: new Date(event.endDate),
      title: event.title,
      resource: event,
      isDraggable: true,
      isResizable: true,
    }));
  }, [events]);

  // Filtrar eventos baseado nos filtros ativos
  const filteredEvents = useMemo(() => {
    let filtered = calendarEvents;

    // Filtro por busca
    if (calendarSnapshot.filters.search) {
      const search = calendarSnapshot.filters.search.toLowerCase();
      filtered = filtered.filter(event =>
        event.title.toLowerCase().includes(search) ||
        event.description?.toLowerCase().includes(search) ||
        event.location?.toLowerCase().includes(search)
      );
    }

    // Filtro por categoria
    if (calendarSnapshot.filters.categories && calendarSnapshot.filters.categories.length > 0) {
      filtered = filtered.filter(event =>
        event.category && calendarSnapshot.filters.categories!.includes(event.category)
      );
    }

    // Filtro por tipo
    if (calendarSnapshot.filters.types && calendarSnapshot.filters.types.length > 0) {
      filtered = filtered.filter(event =>
        calendarSnapshot.filters.types!.includes(event.type)
      );
    }

    // Filtro por status
    if (calendarSnapshot.filters.status && calendarSnapshot.filters.status.length > 0) {
      filtered = filtered.filter(event =>
        calendarSnapshot.filters.status!.includes(event.status)
      );
    }

    // Filtro por prioridade
    if (calendarSnapshot.filters.priorities && calendarSnapshot.filters.priorities.length > 0) {
      filtered = filtered.filter(event =>
        calendarSnapshot.filters.priorities!.includes(event.priority)
      );
    }

    return filtered;
  }, [calendarEvents, calendarSnapshot.filters]);

  // Handlers
  const handleSelectSlot = useCallback(({ start, end }: { start: Date; end: Date }) => {
    const startTime = format(start, 'HH:mm');
    const endTime = format(end, 'HH:mm');

    modalsActions.openCreateEvent({
      date: start,
      startTime,
      endTime,
      allDay: start.getHours() === 0 && end.getHours() === 0,
    });
  }, [modalsActions]);

  const handleSelectEvent = useCallback((event: CalendarEvent) => {
    calendarActions.selectEvent(event.$id);
    modalsActions.openViewEvent(event as Event);
  }, [calendarActions, modalsActions]);

  const handleViewChange = useCallback((view: View) => {
    calendarActions.setView(view as 'month' | 'week' | 'day' | 'agenda');
  }, [calendarActions]);

  const handleNavigate = useCallback((date: Date) => {
    calendarActions.setStartDate(date);
  }, [calendarActions]);

  const handleEventDrop = useCallback(({ event, start, end }: any) => {
    // TODO: Implementar drag & drop
    toast.info('Funcionalidade de arrastar eventos será implementada em breve');
  }, []);

  const handleEventResize = useCallback(({ event, start, end }: any) => {
    // TODO: Implementar resize
    toast.info('Funcionalidade de redimensionar eventos será implementada em breve');
  }, []);

  // Componentes customizados
  const EventComponent = useCallback(({ event }: { event: CalendarEvent }) => {
    return (
      <EventCard
        event={event as Event}
        compact={calendarSnapshot.view.view === 'month'}
      />
    );
  }, [calendarSnapshot.view.view]);

  const EventAgenda = useCallback(({ event }: { event: CalendarEvent }) => {
    return (
      <div className="flex items-center gap-2 p-2">
        <div
          className="w-3 h-3 rounded-full flex-shrink-0"
          style={{ backgroundColor: event.color }}
        />
        <div className="flex-1 min-w-0">
          <div className="font-medium truncate">{event.title}</div>
          {event.location && (
            <div className="text-sm text-muted-foreground truncate">
              📍 {event.location}
            </div>
          )}
        </div>
      </div>
    );
  }, []);

  // Loading state
  if (isLoading) {
    return (
      <div className={cn('flex h-full', className)}>
        <div className="flex-1 p-6">
          <div className="space-y-4">
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-96 w-full" />
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={cn('flex h-full items-center justify-center', className)}>
        <Card className="p-6 max-w-md text-center">
          <h3 className="text-lg font-semibold mb-2">Erro ao carregar calendário</h3>
          <p className="text-muted-foreground mb-4">
            Não foi possível carregar os eventos. Tente novamente.
          </p>
          <Button onClick={() => window.location.reload()}>
            Tentar novamente
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className={cn('flex h-full bg-background', className)}>
      {/* Sidebar */}
      {calendarSnapshot.ui.sidebarOpen && (
        <CalendarSidebar teamId={teamId} />
      )}

      {/* Main Calendar */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Header */}
        <CalendarHeader />

        {/* Calendar Content */}
        <div className="flex-1 p-6 overflow-hidden">
          <Card className="h-full">
            <div className="h-full p-4">
              <BigCalendar
                localizer={localizer}
                events={filteredEvents}
                startAccessor="start"
                endAccessor="end"
                titleAccessor="title"
                allDayAccessor="allDay"
                resourceAccessor="resource"
                view={calendarSnapshot.view.view}
                views={[Views.MONTH, Views.WEEK, Views.DAY, Views.AGENDA]}
                date={calendarSnapshot.view.startDate}
                onView={handleViewChange}
                onNavigate={handleNavigate}
                onSelectSlot={handleSelectSlot}
                onSelectEvent={handleSelectEvent}
                // onEventDrop={handleEventDrop}
                // onEventResize={handleEventResize}
                selectable
                popup
                showMultiDayTimes
                step={calendarSnapshot.view.timeSlotDuration}
                timeslots={60 / calendarSnapshot.view.timeSlotDuration}
                min={new Date(2024, 0, 1, 7, 0)} // 07:00
                max={new Date(2024, 0, 1, 22, 0)} // 22:00
                scrollToTime={new Date(2024, 0, 1, 9, 0)} // 09:00
                culture="pt-BR"
                messages={{
                  allDay: 'Dia todo',
                  previous: 'Anterior',
                  next: 'Próximo',
                  today: 'Hoje',
                  month: 'Mês',
                  week: 'Semana',
                  day: 'Dia',
                  agenda: 'Agenda',
                  date: 'Data',
                  time: 'Hora',
                  event: 'Evento',
                  noEventsInRange: 'Não há eventos neste período',
                  showMore: (total) => `+ ${total} mais`,
                }}
                formats={{
                  dateFormat: 'dd',
                  dayFormat: (date, culture, localizer) =>
                    localizer?.format(date, 'EEE', culture) || '',
                  dayHeaderFormat: (date, culture, localizer) =>
                    localizer?.format(date, 'EEEE, dd/MM', culture) || '',
                  dayRangeHeaderFormat: ({ start, end }, culture, localizer) =>
                    `${localizer?.format(start, 'dd/MM', culture)} - ${localizer?.format(end, 'dd/MM', culture)}`,
                  monthHeaderFormat: (date, culture, localizer) =>
                    localizer?.format(date, 'MMMM yyyy', culture) || '',
                  agendaHeaderFormat: ({ start, end }, culture, localizer) =>
                    `${localizer?.format(start, 'dd/MM', culture)} - ${localizer?.format(end, 'dd/MM', culture)}`,
                  agendaDateFormat: (date, culture, localizer) =>
                    localizer?.format(date, 'EEE dd/MM', culture) || '',
                  agendaTimeFormat: (date, culture, localizer) =>
                    localizer?.format(date, 'HH:mm', culture) || '',
                  agendaTimeRangeFormat: ({ start, end }, culture, localizer) =>
                    `${localizer?.format(start, 'HH:mm', culture)} - ${localizer?.format(end, 'HH:mm', culture)}`,
                }}
                components={{
                  event: EventComponent,
                  agenda: {
                    event: EventAgenda,
                  },
                }}
                className="rbc-calendar"
                dayLayoutAlgorithm="no-overlap"
              />
            </div>
          </Card>
        </div>
      </div>

      {/* Modals */}
      <EventModal
        isOpen={modalsSnapshot.createEvent.isOpen || modalsSnapshot.editEvent.isOpen || modalsSnapshot.viewEvent.isOpen}
        onClose={() => {
          modalsActions.closeCreateEvent();
          modalsActions.closeEditEvent();
          modalsActions.closeViewEvent();
        }}
        onSave={async (eventData) => {
          try {
            if (modalsSnapshot.editEvent.isOpen && modalsSnapshot.editEvent.event) {
              // Editar evento existente
              await updateEventMutation.mutateAsync({
                eventId: modalsSnapshot.editEvent.event.$id,
                data: {
                  ...eventData,
                  updatedBy: user?.$id,
                }
              });
              modalsActions.closeEditEvent();
            } else {
              // Criar novo evento
              const newEventData = {
                ...eventData,
                title: eventData.title || 'Novo Evento',
                description: eventData.description || '',
                startDate: eventData.startDate || new Date().toISOString(),
                endDate: eventData.endDate || new Date().toISOString(),
                allDay: eventData.allDay || false,
                timezone: eventData.timezone || 'America/Sao_Paulo',
                userId: user?.$id || '',
                teamId: teamId || '',
                status: 'agendado' as const,
                priority: 'media' as const,
                color: '#3B82F6',
                type: 'meeting' as const,
                isPublic: false,
                recurrenceType: 'none' as const,
                recurrenceInterval: 1,
                allowGuestInvites: false,
                reminderType: 'none' as const,
                attendees: [],
                attachments: [],
                links: [],
                tags: [],
                isDeleted: false,
              };
              await createEventMutation.mutateAsync(newEventData);
              modalsActions.closeCreateEvent();
            }
          } catch (error) {
            console.error('Erro ao salvar evento:', error);
            toast.error('Erro ao salvar evento. Tente novamente.');
          }
        }}
        onDelete={async (eventId) => {
          try {
            await deleteEventMutation.mutateAsync(eventId);
            modalsActions.closeViewEvent();
          } catch (error) {
            console.error('Erro ao excluir evento:', error);
            toast.error('Erro ao excluir evento. Tente novamente.');
          }
        }}
        onEdit={(event) => {
          modalsActions.closeViewEvent();
          modalsActions.openEditEvent(event);
        }}
        event={(modalsSnapshot.editEvent.event || modalsSnapshot.viewEvent.event) as Event | null}
        initialDate={modalsSnapshot.createEvent.defaultDate}
        initialTime={modalsSnapshot.createEvent.defaultStartTime}
        mode={
          modalsSnapshot.createEvent.isOpen ? 'create' :
          modalsSnapshot.editEvent.isOpen ? 'edit' : 'view'
        }
      />
    </div>
  );
}
