import * as React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ArrowDown, Users } from "lucide-react";
import { Button } from "../ui/button";
import { ChatBubble, ChatBubbleAvatar, ChatBubbleMessage, ChatBubbleTimestamp } from './chat-bubble';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import type { ChatMessage } from '@/schemas/chat';

interface AnimatedChatListProps {
  messages: ChatMessage[];
  currentUserId?: string;
  isLoading?: boolean;
  className?: string;
}

export function AnimatedChatList({
  messages,
  currentUserId,
  isLoading = false,
  className
}: AnimatedChatListProps) {
  const scrollRef = React.useRef<HTMLDivElement>(null);
  const [isAtBottom, setIsAtBottom] = React.useState(true);

  // Auto scroll para o final quando novas mensagens chegam
  React.useEffect(() => {
    if (scrollRef.current && isAtBottom) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [messages.length, isAtBottom]);

  // Detectar se está no final
  const handleScroll = React.useCallback(() => {
    if (!scrollRef.current) return;

    const { scrollTop, scrollHeight, clientHeight } = scrollRef.current;
    const atBottom = Math.abs(scrollHeight - scrollTop - clientHeight) < 20;
    setIsAtBottom(atBottom);
  }, []);

  const scrollToBottom = () => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
      setIsAtBottom(true);
    }
  };

  // Renderizar mensagem
  const renderMessage = (msg: ChatMessage, index: number) => {
    const isOwn = msg.senderId === currentUserId;
    const prevMsg = messages[index - 1];
    const nextMsg = messages[index + 1];

    const showAvatar = !prevMsg || prevMsg.senderId !== msg.senderId;
    const showTimestamp = !nextMsg ||
      nextMsg.senderId !== msg.senderId ||
      (new Date(nextMsg.$createdAt).getTime() - new Date(msg.$createdAt).getTime()) > 300000; // 5 min

    return (
      <ChatBubble
        key={msg.$id}
        variant={isOwn ? 'sent' : 'received'}
      >
        {showAvatar && !isOwn && (
          <ChatBubbleAvatar
            src={msg.senderAvatar}
            fallback={msg.senderName.charAt(0).toUpperCase()}
            className="w-8 h-8"
          />
        )}
        <div className="flex flex-col gap-1">
          {!isOwn && showAvatar && (
            <div className="text-xs font-medium text-muted-foreground px-4">
              {msg.senderName}
            </div>
          )}
          <ChatBubbleMessage variant={isOwn ? 'sent' : 'received'}>
            {msg.content}
          </ChatBubbleMessage>
          {showTimestamp && (
            <ChatBubbleTimestamp
              timestamp={formatDistanceToNow(new Date(msg.$createdAt), {
                addSuffix: true,
                locale: ptBR,
              })}
              className={`text-xs ${isOwn ? 'text-right' : 'text-left'}`}
            />
          )}
        </div>
      </ChatBubble>
    );
  };

  return (
    <div className="relative w-full h-full">
      {/* Messages Container */}
      <div
        ref={scrollRef}
        className={`flex flex-col w-full h-full overflow-y-auto ${className}`}
        onScroll={handleScroll}
        style={{
          scrollbarWidth: 'thin',
          scrollbarColor: 'hsl(var(--border)) transparent'
        }}
      >
        <div className="flex flex-col gap-4 p-6 min-h-full">
          {/* Loading State */}
          {isLoading && (
            <div className="flex items-center justify-center py-8">
              <div className="flex items-center gap-3 text-muted-foreground">
                <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin" />
                <span className="text-sm">Carregando mensagens...</span>
              </div>
            </div>
          )}

          {/* Empty State */}
          {!isLoading && messages.length === 0 && (
            <div className="flex flex-col items-center justify-center py-16 text-center">
              <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-500/20 to-purple-600/20 flex items-center justify-center mb-6">
                <Users className="h-8 w-8 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-2">
                Nenhuma mensagem ainda
              </h3>
              <p className="text-muted-foreground max-w-sm">
                Seja o primeiro a enviar uma mensagem e começar a conversa!
              </p>
            </div>
          )}

          {/* Messages List - Animação simples apenas para novas mensagens */}
          <AnimatePresence>
            {messages.map((message, index) => (
              <motion.div
                key={message.$id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{ duration: 0.2 }}
                className="w-full"
              >
                {renderMessage(message, index)}
              </motion.div>
            ))}
          </AnimatePresence>

          {/* Spacer for better UX */}
          <div className="h-4" />
        </div>
      </div>

      {/* Scroll to Bottom Button - Sem animação */}
      {!isAtBottom && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
          <Button
            onClick={scrollToBottom}
            size="sm"
            variant="secondary"
            className="rounded-full shadow-lg border bg-background/80 backdrop-blur-sm hover:bg-background/90 transition-all duration-200"
            aria-label="Rolar para o final"
          >
            <ArrowDown className="h-4 w-4 mr-2" />
            <span className="text-xs">Nova mensagem</span>
          </Button>
        </div>
      )}
    </div>
  );
}
