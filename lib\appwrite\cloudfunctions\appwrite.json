{"projectId": "68465829002ce3fcaeb5", "functions": [{"$id": "admin-users", "name": "Admin Users", "runtime": "node-18.0", "execute": ["role:admin"], "events": [], "schedule": "", "timeout": 15, "enabled": true, "logging": true, "entrypoint": "admin-users.js", "commands": "npm install node-appwrite", "ignore": [".git", "node_modules", ".appwrite"], "path": "app/lib/appwrite/cloudfunctions", "vars": {"APPWRITE_FUNCTION_ENDPOINT": "https://cloud.appwrite.io/v1", "APPWRITE_FUNCTION_PROJECT_ID": "68465829002ce3fcaeb5"}}, {"$id": "admin-user-management", "name": "Admin User Management", "runtime": "node-18.0", "execute": ["role:admin"], "events": [], "schedule": "", "timeout": 30, "enabled": true, "logging": true, "entrypoint": "admin-user-management.js", "commands": "npm install node-appwrite", "ignore": [".git", "node_modules", ".appwrite"], "path": "app/lib/appwrite/cloudfunctions", "vars": {"APPWRITE_FUNCTION_ENDPOINT": "https://cloud.appwrite.io/v1", "APPWRITE_FUNCTION_PROJECT_ID": "68465829002ce3fcaeb5", "APPWRITE_FUNCTION_DATABASE_ID": "main", "APPWRITE_FUNCTION_USERS_COLLECTION_ID": "users"}}, {"$id": "gemini-file-processor", "name": "Gemini File Processor", "runtime": "node-18.0", "execute": ["users"], "events": [], "schedule": "", "timeout": 60, "enabled": true, "logging": true, "entrypoint": "gemini-file-processor.js", "commands": "npm install node-appwrite @google/generative-ai", "ignore": [".git", "node_modules", ".appwrite"], "path": "app/lib/appwrite/cloudfunctions", "vars": {"APPWRITE_FUNCTION_ENDPOINT": "https://cloud.appwrite.io/v1", "APPWRITE_FUNCTION_PROJECT_ID": "68465829002ce3fcaeb5", "GEMINI_MODEL": "gemini-1.5-flash", "MAX_FILE_SIZE": "10485760", "ALLOWED_MIME_TYPES": "image/jpeg,image/png,image/webp,application/pdf"}}, {"$id": "gemini-data-processor", "name": "Gemini Data Processor", "runtime": "node-18.0", "execute": ["users"], "events": [], "schedule": "", "timeout": 60, "enabled": true, "logging": true, "entrypoint": "gemini-data-processor.js", "commands": "npm install node-appwrite @google/generative-ai", "ignore": [".git", "node_modules", ".appwrite"], "path": "app/lib/appwrite/cloudfunctions", "vars": {"APPWRITE_FUNCTION_ENDPOINT": "https://cloud.appwrite.io/v1", "APPWRITE_FUNCTION_PROJECT_ID": "68465829002ce3fcaeb5", "GEMINI_MODEL": "gemini-1.5-flash"}}, {"$id": "stripe-payments", "name": "Stripe Payments", "runtime": "node-18.0", "execute": ["users"], "events": [], "schedule": "", "timeout": 30, "enabled": true, "logging": true, "entrypoint": "stripe-payments.js", "commands": "npm install node-appwrite stripe", "ignore": [".git", "node_modules", ".appwrite"], "path": "app/lib/appwrite/cloudfunctions", "vars": {"APPWRITE_FUNCTION_ENDPOINT": "https://cloud.appwrite.io/v1", "APPWRITE_FUNCTION_PROJECT_ID": "68465829002ce3fcaeb5", "APPWRITE_DATABASE_ID": "main", "APPWRITE_SUBSCRIPTIONS_COLLECTION_ID": "subscriptions", "STRIPE_SECRET_KEY": "", "STRIPE_WEBHOOK_SECRET": "", "STRIPE_PRICE_PRO_MONTHLY": "", "STRIPE_PRICE_PRO_YEARLY": "", "STRIPE_PRICE_ENTERPRISE_MONTHLY": "", "STRIPE_PRICE_ENTERPRISE_YEARLY": ""}}]}