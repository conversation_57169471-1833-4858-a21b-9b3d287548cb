import {
  type ReactNode,
  createContext,
  useContext,
  useEffect,
  useState,
} from "react";

const STORAGE_KEY = "active_theme";
const DEFAULT_THEME = "default";

function setThemeStorage(theme: string) {
  if (typeof window === "undefined") return;

  try {
    localStorage.setItem(STORAGE_KEY, theme);
  } catch (error) {
    console.warn("Failed to save theme to localStorage:", error);
  }
}

function getThemeStorage(): string | null {
  if (typeof window === "undefined") return null;

  try {
    return localStorage.getItem(STORAGE_KEY);
  } catch (error) {
    console.warn("Failed to read theme from localStorage:", error);
    return null;
  }
}

type ThemeContextType = {
  activeTheme: string;
  setActiveTheme: (theme: string) => void;
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ActiveThemeProvider({
  children,
  initialTheme,
}: {
  children: ReactNode;
  initialTheme?: string;
}) {
  const [activeTheme, setActiveTheme] = useState<string>(() => {
    // Prioridade: initialTheme (SSR) > localStorage > default
    if (initialTheme) return initialTheme;
    const stored = getThemeStorage();
    return stored || DEFAULT_THEME;
  });

  useEffect(() => {
    // Salvar no localStorage quando o tema mudar
    setThemeStorage(activeTheme);

    // Remover classes de tema anteriores
    Array.from(document.body.classList)
      .filter((className) => className.startsWith("theme-"))
      .forEach((className) => {
        document.body.classList.remove(className);
      });

    // Adicionar nova classe de tema
    document.body.classList.add(`theme-${activeTheme}`);

    // Adicionar classe scaled se necessário
    if (activeTheme.endsWith("-scaled")) {
      document.body.classList.add("theme-scaled");
    } else {
      document.body.classList.remove("theme-scaled");
    }
  }, [activeTheme]);

  return (
    <ThemeContext.Provider value={{ activeTheme, setActiveTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useThemeConfig() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error(
      "useThemeConfig must be used within an ActiveThemeProvider"
    );
  }
  return context;
}
