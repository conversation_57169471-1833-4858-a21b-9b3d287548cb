'use client';

/**
 * Componente para exibir e gerenciar reações de mensagens
 */

import React, { useState } from 'react';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '../ui/popover';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../ui/tooltip';
import { Plus, Smile } from 'lucide-react';
import { cn } from '../../lib/utils';
import { summarizeReactions, type ReactionSummary } from '../../lib/chat-utils';
import { useToggleReaction } from '../../hooks/api/use-reactions';
import { useAuth } from '../../hooks/use-auth';
import { EmojiPicker } from './emoji-picker';

interface MessageReactionsProps {
  messageId: string;
  chatId: string;
  reactions?: string[];
  className?: string;
}

// Emojis mais comuns para reação rápida
const QUICK_EMOJIS = ['👍', '❤️', '😂', '😮', '😢', '😡'];

export function MessageReactions({
  messageId,
  chatId,
  reactions = [],
  className
}: MessageReactionsProps) {
  const { user } = useAuth();
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const toggleReaction = useToggleReaction();

  const reactionSummary = summarizeReactions(reactions, user?.$id);

  const handleReactionClick = async (emoji: string) => {
    if (!user) return;

    try {
      await toggleReaction.mutateAsync({
        messageId,
        chatId,
        emoji,
        currentReactions: reactions,
      });
    } catch (error) {
      console.error('Erro ao alternar reação:', error);
    }
  };

  const handleEmojiSelect = async (emoji: string) => {
    setShowEmojiPicker(false);
    await handleReactionClick(emoji);
  };

  if (reactionSummary.length === 0) {
    return (
      <div className={cn("flex items-center gap-1", className)}>
        <Popover open={showEmojiPicker} onOpenChange={setShowEmojiPicker}>
          <PopoverTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <Plus className="h-3 w-3" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-2" align="start">
            <div className="flex flex-col gap-2">
              {/* Emojis rápidos */}
              <div className="flex gap-1">
                {QUICK_EMOJIS.map(emoji => (
                  <Button
                    key={emoji}
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 text-lg hover:bg-muted"
                    onClick={() => handleEmojiSelect(emoji)}
                  >
                    {emoji}
                  </Button>
                ))}
              </div>

              {/* Separador */}
              <div className="border-t" />

              {/* Picker completo */}
              <div className="flex items-center gap-2">
                <Smile className="h-4 w-4 text-muted-foreground" />
                <EmojiPicker
                  onEmojiSelect={handleEmojiSelect}
                  disabled={toggleReaction.isPending}
                />
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>
    );
  }

  return (
    <div className={cn("flex items-center gap-1 flex-wrap", className)}>
      {/* Reações existentes */}
      {reactionSummary.map((reaction) => (
        <ReactionBadge
          key={reaction.emoji}
          reaction={reaction}
          onClick={() => handleReactionClick(reaction.emoji)}
          isLoading={toggleReaction.isPending}
        />
      ))}

      {/* Botão para adicionar nova reação */}
      <Popover open={showEmojiPicker} onOpenChange={setShowEmojiPicker}>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
          >
            <Plus className="h-3 w-3" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-2" align="start">
          <div className="flex flex-col gap-2">
            {/* Emojis rápidos */}
            <div className="flex gap-1">
              {QUICK_EMOJIS.map(emoji => (
                <Button
                  key={emoji}
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 text-lg hover:bg-muted"
                  onClick={() => handleEmojiSelect(emoji)}
                >
                  {emoji}
                </Button>
              ))}
            </div>

            {/* Separador */}
            <div className="border-t" />

            {/* Picker completo */}
            <div className="flex items-center gap-2">
              <Smile className="h-4 w-4 text-muted-foreground" />
              <EmojiPicker
                onEmojiSelect={handleEmojiSelect}
                disabled={toggleReaction.isPending}
              />
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}

interface ReactionBadgeProps {
  reaction: ReactionSummary;
  onClick: () => void;
  isLoading?: boolean;
}

function ReactionBadge({ reaction, onClick, isLoading }: ReactionBadgeProps) {
  const getUsersText = () => {
    if (reaction.count === 1) {
      return reaction.hasCurrentUser ? 'Você' : '1 pessoa';
    }

    if (reaction.hasCurrentUser) {
      return reaction.count === 2
        ? 'Você e mais 1 pessoa'
        : `Você e mais ${reaction.count - 1} pessoas`;
    }

    return `${reaction.count} pessoas`;
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge
            variant={reaction.hasCurrentUser ? "default" : "secondary"}
            className={cn(
              "cursor-pointer transition-all hover:scale-105",
              "flex items-center gap-1 px-2 py-1 text-xs",
              reaction.hasCurrentUser && "bg-primary/20 text-primary border-primary/30",
              isLoading && "opacity-50 cursor-not-allowed"
            )}
            onClick={isLoading ? undefined : onClick}
          >
            <span className="text-sm">{reaction.emoji}</span>
            <span className="font-medium">{reaction.count}</span>
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <p>{getUsersText()} reagiu com {reaction.emoji}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
