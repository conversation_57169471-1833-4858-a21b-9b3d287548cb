/**
 * Hook para gerenciar mensagens de chat
 * Nova estratégia: buscar mensagens diretamente da coleção em vez de através do relacionamento
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useAuth } from '../use-auth';
import { chatMessages } from '../../lib/appwrite/functions/database';
import { isCacheEnabled } from '../../lib/cache-config';
import {
  hasDataInIndexedDB,
  getFromIndexedDB,
  saveToIndexedDB,
  syncUpdatedDataFromServer,
  syncAfterMutation
} from '../../lib/cache-sync';
import type { ChatMessage, CreateChatMessageData } from '@/schemas/chat';

// Função para gerar ID único para mensagens
function generateMessageId(): string {
  return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * Buscar mensagens de um chat usando estratégia local-first
 */
export function useMessages(chatId: string, teamId?: string) {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useQuery({
    queryKey: ['messages', chatId, teamId],
    queryFn: async (): Promise<ChatMessage[]> => {
      if (!chatId || !user?.$id) return [];

      // Verificar se o cache está habilitado
      if (isCacheEnabled()) {
        // Verificar se IndexedDB tem dados para estas mensagens
        const hasData = await hasDataInIndexedDB(`messages_${chatId}`, teamId || chatId);

        if (hasData) {
          // Servir dados do IndexedDB
          const cachedData = await getFromIndexedDB<ChatMessage[]>(`messages_${chatId}`, teamId || chatId);
          // Garantir que temos um array simples de mensagens
          let cachedMessages: ChatMessage[] = [];
          if (Array.isArray(cachedData)) {
            // Se é array, verificar se é array de arrays ou array simples
            if (cachedData.length > 0 && Array.isArray(cachedData[0])) {
              // Array de arrays - pegar o primeiro array
              cachedMessages = cachedData[0] as ChatMessage[];
            } else {
              // Array simples - fazer cast explícito
              cachedMessages = cachedData as unknown as ChatMessage[];
            }
          } else if (cachedData) {
            // Objeto único
            cachedMessages = [cachedData as ChatMessage];
          }

          if (cachedMessages.length > 0) {
            console.log(`🗄️ Mensagens do chat ${chatId} carregadas do cache local`);

            // Sincronizar em background
            setTimeout(async () => {
              try {
                const serverMessages = await chatMessages.getByChatIdWithDeleted(chatId);
                const activeMessages = serverMessages.filter((msg: any) => !msg.isDeleted);
                const deletedMessages = serverMessages.filter((msg: any) => msg.isDeleted);

                // Merge inteligente: manter mensagens locais + adicionar novas do servidor
                let mergedMessages = [...cachedMessages];

                // Remover mensagens que foram deletadas no servidor
                deletedMessages.forEach((deletedMsg: any) => {
                  mergedMessages = mergedMessages.filter(msg => msg.$id !== deletedMsg.$id);
                });

                // Adicionar/atualizar mensagens ativas
                activeMessages.forEach((newMessage: any) => {
                  const existingIndex = mergedMessages.findIndex(msg => msg.$id === newMessage.$id);
                  if (existingIndex >= 0) {
                    mergedMessages[existingIndex] = newMessage;
                  } else {
                    mergedMessages.push(newMessage);
                  }
                });

                // Ordenar mensagens por data de criação
                mergedMessages.sort((a, b) => new Date(a.$createdAt).getTime() - new Date(b.$createdAt).getTime());

                // Atualizar cache do React Query
                queryClient.setQueryData<ChatMessage[]>(['messages', chatId, teamId], mergedMessages);

                // Salvar mensagens atualizadas no IndexedDB
                await saveToIndexedDB(`messages_${chatId}`, mergedMessages, {
                  collection: `messages_${chatId}`,
                  userId: teamId || chatId
                });

                console.log(`🔄 Mensagens do chat ${chatId} sincronizadas em background`);
              } catch (error) {
                console.warn('Erro na sincronização em background:', error);
              }
            }, 100);

            return cachedMessages;
          }
        }
      }

      // Buscar do servidor (só se IndexedDB estiver vazio)
      console.log(`🌐 Buscando mensagens do chat ${chatId} do servidor`);

      // Buscar mensagens diretamente da coleção
      const messages = await chatMessages.getByChatId(chatId) as ChatMessage[];

      // Salvar no cache se habilitado
      if (isCacheEnabled() && messages.length > 0) {
        await saveToIndexedDB(`messages_${chatId}`, messages, {
          collection: `messages_${chatId}`,
          userId: teamId || chatId
        });
      }

      return messages;
    },
    enabled: !!chatId && !!user?.$id,
    staleTime: isCacheEnabled() ? 0 : 5 * 60 * 1000,
    refetchOnWindowFocus: false,
  });
}

/**
 * Enviar nova mensagem
 */
export function useSendMessage() {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async (data: CreateChatMessageData & { messageId?: string }): Promise<ChatMessage> => {
      if (!user) throw new Error('Usuário não autenticado');

      const messageData = {
        content: data.content,
        teamId: data.teamId,
        type: data.type || 'text',
        senderId: user.$id,
        senderName: user.name || 'Usuário',
        senderAvatar: user.prefs?.avatar || undefined,
        status: 'sent' as const,
        userId: user.$id,
        createdBy: user.$id,
        isDeleted: false,
        // Relacionamento - usar 'chat' em vez de 'chatId'
        chat: data.chatId,
        // Novas funcionalidades
        replyTo: data.replyTo,
        reactions: data.reactions || [],
        mentions: data.mentions || [],
      };

      // Usar o ID fornecido ou gerar um novo
      const messageId = data.messageId || generateMessageId();
      const result = await chatMessages.create(messageData, undefined, messageId);
      return result as ChatMessage;
    },

    onMutate: async (data) => {
      // Cancelar queries em andamento
      await queryClient.cancelQueries({ queryKey: ['messages', data.chatId, data.teamId] });

      // Snapshot do estado anterior
      const previousMessages = queryClient.getQueryData<ChatMessage[]>(['messages', data.chatId, data.teamId]);

      // Criar mensagem temporária
      const tempMessage: ChatMessage = {
        $id: data.messageId || generateMessageId(),
        $createdAt: new Date().toISOString(),
        $updatedAt: new Date().toISOString(),
        $permissions: [],
        $databaseId: '',
        $collectionId: '',
        content: data.content,
        senderId: user?.$id || '',
        senderName: user?.name || 'Usuário',
        senderAvatar: user?.prefs?.avatar,
        teamId: data.teamId,
        type: data.type || 'text',
        status: 'sending',
        userId: user?.$id || '',
        createdBy: user?.$id || '',
        isDeleted: false,
        chatId: data.chatId, // Usar chatId em vez de chat para mensagem temporária
        // Novas funcionalidades
        replyTo: data.replyTo,
        reactions: data.reactions || [],
        mentions: data.mentions || [],
      } as ChatMessage;

      // Atualizar cache otimisticamente
      const updatedMessages = [...(previousMessages || []), tempMessage];
      queryClient.setQueryData<ChatMessage[]>(['messages', data.chatId, data.teamId], updatedMessages);

      return { previousMessages, tempMessage };
    },

    onError: (error, data, context) => {
      // Reverter para estado anterior em caso de erro
      if (context?.previousMessages) {
        queryClient.setQueryData<ChatMessage[]>(['messages', data.chatId, data.teamId], context.previousMessages);
      }
      toast.error('Erro ao enviar mensagem');
      console.error('Erro ao enviar mensagem:', error);
    },

    onSuccess: async (newMessage, data) => {
      // Atualizar mensagem temporária com dados reais do servidor
      const currentMessages = queryClient.getQueryData<ChatMessage[]>(['messages', data.chatId, data.teamId]);
      if (currentMessages) {
        const updatedMessages = currentMessages.map(msg =>
          msg.$id === newMessage.$id ? { ...newMessage, status: 'sent' as const } : msg
        );

        queryClient.setQueryData<ChatMessage[]>(['messages', data.chatId, data.teamId], updatedMessages);

        // Sincronizar com IndexedDB se cache estiver habilitado
        if (isCacheEnabled()) {
          await saveToIndexedDB(`messages_${data.chatId}`, updatedMessages, {
            collection: `messages_${data.chatId}`,
            userId: data.teamId
          });
        }
      }

      console.log('✅ Mensagem enviada com sucesso');
    },
  });
}

/**
 * Editar mensagem existente
 */
export function useEditMessage() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ messageId, content, chatId, teamId }: { messageId: string; content: string; chatId: string; teamId: string }) => {
      const result = await chatMessages.update(messageId, {
        content,
        edited: true,
        editedAt: new Date().toISOString(),
      });
      return { result, chatId, teamId };
    },

    onSuccess: async ({ result, chatId, teamId }) => {
      // Atualizar cache local
      const currentMessages = queryClient.getQueryData<ChatMessage[]>(['messages', chatId, teamId]);
      if (currentMessages) {
        const updatedMessages = currentMessages.map(msg =>
          msg.$id === result.$id ? { ...result as ChatMessage } : msg
        );

        queryClient.setQueryData<ChatMessage[]>(['messages', chatId, teamId], updatedMessages);

        // Sincronizar com IndexedDB
        if (isCacheEnabled()) {
          await saveToIndexedDB(`messages_${chatId}`, updatedMessages, {
            collection: `messages_${chatId}`,
            userId: teamId
          });
        }
      }

      toast.success('Mensagem editada');
    },

    onError: (error) => {
      toast.error('Erro ao editar mensagem');
      console.error('Erro ao editar mensagem:', error);
    },
  });
}

/**
 * Deletar mensagem (soft delete)
 */
export function useDeleteMessage() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ messageId, chatId, teamId }: { messageId: string; chatId: string; teamId: string }) => {
      await chatMessages.softDelete(messageId);
      return { messageId, chatId, teamId };
    },

    onSuccess: async ({ messageId, chatId, teamId }) => {
      // Remover mensagem do cache local
      const currentMessages = queryClient.getQueryData<ChatMessage[]>(['messages', chatId, teamId]);
      if (currentMessages) {
        const updatedMessages = currentMessages.filter(msg => msg.$id !== messageId);

        queryClient.setQueryData<ChatMessage[]>(['messages', chatId, teamId], updatedMessages);

        // Sincronizar com IndexedDB
        if (isCacheEnabled()) {
          await saveToIndexedDB(`messages_${chatId}`, updatedMessages, {
            collection: `messages_${chatId}`,
            userId: teamId
          });
        }
      }

      toast.success('Mensagem removida');
    },

    onError: (error) => {
      toast.error('Erro ao remover mensagem');
      console.error('Erro ao remover mensagem:', error);
    },
  });
}
