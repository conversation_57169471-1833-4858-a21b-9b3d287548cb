import { useState, useMemo } from 'react';
import {
  type ColumnDef,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  type SortingState,
  type ColumnFiltersState,
  type VisibilityState,
} from '@tanstack/react-table';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from '../ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import { Badge } from '../ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import {
  MoreHorizontal,
  Search,
  ArrowUpDown,
  Mail,
  Crown,
  Shield,
  User,
  Edit,
  Trash2,
  UserMinus,
  Eye,
  Filter,
  Columns,
  X,
  Download,
} from 'lucide-react';
import type { TeamMembership } from '@/schemas/teams';
// import { useTeamPermissions, useCanManageMember } from '../../hooks/use-api'; // TODO: implementar
import { useAuth } from '../../hooks/use-auth';

interface TeamMembersTableProps {
  teamId: string;
  data: TeamMembership[];
  isLoading?: boolean;
  onEdit?: (member: TeamMembership) => void;
  onRemove?: (member: TeamMembership) => void;
  onView?: (member: TeamMembership) => void;
  onExport?: () => void;
}

const getRoleIcon = (roles: string[]) => {
  if (roles.includes('owner')) return <Crown className="h-3 w-3 text-yellow-500" />;
  if (roles.includes('admin')) return <Shield className="h-3 w-3 text-blue-500" />;
  return <User className="h-3 w-3 text-gray-500" />;
};

const getRoleLabel = (roles: string[]) => {
  if (roles.includes('owner')) return 'Proprietário';
  if (roles.includes('admin')) return 'Administrador';
  return 'Membro';
};

const getRoleBadgeVariant = (roles: string[]) => {
  if (roles.includes('owner')) return 'default';
  if (roles.includes('admin')) return 'secondary';
  return 'outline';
};

export function TeamMembersTable({
  teamId,
  data,
  isLoading,
  onEdit,
  onRemove,
  onView,
  onExport
}: TeamMembersTableProps) {
  const { user } = useAuth();
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [globalFilter, setGlobalFilter] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // TODO: implementar verificação de permissões
  const canInviteMembers = true;
  const canRemoveMembers = true;
  const canUpdateRoles = true;

  const columns: ColumnDef<TeamMembership>[] = [
    {
      accessorKey: 'userName',
      enableHiding: true,
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          className="h-auto p-0 font-semibold"
        >
          Membro
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const member = row.original;
        const initials = member.userName
          ? member.userName.split(' ').map(n => n[0]).join('').toUpperCase()
          : member.userEmail.substring(0, 2).toUpperCase();

        return (
          <div className="flex items-center space-x-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src={undefined} />
              <AvatarFallback className="text-xs">
                {initials}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-medium">{member.userName || 'Usuário'}</div>
              <div className="text-sm text-muted-foreground flex items-center gap-1">
                <Mail className="h-3 w-3" />
                {member.userEmail}
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'roles',
      enableHiding: true,
      header: 'Função',
      cell: ({ row }) => {
        const member = row.original;
        const roles = member.roles || [];

        return (
          <div className="flex items-center gap-2">
            {getRoleIcon(roles)}
            <Badge variant={getRoleBadgeVariant(roles)}>
              {getRoleLabel(roles)}
            </Badge>
          </div>
        );
      },
    },
    {
      accessorKey: 'confirm',
      enableHiding: true,
      header: 'Status',
      cell: ({ row }) => {
        const member = row.original;

        return (
          <Badge variant={member.confirm ? 'default' : 'secondary'}>
            {member.confirm ? 'Ativo' : 'Convite Pendente'}
          </Badge>
        );
      },
    },
    {
      accessorKey: '$createdAt',
      enableHiding: true,
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          className="h-auto p-0 font-semibold"
        >
          Adicionado em
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const member = row.original;
        return (
          <div className="text-sm">
            {new Date(member.$createdAt).toLocaleDateString('pt-BR')}
          </div>
        );
      },
    },
    {
      id: 'actions',
      header: 'Ações',
      cell: ({ row }) => {
        const member = row.original;
        const canManage = true; // TODO: implementar verificação de permissões
        const isCurrentUser = member.userId === user?.$id;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Abrir menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {onView && (
                <DropdownMenuItem onClick={() => onView(member)}>
                  <Eye className="mr-2 h-4 w-4" />
                  Visualizar
                </DropdownMenuItem>
              )}

              {canManage && !isCurrentUser && onEdit && canUpdateRoles && (
                <DropdownMenuItem onClick={() => onEdit(member)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Alterar Função
                </DropdownMenuItem>
              )}

              {canManage && !isCurrentUser && onRemove && canRemoveMembers && (
                <DropdownMenuItem
                  onClick={() => onRemove(member)}
                  className="text-destructive"
                >
                  <UserMinus className="mr-2 h-4 w-4" />
                  Remover do Time
                </DropdownMenuItem>
              )}

              {isCurrentUser && (
                <DropdownMenuItem className="text-muted-foreground" disabled>
                  <User className="mr-2 h-4 w-4" />
                  Você mesmo
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  // Filtro customizado que busca em todos os campos
  const customGlobalFilter = (row: any, columnId: string, value: string) => {
    const member = row.original as TeamMembership;
    const searchValue = value.toLowerCase();

    // Buscar em nome, email, função
    const searchableText = [
      member.userName || '',
      member.userEmail || '',
      member.roles?.join(' ') || '',
      getRoleLabel(member.roles || []),
      member.confirm ? 'ativo' : 'pendente' // usar confirm para determinar status
    ].join(' ').toLowerCase();

    return searchableText.includes(searchValue);
  };

  // Filtrar dados baseado nos filtros específicos usando useMemo
  const filteredData = useMemo(() => {
    return data.filter(member => {
      // Filtro por função
      if (roleFilter !== 'all') {
        const hasRole = member.roles?.includes(roleFilter);
        if (!hasRole) return false;
      }

      // Filtro por status (baseado no confirm do Appwrite)
      if (statusFilter !== 'all') {
        const memberStatus = member.confirm ? 'active' : 'pending';
        if (memberStatus !== statusFilter) return false;
      }

      return true;
    });
  }, [data, roleFilter, statusFilter]);

  const table = useReactTable({
    data: filteredData,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    globalFilterFn: customGlobalFilter,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      globalFilter,
    },
  });

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <div className="h-10 w-80 bg-muted animate-pulse rounded"></div>
        </div>
        <div className="rounded-md border">
          <div className="h-96 bg-muted animate-pulse"></div>
        </div>
      </div>
    );
  }

  const clearFilters = () => {
    setGlobalFilter('');
    setRoleFilter('all');
    setStatusFilter('all');
  };

  const hasActiveFilters = globalFilter || roleFilter !== 'all' || statusFilter !== 'all';

  return (
    <div className="space-y-4">
      {/* Filtros e Controles */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex flex-1 items-center gap-2">
          {/* Busca Global */}
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Buscar membros..."
              value={globalFilter}
              onChange={(e) => setGlobalFilter(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Filtro por Função */}
          <Select value={roleFilter} onValueChange={setRoleFilter}>
            <SelectTrigger className="w-[160px]">
              <SelectValue>
                {roleFilter === 'all' ? 'Função: Todas' :
                 roleFilter === 'owner' ? 'Função: Proprietário' :
                 roleFilter === 'admin' ? 'Função: Administrador' :
                 roleFilter === 'member' ? 'Função: Membro' :
                 'Função'}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Função: Todas</SelectItem>
              <SelectItem value="owner">Função: Proprietário</SelectItem>
              <SelectItem value="admin">Função: Administrador</SelectItem>
              <SelectItem value="member">Função: Membro</SelectItem>
            </SelectContent>
          </Select>

          {/* Filtro por Status */}
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[140px]">
              <SelectValue>
                {statusFilter === 'all' ? 'Status: Todos' :
                 statusFilter === 'active' ? 'Status: Ativo' :
                 statusFilter === 'pending' ? 'Status: Pendente' :
                 'Status'}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Status: Todos</SelectItem>
              <SelectItem value="active">Status: Ativo</SelectItem>
              <SelectItem value="pending">Status: Pendente</SelectItem>
            </SelectContent>
          </Select>

          {/* Limpar Filtros */}
          {hasActiveFilters && (
            <Button
              variant="ghost"
              onClick={clearFilters}
              className="h-8 px-2 lg:px-3"
            >
              Limpar
              <X className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Controles de Export e Colunas */}
        <div className="flex items-center gap-2">
          {onExport && (
            <Button variant="outline" size="sm" onClick={onExport}>
              <Download className="mr-2 h-4 w-4" />
              Exportar
            </Button>
          )}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Columns className="mr-2 h-4 w-4" />
                Colunas
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[200px]">
              <DropdownMenuLabel>Alternar colunas</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {table
                .getAllColumns()
                .filter(
                  (column) =>
                    typeof column.accessorFn !== "undefined" &&
                    column.getCanHide()
                )
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {column.id === 'userName' ? 'Membro' :
                       column.id === 'roles' ? 'Função' :
                       column.id === 'joinedAt' ? 'Entrada' :
                       column.id === 'status' ? 'Status' :
                       column.id}
                    </DropdownMenuCheckboxItem>
                  )
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  Nenhum membro encontrado.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between space-x-2 py-4">
        <div className="text-sm text-muted-foreground">
          {table.getFilteredRowModel().rows.length} membro(s) encontrado(s)
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Anterior
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Próximo
          </Button>
        </div>
      </div>
    </div>
  );
}
