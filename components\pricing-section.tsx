"use client"
import React, { useState } from 'react';
import { <PERSON><PERSON> } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Switch } from './ui/switch';
import { Label } from './ui/label';
import Link from 'next/link';
import { Check, Crown, Zap, Building, Star } from 'lucide-react';
import { PLANS, formatPrice, calculateYearlyDiscount } from '../lib/plans';

export function PricingSection() {
  const [isYearly, setIsYearly] = useState(false);

  return (
    <section className="py-16 md:py-24">
      <div className="container mx-auto px-4 max-w-7xl">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Preços que Crescem com Você
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto mb-8">
            Escolha o plano perfeito para suas necessidades. Comece grátis e faça upgrade quando precisar.
          </p>

          {/* Toggle Anual/Mensal */}
          <div className="flex items-center justify-center gap-4 mb-8">
            <Label htmlFor="billing-toggle" className={!isYearly ? 'font-medium' : ''}>
              Mensal
            </Label>
            <Switch
              id="billing-toggle"
              checked={isYearly}
              onCheckedChange={setIsYearly}
            />
            <Label htmlFor="billing-toggle" className={isYearly ? 'font-medium' : ''}>
              Anual
            </Label>
            {isYearly && (
              <Badge variant="secondary" className="ml-2">
                Economize 2 meses
              </Badge>
            )}
          </div>
        </div>

        <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {PLANS.map((plan) => {
            const price = isYearly ? plan.price.yearly : plan.price.monthly;
            const period = isYearly ? plan.period.yearly : plan.period.monthly;
            const discount = isYearly ? calculateYearlyDiscount(plan.price.monthly, plan.price.yearly) : 0;

            const PlanIcon = plan.id === 'free' ? Star :
                           plan.id === 'pro' ? Zap :
                           Building;

            return (
              <Card
                key={plan.id}
                className={`relative ${plan.popular ? 'border-primary shadow-lg scale-105' : ''}`}
              >
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-primary text-primary-foreground">
                      Mais Popular
                    </Badge>
                  </div>
                )}

                <CardHeader className="text-center pb-8">
                  <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                    <PlanIcon className="w-6 h-6 text-primary" />
                  </div>

                  <CardTitle className="text-xl">{plan.name}</CardTitle>
                  <CardDescription className="text-sm">
                    {plan.description}
                  </CardDescription>

                  <div className="mt-4">
                    <div className="flex items-baseline justify-center gap-1">
                      <span className="text-3xl font-bold">
                        {formatPrice(price)}
                      </span>
                      <span className="text-muted-foreground">
                        {period}
                      </span>
                    </div>

                    {isYearly && discount > 0 && (
                      <div className="text-sm text-muted-foreground mt-1">
                        <span className="line-through">
                          {formatPrice(plan.price.monthly * 12)}/ano
                        </span>
                        <span className="text-green-600 ml-2">
                          -{discount}%
                        </span>
                      </div>
                    )}
                  </div>
                </CardHeader>

                <CardContent className="space-y-6">
                  {/* Recursos */}
                  <div className="space-y-3">
                    {plan.features.map((feature, index) => (
                      <div key={index} className="flex items-start gap-3">
                        <Check className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-sm">{feature.name}</span>
                      </div>
                    ))}
                  </div>

                  {/* Limites */}
                  <div className="border-t pt-4 space-y-2">
                    <h4 className="font-medium text-sm">Limites inclusos:</h4>
                    <div className="space-y-1 text-sm text-muted-foreground">
                      <div>Times: {plan.limits.teams === -1 ? 'Ilimitados' : plan.limits.teams}</div>
                      <div>Membros por time: {plan.limits.membersPerTeam === -1 ? 'Ilimitados' : plan.limits.membersPerTeam}</div>
                      <div>Armazenamento: {plan.limits.storage}</div>
                      <div>Chamadas API: {plan.limits.apiCalls === -1 ? 'Ilimitadas' : plan.limits.apiCalls.toLocaleString()}</div>
                    </div>
                  </div>

                  {/* Botão */}
                  <Button
                    className="w-full"
                    variant={plan.buttonVariant}
                    asChild
                  >
                    {plan.id === 'free' ? (
                      <Link href="/register">
                        {plan.buttonText}
                      </Link>
                    ) : plan.id === 'enterprise' ? (
                      <a href="mailto:<EMAIL>">
                        {plan.buttonText}
                      </a>
                    ) : (
                      <Link href="/register">
                        {plan.buttonText}
                      </Link>
                    )}
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Recursos Comuns */}
        <div className="mt-16 text-center">
          <h3 className="text-xl font-semibold mb-6">Todos os planos incluem:</h3>
          <div className="grid md:grid-cols-5 gap-4 max-w-4xl mx-auto">
            {[
              'SSL e Segurança',
              'Suporte Técnico',
              'Atualizações Automáticas',
              'Backup de Dados',
              'Monitoramento 24/7'
            ].map((feature, index) => (
              <div key={index} className="flex items-center gap-2 justify-center">
                <Check className="w-4 h-4 text-green-500" />
                <span className="text-sm">{feature}</span>
              </div>
            ))}
          </div>
        </div>

        {/* FAQ Rápido */}
        <div className="mt-16 bg-muted/30 rounded-lg p-8">
          <h3 className="text-xl font-semibold mb-6 text-center">Perguntas Frequentes sobre Preços</h3>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2">Posso mudar de plano a qualquer momento?</h4>
              <p className="text-sm text-muted-foreground">
                Sim! Você pode fazer upgrade ou downgrade do seu plano a qualquer momento.
                As mudanças são aplicadas imediatamente.
              </p>
            </div>
            <div>
              <h4 className="font-medium mb-2">Há período de teste gratuito?</h4>
              <p className="text-sm text-muted-foreground">
                O plano gratuito permite testar todas as funcionalidades básicas.
                Para recursos premium, oferecemos garantia de 30 dias.
              </p>
            </div>
            <div>
              <h4 className="font-medium mb-2">Como funciona o pagamento anual?</h4>
              <p className="text-sm text-muted-foreground">
                Com o pagamento anual, você economiza 2 meses e recebe desconto
                imediato na renovação.
              </p>
            </div>
            <div>
              <h4 className="font-medium mb-2">Posso cancelar a qualquer momento?</h4>
              <p className="text-sm text-muted-foreground">
                Sim, não há contratos de longo prazo. Você pode cancelar sua
                assinatura a qualquer momento.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
