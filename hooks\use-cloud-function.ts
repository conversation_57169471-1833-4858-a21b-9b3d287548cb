/**
 * Hook para execução de cloud functions
 * 
 * Fornece uma interface React para executar cloud functions do Appwrite
 * com estado de loading, erro e configuração automática.
 */

import { useState, useCallback } from 'react';
import { 
  executeFunction, 
  type FunctionExecutionOptions, 
  type FunctionExecutionResult 
} from '../lib/appwrite/cloudfunctions/executor';
import { 
  isFunctionConfigured, 
  type CloudFunctionName 
} from '../lib/appwrite/cloudfunctions/const';

export interface CloudFunctionState {
  isLoading: boolean;
  error: string | null;
  lastResult: FunctionExecutionResult | null;
  isConfigured: boolean;
}

export interface CloudFunctionHook extends CloudFunctionState {
  execute: (options?: FunctionExecutionOptions) => Promise<FunctionExecutionResult>;
  reset: () => void;
}

/**
 * Hook para executar uma cloud function específica
 */
export function useCloudFunction(functionName: CloudFunctionName): CloudFunctionHook {
  const [state, setState] = useState<CloudFunctionState>({
    isLoading: false,
    error: null,
    lastResult: null,
    isConfigured: isFunctionConfigured(functionName),
  });

  const execute = useCallback(async (options: FunctionExecutionOptions = {}) => {
    setState(prev => ({
      ...prev,
      isLoading: true,
      error: null,
    }));

    try {
      const result = await executeFunction(functionName, options);
      
      setState(prev => ({
        ...prev,
        isLoading: false,
        lastResult: result,
        error: result.success ? null : result.error || 'Unknown error',
      }));

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));

      // Return error result
      return {
        success: false,
        error: errorMessage,
      };
    }
  }, [functionName]);

  const reset = useCallback(() => {
    setState({
      isLoading: false,
      error: null,
      lastResult: null,
      isConfigured: isFunctionConfigured(functionName),
    });
  }, [functionName]);

  return {
    ...state,
    execute,
    reset,
  };
}

/**
 * Hook para executar múltiplas cloud functions
 */
export function useCloudFunctions(functionNames: CloudFunctionName[]) {
  const [globalState, setGlobalState] = useState({
    isLoading: false,
    error: null as string | null,
  });

  const executeMultiple = useCallback(async (
    executions: Array<{
      functionName: CloudFunctionName;
      options?: FunctionExecutionOptions;
    }>
  ) => {
    setGlobalState({ isLoading: true, error: null });

    try {
      const results = await Promise.allSettled(
        executions.map(({ functionName, options }) => 
          executeFunction(functionName, options)
        )
      );

      const allSuccessful = results.every(
        result => result.status === 'fulfilled' && result.value.success
      );

      if (!allSuccessful) {
        const errors = results
          .filter(result => result.status === 'rejected' || !result.value.success)
          .map(result => 
            result.status === 'rejected' 
              ? result.reason.message 
              : result.value.error
          );
        
        throw new Error(`Some functions failed: ${errors.join(', ')}`);
      }

      setGlobalState({ isLoading: false, error: null });
      
      return results.map(result => 
        result.status === 'fulfilled' ? result.value : null
      );
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setGlobalState({ isLoading: false, error: errorMessage });
      throw error;
    }
  }, []);

  const functions = functionNames.reduce((acc, name) => {
    acc[name] = useCloudFunction(name);
    return acc;
  }, {} as Record<CloudFunctionName, CloudFunctionHook>);

  return {
    ...globalState,
    functions,
    executeMultiple,
  };
}

/**
 * Hook para verificar status de configuração de funções
 */
export function useCloudFunctionStatus(functionNames: CloudFunctionName[]) {
  const status = functionNames.reduce((acc, name) => {
    acc[name] = {
      isConfigured: isFunctionConfigured(name),
      name,
    };
    return acc;
  }, {} as Record<CloudFunctionName, { isConfigured: boolean; name: string }>);

  const allConfigured = Object.values(status).every(s => s.isConfigured);
  const configuredCount = Object.values(status).filter(s => s.isConfigured).length;
  const totalCount = functionNames.length;

  return {
    status,
    allConfigured,
    configuredCount,
    totalCount,
    configurationRate: totalCount > 0 ? configuredCount / totalCount : 0,
  };
}

/**
 * Hook simplificado para execução rápida
 */
export function useQuickCloudFunction() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const execute = useCallback(async (
    functionName: CloudFunctionName,
    data?: Record<string, unknown>,
    options?: Omit<FunctionExecutionOptions, 'data'>
  ) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await executeFunction(functionName, {
        ...options,
        data,
      });

      setIsLoading(false);
      
      if (!result.success) {
        setError(result.error || 'Unknown error');
      }

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setIsLoading(false);
      setError(errorMessage);
      
      return {
        success: false,
        error: errorMessage,
      };
    }
  }, []);

  return {
    execute,
    isLoading,
    error,
    reset: () => {
      setIsLoading(false);
      setError(null);
    },
  };
}
