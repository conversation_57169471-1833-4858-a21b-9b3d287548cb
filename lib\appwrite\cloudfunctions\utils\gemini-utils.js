/**
 * Gemini AI Utilities
 * 
 * Funções utilitárias para processamento com Google Gemini AI
 */

/**
 * Prompts especializados para diferentes tipos de documentos
 */
export const SPECIALIZED_PROMPTS = {
  // Documentos de identidade
  identity_document: `
    Analise este documento de identidade e extraia as informações em formato JSON:
    {
      "document_type": "Tipo do documento (RG, CPF, CNH, Passaporte, etc.)",
      "document_number": "Número do documento",
      "name": "Nome completo",
      "birth_date": "Data de nascimento",
      "nationality": "Nacionalidade",
      "issuing_authority": "Órgão emissor",
      "issue_date": "Data de emissão",
      "expiry_date": "Data de validade (se aplicável)",
      "address": "Endereço (se disponível)",
      "photo_present": "true/false se há foto",
      "additional_info": "Outras informações relevantes"
    }
    
    Seja preciso com datas (formato YYYY-MM-DD). Use null para campos não disponíveis.
    Retorne apenas o JSON válido.
  `,

  // Comprovantes de endereço
  address_proof: `
    Analise este comprovante de endereço e extraia as informações em formato JSON:
    {
      "document_type": "Tipo (conta de luz, água, telefone, etc.)",
      "recipient_name": "Nome do destinatário",
      "address": {
        "street": "Rua/Avenida",
        "number": "Número",
        "complement": "Complemento",
        "neighborhood": "Bairro",
        "city": "Cidade",
        "state": "Estado",
        "zip_code": "CEP",
        "full_address": "Endereço completo"
      },
      "issue_date": "Data de emissão",
      "due_date": "Data de vencimento (se aplicável)",
      "amount": "Valor (se aplicável)",
      "company": "Empresa emissora",
      "account_number": "Número da conta (se visível)"
    }
    
    Use null para campos não disponíveis. Retorne apenas o JSON válido.
  `,

  // Contratos
  contract: `
    Analise este contrato e extraia as informações principais em formato JSON:
    {
      "contract_type": "Tipo do contrato",
      "parties": [
        {
          "role": "Papel (contratante, contratado, etc.)",
          "name": "Nome da parte",
          "document": "CPF/CNPJ",
          "address": "Endereço"
        }
      ],
      "object": "Objeto do contrato",
      "value": "Valor do contrato",
      "currency": "Moeda",
      "start_date": "Data de início",
      "end_date": "Data de término",
      "payment_terms": "Condições de pagamento",
      "key_clauses": ["Cláusulas importantes"],
      "signatures_present": "true/false se há assinaturas"
    }
    
    Retorne apenas o JSON válido.
  `,

  // Recibos/Notas fiscais
  receipt: `
    Analise este recibo/nota fiscal e extraia as informações em formato JSON:
    {
      "document_type": "Tipo (recibo, nota fiscal, cupom, etc.)",
      "number": "Número do documento",
      "series": "Série (se aplicável)",
      "date": "Data de emissão",
      "seller": {
        "name": "Nome/Razão social",
        "document": "CPF/CNPJ",
        "address": "Endereço"
      },
      "buyer": {
        "name": "Nome/Razão social",
        "document": "CPF/CNPJ",
        "address": "Endereço"
      },
      "items": [
        {
          "description": "Descrição do item/serviço",
          "quantity": "Quantidade",
          "unit_price": "Preço unitário",
          "total": "Total do item"
        }
      ],
      "subtotal": "Subtotal",
      "taxes": "Impostos",
      "total": "Valor total",
      "payment_method": "Forma de pagamento"
    }
    
    Retorne apenas o JSON válido.
  `,

  // Extratos bancários
  bank_statement: `
    Analise este extrato bancário e extraia as informações em formato JSON:
    {
      "bank_name": "Nome do banco",
      "account_holder": "Nome do titular",
      "account_number": "Número da conta",
      "agency": "Agência",
      "period": {
        "start_date": "Data inicial",
        "end_date": "Data final"
      },
      "opening_balance": "Saldo inicial",
      "closing_balance": "Saldo final",
      "transactions": [
        {
          "date": "Data da transação",
          "description": "Descrição",
          "type": "Débito/Crédito",
          "amount": "Valor",
          "balance": "Saldo após transação"
        }
      ],
      "summary": {
        "total_credits": "Total de créditos",
        "total_debits": "Total de débitos",
        "transaction_count": "Número de transações"
      }
    }
    
    Retorne apenas o JSON válido.
  `
};

/**
 * Configurações de modelo por tipo de processamento
 */
export const MODEL_CONFIGS = {
  // Para documentos simples - modelo mais rápido e barato
  simple: {
    model: 'gemini-1.5-flash',
    temperature: 0.1,
    maxOutputTokens: 1024
  },
  
  // Para documentos complexos - modelo mais preciso
  complex: {
    model: 'gemini-1.5-pro',
    temperature: 0.1,
    maxOutputTokens: 2048
  },
  
  // Para análise criativa/interpretativa
  creative: {
    model: 'gemini-1.5-pro',
    temperature: 0.7,
    maxOutputTokens: 2048
  }
};

/**
 * Valida e sanitiza dados extraídos
 */
export function validateExtractedData(data, documentType) {
  const validators = {
    cpf: (value) => {
      if (!value) return null;
      const cleaned = value.replace(/\D/g, '');
      return cleaned.length === 11 ? cleaned : null;
    },
    
    cnpj: (value) => {
      if (!value) return null;
      const cleaned = value.replace(/\D/g, '');
      return cleaned.length === 14 ? cleaned : null;
    },
    
    date: (value) => {
      if (!value) return null;
      try {
        const date = new Date(value);
        return isNaN(date.getTime()) ? null : date.toISOString().split('T')[0];
      } catch {
        return null;
      }
    },
    
    currency: (value) => {
      if (!value) return null;
      const cleaned = value.replace(/[^\d,.-]/g, '');
      return parseFloat(cleaned.replace(',', '.')) || null;
    },
    
    phone: (value) => {
      if (!value) return null;
      const cleaned = value.replace(/\D/g, '');
      return cleaned.length >= 10 ? cleaned : null;
    },
    
    email: (value) => {
      if (!value) return null;
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(value) ? value.toLowerCase() : null;
    }
  };

  // Aplicar validações baseadas no tipo de documento
  const validatedData = { ...data };
  
  if (data.document_number) {
    if (documentType === 'cpf' || data.document_type?.toLowerCase().includes('cpf')) {
      validatedData.document_number = validators.cpf(data.document_number);
    } else if (documentType === 'cnpj' || data.document_type?.toLowerCase().includes('cnpj')) {
      validatedData.document_number = validators.cnpj(data.document_number);
    }
  }
  
  // Validar datas
  ['birth_date', 'issue_date', 'expiry_date', 'start_date', 'end_date', 'date'].forEach(field => {
    if (data[field]) {
      validatedData[field] = validators.date(data[field]);
    }
  });
  
  // Validar valores monetários
  ['amount', 'value', 'total', 'subtotal'].forEach(field => {
    if (data[field]) {
      validatedData[field] = validators.currency(data[field]);
    }
  });
  
  // Validar email
  if (data.email) {
    validatedData.email = validators.email(data.email);
  }
  
  // Validar telefone
  if (data.phone) {
    validatedData.phone = validators.phone(data.phone);
  }
  
  return validatedData;
}

/**
 * Detecta automaticamente o tipo de documento baseado no conteúdo
 */
export function detectDocumentType(filename, mimeType, extractedText = '') {
  const filename_lower = filename.toLowerCase();
  const text_lower = extractedText.toLowerCase();
  
  // Detectar por nome do arquivo
  if (filename_lower.includes('rg') || filename_lower.includes('identidade')) {
    return 'identity_document';
  }
  if (filename_lower.includes('cpf')) {
    return 'identity_document';
  }
  if (filename_lower.includes('cnh') || filename_lower.includes('carteira')) {
    return 'identity_document';
  }
  if (filename_lower.includes('comprovante') || filename_lower.includes('endereco')) {
    return 'address_proof';
  }
  if (filename_lower.includes('contrato')) {
    return 'contract';
  }
  if (filename_lower.includes('recibo') || filename_lower.includes('nota')) {
    return 'receipt';
  }
  if (filename_lower.includes('extrato') || filename_lower.includes('banco')) {
    return 'bank_statement';
  }
  
  // Detectar por conteúdo (se disponível)
  if (text_lower.includes('registro geral') || text_lower.includes('carteira de identidade')) {
    return 'identity_document';
  }
  if (text_lower.includes('comprovante de residência') || text_lower.includes('conta de luz')) {
    return 'address_proof';
  }
  if (text_lower.includes('contrato de') || text_lower.includes('contratante')) {
    return 'contract';
  }
  if (text_lower.includes('nota fiscal') || text_lower.includes('recibo')) {
    return 'receipt';
  }
  if (text_lower.includes('extrato bancário') || text_lower.includes('saldo')) {
    return 'bank_statement';
  }
  
  return 'general';
}

/**
 * Formata dados extraídos para exibição
 */
export function formatExtractedData(data, documentType) {
  const formatters = {
    cpf: (value) => value ? value.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4') : null,
    cnpj: (value) => value ? value.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5') : null,
    currency: (value) => value ? new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(value) : null,
    phone: (value) => {
      if (!value) return null;
      if (value.length === 11) {
        return value.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
      } else if (value.length === 10) {
        return value.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
      }
      return value;
    }
  };

  const formatted = { ...data };
  
  // Aplicar formatações
  if (formatted.document_number) {
    if (documentType === 'cpf' || formatted.document_type?.toLowerCase().includes('cpf')) {
      formatted.document_number_formatted = formatters.cpf(formatted.document_number);
    } else if (documentType === 'cnpj' || formatted.document_type?.toLowerCase().includes('cnpj')) {
      formatted.document_number_formatted = formatters.cnpj(formatted.document_number);
    }
  }
  
  // Formatar valores monetários
  ['amount', 'value', 'total', 'subtotal'].forEach(field => {
    if (formatted[field]) {
      formatted[`${field}_formatted`] = formatters.currency(formatted[field]);
    }
  });
  
  // Formatar telefone
  if (formatted.phone) {
    formatted.phone_formatted = formatters.phone(formatted.phone);
  }
  
  return formatted;
}
