import { useState, type KeyboardEvent } from 'react';
import { X, Plus } from 'lucide-react';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Button } from '../ui/button';

interface TagsInputProps {
  value: string[];
  onChange: (tags: string[]) => void;
  placeholder?: string;
  disabled?: boolean;
  maxTags?: number;
}

export function TagsInput({
  value = [],
  onChange,
  placeholder = "Digite uma tag e pressione Enter",
  disabled = false,
  maxTags = 10
}: TagsInputProps) {
  const [inputValue, setInputValue] = useState('');

  const addTag = (tag: string) => {
    const trimmedTag = tag.trim();

    // Validações
    if (!trimmedTag) return;
    if (value.includes(trimmedTag)) return;
    if (value.length >= maxTags) return;
    if (trimmedTag.length > 20) return; // Limite de caracteres por tag

    onChange([...value, trimmedTag]);
    setInputValue('');
  };

  const removeTag = (tagToRemove: string) => {
    onChange(value.filter(tag => tag !== tagToRemove));
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addTag(inputValue);
    } else if (e.key === 'Backspace' && !inputValue && value.length > 0) {
      // Remove a última tag se o input estiver vazio
      removeTag(value[value.length - 1]);
    }
  };

  const handleAddClick = () => {
    addTag(inputValue);
  };

  return (
    <div className="space-y-3">
      {/* Input para adicionar tags */}
      <div className="flex gap-2">
        <Input
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={disabled || value.length >= maxTags}
          className="flex-1"
          maxLength={20}
        />
        <Button
          type="button"
          variant="outline"
          size="icon"
          onClick={handleAddClick}
          disabled={disabled || !inputValue.trim() || value.includes(inputValue.trim()) || value.length >= maxTags}
          aria-label="Adicionar tag"
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>

      {/* Lista de tags */}
      {value.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {value.map((tag, index) => (
            <Badge
              key={index}
              variant="secondary"
              className="flex items-center gap-1 pr-1"
            >
              <span>{tag}</span>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                onClick={() => removeTag(tag)}
                disabled={disabled}
                aria-label={`Remover tag ${tag}`}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
        </div>
      )}

      {/* Informações auxiliares */}
      <div className="flex justify-between text-xs text-muted-foreground">
        <span>
          {value.length > 0 && `${value.length} tag${value.length !== 1 ? 's' : ''}`}
        </span>
        <span>
          {maxTags - value.length} restante{maxTags - value.length !== 1 ? 's' : ''}
        </span>
      </div>

      {/* Dicas de uso */}
      <p className="text-xs text-muted-foreground">
        Pressione Enter ou clique em + para adicionar. Máximo {maxTags} tags.
      </p>
    </div>
  );
}
