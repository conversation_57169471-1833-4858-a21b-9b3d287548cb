import { useState, useMemo } from 'react';
import {
  type ColumnDef,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  type SortingState,
  type ColumnFiltersState,
  type VisibilityState,
} from '@tanstack/react-table';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from '../ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import { Badge } from '../ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import {
  MoreHorizontal,
  Search,
  ArrowUpDown,
  Mail,
  Phone,
  Building,
  User,
  Edit,
  Trash2,
  Archive,
  Eye,
  Filter,
  Columns,
  X,
  Download,
  Upload,
} from 'lucide-react';
import type { Client, ClientStatus, ClientPriority } from '@/schemas/clients';

interface ClientsTableProps {
  data: Client[];
  isLoading?: boolean;
  onEdit: (client: Client) => void;
  onDelete: (client: Client) => void;
  onView?: (client: Client) => void;
  onExport?: () => void;
  onImport?: () => void;
}

export function ClientsTable({ data, isLoading, onEdit, onDelete, onView, onExport, onImport }: ClientsTableProps) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [globalFilter, setGlobalFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');

  const getStatusColor = (status: ClientStatus) => {
    switch (status) {
      case 'ativo':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'inativo':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
      case 'prospecto':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'arquivado':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getPriorityColor = (priority: ClientPriority) => {
    switch (priority) {
      case 'critica':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'alta':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
      case 'media':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'baixa':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const columns: ColumnDef<Client>[] = [
    {
      accessorKey: 'name',
      enableHiding: true,
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-auto p-0 font-medium"
          >
            Cliente
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const client = row.original;
        return (
          <div className="flex items-center space-x-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src={client.avatar} />
              <AvatarFallback>
                {client.name.split(' ').map(n => n[0]).join('').toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-medium">{client.name}</div>
              <div className="text-sm text-muted-foreground flex items-center gap-1">
                {client.type === 'pessoa_juridica' ? <Building className="h-3 w-3" /> : <User className="h-3 w-3" />}
                {client.company || 'Pessoa Física'}
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'email',
      enableHiding: true,
      header: 'Contato',
      cell: ({ row }) => {
        const client = row.original;
        return (
          <div className="space-y-1">
            <div className="flex items-center gap-1 text-sm">
              <Mail className="h-3 w-3" />
              <span className="truncate max-w-[200px]">{client.email}</span>
            </div>
            {client.phone && (
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <Phone className="h-3 w-3" />
                <span>{client.phone}</span>
              </div>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      enableHiding: true,
      header: 'Status',
      cell: ({ row }) => {
        const status = row.getValue('status') as ClientStatus;
        const statusLabels = {
          'ativo': 'Ativo',
          'inativo': 'Inativo',
          'prospecto': 'Prospecto',
          'arquivado': 'Arquivado'
        };
        return (
          <Badge className={getStatusColor(status)}>
            {statusLabels[status] || status}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'priority',
      enableHiding: true,
      header: 'Prioridade',
      cell: ({ row }) => {
        const priority = row.getValue('priority') as ClientPriority;
        const priorityLabels = {
          'baixa': 'Baixa',
          'media': 'Média',
          'alta': 'Alta',
          'critica': 'Crítica'
        };
        return (
          <Badge variant="outline" className={getPriorityColor(priority)}>
            {priorityLabels[priority] || priority}
          </Badge>
        );
      },
    },
    {
      accessorKey: '$createdAt',
      enableHiding: true,
      header: 'Criado em',
      cell: ({ row }) => {
        const date = row.getValue('$createdAt') as string;
        return (
          <div className="text-sm text-muted-foreground">
            {formatDate(date)}
          </div>
        );
      },
    },
    {
      id: 'actions',
      header: 'Ações',
      cell: ({ row }) => {
        const client = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Abrir menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {onView && (
                <DropdownMenuItem onClick={() => onView(client)}>
                  <Eye className="mr-2 h-4 w-4" />
                  Visualizar
                </DropdownMenuItem>
              )}
              <DropdownMenuItem onClick={() => onEdit(client)}>
                <Edit className="mr-2 h-4 w-4" />
                Editar
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onDelete(client)} className="text-destructive">
                <Trash2 className="mr-2 h-4 w-4" />
                Excluir
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  // Filtro customizado que busca em todos os campos
  const customGlobalFilter = (row: { original: Client }, columnId: string, value: string) => {
    const client = row.original;
    const searchValue = value.toLowerCase();

    // Buscar em nome, email, empresa, telefone, status, prioridade
    const searchableText = [
      client.name || '',
      client.email || '',
      client.company || '',
      client.phone || '',
      client.status || '',
      client.priority || '',
      '' // notes removido
    ].join(' ').toLowerCase();

    return searchableText.includes(searchValue);
  };

  // Filtrar dados baseado nos filtros específicos usando useMemo
  const filteredData = useMemo(() => {
    return data.filter(client => {
      // Filtro por status
      if (statusFilter !== 'all') {
        if (client.status !== statusFilter) return false;
      }

      // Filtro por prioridade
      if (priorityFilter !== 'all') {
        if (client.priority !== priorityFilter) return false;
      }

      return true;
    });
  }, [data, statusFilter, priorityFilter]);

  const table = useReactTable({
    data: filteredData,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    globalFilterFn: customGlobalFilter,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      globalFilter,
    },
  });

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <div className="h-10 w-80 bg-muted animate-pulse rounded"></div>
        </div>
        <div className="rounded-md border">
          <div className="h-96 bg-muted animate-pulse"></div>
        </div>
      </div>
    );
  }

  const clearFilters = () => {
    setGlobalFilter('');
    setStatusFilter('all');
    setPriorityFilter('all');
  };

  const hasActiveFilters = globalFilter || statusFilter !== 'all' || priorityFilter !== 'all';

  return (
    <div className="space-y-4">
      {/* Filtros e Controles */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex flex-1 items-center gap-2">
          {/* Busca Global */}
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Buscar clientes..."
              value={globalFilter}
              onChange={(e) => setGlobalFilter(e.target.value)}
              className="pl-8"
            />
          </div>

          {/* Filtro por Status */}
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[140px]">
              <SelectValue>
                {statusFilter === 'all' ? 'Status: Todos' :
                 statusFilter === 'ativo' ? 'Status: Ativo' :
                 statusFilter === 'inativo' ? 'Status: Inativo' :
                 statusFilter === 'prospecto' ? 'Status: Prospecto' :
                 statusFilter === 'arquivado' ? 'Status: Arquivado' :
                 'Status'}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Status: Todos</SelectItem>
              <SelectItem value="ativo">Status: Ativo</SelectItem>
              <SelectItem value="inativo">Status: Inativo</SelectItem>
              <SelectItem value="prospecto">Status: Prospecto</SelectItem>
              <SelectItem value="arquivado">Status: Arquivado</SelectItem>
            </SelectContent>
          </Select>

          {/* Filtro por Prioridade */}
          <Select value={priorityFilter} onValueChange={setPriorityFilter}>
            <SelectTrigger className="w-[160px]">
              <SelectValue>
                {priorityFilter === 'all' ? 'Prioridade: Todas' :
                 priorityFilter === 'baixa' ? 'Prioridade: Baixa' :
                 priorityFilter === 'media' ? 'Prioridade: Média' :
                 priorityFilter === 'alta' ? 'Prioridade: Alta' :
                 priorityFilter === 'critica' ? 'Prioridade: Crítica' :
                 'Prioridade'}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Prioridade: Todas</SelectItem>
              <SelectItem value="baixa">Prioridade: Baixa</SelectItem>
              <SelectItem value="media">Prioridade: Média</SelectItem>
              <SelectItem value="alta">Prioridade: Alta</SelectItem>
              <SelectItem value="critica">Prioridade: Crítica</SelectItem>
            </SelectContent>
          </Select>

          {/* Limpar Filtros */}
          {hasActiveFilters && (
            <Button
              variant="ghost"
              onClick={clearFilters}
              className="h-8 px-2 lg:px-3"
            >
              Limpar
              <X className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Controles de Import/Export e Colunas */}
        <div className="flex items-center gap-2">
          {/* Sempre mostrar botões de importar/exportar */}
          {onImport && (
            <Button variant="outline" size="sm" onClick={onImport}>
              <Upload className="mr-2 h-4 w-4" />
              Importar
            </Button>
          )}
          {onExport && (
            <Button variant="outline" size="sm" onClick={onExport}>
              <Download className="mr-2 h-4 w-4" />
              Exportar
            </Button>
          )}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Columns className="mr-2 h-4 w-4" />
                Colunas
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[200px]">
              <DropdownMenuLabel>Alternar colunas</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {table
                .getAllColumns()
                .filter(
                  (column) =>
                    typeof column.accessorFn !== "undefined" &&
                    column.getCanHide()
                )
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {column.id === 'name' ? 'Cliente' :
                       column.id === 'email' ? 'Contato' :
                       column.id === 'status' ? 'Status' :
                       column.id === 'priority' ? 'Prioridade' :
                       column.id === '$createdAt' ? 'Criado em' :
                       column.id}
                    </DropdownMenuCheckboxItem>
                  )
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  Nenhum cliente encontrado.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between space-x-2 py-4">
        <div className="text-sm text-muted-foreground">
          {table.getFilteredRowModel().rows.length} cliente(s) encontrado(s)
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Anterior
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Próximo
          </Button>
        </div>
      </div>
    </div>
  );
}
