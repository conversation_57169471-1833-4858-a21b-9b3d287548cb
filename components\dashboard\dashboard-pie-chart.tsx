import { useMemo } from "react";
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer } from "recharts";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "../ui/card";
import {
  type ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "../ui/chart";
import { Badge } from "../ui/badge";
import { useAnalytics } from "../../hooks/use-analytics";
import { useClients } from "../../hooks/api/use-clients";
import { useEvents } from "../../hooks/api/use-events";
import { useBoards } from "../../hooks/api/use-kanban";
import { Skeleton } from "../ui/skeleton";
import { PieChartIcon, TrendingUp } from "lucide-react";

const chartConfig = {
  value: {
    label: "Quantidade",
  },
  ativo: {
    label: "Ativo",
    color: "hsl(var(--chart-1))",
  },
  pendente: {
    label: "Pendente",
    color: "hsl(var(--chart-2))",
  },
  concluido: {
    label: "Concluído",
    color: "hsl(var(--chart-3))",
  },
  cancelado: {
    label: "Cancelado",
    color: "hsl(var(--chart-4))",
  },
  analise: {
    label: "Em Análise",
    color: "hsl(var(--chart-5))",
  },
} satisfies ChartConfig;

export function DashboardPieChart() {
  const { data: analytics, isLoading } = useAnalytics();
  const { data: clients } = useClients();
  const { data: events } = useEvents();
  const { data: boards } = useBoards();

  // Gerar dados baseados em dados reais
  const chartData = useMemo(() => {
    if (!analytics || !clients || !events || !boards) return [];

    // Calcular distribuição baseada em dados reais
    const activeClients = clients.filter(client => client.status === 'ativo').length;
    const inactiveClients = clients.filter(client => client.status === 'inativo').length;
    const prospectClients = clients.filter(client => client.status === 'prospecto').length;
    const archivedClients = clients.filter(client => client.status === 'arquivado').length;

    return [
      { status: "Clientes Ativos", value: activeClients, fill: "hsl(var(--chart-1))" },
      { status: "Eventos Ativos", value: events.length, fill: "hsl(var(--chart-2))" },
      { status: "Boards Kanban", value: boards.length, fill: "hsl(var(--chart-3))" },
      { status: "Clientes Inativos", value: inactiveClients, fill: "hsl(var(--chart-4))" },
      { status: "Prospectos", value: prospectClients, fill: "hsl(var(--chart-5))" },
    ].filter(item => item.value > 0); // Filtrar itens com valor 0
  }, [analytics, clients, events, boards]);

  // Calcular total e percentuais
  const total = chartData.reduce((acc, item) => acc + item.value, 0);
  const mainCategory = chartData.length > 0
    ? chartData.reduce((prev, current) => prev.value > current.value ? prev : current)
    : { status: "", value: 0, fill: "hsl(var(--chart-1))" };
  const mainPercentage = total > 0 ? ((mainCategory.value / total) * 100).toFixed(1) : "0";

  if (isLoading) {
    return <Skeleton className="h-[400px] w-full" />;
  }

  const hasData = chartData.length > 0 && total > 0;

  if (!hasData) {
    return (
      <Card className="gap-4">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PieChartIcon className="h-5 w-5" />
            Distribuição do Sistema
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-60 text-muted-foreground">
            <div className="text-center">
              <p className="text-lg font-medium">Não há dados disponíveis</p>
              <p className="text-sm">Nenhum item foi criado no sistema ainda</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="gap-4">
      <CardHeader>
        <div className="flex flex-wrap items-center justify-between gap-3">
          <div className="space-y-0.5">
            <CardTitle className="flex items-center gap-2">
              <PieChartIcon className="h-5 w-5" />
              Distribuição do Sistema
            </CardTitle>
            <div className="flex items-start gap-2">
              <div className="font-semibold text-2xl">{total.toLocaleString('pt-BR')}</div>
              <Badge className="mt-1.5 bg-emerald-500/24 text-emerald-500 border-none">
                <TrendingUp className="h-3 w-3 mr-1" />
                {mainCategory.status} lidera
              </Badge>
            </div>
            <div className="text-sm text-muted-foreground">
              Total de itens no sistema
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="flex flex-col gap-4">
        {/* Gráfico de Pizza */}
        <div className="flex items-center justify-center">
          <ChartContainer
            config={chartConfig}
            className="mx-auto aspect-square max-h-[250px] w-full"
          >
            <PieChart>
              <ChartTooltip
                cursor={false}
                content={
                  <ChartTooltipContent
                    hideLabel
                    formatter={(value, name) => [
                      `${Number(value).toLocaleString('pt-BR')} projetos`,
                      name
                    ]}
                  />
                }
              />
              <Pie
                data={chartData}
                dataKey="value"
                nameKey="status"
                innerRadius={60}
                strokeWidth={2}
                stroke="hsl(var(--background))"
              >
                {chartData.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={entry.fill}
                    className="hover:opacity-80 transition-opacity"
                  />
                ))}
              </Pie>
            </PieChart>
          </ChartContainer>
        </div>

        {/* Legenda personalizada */}
        <div className="grid grid-cols-2 gap-3 text-sm">
          {chartData.map((item, index) => {
            const percentage = ((item.value / total) * 100).toFixed(1);
            return (
              <div key={index} className="flex items-center gap-2">
                <div
                  className="size-3 shrink-0 rounded-sm"
                  style={{ backgroundColor: item.fill }}
                />
                <div className="flex-1 min-w-0">
                  <div className="font-medium truncate">{item.status}</div>
                  <div className="text-muted-foreground text-xs">
                    {item.value.toLocaleString('pt-BR')} ({percentage}%)
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Estatísticas resumidas */}
        <div className="pt-4 border-t">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="font-semibold text-lg" style={{ color: chartData[0]?.fill || "hsl(var(--chart-1))" }}>
                {chartData[0]?.value || 0}
              </div>
              <div className="text-xs text-muted-foreground">Mais Comum</div>
            </div>
            <div>
              <div className="font-semibold text-lg text-muted-foreground">
                {chartData.length}
              </div>
              <div className="text-xs text-muted-foreground">Categorias</div>
            </div>
            <div>
              <div className="font-semibold text-lg" style={{ color: mainCategory?.fill || "hsl(var(--chart-1))" }}>
                {mainPercentage}%
              </div>
              <div className="text-xs text-muted-foreground">Dominante</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
