import { useThemeConfig as useActiveTheme } from '../components/active-theme';

/**
 * Hook for advanced theme configuration
 * Provides access to custom theme settings and utilities
 */
export function useThemeConfig() {
  const { activeTheme, setActiveTheme } = useActiveTheme();

  // Verificar se é um tema scaled
  const isScaled = activeTheme.endsWith('-scaled');

  // Extrair o tema base (sem o sufixo -scaled)
  const baseTheme = isScaled ? activeTheme.replace('-scaled', '') : activeTheme;

  // Verificar se é tema mono
  const isMono = activeTheme.includes('mono');

  // Verificar se é tema dark (baseado no tema ativo)
  const isDarkTheme = ['dark', 'ecommerce'].includes(baseTheme);

  // Função para alternar entre versão normal e scaled
  const toggleScaled = () => {
    if (isScaled) {
      setActiveTheme(baseTheme);
    } else {
      setActiveTheme(`${activeTheme}-scaled`);
    }
  };

  // Função para definir tema com opção de scaled
  const setThemeWithScaled = (theme: string, scaled: boolean = false) => {
    const newTheme = scaled ? `${theme}-scaled` : theme;
    setActiveTheme(newTheme);
  };

  // Lista de temas disponíveis
  const availableThemes = [
    'default',
    'blue',
    'green',
    'amber',
    'ecommerce',
    'mono',
    'candyland',
    'cyberpunk',
    'sunset',
    'ocean',
    'nature'
  ];

  // Lista de temas que suportam versão scaled
  const scalableThemes = [
    'default',
    'blue',
    'mono'
  ];

  return {
    // Estado atual
    activeTheme,
    baseTheme,
    isScaled,
    isMono,
    isDarkTheme,

    // Funções de controle
    setActiveTheme,
    toggleScaled,
    setThemeWithScaled,

    // Listas de temas
    availableThemes,
    scalableThemes,

    // Verificações de suporte
    supportsScaled: scalableThemes.includes(baseTheme),
  };
}
