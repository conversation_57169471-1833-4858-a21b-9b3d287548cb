/**
 * Team Chat API Hooks - Local-First com Cache
 * Seguindo padrão dos clientes com IndexedDB cache
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useAuth } from '../use-auth';
import { teamChats, chatMessages } from '../../lib/appwrite/functions/database';
import { isCacheEnabled } from '../../lib/cache-config';
import {
  hasDataInIndexedDB,
  getFromIndexedDB,
  saveToIndexedDB,
  syncUpdatedDataFromServer,
  syncAfterMutation
} from '../../lib/cache-sync';
import type { TeamChat, ChatMessage, CreateTeamChatData, CreateChatMessageData } from '@/schemas/chat';

// Função para gerar ID único para mensagens
function generateMessageId(): string {
  return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Buscar chat por teamId
 */
export function useTeamChat(teamId: string) {
  return useQuery({
    queryKey: ['team-chat', teamId],
    queryFn: async (): Promise<TeamChat | null> => {
      if (!teamId) return null;
      const result = await teamChats.getByTeamId(teamId);
      return result as TeamChat | null;
    },
    enabled: !!teamId,
    staleTime: 30 * 1000, // 30 segundos
  });
}

/**
 * Buscar dados básicos do chat (sem mensagens)
 * Mensagens devem ser buscadas separadamente com useMessages
 */
export function useChat(chatId: string) {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['chat', chatId, user?.$id],
    queryFn: async (): Promise<TeamChat | null> => {
      if (!chatId || !user?.$id) return null;

      console.log(`🌐 Buscando dados básicos do chat ${chatId}`);

      // Buscar apenas dados básicos do chat
      const chat = await teamChats.get(chatId) as TeamChat;

      return chat;
    },
    enabled: !!chatId && !!user?.$id,
    staleTime: 5 * 60 * 1000, // 5 minutos
    refetchOnWindowFocus: false,
  });
}

/**
 * Criar ou buscar chat do time
 */
export function useCreateOrGetTeamChat() {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async ({ teamId, teamName }: { teamId: string; teamName: string }): Promise<TeamChat> => {
      if (!user) throw new Error('Usuário não autenticado');

      // Primeiro, tentar buscar chat existente
      const existingChat = await teamChats.getByTeamId(teamId);
      if (existingChat) {
        return existingChat as TeamChat;
      }

      // Se não existir, criar novo chat
      const chatData = {
        teamId,
        name: `Chat do ${teamName}`,
        description: `Chat principal do time ${teamName}`,
        isPrivate: false,
        isActive: true,
        members: [user.$id],
        lastActivity: new Date().toISOString(),
        unreadCount: 0,
        allowFileSharing: true,
        allowReactions: true,
        retentionDays: 30,
        userId: user.$id,
        createdBy: user.$id,
        isDeleted: false,
      };

      const result = await teamChats.create(chatData);
      return result as TeamChat;
    },
    onSuccess: (chat, { teamId }) => {
      // Atualizar cache do team chat
      queryClient.setQueryData(['team-chat', teamId], chat);
      toast.success('Chat configurado com sucesso!');
    },
    onError: (error) => {
      console.error('Erro ao configurar chat:', error);
      toast.error('Erro ao configurar chat');
    },
  });
}

// ⚠️ DEPRECATED: Funções de mensagens movidas para use-messages.ts
// Mantidas aqui temporariamente para compatibilidade

// Funções de mensagens removidas - usar use-messages.ts

/**
 * Hook para realtime usando Appwrite Realtime (não WebSocket)
 */
export function useChatRealtime(chatId: string) {
  const queryClient = useQueryClient();

  // TODO: Implementar Appwrite Realtime quando necessário
  // Por enquanto, apenas invalidar cache periodicamente

  return {
    isConnected: true,
  };
}
