/**
 * Página de Chat de Time
 * Interface para comunicação em tempo real entre membros do time
 */

'use client';

import React, { useState } from 'react';
import { MessageCircle, Users, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { TeamChat } from '@/components/chat/team-chat';
import { TeamSelector } from '@/components/teams/team-selector';
import { useTeams, useTeamChat, useCreateOrGetTeamChat } from '@/hooks/use-api';
import { useAuth } from '@/hooks/use-auth';

export default function TeamChatPage() {
  const { user } = useAuth();
  const [selectedTeamId, setSelectedTeamId] = useState<string>('');

  // Hooks para dados
  const { data: teams = [] } = useTeams();
  const currentTeam = teams.find(team => team.$id === selectedTeamId);

  // Hooks para chat
  const { data: teamChat, isLoading: loadingTeamChat } = useTeamChat(selectedTeamId);
  const createOrGetChatMutation = useCreateOrGetTeamChat();

  // Selecionar primeiro time automaticamente
  React.useEffect(() => {
    if (!selectedTeamId && teams.length > 0) {
      setSelectedTeamId(teams[0].$id);
    }
  }, [teams, selectedTeamId]);

  // Função para criar/iniciar chat
  const handleInitializeChat = async () => {
    if (!currentTeam) return;

    try {
      await createOrGetChatMutation.mutateAsync({
        teamId: currentTeam.$id,
        teamName: currentTeam.name,
      });
    } catch (error) {
      console.error('Erro ao inicializar chat:', error);
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex flex-col gap-4 p-4 md:p-6 border-b md:flex-row md:items-center md:justify-between">
        <div className="min-w-0">
          <h1 className="text-xl md:text-2xl font-bold">Chat de Time</h1>
          <p className="text-muted-foreground text-sm md:text-base">
            Comunique-se em tempo real com sua equipe
          </p>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 p-4 md:p-6 space-y-6">

        {/* Seletor de Time */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Users className="h-5 w-5" />
              Selecionar Time
            </CardTitle>
          </CardHeader>
          <CardContent>
            <TeamSelector
              selectedTeamId={selectedTeamId}
              onTeamSelect={setSelectedTeamId}
            />
          </CardContent>
        </Card>

        {/* Chat Interface */}
        {selectedTeamId ? (
          <>
            {loadingTeamChat ? (
              <Card>
                <CardContent className="p-12">
                  <div className="text-center text-muted-foreground">
                    <MessageCircle className="h-16 w-16 mx-auto mb-4 opacity-50 animate-pulse" />
                    <h3 className="text-lg font-semibold mb-2">
                      Verificando chat do time...
                    </h3>
                    <p className="text-sm max-w-md mx-auto">
                      Aguarde enquanto verificamos se existe um chat para este time.
                    </p>
                  </div>
                </CardContent>
              </Card>
            ) : teamChat ? (
              <TeamChat
                teamId={selectedTeamId}
                chatId={teamChat.$id}
              />
            ) : (
              <Card>
                <CardContent className="p-12">
                  <div className="text-center text-muted-foreground">
                    <MessageCircle className="h-16 w-16 mx-auto mb-4 opacity-50" />
                    <h3 className="text-lg font-semibold mb-2">
                      Chat não configurado
                    </h3>
                    <p className="text-sm max-w-md mx-auto mb-6">
                      Este time ainda não possui um chat configurado. Clique no botão abaixo para inicializar o chat e começar a conversar com os membros da equipe.
                    </p>
                    <Button
                      onClick={handleInitializeChat}
                      disabled={createOrGetChatMutation.isPending}
                      size="lg"
                      className="gap-2"
                    >
                      {createOrGetChatMutation.isPending ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          Inicializando Chat...
                        </>
                      ) : (
                        <>
                          <Plus className="h-4 w-4" />
                          Iniciar Chat do Time
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </>
        ) : (
          <Card>
            <CardContent className="p-12">
              <div className="text-center text-muted-foreground">
                <MessageCircle className="h-16 w-16 mx-auto mb-4 opacity-50" />
                <h3 className="text-lg font-semibold mb-2">
                  Selecione um time para começar
                </h3>
                <p className="text-sm max-w-md mx-auto">
                  {teams.length === 0
                    ? 'Você precisa fazer parte de um time para usar o chat. Acesse a página de Teams para criar ou ser convidado para um time.'
                    : 'Escolha um time na lista acima para iniciar uma conversa em tempo real com os membros da sua equipe.'
                  }
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
