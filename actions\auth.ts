"use server";

import { User } from "@/lib/appwrite/functions/auth";
import { cookies } from "next/headers";

/**
 * Obtém a sessão do cookie (server-side only)
 */
export async function getSession() {
  const cookieStore = await cookies();
  const session = cookieStore.get("session")?.value;
  return session;
}

/**
 * Verifica se existe uma sessão válida (server-side only)
 * Usado pelo middleware para verificação rápida de autenticação
 */
export async function hasValidSession(): Promise<boolean> {
  try {
    const cookieStore = await cookies();
    const sessionCookie = cookieStore.get("session");

    // Se não há cookie de sessão, não está autenticado
    if (!sessionCookie?.value) {
      return false;
    }

    // Verificação rápida: se o cookie existe e tem formato válido
    // Para o middleware, isso é suficiente para determinar se deve redirecionar
    return sessionCookie.value.length > 10; // Sessões do Appwrite são longas
  } catch (error) {
    return false;
  }
}

/**
 * Obtém dados completos do usuário (server-side only)
 * Usado quando precisamos dos dados completos do usuário no servidor
 */
export async function getUser() {
  try {
    const cookieStore = await cookies();
    const sessionCookie = cookieStore.get("session");

    if (!sessionCookie?.value) {
      return null;
    }

    const response = await fetch(`${process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT}/account`, {
      headers: {
        'Content-Type': 'application/json',
        'X-Appwrite-Project': process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID!,
        'cookie': `a_session_${process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID}=${sessionCookie.value}`,
      },
      // Cache para evitar múltiplas chamadas desnecessárias
      next: { revalidate: 60 } // Cache por 1 minuto
    });

    if (!response.ok) {
      console.log("Falha na autenticação");
      return null;
    }

    const user: User = await response.json();
    return user;
  } catch (error) {
    console.log("Erro ao obter usuário:", error);
    return null;
  }
}

/**
 * Obtém dados do usuário com verificação de admin (server-side only)
 * Usado pelo middleware para verificar permissões de admin
 */
export async function getUserWithAdminCheck() {
  const user = await getUser();

  if (!user) {
    return { user: null, isAdmin: false };
  }

  const isAdmin = user && (
    user.email === '<EMAIL>' ||
    user.labels?.includes('admin') ||
    user.labels?.includes('moderator') ||
    user.prefs?.role === 'admin'
  );

  return { user, isAdmin };
}