'use client';

import { RouteGuard } from '@/components/route-guard';
import { Logo } from '@/components/logo';
import Link from 'next/link';

/**
 * Layout for authentication pages (login, register, etc.)
 * Redirects authenticated users to dashboard
 */
export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <RouteGuard requireAuth={false}>
      <div className="min-h-screen grid lg:grid-cols-2">
        {/* Left side - Form */}
        <div className="flex flex-col gap-4 p-6 md:p-10">
          <div className="flex justify-center gap-2 md:justify-start">
            <Link href="/" className="flex items-center gap-2 font-medium">
              <Logo />
            </Link>
          </div>
          <div className="flex flex-1 items-center justify-center">
            <div className="w-full max-w-xs">
              {children}
            </div>
          </div>
        </div>
        
        {/* Right side - Image/Branding */}
        <div className="bg-muted relative hidden lg:block">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-primary/5" />
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center space-y-4 p-8">
              <h2 className="text-3xl font-bold text-foreground">
                Bem-vindo ao Template Appwrite
              </h2>
              <p className="text-lg text-muted-foreground max-w-md">
                Uma solução completa para suas aplicações com autenticação, 
                dashboard e todas as funcionalidades que você precisa.
              </p>
            </div>
          </div>
        </div>
      </div>
    </RouteGuard>
  );
}
