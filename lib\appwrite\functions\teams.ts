import { ID, Query, type Models } from 'appwrite';
import { teams, databases, DATABASE_ID, COLLECTIONS } from '../config';

/**
 * Simple Appwrite teams functions
 * Direct calls without unnecessary complexity
 */

export interface CreateTeamData {
  name: string;
  roles?: string[];
}

export interface InviteMemberData {
  email: string;
  roles: string[];
  url?: string;
  name?: string;
}

export interface Team extends Models.Team<Models.Preferences> {}
export interface TeamMembership extends Models.Membership {}

/**
 * Create a new team
 */
export async function createTeam(data: CreateTeamData) {
  const result = await teams.create(
    ID.unique(),
    data.name,
    data.roles || ['owner', 'admin', 'member', 'guest']
  );

  return result;
}

/**
 * List all teams for current user
 */
export async function listTeams() {
  const result = await teams.list();
  return result;
}

/**
 * Get a specific team
 */
export async function getTeam(teamId: string) {
  const result = await teams.get(teamId);
  return result;
}

/**
 * Update team name
 */
export async function updateTeam(teamId: string, name: string) {
  const result = await teams.updateName(teamId, name);
  return result;
}

/**
 * Delete a team
 */
export async function deleteTeam(teamId: string) {
  const result = await teams.delete(teamId);
  return result;
}

/**
 * Get team members
 */
export async function getTeamMembers(teamId: string) {
  const result = await teams.listMemberships(teamId);
  return result;
}

/**
 * Invite someone to a team
 */
export async function inviteToTeam(teamId: string, data: InviteMemberData) {
  const result = await teams.createMembership(
    teamId,
    data.roles,
    data.email, // email
    undefined, // userId - will be set when user accepts
    undefined, // phone - not using phone invites
    data.url || `${window.location.origin}/dashboard/teams`, // url for redirect
    data.name || data.email.split('@')[0] // name
  );

  return result;
}

/**
 * Remove a member from a team
 */
export async function removeTeamMember(teamId: string, membershipId: string) {
  const result = await teams.deleteMembership(teamId, membershipId);
  return result;
}

/**
 * Update member roles
 */
export async function updateMemberRoles(teamId: string, membershipId: string, roles: string[]) {
  const result = await teams.updateMembership(teamId, membershipId, roles);
  return result;
}

/**
 * Get team preferences
 */
export async function getTeamPreferences(teamId: string) {
  const result = await teams.getPrefs(teamId);
  return result;
}

/**
 * Update team preferences
 */
export async function updateTeamPreferences(teamId: string, preferences: Record<string, any>) {
  const result = await teams.updatePrefs(teamId, preferences);
  return result;
}

/**
 * Get or create team chat
 * Busca chat pelo teamId, se não existir cria um automaticamente
 */
export async function getOrCreateTeamChat(teamId: string, teamName: string, userId: string) {
  try {
    // 1. Buscar chat existente pelo teamId
    const response = await databases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.TEAM_CHATS,
      [
        Query.equal('teamId', teamId),
        Query.equal('isActive', true)
      ]
    );

    // 2. Se já existe um chat ativo, retornar
    if (response.documents.length > 0) {
      return response.documents[0];
    }

    // 3. Criar novo chat para o time
    const chatData = {
      teamId,
      name: `Chat do ${teamName}`,
      description: `Chat principal do time ${teamName}`,
      isPrivate: false,
      isActive: true,
      members: [userId], // Criador é automaticamente membro
      lastActivity: new Date().toISOString(),
      unreadCount: 0,
      allowFileSharing: true,
      allowReactions: true,
      retentionDays: 30,
      // Campos obrigatórios de auditoria
      userId,
      createdBy: userId,
      // Soft delete
      isDeleted: false,
    };

    const newChat = await databases.createDocument(
      DATABASE_ID,
      COLLECTIONS.TEAM_CHATS,
      ID.unique(),
      chatData
    );

    return newChat;
  } catch (error) {
    console.error('Erro ao obter/criar chat do time:', error);
    throw error;
  }
}

/**
 * Get team chat by teamId
 */
export async function getTeamChat(teamId: string) {
  try {
    const response = await databases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.TEAM_CHATS,
      [
        Query.equal('teamId', teamId),
        Query.equal('isActive', true)
      ]
    );

    return response.documents.length > 0 ? response.documents[0] : null;
  } catch (error) {
    console.error('Erro ao buscar chat do time:', error);
    return null;
  }
}
