import { useState, useEffect, useCallback } from 'react';
import type {
  PWAContextType,
  BeforeInstallPromptEvent,
  ServiceWorkerUpdateEvent
} from '@/schemas/pwa';
import {
  getPWAConfig,
  isPWAEnabled,
  removePWA,
  initializePWA,
  logPWAConfiguration
} from '../lib/pwa-config';
import { log } from '../lib/logger';

/**
 * Custom hook for PWA functionality
 */
export function usePWA(): PWAContextType {
  const [isInstallable, setIsInstallable] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [isOffline, setIsOffline] = useState(!navigator.onLine);
  const [updateAvailable, setUpdateAvailable] = useState(false);
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [registration, setRegistration] = useState<ServiceWorkerRegistration | null>(null);
  const [pwaConfig] = useState(() => getPWAConfig());

  // Check if PWA is enabled from environment configuration
  const isPWAConfigEnabled = pwaConfig.enabled;

  /**
   * Check if app is already installed
   */
  const checkInstallStatus = useCallback(() => {
    if (typeof window === 'undefined') return;

    // Check if running in standalone mode (installed PWA)
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
    const isInWebAppiOS = (window.navigator as any).standalone === true;

    setIsInstalled(isStandalone || isInWebAppiOS);
  }, []);

  /**
   * Show install prompt
   */
  const showInstallPrompt = useCallback(async (): Promise<void> => {
    if (!isPWAConfigEnabled) {
      log.pwa('PWA is disabled, cannot show install prompt');
      return;
    }

    // Install prompt is always enabled when PWA is enabled

    if (!deferredPrompt) {
      log.pwa('Install prompt not available');
      return;
    }

    try {
      await deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;

      if (outcome === 'accepted') {
        log.pwa('PWA installation accepted');
        setIsInstalled(true);
      } else {
        log.pwa('PWA installation dismissed');
      }

      setDeferredPrompt(null);
      setIsInstallable(false);
    } catch (error) {
      log.error('Error showing install prompt', error instanceof Error ? error : undefined);
    }
  }, [deferredPrompt, isPWAConfigEnabled]);

  /**
   * Dismiss install prompt
   */
  const dismissInstallPrompt = useCallback(() => {
    setDeferredPrompt(null);
    setIsInstallable(false);
  }, []);

  /**
   * Request notification permission
   */
  const requestNotificationPermission = useCallback(async (): Promise<boolean> => {
    if (!isPWAConfigEnabled) {
      log.pwa('PWA is disabled, cannot request notification permission');
      return false;
    }

    // Notifications are always enabled when PWA is enabled

    if (!('Notification' in window)) {
      log.pwa('This browser does not support notifications');
      return false;
    }

    if (Notification.permission === 'granted') {
      return true;
    }

    if (Notification.permission === 'denied') {
      log.pwa('Notification permission denied');
      return false;
    }

    try {
      const permission = await Notification.requestPermission();
      const granted = permission === 'granted';
      log.pwa('Notification permission result', { permission, granted });
      return granted;
    } catch (error) {
      log.error('Error requesting notification permission', error instanceof Error ? error : undefined);
      return false;
    }
  }, [isPWAConfigEnabled]);

  /**
   * Send notification
   */
  const sendNotification = useCallback((options: {
    title: string;
    body?: string;
    icon?: string;
    badge?: string;
    tag?: string;
    data?: any;
  }) => {
    if (!isPWAConfigEnabled) {
      log.pwa('PWA is disabled, cannot send notification');
      return;
    }

    // Notifications are always enabled when PWA is enabled

    if (!('Notification' in window)) {
      log.pwa('This browser does not support notifications');
      return;
    }

    if (Notification.permission !== 'granted') {
      log.pwa('Notification permission not granted');
      return;
    }

    try {
      const notification = new Notification(options.title, {
        body: options.body,
        icon: options.icon || '/icon-192x192.png',
        badge: options.badge || '/badge-72x72.png',
        tag: options.tag,
        data: options.data,
        requireInteraction: false,
        silent: false,
      });

      // Auto close after 5 seconds
      setTimeout(() => {
        notification.close();
      }, 5000);

      log.pwa('Notification sent', { title: options.title, body: options.body });
    } catch (error) {
      log.error('Error sending notification', error instanceof Error ? error : undefined);
    }
  }, [isPWAConfigEnabled]);

  /**
   * Update the app
   */
  const updateApp = useCallback(async (): Promise<void> => {
    if (!isPWAConfigEnabled) {
      log.pwa('PWA is disabled, cannot update app');
      return;
    }

    // Auto update is always enabled when PWA is enabled

    if (!registration || !registration.waiting) {
      log.pwa('No service worker update available');
      return;
    }

    try {
      // Send message to waiting service worker to skip waiting
      registration.waiting.postMessage({ type: 'SKIP_WAITING' });

      log.pwa('App update initiated, reloading page...');

      // Reload the page to activate the new service worker
      window.location.reload();
    } catch (error) {
      log.error('Error updating app', error instanceof Error ? error : undefined);
    }
  }, [registration, isPWAConfigEnabled]);

  /**
   * Handle service worker events
   */
  const handleServiceWorkerUpdate = useCallback((event: ServiceWorkerUpdateEvent) => {
    switch (event.type) {
      case 'update-available':
        setUpdateAvailable(true);
        setRegistration(event.registration || null);
        console.log('🔄 App update available');
        break;
      case 'update-installed':
        console.log('✅ App updated successfully');
        break;
      case 'offline-ready':
        console.log('📱 App ready for offline use');
        break;
    }
  }, []);

  // Setup PWA event listeners
  useEffect(() => {
    // Log PWA configuration on startup
    logPWAConfiguration();

    if (!isPWAConfigEnabled || typeof window === 'undefined') {
      log.pwa('PWA is disabled in configuration');
      log.pwa('Removing existing PWA installation...');
      removePWA().catch(error => {
        log.error('Failed to remove PWA', error instanceof Error ? error : undefined);
      });
      return;
    }

    log.pwa('Initializing PWA with configuration', pwaConfig as unknown as Record<string, unknown>);

    checkInstallStatus();

    // Listen for beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      const installEvent = e as BeforeInstallPromptEvent;
      setDeferredPrompt(installEvent);
      setIsInstallable(true);
      console.log('📱 PWA install prompt available');
    };

    // Listen for app installed event
    const handleAppInstalled = () => {
      setIsInstalled(true);
      setIsInstallable(false);
      setDeferredPrompt(null);
      console.log('✅ PWA installed successfully');
    };

    // Listen for online/offline events
    const handleOnline = () => setIsOffline(false);
    const handleOffline = () => setIsOffline(true);

    // Add event listeners
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Service Worker registration and update handling
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.ready.then((reg) => {
        setRegistration(reg);

        // Check for updates
        reg.addEventListener('updatefound', () => {
          const newWorker = reg.installing;
          if (newWorker) {
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                const event: ServiceWorkerUpdateEvent = {
                  type: 'update-available',
                  target: newWorker,
                  registration: reg,
                } as ServiceWorkerUpdateEvent;
                handleServiceWorkerUpdate(event);
              }
            });
          }
        });
      });

      // Listen for service worker messages
      navigator.serviceWorker.addEventListener('message', (messageEvent) => {
        if (messageEvent.data && messageEvent.data.type === 'SW_UPDATE') {
          const event = new Event('update-installed') as ServiceWorkerUpdateEvent;
          handleServiceWorkerUpdate(event);
        }
      });
    }

    // Cleanup
    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [isPWAConfigEnabled, pwaConfig, checkInstallStatus, handleServiceWorkerUpdate]);

  return {
    isInstallable,
    isInstalled,
    canInstall: isInstallable,
    installPrompt: deferredPrompt,
    isStandalone: isInstalled,
    isOnline: !isOffline,
    updateAvailable,
    install: showInstallPrompt,
    skipWaiting: updateApp,
    updateApp,
    requestNotificationPermission,
    sendNotification,
    checkForUpdates: async () => {
      if (registration) {
        await registration.update();
      }
    },
    showInstallPrompt,
    dismissInstallPrompt,
  };
}

/**
 * Hook to check if PWA is enabled
 */
export function usePWAEnabled(): boolean {
  return isPWAEnabled();
}

/**
 * Hook for PWA install banner
 */
export function usePWAInstallBanner() {
  const { isInstallable, isInstalled, showInstallPrompt, dismissInstallPrompt } = usePWA();
  const [showBanner, setShowBanner] = useState(false);

  useEffect(() => {
    // Show banner if installable and not already installed
    setShowBanner(isInstallable && !isInstalled);
  }, [isInstallable, isInstalled]);

  const handleInstall = useCallback(async () => {
    await showInstallPrompt();
    setShowBanner(false);
  }, [showInstallPrompt]);

  const handleDismiss = useCallback(() => {
    dismissInstallPrompt();
    setShowBanner(false);
  }, [dismissInstallPrompt]);

  return {
    showBanner,
    handleInstall,
    handleDismiss,
  };
}
