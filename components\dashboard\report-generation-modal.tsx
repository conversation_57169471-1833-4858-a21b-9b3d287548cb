'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Users,
  Calendar,
  Folder,
  Activity,
  FileText,
  Download,
  Settings,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  TrendingUp,
  Loader2,
} from 'lucide-react';
import { useAuth } from '@/hooks/use-auth';
import { canUseDataImportExport } from '@/lib/plan-limits';
import { toast } from 'sonner';
import { usePDFGenerator } from '@/hooks/use-pdf-generator';
import { useClients } from '@/hooks/api/use-clients';
import { useActivities } from '@/hooks/api/use-activities';
import { useTeams } from '@/hooks/api/use-teams';
import { useEvents } from '@/hooks/api/use-events';
import { useFiles } from '@/hooks/api/use-storage';

interface ReportGenerationModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const REPORT_TYPES = [
  {
    id: 'clients',
    title: 'Relatório de Clientes',
    description: 'Dados completos dos clientes cadastrados',
    icon: Users,
    color: 'bg-blue-500',
  },
  {
    id: 'activities',
    title: 'Relatório de Atividades',
    description: 'Log de atividades do sistema',
    icon: Activity,
    color: 'bg-green-500',
  },
  {
    id: 'events',
    title: 'Relatório de Eventos',
    description: 'Calendário e eventos agendados',
    icon: Calendar,
    color: 'bg-purple-500',
  },
  {
    id: 'documents',
    title: 'Relatório de Documentos',
    description: 'Arquivos e documentos armazenados',
    icon: Folder,
    color: 'bg-orange-500',
  },
  {
    id: 'analytics',
    title: 'Relatório de Analytics',
    description: 'Estatísticas e métricas gerais',
    icon: BarChart3,
    color: 'bg-indigo-500',
  },
];

const FORMAT_OPTIONS = [
  { value: 'pdf', label: 'PDF', icon: FileText, description: 'Documento formatado' },
  { value: 'csv', label: 'CSV', icon: FileText, description: 'Planilha de dados' },
  { value: 'json', label: 'JSON', icon: FileText, description: 'Dados estruturados' },
];

const CHART_OPTIONS = [
  { id: 'summary', label: 'Resumo Executivo', icon: TrendingUp },
  { id: 'charts', label: 'Gráficos e Visualizações', icon: PieChart },
  { id: 'tables', label: 'Tabelas Detalhadas', icon: BarChart3 },
];

export function ReportGenerationModal({ open, onOpenChange }: ReportGenerationModalProps) {
  const { user } = useAuth();
  const { generateTablePDF, generateTextPDF } = usePDFGenerator();
  
  // Data hooks
  const { data: clients = [] } = useClients();
  const { data: activities = [] } = useActivities();
  const { data: teams = [] } = useTeams();
  const { data: events = [] } = useEvents();
  const { data: files } = useFiles();

  // State
  const [selectedReports, setSelectedReports] = useState<string[]>(['clients']);
  const [format, setFormat] = useState('pdf');
  const [includeCharts, setIncludeCharts] = useState<string[]>(['summary', 'charts']);
  const [customNotes, setCustomNotes] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);

  // Check permissions
  const canUseFeatures = user ? canUseDataImportExport(user).allowed : false;

  const handleReportToggle = (reportId: string) => {
    setSelectedReports(prev => 
      prev.includes(reportId) 
        ? prev.filter(id => id !== reportId)
        : [...prev, reportId]
    );
  };

  const handleChartToggle = (chartId: string) => {
    setIncludeCharts(prev => 
      prev.includes(chartId) 
        ? prev.filter(id => id !== chartId)
        : [...prev, chartId]
    );
  };

  const generateReport = async () => {
    if (!canUseFeatures) {
      toast.error('Você precisa de um plano superior para gerar relatórios');
      return;
    }

    if (selectedReports.length === 0) {
      toast.error('Selecione pelo menos um tipo de relatório');
      return;
    }

    setIsGenerating(true);

    try {
      // Simulate report generation
      await new Promise(resolve => setTimeout(resolve, 2000));

      if (format === 'pdf') {
        // Generate PDF report
        let reportContent = `RELATÓRIO PERSONALIZADO\n\n`;
        reportContent += `Gerado em: ${new Date().toLocaleDateString('pt-BR')}\n`;
        reportContent += `Usuário: ${user?.name}\n\n`;

        if (customNotes) {
          reportContent += `OBSERVAÇÕES:\n${customNotes}\n\n`;
        }

        // Add selected report sections
        selectedReports.forEach(reportId => {
          const reportType = REPORT_TYPES.find(r => r.id === reportId);
          if (reportType) {
            reportContent += `${reportType.title.toUpperCase()}\n`;
            reportContent += `${reportType.description}\n\n`;
            
            // Add specific data based on report type
            switch (reportId) {
              case 'clients':
                reportContent += `Total de clientes: ${clients.length}\n`;
                break;
              case 'activities':
                reportContent += `Total de atividades: ${activities.length}\n`;
                break;
              case 'events':
                reportContent += `Total de eventos: ${events.length}\n`;
                break;
              case 'documents':
                reportContent += `Total de arquivos: ${files?.files?.length || 0}\n`;
                break;
              case 'analytics':
                reportContent += `Estatísticas gerais da aplicação\n`;
                break;
            }
            reportContent += '\n';
          }
        });

        await generateTextPDF(reportContent, {
          title: 'Relatório Personalizado',
          subtitle: `Gerado em ${new Date().toLocaleDateString('pt-BR')}`,
        });
      } else {
        // For other formats, create a simple download
        const reportData = {
          timestamp: new Date().toISOString(),
          user: user?.name,
          reports: selectedReports,
          notes: customNotes,
          data: {
            clients: clients.length,
            activities: activities.length,
            events: events.length,
            files: files?.files?.length || 0,
          }
        };

        const blob = new Blob([
          format === 'json' 
            ? JSON.stringify(reportData, null, 2)
            : Object.entries(reportData.data).map(([key, value]) => `${key},${value}`).join('\n')
        ], { 
          type: format === 'json' ? 'application/json' : 'text/csv' 
        });

        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `relatorio-personalizado-${new Date().toISOString().split('T')[0]}.${format}`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }

      toast.success('Relatório gerado com sucesso!');
      onOpenChange(false);
    } catch (error) {
      console.error('Erro ao gerar relatório:', error);
      toast.error('Erro ao gerar relatório');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Gerar Relatório Personalizado
          </DialogTitle>
          <DialogDescription>
            Configure e gere relatórios personalizados com os dados da sua aplicação.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="types" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="types">Tipos de Relatório</TabsTrigger>
            <TabsTrigger value="format">Formato e Opções</TabsTrigger>
            <TabsTrigger value="preview">Prévia e Geração</TabsTrigger>
          </TabsList>

          {/* Report Types Tab */}
          <TabsContent value="types" className="space-y-4">
            <div className="space-y-3">
              <Label>Selecione os tipos de relatório a incluir:</Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {REPORT_TYPES.map((report) => {
                  const Icon = report.icon;
                  const isSelected = selectedReports.includes(report.id);
                  
                  return (
                    <Card 
                      key={report.id}
                      className={`cursor-pointer transition-all ${
                        isSelected ? 'ring-2 ring-primary' : 'hover:shadow-md'
                      }`}
                      onClick={() => handleReportToggle(report.id)}
                    >
                      <CardHeader className="pb-3">
                        <div className="flex items-center gap-3">
                          <div className={`p-2 rounded-lg ${report.color} text-white`}>
                            <Icon className="h-4 w-4" />
                          </div>
                          <div className="flex-1">
                            <CardTitle className="text-sm">{report.title}</CardTitle>
                            <CardDescription className="text-xs">
                              {report.description}
                            </CardDescription>
                          </div>
                          <Checkbox 
                            checked={isSelected}
                            onChange={() => handleReportToggle(report.id)}
                          />
                        </div>
                      </CardHeader>
                    </Card>
                  );
                })}
              </div>
            </div>
          </TabsContent>

          {/* Format Tab */}
          <TabsContent value="format" className="space-y-6">
            <div className="space-y-4">
              <div className="space-y-3">
                <Label>Formato do Arquivo</Label>
                <RadioGroup value={format} onValueChange={setFormat}>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    {FORMAT_OPTIONS.map((option) => {
                      const Icon = option.icon;
                      return (
                        <div key={option.value} className="flex items-center space-x-2">
                          <RadioGroupItem value={option.value} id={option.value} />
                          <Label 
                            htmlFor={option.value}
                            className="flex items-center gap-2 cursor-pointer flex-1 p-3 border rounded-lg hover:bg-muted/50"
                          >
                            <Icon className="h-4 w-4" />
                            <div>
                              <div className="font-medium">{option.label}</div>
                              <div className="text-xs text-muted-foreground">
                                {option.description}
                              </div>
                            </div>
                          </Label>
                        </div>
                      );
                    })}
                  </div>
                </RadioGroup>
              </div>

              <Separator />

              <div className="space-y-3">
                <Label>Incluir no Relatório</Label>
                <div className="space-y-2">
                  {CHART_OPTIONS.map((option) => {
                    const Icon = option.icon;
                    const isSelected = includeCharts.includes(option.id);
                    
                    return (
                      <div key={option.id} className="flex items-center space-x-2">
                        <Checkbox 
                          id={option.id}
                          checked={isSelected}
                          onCheckedChange={() => handleChartToggle(option.id)}
                        />
                        <Label 
                          htmlFor={option.id}
                          className="flex items-center gap-2 cursor-pointer"
                        >
                          <Icon className="h-4 w-4" />
                          {option.label}
                        </Label>
                      </div>
                    );
                  })}
                </div>
              </div>

              <Separator />

              <div className="space-y-3">
                <Label htmlFor="notes">Observações Personalizadas</Label>
                <Textarea
                  id="notes"
                  placeholder="Adicione observações ou contexto adicional para o relatório..."
                  value={customNotes}
                  onChange={(e) => setCustomNotes(e.target.value)}
                  rows={4}
                />
              </div>
            </div>
          </TabsContent>

          {/* Preview Tab */}
          <TabsContent value="preview" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Prévia do Relatório</CardTitle>
                <CardDescription>
                  Revise as configurações antes de gerar o relatório
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium">Tipos Selecionados</Label>
                    <div className="mt-2 space-y-1">
                      {selectedReports.map(reportId => {
                        const report = REPORT_TYPES.find(r => r.id === reportId);
                        return report ? (
                          <Badge key={reportId} variant="secondary" className="mr-1">
                            {report.title}
                          </Badge>
                        ) : null;
                      })}
                    </div>
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium">Formato</Label>
                    <div className="mt-2">
                      <Badge variant="outline">
                        {FORMAT_OPTIONS.find(f => f.value === format)?.label}
                      </Badge>
                    </div>
                  </div>
                </div>

                {customNotes && (
                  <div>
                    <Label className="text-sm font-medium">Observações</Label>
                    <p className="mt-1 text-sm text-muted-foreground bg-muted p-3 rounded-lg">
                      {customNotes}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button 
            onClick={generateReport}
            disabled={isGenerating || selectedReports.length === 0 || !canUseFeatures}
          >
            {isGenerating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Gerando...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Gerar Relatório
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
