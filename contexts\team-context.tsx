'use client';

/**
 * Contexto de Time e Permissões
 * Gerencia o time ativo e as permissões do usuário
 */

import { createContext, useContext, useEffect, useState, useMemo, useCallback, type ReactNode } from 'react';
import { useAuth } from '../hooks/use-auth';
import { useTeams, useTeamMembers } from '../hooks/api/use-teams';
import { getUserPermissionContext } from '../lib/permissions';
import type { Team, TeamMembership, TeamPreferences } from '@/schemas/teams';
import type { UserPermissionContext } from '@/schemas/permissions';
import { log } from '../lib/logger';

// ============================================================================
// TIPOS DO CONTEXTO
// ============================================================================

interface TeamContextType {
  // Time ativo
  currentTeam: Team | null;
  currentTeamId: string | null;

  // Times disponíveis
  teams: Team[];
  isLoadingTeams: boolean;

  // Membros do time atual
  teamMembers: TeamMembership[];
  currentUserMembership: TeamMembership | null;

  // Permissões do usuário atual
  permissionContext: UserPermissionContext | null;

  // Ações
  switchTeam: (teamId: string) => void;
  refreshTeamData: () => void;

  // Estados
  isLoading: boolean;
  error: string | null;
}

// ============================================================================
// CONTEXTO
// ============================================================================

const TeamContext = createContext<TeamContextType | undefined>(undefined);

interface TeamProviderProps {
  children: ReactNode;
}

export function TeamProvider({ children }: TeamProviderProps) {
  const { user, isAuthenticated } = useAuth();
  const [currentTeamId, setCurrentTeamId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Buscar times do usuário
  const {
    data: teams = [],
    isLoading: isLoadingTeams,
    refetch: refetchTeams
  } = useTeams();

  // Buscar membros do time atual
  const {
    data: teamMembers = [],
    isLoading: isLoadingMembers,
    refetch: refetchMembers
  } = useTeamMembers(currentTeamId || '');

  // Estados derivados
  const isLoading = isLoadingTeams || isLoadingMembers;
  const currentTeam = teams.find(team => team.$id === currentTeamId) || null;
  const currentUserMembership = teamMembers.find(member => member.userId === user?.$id) || null;

  // ============================================================================
  // INICIALIZAÇÃO DO TIME PADRÃO
  // ============================================================================

  useEffect(() => {
    if (!isAuthenticated || !user || isLoadingTeams) return;

    // Se já tem um time selecionado, manter
    if (currentTeamId && teams.some(team => team.$id === currentTeamId)) {
      return;
    }

    // Tentar recuperar time salvo no localStorage
    const savedTeamId = localStorage.getItem(`currentTeam_${user.$id}`);
    if (savedTeamId && teams.some(team => team.$id === savedTeamId)) {
      setCurrentTeamId(savedTeamId);
      return;
    }

    // Selecionar primeiro time disponível
    if (teams.length > 0) {
      const firstTeam = teams[0];
      setCurrentTeamId(firstTeam.$id);
      localStorage.setItem(`currentTeam_${user.$id}`, firstTeam.$id);
      log.info('Time padrão selecionado', { teamId: firstTeam.$id, teamName: firstTeam.name });
    }
  }, [isAuthenticated, user, teams, isLoadingTeams, currentTeamId]);

  // ============================================================================
  // CONTEXTO DE PERMISSÕES
  // ============================================================================

  const permissionContext = useMemo((): UserPermissionContext | null => {
    if (!user || !currentTeamId || !currentUserMembership) {
      return null;
    }

    try {
      // Buscar preferências do time (incluindo cargos customizados)
      const teamPreferences = currentTeam?.prefs as TeamPreferences | undefined;

      return getUserPermissionContext(
        user.$id,
        currentTeamId,
        currentUserMembership,
        teamPreferences || null
      );
    } catch (error) {
      log.error('Erro ao calcular contexto de permissões', error instanceof Error ? error : undefined);
      setError('Erro ao carregar permissões');
      return null;
    }
  }, [user, currentTeamId, currentUserMembership, currentTeam]);

  // ============================================================================
  // AÇÕES
  // ============================================================================

  const switchTeam = useCallback((teamId: string) => {
    if (!user) return;

    const team = teams.find(t => t.$id === teamId);
    if (!team) {
      setError('Time não encontrado');
      return;
    }

    setCurrentTeamId(teamId);
    localStorage.setItem(`currentTeam_${user.$id}`, teamId);
    setError(null);

    log.info('Time alterado', {
      teamId,
      teamName: team.name,
      userId: user.$id
    });
  }, [user, teams]);

  const refreshTeamData = useCallback(() => {
    refetchTeams();
    if (currentTeamId) {
      refetchMembers();
    }
  }, [refetchTeams, refetchMembers, currentTeamId]);

  // ============================================================================
  // LIMPEZA DE ERROS
  // ============================================================================

  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => setError(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [error]);

  // ============================================================================
  // VALOR DO CONTEXTO
  // ============================================================================

  const value: TeamContextType = useMemo(() => ({
    currentTeam,
    currentTeamId,
    teams,
    isLoadingTeams,
    teamMembers,
    currentUserMembership,
    permissionContext,
    switchTeam,
    refreshTeamData,
    isLoading,
    error,
  }), [
    currentTeam,
    currentTeamId,
    teams,
    isLoadingTeams,
    teamMembers,
    currentUserMembership,
    permissionContext,
    switchTeam,
    refreshTeamData,
    isLoading,
    error,
  ]);

  return (
    <TeamContext.Provider value={value}>
      {children}
    </TeamContext.Provider>
  );
}

// ============================================================================
// HOOK
// ============================================================================

export function useTeamContext() {
  const context = useContext(TeamContext);
  if (context === undefined) {
    throw new Error('useTeamContext must be used within a TeamProvider');
  }
  return context;
}

// ============================================================================
// HOOKS DE CONVENIÊNCIA
// ============================================================================

/**
 * Hook para verificar permissões
 */
export function usePermissions() {
  const { permissionContext } = useTeamContext();

  return {
    context: permissionContext,
    hasFullAccess: permissionContext?.hasFullAccess || false,
    isOwner: permissionContext?.userType === 'owner',
    isAdmin: permissionContext?.userType === 'admin',
    isUser: permissionContext?.userType === 'user',
    isGuest: permissionContext?.userType === 'guest',
    accessibleResources: permissionContext?.accessibleResources || [],
  };
}

/**
 * Hook para verificar se tem acesso a um recurso específico
 */
export function useHasAccess(resource: string) {
  const { permissionContext } = useTeamContext();

  return useMemo(() => {
    if (!permissionContext) return false;
    return permissionContext.accessibleResources.includes(resource as any);
  }, [permissionContext, resource]);
}

/**
 * Hook para obter informações do time atual
 */
export function useCurrentTeam() {
  const { currentTeam, currentTeamId, currentUserMembership } = useTeamContext();

  return {
    team: currentTeam,
    teamId: currentTeamId,
    membership: currentUserMembership,
    isOwner: currentUserMembership?.roles.includes('owner') || false,
  };
}
