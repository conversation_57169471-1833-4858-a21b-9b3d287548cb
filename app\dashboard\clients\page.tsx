'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Plus, Upload, Download } from 'lucide-react';
import { ClientStats } from '@/components/clients/client-stats';
import { ClientsTable } from '@/components/clients/clients-table';
import { ClientCreateModal } from '@/components/clients/client-create-modal';
import { ClientEditModal } from '@/components/clients/client-edit-modal';
import { ClientViewModal } from '@/components/clients/client-view-modal';
import { ClientDeleteModal } from '@/components/clients/client-delete-modal';
import { DataImportModal } from '@/components/clients/data-import-modal';
import { DataExportModal } from '@/components/clients/data-export-modal';
import { useClients } from '@/hooks/use-api';
import { useAuth } from '@/hooks/use-auth';
import { canImportData } from '@/lib/plan-limits';
import { TeamUpgradeModal } from '@/components/teams/team-upgrade-modal';
import { clientModalsStore, clientModalsActions } from '@/lib/stores/client-modals';
import type { Client } from '@/schemas/clients';

export default function ClientsPage() {
  const { user } = useAuth();

  // Modal states (apenas para modais que não usam Valtio ainda)
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);

  const { data: clients, isLoading, error } = useClients();

  const handleImportClick = () => {
    if (!user || !canImportData(user)) {
      setShowUpgradeModal(true);
      return;
    }
    clientModalsActions.openImportModal();
  };

  const handleExportClick = () => {
    clientModalsActions.openExportModal();
  };

  const handleCreateClick = () => {
    clientModalsActions.openCreateModal();
  };

  const handleEditClient = (client: Client) => {
    clientModalsActions.openEditModal(client);
  };

  const handleViewClient = (client: Client) => {
    clientModalsActions.openViewModal(client);
  };

  const handleDeleteClient = (client: Client) => {
    clientModalsActions.openDeleteModal(client);
  };

  if (error) {
    return (
      <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
        <div className="px-4 lg:px-6">
          <div className="rounded-lg border border-destructive/50 bg-destructive/10 p-4">
            <h3 className="font-semibold text-destructive">Erro ao carregar clientes</h3>
            <p className="text-sm text-destructive/80 mt-1">
              {error instanceof Error ? error.message : 'Erro desconhecido'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
      {/* Header */}
      <div className="px-4 lg:px-6">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div className="space-y-1">
            <h1 className="text-2xl font-bold tracking-tight">Clientes</h1>
            <p className="text-muted-foreground">
              Gerencie seus clientes e mantenha contato com eles.
            </p>
          </div>

          <div className="flex flex-col gap-2 sm:flex-row">
            <Button
              variant="outline"
              size="sm"
              onClick={handleImportClick}
              className="justify-start sm:justify-center"
            >
              <Upload className="mr-2 h-4 w-4" />
              Importar
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleExportClick}
              className="justify-start sm:justify-center"
            >
              <Download className="mr-2 h-4 w-4" />
              Exportar
            </Button>

            <Button
              size="sm"
              onClick={handleCreateClick}
              className="justify-start sm:justify-center"
            >
              <Plus className="mr-2 h-4 w-4" />
              Novo Cliente
            </Button>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="px-4 lg:px-6">
        <ClientStats />
      </div>

      {/* Table */}
      <div className="px-4 lg:px-6">
        <ClientsTable
          data={clients || []}
          isLoading={isLoading}
          onEdit={handleEditClient}
          onView={handleViewClient}
          onDelete={handleDeleteClient}
        />
      </div>

      {/* Modals */}
      <ClientCreateModal />
      <ClientEditModal />
      <ClientViewModal />
      <ClientDeleteModal />
      <DataImportModal
        open={clientModalsStore.import.isOpen}
        onOpenChange={(open) => clientModalsStore.import.isOpen = open}
      />
      <DataExportModal
        open={clientModalsStore.export.isOpen}
        onOpenChange={(open) => clientModalsStore.export.isOpen = open}
      />

      {/* Upgrade Modal */}
      <TeamUpgradeModal
        open={showUpgradeModal}
        onOpenChange={setShowUpgradeModal}
        feature="Importação de dados"
      />
    </div>
  );
}
