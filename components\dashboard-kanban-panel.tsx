/**
 * Dashboard Kanban Panel Component
 * Displays kanban boards summary and recent tasks
 */

import React from 'react';
import Link from 'next/link';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from './ui/card';
import { Badge } from './ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { Button } from './ui/button';
import { Skeleton } from './ui/skeleton';
import { Progress } from './ui/progress';
import {
  Kanban,
  ArrowRight,
  Plus,
  Clock,
  CheckCircle,
  Circle,
  AlertCircle,
  Users,
  Calendar,
  Target,
} from 'lucide-react';
import { useBoards, useBoardsSummary } from '../hooks/use-api';
import { useAuth } from '../hooks/use-auth';
import { useIsMobile } from '../hooks/use-mobile';
import type { Board, Task, TaskPriority, Column, BoardWithData } from '@/schemas/kanban';

// ============================================================================
// TYPES
// ============================================================================

interface DashboardKanbanPanelProps {
  limit?: number;
  showHeader?: boolean;
  className?: string;
}

interface BoardSummary {
  board: Board;
  totalTasks: number;
  completedTasks: number;
  inProgressTasks: number;
  overdueTasks: number;
  recentTasks: Task[];
}

// ============================================================================
// COMPONENTS
// ============================================================================

function BoardItem({ summary }: { summary: BoardSummary }) {
  const { board, totalTasks, completedTasks, inProgressTasks } = summary;
  const completionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

  const getVisibilityIcon = (visibility: string) => {
    switch (visibility) {
      case 'team':
        return <Users className="h-3 w-3" />;
      case 'public':
        return <Target className="h-3 w-3" />;
      default:
        return <Circle className="h-3 w-3" />;
    }
  };

  return (
    <div className="p-3 hover:bg-muted/50 rounded-lg transition-colors">
      <div className="flex items-start justify-between mb-2">
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <h4 className="font-medium text-sm truncate">{board.title}</h4>
            <div className="flex items-center gap-1 text-muted-foreground">
              {getVisibilityIcon(board.visibility)}
            </div>
          </div>
          {board.description && (
            <p className="text-xs text-muted-foreground truncate mt-1">
              {board.description}
            </p>
          )}
        </div>

        <Badge variant="outline" className="text-xs ml-2">
          {totalTasks} tarefas
        </Badge>
      </div>

      <div className="space-y-2">
        <div className="flex items-center justify-between text-xs">
          <span className="text-muted-foreground">Progresso</span>
          <span className="font-medium">{Math.round(completionRate)}%</span>
        </div>
        <Progress value={completionRate} className="h-1.5" />

        <div className="flex items-center justify-between text-xs">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-1 text-green-600">
              <CheckCircle className="h-3 w-3" />
              <span>{completedTasks}</span>
            </div>
            <div className="flex items-center gap-1 text-blue-600">
              <Circle className="h-3 w-3" />
              <span>{inProgressTasks}</span>
            </div>
            {summary.overdueTasks > 0 && (
              <div className="flex items-center gap-1 text-red-600">
                <AlertCircle className="h-3 w-3" />
                <span>{summary.overdueTasks}</span>
              </div>
            )}
          </div>

          <div className="text-muted-foreground">
            {formatDistanceToNow(new Date(board.$updatedAt), {
              addSuffix: true,
              locale: ptBR,
            })}
          </div>
        </div>
      </div>
    </div>
  );
}

function TaskItem({ task }: { task: Task }) {
  const getPriorityColor = (priority: TaskPriority) => {
    switch (priority) {
      case 'critica':
        return 'bg-red-100 text-red-800';
      case 'alta':
        return 'bg-orange-100 text-orange-800';
      case 'media':
        return 'bg-yellow-100 text-yellow-800';
      case 'baixa':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const isOverdue = task.dueDate && new Date(task.dueDate) < new Date();

  return (
    <div className="flex items-center justify-between p-2 hover:bg-muted/50 rounded-lg transition-colors">
      <div className="flex items-center gap-2 flex-1 min-w-0">
        <div className={`w-2 h-2 rounded-full ${task.status === 'done' ? 'bg-green-500' : 'bg-blue-500'}`} />
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium truncate">{task.title}</p>
          <div className="flex items-center gap-2 mt-1">
            <Badge variant="outline" className={`text-xs ${getPriorityColor(task.priority)}`}>
              {task.priority}
            </Badge>
            {task.dueDate && (
              <div className={`flex items-center gap-1 text-xs ${isOverdue ? 'text-red-600' : 'text-muted-foreground'}`}>
                <Calendar className="h-3 w-3" />
                <span>
                  {formatDistanceToNow(new Date(task.dueDate), {
                    addSuffix: true,
                    locale: ptBR,
                  })}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

function KanbanSkeleton({ count = 3 }: { count?: number }) {
  return (
    <div className="space-y-3">
      {Array.from({ length: count }).map((_, i) => (
        <div key={i} className="p-3">
          <div className="flex items-center justify-between mb-2">
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-5 w-16" />
          </div>
          <Skeleton className="h-2 w-full mb-2" />
          <div className="flex items-center justify-between">
            <div className="flex gap-2">
              <Skeleton className="h-3 w-8" />
              <Skeleton className="h-3 w-8" />
            </div>
            <Skeleton className="h-3 w-16" />
          </div>
        </div>
      ))}
    </div>
  );
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

// Custom hook to fetch board summaries with real task data
function useBoardSummaries(boards: Board[], limit: number) {
  const { user } = useAuth();
  const { data: boardsData } = useBoardsSummary(user?.$id);

  return React.useMemo(() => {
    if (!boardsData || !boards) return [];

    const summaries: BoardSummary[] = [];

    boards.slice(0, limit).forEach((board) => {
      // Para estrutura otimizada, usar dados do summary
      const totalTasks = boardsData.total || 0;
      const activeTasks = boardsData.active || 0;

        summaries.push({
          board,
          totalTasks: Math.floor(totalTasks / boards.length),
          completedTasks: Math.floor(activeTasks / boards.length),
          inProgressTasks: 0,
          overdueTasks: 0,
          recentTasks: [],
        });
    });

    return summaries;
  }, [boardsData]);
}

export function DashboardKanbanPanel({
  limit = 5,
  showHeader = true,
  className = ""
}: DashboardKanbanPanelProps) {
  const { data: boards = [], isLoading, error } = useBoards();
  const boardSummaries = useBoardSummaries(boards, limit);
  const isMobile = useIsMobile();

  // Calculate overall stats
  const overallStats = React.useMemo(() => {
    const totalBoards = boards.length;
    const totalTasks = boardSummaries.reduce((sum, summary) => sum + summary.totalTasks, 0);
    const completedTasks = boardSummaries.reduce((sum, summary) => sum + summary.completedTasks, 0);
    const overdueTasks = boardSummaries.reduce((sum, summary) => sum + summary.overdueTasks, 0);

    return {
      totalBoards,
      totalTasks,
      completedTasks,
      overdueTasks,
      completionRate: totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0,
    };
  }, [boards.length, boardSummaries]);

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <Kanban className="h-8 w-8 mx-auto mb-2" />
            <p>Erro ao carregar boards kanban</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      {showHeader && (
        <CardHeader className="pb-3">
          <div className={`flex ${isMobile ? 'flex-col gap-3' : 'items-center justify-between'}`}>
            <div className="flex items-center gap-2">
              <Kanban className="h-5 w-5 text-primary" />
              <div className="flex-1 min-w-0">
                <CardTitle className={`${isMobile ? 'text-base' : 'text-lg'}`}>Kanban Boards</CardTitle>
                <CardDescription className={isMobile ? 'text-xs' : ''}>
                  {isLoading ? 'Carregando...' : `${overallStats.totalBoards} board(s), ${overallStats.totalTasks} tarefa(s)`}
                </CardDescription>
              </div>
            </div>

            <div className="flex gap-2">
              <Button variant="outline" size="sm" asChild>
                <Link href="/dashboard/kanban">
                  Ver todos
                  <ArrowRight className="h-4 w-4 ml-1" />
                </Link>
              </Button>
              <Button size="sm" asChild>
                <Link href="/dashboard/kanban">
                  <Plus className="h-4 w-4 mr-1" />
                  Novo
                </Link>
              </Button>
            </div>
          </div>

          {/* Overall Stats */}
          {!isLoading && overallStats.totalTasks > 0 && (
            <div className={`grid gap-3 mt-4 ${isMobile ? 'grid-cols-2' : 'grid-cols-2 md:grid-cols-4'}`}>
              <div className="text-center">
                <div className={`font-bold text-blue-600 ${isMobile ? 'text-lg' : 'text-2xl'}`}>{overallStats.totalBoards}</div>
                <div className="text-xs text-muted-foreground">Boards</div>
              </div>
              <div className="text-center">
                <div className={`font-bold text-green-600 ${isMobile ? 'text-lg' : 'text-2xl'}`}>{overallStats.completedTasks}</div>
                <div className="text-xs text-muted-foreground">Concluídas</div>
              </div>
              <div className="text-center">
                <div className={`font-bold text-orange-600 ${isMobile ? 'text-lg' : 'text-2xl'}`}>{overallStats.totalTasks - overallStats.completedTasks}</div>
                <div className="text-xs text-muted-foreground">Pendentes</div>
              </div>
              <div className="text-center">
                <div className={`font-bold text-purple-600 ${isMobile ? 'text-lg' : 'text-2xl'}`}>{Math.round(overallStats.completionRate)}%</div>
                <div className="text-xs text-muted-foreground">Progresso</div>
              </div>
            </div>
          )}
        </CardHeader>
      )}

      <CardContent className={showHeader ? "pt-0" : "p-6"}>
        {isLoading ? (
          <KanbanSkeleton count={limit} />
        ) : boardSummaries.length === 0 ? (
          <div className="text-center py-8">
            <Kanban className="h-12 w-12 mx-auto text-muted-foreground mb-3" />
            <h3 className="text-sm font-medium text-muted-foreground mb-2">
              Nenhum board kanban
            </h3>
            <p className="text-xs text-muted-foreground mb-4">
              Crie seu primeiro board para organizar tarefas
            </p>
            <Button size="sm" asChild>
              <Link href="/dashboard/kanban">
                <Plus className="h-4 w-4 mr-1" />
                Criar Board
              </Link>
            </Button>
          </div>
        ) : (
          <div className="space-y-1">
            {boardSummaries.map((summary) => (
              <BoardItem key={summary.board.$id} summary={summary} />
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
