"use client"

import { <PERSON>, <PERSON><PERSON><PERSON> } from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
} from "@/components/ui/chart"
import { useBoards } from "@/hooks/api/use-kanban"

const chartConfig = {
  tasks: {
    label: "Tarefas",
  },
  todo: {
    label: "A Fazer",
    color: "hsl(var(--chart-1))",
  },
  inProgress: {
    label: "Em Progresso",
    color: "hsl(var(--chart-2))",
  },
  review: {
    label: "Em Revisão",
    color: "hsl(var(--chart-3))",
  },
  done: {
    label: "Concluído",
    color: "hsl(var(--chart-4))",
  },
  blocked: {
    label: "Bloqueado",
    color: "hsl(var(--chart-5))",
  },
} satisfies ChartConfig

export function KanbanPieChart() {
  const { data: boards } = useBoards()

  // Processar dados do kanban
  const tasksByStatus = boards?.reduce((acc, board) => {
    board.columns?.forEach(column => {
      column.tasks?.forEach(task => {
        const status = task.status || 'todo'
        acc[status] = (acc[status] || 0) + 1
      })
    })
    return acc
  }, {} as Record<string, number>) || {}

  const chartData = [
    {
      status: "todo",
      tasks: tasksByStatus.todo || 0,
      fill: "var(--color-todo)"
    },
    {
      status: "inProgress",
      tasks: tasksByStatus.inProgress || tasksByStatus.in_progress || 0,
      fill: "var(--color-inProgress)"
    },
    {
      status: "review",
      tasks: tasksByStatus.review || 0,
      fill: "var(--color-review)"
    },
    {
      status: "done",
      tasks: tasksByStatus.done || tasksByStatus.completed || 0,
      fill: "var(--color-done)"
    },
    {
      status: "blocked",
      tasks: tasksByStatus.blocked || 0,
      fill: "var(--color-blocked)"
    },
  ].filter(item => item.tasks > 0) // Só mostrar status com tarefas

  const totalTasks = chartData.reduce((sum, item) => sum + item.tasks, 0)
  const hasData = totalTasks > 0

  return (
    <Card className="flex flex-col">
      <CardHeader className="items-center pb-0">
        <CardTitle>Status das Tarefas</CardTitle>
        <CardDescription>Distribuição por status no Kanban</CardDescription>
      </CardHeader>
      <CardContent className="flex-1 pb-0">
        {hasData ? (
          <ChartContainer
            config={chartConfig}
            className="mx-auto aspect-square max-h-[300px]"
          >
            <PieChart>
              <Pie
                data={chartData}
                dataKey="tasks"
                nameKey="status"
              />
              <ChartLegend
                content={<ChartLegendContent nameKey="status" />}
                className="-translate-y-2 flex-wrap gap-2 *:basis-1/4 *:justify-center"
              />
            </PieChart>
          </ChartContainer>
        ) : (
          <div className="flex items-center justify-center h-[300px] text-muted-foreground">
            <div className="text-center">
              <p className="text-lg font-medium">Não há dados disponíveis</p>
              <p className="text-sm">Nenhuma tarefa foi criada ainda</p>
            </div>
          </div>
        )}
      </CardContent>
      {hasData && (
        <div className="text-center text-sm text-muted-foreground pb-4">
          Total de {totalTasks} tarefas
        </div>
      )}
    </Card>
  )
}
