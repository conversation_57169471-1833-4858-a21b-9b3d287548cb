@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

body {
  @apply overscroll-none bg-transparent;
}

:root {
  --font-sans: var(--font-inter);
  --header-height: calc(var(--spacing) * 12 + 1px);
}

.theme-scaled {
  @media (min-width: 1024px) {
    --radius: 0.6rem;
    --text-lg: 1.05rem;
    --text-base: 0.85rem;
    --text-sm: 0.8rem;
    --spacing: 0.222222rem;
  }

  [data-slot="card"] {
    --spacing: 0.16rem;
  }

  [data-slot="select-trigger"],
  [data-slot="toggle-group-item"] {
    --spacing: 0.222222rem;
  }
}

.theme-default,
.theme-default-scaled {
  --primary: var(--color-neutral-600);
  --primary-foreground: var(--color-neutral-50);

  @variant dark {
    --primary: var(--color-neutral-500);
    --primary-foreground: var(--color-neutral-50);
  }
}

.theme-blue,
.theme-blue-scaled {
  --primary: var(--color-blue-600);
  --primary-foreground: var(--color-blue-50);

  @variant dark {
    --primary: var(--color-blue-500);
    --primary-foreground: var(--color-blue-50);
  }
}

.theme-green,
.theme-green-scaled {
  --primary: var(--color-lime-600);
  --primary-foreground: var(--color-lime-50);

  @variant dark {
    --primary: var(--color-lime-600);
    --primary-foreground: var(--color-lime-50);
  }
}

.theme-amber,
.theme-amber-scaled {
  --primary: var(--color-amber-600);
  --primary-foreground: var(--color-amber-50);

  @variant dark {
    --primary: var(--color-amber-500);
    --primary-foreground: var(--color-amber-50);
  }
}

.theme-mono,
.theme-mono-scaled {
  --font-sans: var(--font-mono);
  --primary: var(--color-neutral-600);
  --primary-foreground: var(--color-neutral-50);

  @variant dark {
    --primary: var(--color-neutral-500);
    --primary-foreground: var(--color-neutral-50);
  }

  .rounded-xs,
  .rounded-sm,
  .rounded-md,
  .rounded-lg,
  .rounded-xl {
    @apply !rounded-none;
    border-radius: 0;
  }

  .shadow-xs,
  .shadow-sm,
  .shadow-md,
  .shadow-lg,
  .shadow-xl {
    @apply !shadow-none;
  }

  [data-slot="toggle-group"],
  [data-slot="toggle-group-item"] {
    @apply !rounded-none !shadow-none;
  }
}

/* E-commerce Modern Theme - Dark with Teal Accents */
.theme-ecommerce,
.theme-ecommerce-scaled {
  --primary: oklch(0.7 0.15 180); /* Teal 500 */
  --primary-foreground: oklch(0.98 0.02 180); /* Teal 50 */
  --secondary: oklch(0.25 0.02 240); /* Dark Blue Gray */
  --secondary-foreground: oklch(0.98 0.02 180);
  --accent: oklch(0.75 0.18 195); /* Cyan 400 */
  --accent-foreground: oklch(0.15 0.02 240);
  --background: oklch(0.98 0.005 240); /* Very light blue-gray */
  --foreground: oklch(0.15 0.02 240); /* Dark blue-gray */
  --card: oklch(1 0 0); /* Pure white */
  --card-foreground: oklch(0.15 0.02 240);
  --muted: oklch(0.96 0.01 240); /* Light gray with blue tint */
  --muted-foreground: oklch(0.45 0.02 240);
  --border: oklch(0.92 0.01 240);
  --input: oklch(0.96 0.01 240);
  --ring: oklch(0.7 0.15 180);

  @variant dark {
    --primary: oklch(0.65 0.15 180); /* Teal 400 */
    --primary-foreground: oklch(0.98 0.02 180);
    --secondary: oklch(0.2 0.02 240); /* Darker blue-gray */
    --secondary-foreground: oklch(0.98 0.02 180);
    --accent: oklch(0.7 0.18 195); /* Cyan 500 */
    --accent-foreground: oklch(0.98 0.02 180);
    --background: oklch(0.12 0.02 240); /* Very dark blue-gray */
    --foreground: oklch(0.98 0.02 180); /* Light teal */
    --card: oklch(0.15 0.02 240); /* Dark card background */
    --card-foreground: oklch(0.98 0.02 180);
    --muted: oklch(0.18 0.02 240);
    --muted-foreground: oklch(0.65 0.02 240);
    --border: oklch(0.25 0.02 240);
    --input: oklch(0.18 0.02 240);
    --ring: oklch(0.65 0.15 180);
  }
}

/* E-commerce Custom Gradient Classes */
.bg-ecommerce-gradient {
  background: linear-gradient(135deg,
    oklch(0.12 0.02 240) 0%,
    oklch(0.15 0.02 240) 50%,
    oklch(0.12 0.02 240) 100%);
}

.bg-ecommerce-card-gradient {
  background: linear-gradient(135deg,
    oklch(0.15 0.02 240 / 0.8) 0%,
    oklch(0.18 0.02 240 / 0.8) 100%);
}

.bg-ecommerce-primary-gradient {
  background: linear-gradient(135deg,
    oklch(0.7 0.15 180) 0%,
    oklch(0.75 0.18 195) 100%);
}

.text-ecommerce-gradient {
  background: linear-gradient(135deg,
    oklch(0.7 0.15 180) 0%,
    oklch(0.75 0.18 195) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Product Card Enhancements */
.product-card-modern {
  background: linear-gradient(135deg,
    oklch(0.15 0.02 240 / 0.9) 0%,
    oklch(0.18 0.02 240 / 0.9) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid oklch(0.25 0.02 240 / 0.5);
}

.product-card-modern:hover {
  background: linear-gradient(135deg,
    oklch(0.18 0.02 240 / 0.95) 0%,
    oklch(0.2 0.02 240 / 0.95) 100%);
  border-color: oklch(0.65 0.15 180 / 0.5);
}

/* Candyland Theme - Colorful and vibrant */
.theme-candyland,
.theme-candyland-scaled {
  --background: oklch(0.9809 0.0025 228.7836);
  --foreground: oklch(0.3211 0 0);
  --card: oklch(1.0000 0 0);
  --card-foreground: oklch(0.3211 0 0);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.3211 0 0);
  --primary: oklch(0.8677 0.0735 7.0855);
  --primary-foreground: oklch(0 0 0);
  --secondary: oklch(0.8148 0.0819 225.7537);
  --secondary-foreground: oklch(0 0 0);
  --muted: oklch(0.8828 0.0285 98.1033);
  --muted-foreground: oklch(0.5382 0 0);
  --accent: oklch(0.9680 0.2110 109.7692);
  --accent-foreground: oklch(0 0 0);
  --destructive: oklch(0.6368 0.2078 25.3313);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.8699 0 0);
  --input: oklch(0.8699 0 0);
  --ring: oklch(0.8677 0.0735 7.0855);
  --sidebar: oklch(0.9809 0.0025 228.7836);
  --sidebar-foreground: oklch(0.3211 0 0);
  --sidebar-primary: oklch(0.8677 0.0735 7.0855);
  --sidebar-primary-foreground: oklch(0 0 0);
  --sidebar-accent: oklch(0.9680 0.2110 109.7692);
  --sidebar-accent-foreground: oklch(0 0 0);
  --sidebar-border: oklch(0.8699 0 0);
  --sidebar-ring: oklch(0.8677 0.0735 7.0855);

  @variant dark {
    --background: oklch(0.2303 0.0125 264.2926);
    --foreground: oklch(0.9219 0 0);
    --card: oklch(0.3210 0.0078 223.6661);
    --card-foreground: oklch(0.9219 0 0);
    --popover: oklch(0.3210 0.0078 223.6661);
    --popover-foreground: oklch(0.9219 0 0);
    --primary: oklch(0.8027 0.1355 349.2347);
    --primary-foreground: oklch(0 0 0);
    --secondary: oklch(0.7395 0.2268 142.8504);
    --secondary-foreground: oklch(0 0 0);
    --muted: oklch(0.3867 0 0);
    --muted-foreground: oklch(0.7155 0 0);
    --accent: oklch(0.8148 0.0819 225.7537);
    --accent-foreground: oklch(0 0 0);
    --destructive: oklch(0.6368 0.2078 25.3313);
    --destructive-foreground: oklch(1.0000 0 0);
    --border: oklch(0.3867 0 0);
    --input: oklch(0.3867 0 0);
    --ring: oklch(0.8027 0.1355 349.2347);
    --sidebar: oklch(0.2303 0.0125 264.2926);
    --sidebar-foreground: oklch(0.9219 0 0);
    --sidebar-primary: oklch(0.8027 0.1355 349.2347);
    --sidebar-primary-foreground: oklch(0 0 0);
    --sidebar-accent: oklch(0.8148 0.0819 225.7537);
    --sidebar-accent-foreground: oklch(0 0 0);
    --sidebar-border: oklch(0.3867 0 0);
    --sidebar-ring: oklch(0.8027 0.1355 349.2347);
  }
}

/* Cyberpunk Theme - Futuristic with neon colors */
.theme-cyberpunk,
.theme-cyberpunk-scaled {
  --background: oklch(0.9816 0.0017 247.8390);
  --foreground: oklch(0.1649 0.0352 281.8285);
  --card: oklch(1.0000 0 0);
  --card-foreground: oklch(0.1649 0.0352 281.8285);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.1649 0.0352 281.8285);
  --primary: oklch(0.6726 0.2904 341.4084);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.9595 0.0200 286.0164);
  --secondary-foreground: oklch(0.1649 0.0352 281.8285);
  --muted: oklch(0.9595 0.0200 286.0164);
  --muted-foreground: oklch(0.1649 0.0352 281.8285);
  --accent: oklch(0.8903 0.1739 171.2690);
  --accent-foreground: oklch(0.1649 0.0352 281.8285);
  --destructive: oklch(0.6535 0.2348 34.0370);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.9205 0.0086 225.0878);
  --input: oklch(0.9205 0.0086 225.0878);
  --ring: oklch(0.6726 0.2904 341.4084);
  --sidebar: oklch(0.9595 0.0200 286.0164);
  --sidebar-foreground: oklch(0.1649 0.0352 281.8285);
  --sidebar-primary: oklch(0.6726 0.2904 341.4084);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.8903 0.1739 171.2690);
  --sidebar-accent-foreground: oklch(0.1649 0.0352 281.8285);
  --sidebar-border: oklch(0.9205 0.0086 225.0878);
  --sidebar-ring: oklch(0.6726 0.2904 341.4084);

  @variant dark {
    --background: oklch(0.1649 0.0352 281.8285);
    --foreground: oklch(0.9513 0.0074 260.7315);
    --card: oklch(0.2542 0.0611 281.1423);
    --card-foreground: oklch(0.9513 0.0074 260.7315);
    --popover: oklch(0.2542 0.0611 281.1423);
    --popover-foreground: oklch(0.9513 0.0074 260.7315);
    --primary: oklch(0.6726 0.2904 341.4084);
    --primary-foreground: oklch(1.0000 0 0);
    --secondary: oklch(0.2542 0.0611 281.1423);
    --secondary-foreground: oklch(0.9513 0.0074 260.7315);
    --muted: oklch(0.2542 0.0611 281.1423);
    --muted-foreground: oklch(0.6245 0.0500 278.1046);
    --accent: oklch(0.8903 0.1739 171.2690);
    --accent-foreground: oklch(0.1649 0.0352 281.8285);
    --destructive: oklch(0.6535 0.2348 34.0370);
    --destructive-foreground: oklch(1.0000 0 0);
    --border: oklch(0.3279 0.0832 280.7890);
    --input: oklch(0.3279 0.0832 280.7890);
    --ring: oklch(0.6726 0.2904 341.4084);
    --sidebar: oklch(0.1649 0.0352 281.8285);
    --sidebar-foreground: oklch(0.9513 0.0074 260.7315);
    --sidebar-primary: oklch(0.6726 0.2904 341.4084);
    --sidebar-primary-foreground: oklch(1.0000 0 0);
    --sidebar-accent: oklch(0.8903 0.1739 171.2690);
    --sidebar-accent-foreground: oklch(0.1649 0.0352 281.8285);
    --sidebar-border: oklch(0.3279 0.0832 280.7890);
    --sidebar-ring: oklch(0.6726 0.2904 341.4084);
  }
}

/* Sunset Horizon Theme - Warm sunset colors */
.theme-sunset,
.theme-sunset-scaled {
  --background: oklch(0.9856 0.0084 56.3169);
  --foreground: oklch(0.3353 0.0132 2.7676);
  --card: oklch(1.0000 0 0);
  --card-foreground: oklch(0.3353 0.0132 2.7676);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.3353 0.0132 2.7676);
  --primary: oklch(0.7357 0.1641 34.7091);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.9596 0.0200 28.9029);
  --secondary-foreground: oklch(0.5587 0.1294 32.7364);
  --muted: oklch(0.9656 0.0176 39.4009);
  --muted-foreground: oklch(0.5534 0.0116 58.0708);
  --accent: oklch(0.8278 0.1131 57.9984);
  --accent-foreground: oklch(0.3353 0.0132 2.7676);
  --destructive: oklch(0.6122 0.2082 22.2410);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.9296 0.0370 38.6868);
  --input: oklch(0.9296 0.0370 38.6868);
  --ring: oklch(0.7357 0.1641 34.7091);
  --sidebar: oklch(0.9656 0.0176 39.4009);
  --sidebar-foreground: oklch(0.3353 0.0132 2.7676);
  --sidebar-primary: oklch(0.7357 0.1641 34.7091);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.8278 0.1131 57.9984);
  --sidebar-accent-foreground: oklch(0.3353 0.0132 2.7676);
  --sidebar-border: oklch(0.9296 0.0370 38.6868);
  --sidebar-ring: oklch(0.7357 0.1641 34.7091);

  @variant dark {
    --background: oklch(0.2569 0.0169 352.4042);
    --foreground: oklch(0.9397 0.0119 51.3156);
    --card: oklch(0.3184 0.0176 341.4465);
    --card-foreground: oklch(0.9397 0.0119 51.3156);
    --popover: oklch(0.3184 0.0176 341.4465);
    --popover-foreground: oklch(0.9397 0.0119 51.3156);
    --primary: oklch(0.7357 0.1641 34.7091);
    --primary-foreground: oklch(1.0000 0 0);
    --secondary: oklch(0.3637 0.0203 342.2664);
    --secondary-foreground: oklch(0.9397 0.0119 51.3156);
    --muted: oklch(0.3184 0.0176 341.4465);
    --muted-foreground: oklch(0.8378 0.0237 52.6346);
    --accent: oklch(0.8278 0.1131 57.9984);
    --accent-foreground: oklch(0.2569 0.0169 352.4042);
    --destructive: oklch(0.6122 0.2082 22.2410);
    --destructive-foreground: oklch(1.0000 0 0);
    --border: oklch(0.3637 0.0203 342.2664);
    --input: oklch(0.3637 0.0203 342.2664);
    --ring: oklch(0.7357 0.1641 34.7091);
    --sidebar: oklch(0.2569 0.0169 352.4042);
    --sidebar-foreground: oklch(0.9397 0.0119 51.3156);
    --sidebar-primary: oklch(0.7357 0.1641 34.7091);
    --sidebar-primary-foreground: oklch(1.0000 0 0);
    --sidebar-accent: oklch(0.8278 0.1131 57.9984);
    --sidebar-accent-foreground: oklch(0.2569 0.0169 352.4042);
    --sidebar-border: oklch(0.3637 0.0203 342.2664);
    --sidebar-ring: oklch(0.7357 0.1641 34.7091);
  }
}

/* Ocean Breeze Theme - Blue/green oceanic */
.theme-ocean,
.theme-ocean-scaled {
  --background: oklch(0.9751 0.0127 244.2507);
  --foreground: oklch(0.3729 0.0306 259.7328);
  --card: oklch(1.0000 0 0);
  --card-foreground: oklch(0.3729 0.0306 259.7328);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.3729 0.0306 259.7328);
  --primary: oklch(0.7227 0.1920 149.5793);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.9514 0.0250 236.8242);
  --secondary-foreground: oklch(0.4461 0.0263 256.8018);
  --muted: oklch(0.9670 0.0029 264.5419);
  --muted-foreground: oklch(0.5510 0.0234 264.3637);
  --accent: oklch(0.9505 0.0507 163.0508);
  --accent-foreground: oklch(0.3729 0.0306 259.7328);
  --destructive: oklch(0.6368 0.2078 25.3313);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.9276 0.0058 264.5313);
  --input: oklch(0.9276 0.0058 264.5313);
  --ring: oklch(0.7227 0.1920 149.5793);
  --sidebar: oklch(0.9514 0.0250 236.8242);
  --sidebar-foreground: oklch(0.3729 0.0306 259.7328);
  --sidebar-primary: oklch(0.7227 0.1920 149.5793);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.9505 0.0507 163.0508);
  --sidebar-accent-foreground: oklch(0.3729 0.0306 259.7328);
  --sidebar-border: oklch(0.9276 0.0058 264.5313);
  --sidebar-ring: oklch(0.7227 0.1920 149.5793);

  @variant dark {
    --background: oklch(0.2077 0.0398 265.7549);
    --foreground: oklch(0.8717 0.0093 258.3382);
    --card: oklch(0.2795 0.0368 260.0310);
    --card-foreground: oklch(0.8717 0.0093 258.3382);
    --popover: oklch(0.2795 0.0368 260.0310);
    --popover-foreground: oklch(0.8717 0.0093 258.3382);
    --primary: oklch(0.7729 0.1535 163.2231);
    --primary-foreground: oklch(0.2077 0.0398 265.7549);
    --secondary: oklch(0.3351 0.0331 260.9120);
    --secondary-foreground: oklch(0.7118 0.0129 286.0665);
    --muted: oklch(0.2795 0.0368 260.0310);
    --muted-foreground: oklch(0.5510 0.0234 264.3637);
    --accent: oklch(0.3729 0.0306 259.7328);
    --accent-foreground: oklch(0.7118 0.0129 286.0665);
    --destructive: oklch(0.6368 0.2078 25.3313);
    --destructive-foreground: oklch(0.2077 0.0398 265.7549);
    --border: oklch(0.4461 0.0263 256.8018);
    --input: oklch(0.4461 0.0263 256.8018);
    --ring: oklch(0.7729 0.1535 163.2231);
    --sidebar: oklch(0.2795 0.0368 260.0310);
    --sidebar-foreground: oklch(0.8717 0.0093 258.3382);
    --sidebar-primary: oklch(0.7729 0.1535 163.2231);
    --sidebar-primary-foreground: oklch(0.2077 0.0398 265.7549);
    --sidebar-accent: oklch(0.3729 0.0306 259.7328);
    --sidebar-accent-foreground: oklch(0.7118 0.0129 286.0665);
    --sidebar-border: oklch(0.4461 0.0263 256.8018);
    --sidebar-ring: oklch(0.7729 0.1535 163.2231);
  }
}

/* Nature Theme - Green natural */
.theme-nature,
.theme-nature-scaled {
  --background: oklch(0.9711 0.0074 80.7211);
  --foreground: oklch(0.3000 0.0358 30.2042);
  --card: oklch(0.9711 0.0074 80.7211);
  --card-foreground: oklch(0.3000 0.0358 30.2042);
  --popover: oklch(0.9711 0.0074 80.7211);
  --popover-foreground: oklch(0.3000 0.0358 30.2042);
  --primary: oklch(0.5234 0.1347 144.1672);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.9571 0.0210 147.6360);
  --secondary-foreground: oklch(0.4254 0.1159 144.3078);
  --muted: oklch(0.9370 0.0142 74.4218);
  --muted-foreground: oklch(0.4495 0.0486 39.2110);
  --accent: oklch(0.8952 0.0504 146.0366);
  --accent-foreground: oklch(0.4254 0.1159 144.3078);
  --destructive: oklch(0.5386 0.1937 26.7249);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.8805 0.0208 74.6428);
  --input: oklch(0.8805 0.0208 74.6428);
  --ring: oklch(0.5234 0.1347 144.1672);
  --sidebar: oklch(0.9370 0.0142 74.4218);
  --sidebar-foreground: oklch(0.3000 0.0358 30.2042);
  --sidebar-primary: oklch(0.5234 0.1347 144.1672);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.8952 0.0504 146.0366);
  --sidebar-accent-foreground: oklch(0.4254 0.1159 144.3078);
  --sidebar-border: oklch(0.8805 0.0208 74.6428);
  --sidebar-ring: oklch(0.5234 0.1347 144.1672);

  @variant dark {
    --background: oklch(0.2683 0.0279 150.7681);
    --foreground: oklch(0.9423 0.0097 72.6595);
    --card: oklch(0.3327 0.0271 146.9867);
    --card-foreground: oklch(0.9423 0.0097 72.6595);
    --popover: oklch(0.3327 0.0271 146.9867);
    --popover-foreground: oklch(0.9423 0.0097 72.6595);
    --primary: oklch(0.6731 0.1624 144.2083);
    --primary-foreground: oklch(0.2157 0.0453 145.7256);
    --secondary: oklch(0.3942 0.0265 142.9926);
    --secondary-foreground: oklch(0.8970 0.0166 142.5518);
    --muted: oklch(0.3327 0.0271 146.9867);
    --muted-foreground: oklch(0.8579 0.0174 76.0955);
    --accent: oklch(0.5752 0.1446 144.1813);
    --accent-foreground: oklch(0.9423 0.0097 72.6595);
    --destructive: oklch(0.5386 0.1937 26.7249);
    --destructive-foreground: oklch(0.9423 0.0097 72.6595);
    --border: oklch(0.3942 0.0265 142.9926);
    --input: oklch(0.3942 0.0265 142.9926);
    --ring: oklch(0.6731 0.1624 144.2083);
    --sidebar: oklch(0.2683 0.0279 150.7681);
    --sidebar-foreground: oklch(0.9423 0.0097 72.6595);
    --sidebar-primary: oklch(0.6731 0.1624 144.2083);
    --sidebar-primary-foreground: oklch(0.2157 0.0453 145.7256);
    --sidebar-accent: oklch(0.5752 0.1446 144.1813);
    --sidebar-accent-foreground: oklch(0.9423 0.0097 72.6595);
    --sidebar-border: oklch(0.3942 0.0265 142.9926);
    --sidebar-ring: oklch(0.6731 0.1624 144.2083);
  }
}


@layer base {
  :root {
    --chart-1: oklch(0.646 0.222 41.116);
    --chart-2: oklch(0.6 0.118 184.704);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);
  }

  .dark {
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);
  }
}