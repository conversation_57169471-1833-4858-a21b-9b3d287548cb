/**
 * Página de demonstração do chat moderno
 * Mostra os novos componentes de chat baseados no v0-ai-chat
 */

import { ModernChatExample } from "@/components/chat/modern-chat";
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: "Demo Chat Moderno - Template",
  description: "Demonstração do novo sistema de chat moderno",
};

export default function ChatDemoPage() {
  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">Chat Moderno</h1>
        <p className="text-muted-foreground">
          Demonstração do novo sistema de chat baseado no v0-ai-chat com design moderno e funcionalidades aprimoradas.
        </p>
      </div>

      <div className="grid gap-8">
        {/* Exemplo do Chat Moderno */}
        <div className="space-y-4">
          <div>
            <h2 className="text-xl font-semibold">Chat <PERSON>o</h2>
            <p className="text-sm text-muted-foreground">
              Interface limpa e moderna com auto-resize, emoji picker e upload de arquivos.
            </p>
          </div>

          <ModernChatExample />
        </div>

        {/* Características */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Características</h2>
          <div className="grid md:grid-cols-2 gap-4">
            <div className="p-4 border rounded-lg">
              <h3 className="font-medium mb-2">🎨 Design Moderno</h3>
              <p className="text-sm text-muted-foreground">
                Interface limpa baseada no v0-ai-chat com bordas arredondadas e espaçamento otimizado.
              </p>
            </div>

            <div className="p-4 border rounded-lg">
              <h3 className="font-medium mb-2">📏 Auto-resize</h3>
              <p className="text-sm text-muted-foreground">
                Textarea que se ajusta automaticamente ao conteúdo com altura mínima e máxima configuráveis.
              </p>
            </div>

            <div className="p-4 border rounded-lg">
              <h3 className="font-medium mb-2">😊 Emoji Picker</h3>
              <p className="text-sm text-muted-foreground">
                Seletor de emojis integrado com busca e categorias usando Emoji Mart para melhor performance.
              </p>
            </div>

            <div className="p-4 border rounded-lg">
              <h3 className="font-medium mb-2">📎 Upload de Arquivos</h3>
              <p className="text-sm text-muted-foreground">
                Suporte para upload de imagens, documentos e outros tipos de arquivo.
              </p>
            </div>

            <div className="p-4 border rounded-lg">
              <h3 className="font-medium mb-2">💬 Bubbles Inteligentes</h3>
              <p className="text-sm text-muted-foreground">
                Mensagens com avatares, timestamps e status de entrega/erro.
              </p>
            </div>

            <div className="p-4 border rounded-lg">
              <h3 className="font-medium mb-2">⚡ Performance</h3>
              <p className="text-sm text-muted-foreground">
                Otimizado para performance com scroll automático e animações suaves.
              </p>
            </div>
          </div>
        </div>

        {/* Melhorias Implementadas */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Melhorias Implementadas</h2>
          <div className="space-y-3">
            <div className="flex items-start gap-3 p-3 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
              <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
              <div>
                <p className="font-medium text-green-900 dark:text-green-100">Layout Baseado no v0-ai-chat</p>
                <p className="text-sm text-green-700 dark:text-green-300">
                  Estrutura moderna com container principal, área de mensagens e input separado.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3 p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
              <div>
                <p className="font-medium text-blue-900 dark:text-blue-100">Auto-resize Inteligente</p>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  Hook personalizado para redimensionamento automático com altura mínima e máxima.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3 p-3 bg-purple-50 dark:bg-purple-950/20 rounded-lg border border-purple-200 dark:border-purple-800">
              <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
              <div>
                <p className="font-medium text-purple-900 dark:text-purple-100">Componentes Modulares</p>
                <p className="text-sm text-purple-700 dark:text-purple-300">
                  ModernChatInput, ModernChatBubble e ModernChat como componentes reutilizáveis.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3 p-3 bg-orange-50 dark:bg-orange-950/20 rounded-lg border border-orange-200 dark:border-orange-800">
              <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0"></div>
              <div>
                <p className="font-medium text-orange-900 dark:text-orange-100">Integração com Sistemas Existentes</p>
                <p className="text-sm text-orange-700 dark:text-orange-300">
                  GeminiChatbox e TeamChat atualizados para usar os novos componentes.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
