import { useSnapshot } from 'valtio';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '../ui/alert-dialog';
import { useDeleteClient } from '../../hooks/use-api';
import { clientModalsStore } from '../../lib/stores/client-modals';

export function ClientDeleteModal() {
  const snap = useSnapshot(clientModalsStore);
  const deleteClientMutation = useDeleteClient();
  const client = snap.delete.client;

  const handleDelete = async () => {
    if (!client) return;

    try {
      await deleteClientMutation.mutateAsync(client.$id);
      clientModalsStore.delete.isOpen = false;
      clientModalsStore.delete.client = null;
    } catch (error) {
      console.error('Error deleting client:', error);
    }
  };

  const handleClose = () => {
    clientModalsStore.delete.isOpen = false;
    clientModalsStore.delete.client = null;
  };

  if (!client) return null;

  return (
    <AlertDialog open={snap.delete.isOpen} onOpenChange={handleClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Excluir Cliente</AlertDialogTitle>
          <AlertDialogDescription>
            Tem certeza que deseja excluir o cliente <strong>{client.name}</strong>?
            <br />
            <br />
            Esta ação não pode ser desfeita. Todos os dados relacionados a este cliente serão permanentemente removidos.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancelar</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={deleteClientMutation.isPending}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {deleteClientMutation.isPending ? 'Excluindo...' : 'Excluir Cliente'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
