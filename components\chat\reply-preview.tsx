/**
 * Componente para exibir preview de mensagem sendo respondida
 */

import React from 'react';
import { X, Reply } from 'lucide-react';
import { Button } from '../ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { cn } from '../../lib/utils';
import type { ChatMessage } from '@/schemas/chat';

interface ReplyPreviewProps {
  replyingTo: ChatMessage;
  onCancel: () => void;
  className?: string;
}

export function ReplyPreview({ replyingTo, onCancel, className }: ReplyPreviewProps) {
  const truncateContent = (content: string, maxLength: number = 100) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  return (
    <div className={cn(
      "flex items-start gap-3 p-3 bg-muted/50 border-l-4 border-primary rounded-r-md",
      className
    )}>
      {/* Ícone de resposta */}
      <Reply className="h-4 w-4 text-primary mt-1 flex-shrink-0" />
      
      {/* Avatar do usuário sendo respondido */}
      <Avatar className="h-6 w-6 flex-shrink-0">
        <AvatarImage src={replyingTo.senderAvatar} />
        <AvatarFallback className="text-xs">
          {replyingTo.senderName.charAt(0).toUpperCase()}
        </AvatarFallback>
      </Avatar>

      {/* Conteúdo da mensagem */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <span className="text-sm font-medium text-foreground">
            {replyingTo.senderName}
          </span>
          <span className="text-xs text-muted-foreground">
            Respondendo a:
          </span>
        </div>
        
        <div className="text-sm text-muted-foreground">
          {replyingTo.type === 'text' ? (
            truncateContent(replyingTo.content)
          ) : replyingTo.type === 'image' ? (
            <span className="italic">📷 Imagem</span>
          ) : replyingTo.type === 'file' ? (
            <span className="italic">📎 {replyingTo.fileName || 'Arquivo'}</span>
          ) : (
            <span className="italic">Mensagem do sistema</span>
          )}
        </div>
      </div>

      {/* Botão para cancelar resposta */}
      <Button
        variant="ghost"
        size="sm"
        className="h-6 w-6 p-0 flex-shrink-0"
        onClick={onCancel}
      >
        <X className="h-3 w-3" />
      </Button>
    </div>
  );
}

interface ReplyIndicatorProps {
  replyTo: ChatMessage;
  className?: string;
  onClick?: () => void;
}

/**
 * Componente para mostrar que uma mensagem é uma resposta
 */
export function ReplyIndicator({ replyTo, className, onClick }: ReplyIndicatorProps) {
  const truncateContent = (content: string, maxLength: number = 60) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  return (
    <div 
      className={cn(
        "flex items-center gap-2 p-2 mb-2 bg-muted/30 border-l-2 border-primary/50 rounded-r cursor-pointer hover:bg-muted/50 transition-colors",
        className
      )}
      onClick={onClick}
    >
      <Reply className="h-3 w-3 text-primary flex-shrink-0" />
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2">
          <span className="text-xs font-medium text-primary">
            {replyTo.senderName}
          </span>
        </div>
        
        <div className="text-xs text-muted-foreground">
          {replyTo.type === 'text' ? (
            truncateContent(replyTo.content)
          ) : replyTo.type === 'image' ? (
            <span className="italic">📷 Imagem</span>
          ) : replyTo.type === 'file' ? (
            <span className="italic">📎 {replyTo.fileName || 'Arquivo'}</span>
          ) : (
            <span className="italic">Mensagem do sistema</span>
          )}
        </div>
      </div>
    </div>
  );
}
