import type { <PERSON>ada<PERSON> } from "next";
import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Target, Users, Lightbulb, Heart, Code, Zap, Shield, Globe } from 'lucide-react';

export const metadata: Metadata = {
  title: "Sobre Nós - Template Appwrite",
  description: "Conheça nossa missão, valores e a equipe por trás do Template Appwrite",
};

const teamMembers = [
  {
    name: "<PERSON>",
    role: "CEO & Fundadora",
    bio: "Especialista em desenvolvimento de produtos com mais de 10 anos de experiência.",
    avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
  },
  {
    name: "<PERSON>",
    role: "<PERSON><PERSON>",
    bio: "Arquiteto de software apaixonado por tecnologias modernas e escalabilidade.",
    avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
  },
  {
    name: "Maria Oliveira",
    role: "Head of Design",
    bio: "Designer UX/UI com foco em experiências intuitivas e acessíveis.",
    avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
  },
];

const values = [
  {
    icon: Code,
    title: "Qualidade",
    description: "Priorizamos código limpo, bem documentado e testado para garantir a melhor experiência.",
  },
  {
    icon: Zap,
    title: "Performance",
    description: "Otimizamos cada detalhe para entregar aplicações rápidas e responsivas.",
  },
  {
    icon: Shield,
    title: "Segurança",
    description: "Implementamos as melhores práticas de segurança para proteger seus dados.",
  },
  {
    icon: Globe,
    title: "Acessibilidade",
    description: "Criamos soluções inclusivas que funcionam para todos os usuários.",
  },
];

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Voltar
              </Link>
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Sobre Nós</h1>
              <p className="text-muted-foreground">Conheça nossa história e missão</p>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12 max-w-6xl">
        {/* Mission Section */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <Badge variant="outline" className="mb-4">
              <Target className="w-4 h-4 mr-2" />
              Nossa Missão
            </Badge>
            <h2 className="text-3xl font-bold mb-4">
              Democratizar o desenvolvimento de aplicações modernas
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Acreditamos que todo desenvolvedor deveria ter acesso a ferramentas e templates de alta qualidade 
              para criar aplicações incríveis sem reinventar a roda. Nosso objetivo é acelerar o desenvolvimento 
              e permitir que você foque no que realmente importa: seu produto.
            </p>
          </div>
        </section>

        {/* Values Section */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <Badge variant="outline" className="mb-4">
              <Heart className="w-4 h-4 mr-2" />
              Nossos Valores
            </Badge>
            <h2 className="text-3xl font-bold mb-4">O que nos move</h2>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {values.map((value, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <value.icon className="w-6 h-6 text-primary" />
                  </div>
                  <CardTitle className="text-lg">{value.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">{value.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Team Section */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <Badge variant="outline" className="mb-4">
              <Users className="w-4 h-4 mr-2" />
              Nossa Equipe
            </Badge>
            <h2 className="text-3xl font-bold mb-4">Conheça quem está por trás</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Uma equipe apaixonada por tecnologia e comprometida em entregar a melhor experiência possível.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {teamMembers.map((member, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <div className="w-24 h-24 rounded-full overflow-hidden mx-auto mb-4">
                    <img 
                      src={member.avatar} 
                      alt={member.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <CardTitle>{member.name}</CardTitle>
                  <CardDescription className="text-primary font-medium">
                    {member.role}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">{member.bio}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Story Section */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <Badge variant="outline" className="mb-4">
              <Lightbulb className="w-4 h-4 mr-2" />
              Nossa História
            </Badge>
            <h2 className="text-3xl font-bold mb-4">Como tudo começou</h2>
          </div>
          
          <div className="max-w-4xl mx-auto">
            <Card>
              <CardContent className="p-8">
                <div className="prose prose-gray max-w-none">
                  <p className="text-lg leading-relaxed mb-6">
                    Tudo começou com uma frustração comum: passar semanas configurando a mesma infraestrutura 
                    básica para cada novo projeto. Autenticação, dashboard, gerenciamento de usuários, 
                    notificações... sempre os mesmos componentes, sempre o mesmo tempo perdido.
                  </p>
                  
                  <p className="text-lg leading-relaxed mb-6">
                    Em 2024, decidimos criar algo diferente. Um template que não fosse apenas um "hello world" 
                    bonito, mas uma base sólida e completa para aplicações reais. Com Appwrite como backend, 
                    React/TypeScript no frontend, e todas as funcionalidades que você realmente precisa.
                  </p>
                  
                  <p className="text-lg leading-relaxed">
                    Hoje, nosso template já ajudou centenas de desenvolvedores a economizar semanas de trabalho 
                    e focar no que realmente importa: construir produtos incríveis que resolvem problemas reais.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* CTA Section */}
        <section className="text-center">
          <Card className="bg-primary/5 border-primary/20">
            <CardContent className="p-8">
              <h3 className="text-2xl font-bold mb-4">Pronto para começar?</h3>
              <p className="text-lg text-muted-foreground mb-6">
                Junte-se a centenas de desenvolvedores que já estão construindo o futuro com nosso template.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" asChild>
                  <Link href="/register">
                    Começar Agora
                  </Link>
                </Button>
                <Button variant="outline" size="lg" asChild>
                  <Link href="/#features">
                    Ver Recursos
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  );
}
