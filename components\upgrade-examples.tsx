/**
 * Exemplos de uso dos componentes de upgrade padronizados
 * Este arquivo demonstra como usar os novos componentes de upgrade
 */

import { UpgradeAlert, SimpleUpgradeAlert, PlanLimitationAlert } from './upgrade-alert';

export function UpgradeExamples() {
  return (
    <div className="space-y-6 p-6">
      <h2 className="text-2xl font-bold">Exemplos de Componentes de Upgrade</h2>
      
      {/* Exemplo 1: UpgradeAlert completo */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">1. UpgradeAlert Completo</h3>
        <UpgradeAlert
          title="Analytics Avançado"
          description="Acesse relatórios detalhados e métricas avançadas com o plano Pro."
          requiredPlan="pro"
          benefits={[
            'Relatórios personalizados',
            'Exportação de dados',
            'Métricas em tempo real',
            'Dashboards customizáveis'
          ]}
          buttonText="Upgrade para Pro"
        />
      </div>

      {/* Exemplo 2: SimpleUpgradeAlert */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">2. SimpleUpgradeAlert</h3>
        <SimpleUpgradeAlert
          feature="Backup automático"
          requiredPlan="pro"
        />
      </div>

      {/* Exemplo 3: PlanLimitationAlert */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">3. PlanLimitationAlert</h3>
        <PlanLimitationAlert
          feature="criação de projetos"
          requiredPlan="pro"
          reason="Você atingiu o limite de projetos do plano gratuito."
        />
      </div>

      {/* Exemplo 4: UpgradeAlert sem botão */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">4. UpgradeAlert sem botão</h3>
        <UpgradeAlert
          title="Funcionalidade em Desenvolvimento"
          description="Esta funcionalidade estará disponível em breve para usuários Premium."
          showButton={false}
        />
      </div>

      {/* Exemplo 5: UpgradeAlert com callback customizado */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">5. UpgradeAlert com callback customizado</h3>
        <UpgradeAlert
          title="Integração com API Externa"
          description="Conecte-se com APIs externas para expandir suas funcionalidades."
          requiredPlan="enterprise"
          buttonText="Saiba Mais"
          onButtonClick={() => alert('Redirecionando para documentação...')}
        />
      </div>
    </div>
  );
}
