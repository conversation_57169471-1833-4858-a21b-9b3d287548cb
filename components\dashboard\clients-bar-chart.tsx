"use client"

import { TrendingUp } from "lucide-react"
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Axi<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"
import { useClients } from "@/hooks/api/use-clients"
import { useAnalytics } from "@/hooks/use-analytics"

const chartConfig = {
  visitors: {
    label: "Clientes",
  },
  ativos: {
    label: "Ativos",
    color: "hsl(var(--chart-1))",
  },
  inativos: {
    label: "Inativos",
    color: "hsl(var(--chart-2))",
  },
  novos: {
    label: "Novos",
    color: "hsl(var(--chart-3))",
  },
  pessoa_juridica: {
    label: "Pessoa Jurídica",
    color: "hsl(var(--chart-4))",
  },
  pessoa_fisica: {
    label: "Pessoa Física",
    color: "hsl(var(--chart-5))",
  },
} satisfies ChartConfig

export function ClientsBarChart() {
  const { data: clients } = useClients()
  const { data: analytics } = useAnalytics()

  // Processar dados dos clientes
  const chartData = [
    {
      category: "ativos",
      visitors: clients?.filter(c => c.status === 'ativo').length || 0,
      fill: "var(--color-ativos)"
    },
    {
      category: "inativos",
      visitors: clients?.filter(c => c.status === 'inativo').length || 0,
      fill: "var(--color-inativos)"
    },
    {
      category: "novos",
      visitors: clients?.filter(c => {
        const createdDate = new Date(c.$createdAt)
        const thirtyDaysAgo = new Date()
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
        return createdDate >= thirtyDaysAgo
      }).length || 0,
      fill: "var(--color-novos)"
    },
    {
      category: "pessoa_juridica",
      visitors: clients?.filter(c => c.type === 'pessoa_juridica').length || 0,
      fill: "var(--color-premium)"
    },
    {
      category: "pessoa_fisica",
      visitors: clients?.filter(c => c.type === 'pessoa_fisica').length || 0,
      fill: "var(--color-basico)"
    },
  ]

  const totalClients = analytics?.totalClients || 0
  const growthPercentage = analytics?.clientGrowth || 0

  return (
    <Card>
      <CardHeader>
        <CardTitle>Distribuição de Clientes</CardTitle>
        <CardDescription>Categorização por status e tipo</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig}>
          <BarChart
            accessibilityLayer
            data={chartData}
            layout="vertical"
            margin={{
              left: 0,
            }}
          >
            <YAxis
              dataKey="category"
              type="category"
              tickLine={false}
              tickMargin={10}
              axisLine={false}
              tickFormatter={(value) =>
                chartConfig[value as keyof typeof chartConfig]?.label
              }
            />
            <XAxis dataKey="visitors" type="number" hide />
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent hideLabel />}
            />
            <Bar dataKey="visitors" radius={5} />
          </BarChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col items-start gap-2 text-sm">
        <div className="flex gap-2 leading-none font-medium">
          {growthPercentage >= 0 ? (
            <>
              Crescimento de {growthPercentage}% este mês <TrendingUp className="h-4 w-4" />
            </>
          ) : (
            <>
              Redução de {Math.abs(growthPercentage)}% este mês
            </>
          )}
        </div>
        <div className="text-muted-foreground leading-none">
          Total de {totalClients} clientes cadastrados
        </div>
      </CardFooter>
    </Card>
  )
}
