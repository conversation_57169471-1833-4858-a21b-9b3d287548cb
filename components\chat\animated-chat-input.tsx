import * as React from "react";
import { Send, Paperclip, Smile, Loader2 } from "lucide-react";
import { But<PERSON> } from "../ui/button";
import { Textarea } from "../ui/textarea";
import { cn } from "../../lib/utils";

interface AnimatedChatInputProps {
  value: string;
  onChange: (value: string) => void;
  onSend: () => void;
  onKeyDown?: (e: React.KeyboardEvent) => void;
  placeholder?: string;
  disabled?: boolean;
  isLoading?: boolean;
  showFileUpload?: boolean;
  showEmojiPicker?: boolean;
  onFileUpload?: (fileData: { fileId: string; url: string; fileName: string; fileType: string; fileSize: number; }) => void;
  onEmojiSelect?: (emoji: string) => void;
  className?: string;
}

export const AnimatedChatInput = React.forwardRef<
  HTMLTextAreaElement,
  AnimatedChatInputProps
>(({
  value,
  onChange,
  onSend,
  onKeyDown,
  placeholder = "Digite sua mensagem...",
  disabled = false,
  isLoading = false,
  showFileUpload = true,
  showEmojiPicker = true,
  onFileUpload,
  onEmojiSelect,
  className,
  ...props
}, ref) => {
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  // Auto-resize textarea - simplificado
  const textareaRef = React.useRef<HTMLTextAreaElement>(null);
  React.useImperativeHandle(ref, () => textareaRef.current!);

  React.useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
    }
  }, [value]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (value.trim() && !disabled && !isLoading) {
        onSend();
      }
    }
    onKeyDown?.(e);
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && onFileUpload) {
      // Create a mock file data object for now
      // In a real implementation, you would upload the file first
      const fileData = {
        fileId: `temp-${Date.now()}`,
        url: URL.createObjectURL(file),
        fileName: file.name,
        fileType: file.type,
        fileSize: file.size,
      };
      onFileUpload(fileData);
    }
    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const canSend = value.trim() && !disabled && !isLoading;

  return (
    <div className={cn(
      "relative bg-background border-t border-border/50",
      className
    )}>
      {/* Input Container */}
      <div className="p-4">
        <div className={cn(
          "flex items-end gap-3 p-3 rounded-2xl border bg-card/50",
          "transition-all duration-200 hover:border-ring/50",
          disabled && "opacity-50"
        )}>
          {/* File Upload Button */}
          {showFileUpload && (
            <div>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-9 w-9 rounded-full shrink-0"
                disabled={disabled}
                onClick={() => fileInputRef.current?.click()}
              >
                <Paperclip className="h-4 w-4" />
              </Button>
              <input
                ref={fileInputRef}
                type="file"
                className="hidden"
                onChange={handleFileSelect}
                accept="image/*,video/*,.pdf,.doc,.docx,.txt"
              />
            </div>
          )}

          {/* Text Input */}
          <div className="flex-1 min-w-0">
            <Textarea
              ref={textareaRef}
              value={value}
              onChange={(e) => onChange(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              disabled={disabled}
              className={cn(
                "min-h-[44px] max-h-[120px] resize-none border-0 bg-transparent",
                "focus-visible:ring-0 focus-visible:ring-offset-0",
                "placeholder:text-muted-foreground/60",
                "text-sm leading-relaxed"
              )}
              style={{ height: 'auto' }}
              {...props}
            />
          </div>

          {/* Emoji Picker Button */}
          {showEmojiPicker && (
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="h-9 w-9 rounded-full shrink-0"
              disabled={disabled}
              onClick={() => onEmojiSelect?.('😊')} // Simplified for now
            >
              <Smile className="h-4 w-4" />
            </Button>
          )}

          {/* Send Button */}
          <Button
            type="button"
            size="icon"
            className={cn(
              "h-9 w-9 rounded-full shrink-0 transition-all duration-200",
              canSend
                ? "bg-primary hover:bg-primary/90 text-primary-foreground shadow-md"
                : "bg-muted text-muted-foreground"
            )}
            disabled={!canSend}
            onClick={onSend}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>
    </div>
  );
});

AnimatedChatInput.displayName = "AnimatedChatInput";
