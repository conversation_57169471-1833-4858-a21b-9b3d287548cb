import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '../ui/alert-dialog';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { Badge } from '../ui/badge';
import { Crown, Shield, User, Mail } from 'lucide-react';
import { useRemoveTeamMember } from '../../hooks/use-api';
import type { TeamMembership } from '@/schemas/teams';

interface TeamMemberRemoveModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  teamId: string;
  member: TeamMembership | null;
}

const getRoleIcon = (roles: string[]) => {
  if (roles.includes('owner')) return <Crown className="h-4 w-4 text-yellow-500" />;
  if (roles.includes('admin')) return <Shield className="h-4 w-4 text-blue-500" />;
  return <User className="h-4 w-4 text-gray-500" />;
};

const getRoleLabel = (roles: string[]) => {
  if (roles.includes('owner')) return 'Proprietário';
  if (roles.includes('admin')) return 'Administrador';
  return 'Membro';
};

export function TeamMemberRemoveModal({ 
  open, 
  onOpenChange, 
  teamId, 
  member 
}: TeamMemberRemoveModalProps) {
  const removeMemberMutation = useRemoveTeamMember();

  const handleRemove = async () => {
    if (!member || !teamId) return;

    try {
      await removeMemberMutation.mutateAsync({
        teamId,
        membershipId: member.$id,
      });
      onOpenChange(false);
    } catch (error) {
      console.error('Error removing member:', error);
    }
  };

  if (!member) return null;

  const initials = member.userName
    ? member.userName.split(' ').map(n => n[0]).join('').toUpperCase()
    : member.userEmail.substring(0, 2).toUpperCase();

  const roles = member.roles || [];
  const isOwner = roles.includes('owner');

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Remover Membro do Time</AlertDialogTitle>
          <AlertDialogDescription asChild>
            <div className="space-y-4">
              <p>
                Tem certeza que deseja remover este membro do time? Esta ação não pode ser desfeita.
              </p>

              {/* Member Info */}
              <div className="flex items-center space-x-3 p-4 bg-muted/50 rounded-lg">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={undefined} />
                  <AvatarFallback className="text-sm">
                    {initials}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="font-medium">{member.userName || 'Usuário'}</div>
                  <div className="text-sm text-muted-foreground flex items-center gap-1">
                    <Mail className="h-3 w-3" />
                    {member.userEmail}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {getRoleIcon(roles)}
                  <Badge variant="outline">
                    {getRoleLabel(roles)}
                  </Badge>
                </div>
              </div>

              {/* Warning for owner */}
              {isOwner && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-center gap-2 text-red-800">
                    <Crown className="h-4 w-4" />
                    <span className="font-medium text-sm">Atenção!</span>
                  </div>
                  <p className="text-xs text-red-700 mt-1">
                    Este membro é o proprietário do time. Removê-lo pode afetar o controle e 
                    gerenciamento do time. Considere transferir a propriedade antes de remover.
                  </p>
                </div>
              )}

              {/* Consequences */}
              <div className="text-sm text-muted-foreground">
                <p className="font-medium mb-2">Consequências da remoção:</p>
                <ul className="list-disc list-inside space-y-1 text-xs">
                  <li>O membro perderá acesso a todos os recursos do time</li>
                  <li>Histórico de participação será mantido</li>
                  <li>O membro pode ser convidado novamente no futuro</li>
                  {isOwner && (
                    <li className="text-red-600">
                      Como proprietário, a remoção pode afetar o time
                    </li>
                  )}
                </ul>
              </div>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancelar</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleRemove}
            disabled={removeMemberMutation.isPending}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {removeMemberMutation.isPending ? 'Removendo...' : 'Remover Membro'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
