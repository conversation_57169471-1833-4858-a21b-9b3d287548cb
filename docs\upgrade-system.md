# 🚀 Sistema de Upgrade Padronizado

Este documento explica o sistema de upgrade padronizado que garante consistência visual e funcional em todas as seções de aprimoramento/upgrade da aplicação.

## 📋 Visão Geral

O sistema de upgrade foi padronizado para resolver problemas de:
- **Inconsistência visual** entre diferentes seções
- **Cores hardcoded** que não respeitam temas dark/light
- **Duplicação de código** para funcionalidades similares
- **Falta de responsividade** em diferentes dispositivos

## 🎨 Componentes Disponíveis

### 1. UpgradeAlert (Componente Principal)

Componente completo e customizável para alertas de upgrade:

```tsx
import { UpgradeAlert } from '@/components/upgrade-alert';

<UpgradeAlert
  title="Funcionalidade Premium"
  description="Esta funcionalidade requer um plano pago."
  requiredPlan="pro"
  benefits={[
    'Benefício 1',
    'Benefício 2',
    'Benefício 3'
  ]}
  buttonText="Fazer Upgrade"
  buttonSize="default"
/>
```

**Props disponíveis:**
- `title` - Título do alerta
- `description` - Descrição da limitação
- `requiredPlan` - Plano necessário ('pro', 'enterprise')
- `benefits` - Lista de benefícios do upgrade
- `variant` - Tipo visual ('upgrade', 'warning')
- `showButton` - Mostrar/ocultar botão
- `buttonSize` - Tamanho do botão ('sm', 'default')
- `buttonText` - Texto customizado do botão
- `onButtonClick` - Callback customizado
- `children` - Conteúdo adicional
- `className` - Classes CSS adicionais

### 2. SimpleUpgradeAlert

Versão simplificada para casos básicos:

```tsx
import { SimpleUpgradeAlert } from '@/components/upgrade-alert';

<SimpleUpgradeAlert
  feature="Analytics avançado"
  requiredPlan="pro"
/>
```

### 3. PlanLimitationAlert

Para alertas de limitação de plano (vermelho):

```tsx
import { PlanLimitationAlert } from '@/components/upgrade-alert';

<PlanLimitationAlert
  feature="criação de times"
  requiredPlan="pro"
  reason="Você atingiu o limite do plano atual."
/>
```

## 🎯 Variantes de Alert

O sistema estende o componente `Alert` com novas variantes:

### upgrade (Tema Primary)
- Usado para funcionalidades premium
- Cores: primary/5, primary/20 (adaptáveis ao tema)
- Ícone: Crown

### warning (Vermelho)
- Usado para limitações e suspensões
- Cores: red-50/red-200 (light), red-950/red-800 (dark)
- Ícone: AlertTriangle

## 🌙 Suporte a Temas

Todas as cores são baseadas em variáveis CSS que se adaptam automaticamente:

```css
/* Adaptável a qualquer tema */
.upgrade {
  border-color: hsl(var(--primary) / 0.2);
  background-color: hsl(var(--primary) / 0.05);
  color: hsl(var(--primary));
}

/* Cores se adaptam automaticamente ao tema ativo */
/* Funciona com temas claro, escuro, ou personalizados */
```

## 🔄 Migração de Código Existente

### Antes (Inconsistente)
```tsx
// Cores hardcoded, sem suporte a dark mode
<div className="bg-gradient-to-br from-purple-100 to-blue-100">
  <LockIcon className="h-8 w-8 text-purple-600" />
  <h3 className="text-gray-900">Funcionalidade Premium</h3>
  <Button className="bg-gradient-to-r from-purple-600 to-blue-600">
    Upgrade
  </Button>
</div>
```

### Depois (Padronizado)
```tsx
// Usa sistema de temas, responsivo, consistente
<UpgradeAlert
  title="Funcionalidade Premium"
  description="Descrição da limitação"
  requiredPlan="enterprise"
  benefits={['Benefício 1', 'Benefício 2']}
/>
```

## 📱 Responsividade

Os componentes são totalmente responsivos:
- **Mobile**: Layout vertical, botão full-width
- **Desktop**: Layout horizontal, botão compacto
- **Texto**: Quebra automática, tamanhos adaptativos

## 🔧 Integração com Sistema de Pagamentos

Os componentes se integram automaticamente com:
- **useUpgradePlan** - Para upgrades diretos
- **useManageSubscription** - Para gerenciar assinaturas existentes
- **PaymentUtils** - Para verificar configuração de pagamentos

## 📍 Locais Atualizados

O sistema foi implementado em:
- ✅ `app/routes/_dashboard.activities.tsx`
- ✅ `app/components/dashboard-activities-panel.tsx`
- ✅ `app/routes/_dashboard.teams.tsx`
- ✅ `app/components/ui/alert.tsx` (novas variantes)

## 🎯 Benefícios

1. **Consistência Visual**: Todas as seções usam as mesmas cores e estilos
2. **Suporte a Temas**: Funciona perfeitamente em light/dark mode
3. **Reutilização**: Menos código duplicado
4. **Manutenibilidade**: Mudanças centralizadas
5. **Acessibilidade**: Componentes acessíveis por padrão
6. **Type Safety**: Totalmente tipado com TypeScript

## 🚀 Próximos Passos

Para usar o sistema em novos componentes:

1. Importe o componente apropriado
2. Configure as props necessárias
3. Teste em light/dark mode
4. Verifique responsividade

O sistema garante que todas as seções de upgrade mantenham consistência visual e funcional em toda a aplicação.
