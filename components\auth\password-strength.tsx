/**
 * Componente para mostrar a força da senha em tempo real
 */

import { Check, X } from 'lucide-react';
import { validatePasswordStrength } from '../../schemas/auth';
import { cn } from '../../lib/utils';

interface PasswordStrengthProps {
  password: string;
  className?: string;
}

export function PasswordStrength({ password, className }: PasswordStrengthProps) {
  const { checks, strength, isValid } = validatePasswordStrength(password);

  if (!password) return null;

  const strengthColors: Record<string, string> = {
    weak: 'bg-red-500',
    fair: 'bg-orange-500',
    good: 'bg-yellow-500',
    strong: 'bg-green-500',
  };

  const strengthLabels: Record<string, string> = {
    weak: 'Fraca',
    fair: 'Regular',
    good: 'Boa',
    strong: 'Forte',
  };

  const requirements = [
    { key: 'length', label: 'Pelo menos 8 caracteres', met: checks.length },
    { key: 'lowercase', label: 'Uma letra minúscula', met: checks.lowercase },
    { key: 'uppercase', label: 'Uma letra maiúscula', met: checks.uppercase },
    { key: 'number', label: 'Um número', met: checks.number },
  ];

  return (
    <div className={cn('space-y-3', className)}>
      {/* Barra de força da senha */}
      <div className="space-y-2">
        <div className="flex items-center justify-between text-sm">
          <span className="text-muted-foreground">Força da senha:</span>
          <span className={cn(
            'font-medium',
            isValid ? 'text-green-600' : 'text-orange-600'
          )}>
            {strengthLabels[strength]}
          </span>
        </div>

        <div className="flex space-x-1">
          {[1, 2, 3, 4].map((level) => (
            <div
              key={level}
              className={cn(
                'h-2 flex-1 rounded-full transition-colors',
                level <= Object.values(checks).filter(Boolean).length
                  ? strengthColors[strength]
                  : 'bg-muted'
              )}
            />
          ))}
        </div>
      </div>

      {/* Lista de requisitos */}
      <div className="space-y-2">
        <p className="text-sm text-muted-foreground">Sua senha deve conter:</p>
        <ul className="space-y-1">
          {requirements.map((req) => (
            <li
              key={req.key}
              className={cn(
                'flex items-center text-sm transition-colors',
                req.met ? 'text-green-600' : 'text-muted-foreground'
              )}
            >
              {req.met ? (
                <Check className="mr-2 h-3 w-3" />
              ) : (
                <X className="mr-2 h-3 w-3" />
              )}
              {req.label}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}
