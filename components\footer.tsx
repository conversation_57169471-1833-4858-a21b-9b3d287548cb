import { Logo } from './logo'
import Link from 'next/link'
import { Button } from './ui/button'
import { Input } from './ui/input'
import { Badge } from './ui/badge'
import { Mail, Github, Twitter, Linkedin, Youtube, Heart } from 'lucide-react'

const footerSections = {
    product: {
        title: 'Produto',
        links: [
            { title: 'Recursos', href: '#features' },
            { title: 'Preços', href: '#pricing' },
            { title: 'Demo', href: '/login' },
            { title: 'Changelog', href: '/changelog' },
        ]
    },
    company: {
        title: 'Empresa',
        links: [
            { title: 'Sobre', href: '/about' },
            { title: 'Blog', href: '/blog' },
            { title: 'Carreiras', href: '/careers' },
            { title: 'Contato', href: '/contact' },
        ]
    },
    support: {
        title: 'Suporte',
        links: [
            { title: 'Documentação', href: '/docs' },
            { title: '<PERSON><PERSON><PERSON>', href: '/help' },
            { title: 'Status', href: 'https://status.template.com' },
            { title: 'Comunidade', href: 'https://discord.gg/template' },
        ]
    },
    legal: {
        title: 'Legal',
        links: [
            { title: 'Privacidade', href: '/privacy' },
            { title: 'Termos', href: '/terms' },
            { title: 'Cookies', href: '/cookies' },
            { title: 'LGPD', href: '/lgpd' },
        ]
    }
}

const socialLinks = [
    { icon: Github, href: 'https://github.com/template', label: 'GitHub' },
    { icon: Twitter, href: 'https://twitter.com/template', label: 'Twitter' },
    { icon: Linkedin, href: 'https://linkedin.com/company/template', label: 'LinkedIn' },
    { icon: Youtube, href: 'https://youtube.com/@template', label: 'YouTube' },
]

export default function FooterSection() {
    return (
        <footer className="bg-muted/30 border-t">
            <div className="container mx-auto px-4 max-w-7xl">
                {/* Newsletter Section */}
                <div className="py-12 border-b border-border/50">
                    <div className="max-w-2xl mx-auto text-center">
                        <h3 className="text-2xl font-bold mb-4">
                            Fique por dentro das novidades
                        </h3>
                        <p className="text-muted-foreground mb-6">
                            Receba atualizações sobre novas funcionalidades, dicas de desenvolvimento e ofertas especiais.
                        </p>
                        <div className="flex flex-col sm:flex-row gap-3 max-w-md mx-auto">
                            <Input
                                type="email"
                                placeholder="Seu melhor email"
                                className="flex-1"
                            />
                            <Button className="sm:w-auto">
                                <Mail className="w-4 h-4 mr-2" />
                                Inscrever
                            </Button>
                        </div>
                        <p className="text-xs text-muted-foreground mt-3">
                            Sem spam. Cancele quando quiser.
                        </p>
                    </div>
                </div>

                {/* Main Footer Content */}
                <div className="py-12">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
                        {/* Brand Section */}
                        <div className="lg:col-span-2">
                            <Link href="/" className="flex items-center gap-2 mb-4">
                                <Logo />
                            </Link>
                            <p className="text-muted-foreground mb-6 max-w-sm">
                                Template completo para SaaS que acelera o desenvolvimento do seu produto.
                                Economize meses de trabalho com nossa solução pronta para produção.
                            </p>
                            <div className="flex gap-3">
                                {socialLinks.map((social, index) => {
                                    const Icon = social.icon;
                                    return (
                                        <a
                                            key={index}
                                            href={social.href}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            aria-label={social.label}
                                            className="w-10 h-10 bg-background border rounded-lg flex items-center justify-center text-muted-foreground hover:text-primary hover:border-primary transition-colors"
                                        >
                                            <Icon className="w-4 h-4" />
                                        </a>
                                    );
                                })}
                            </div>
                        </div>

                        {/* Footer Links */}
                        {Object.entries(footerSections).map(([key, section]) => (
                            <div key={key}>
                                <h4 className="font-semibold mb-4">{section.title}</h4>
                                <ul className="space-y-3">
                                    {section.links.map((link, index) => {
                                        const isExternal = link.href.startsWith('http') || link.href.startsWith('mailto');
                                        const isAnchor = link.href.startsWith('#');

                                        if (isExternal) {
                                            return (
                                                <li key={index}>
                                                    <a
                                                        href={link.href}
                                                        target="_blank"
                                                        rel="noopener noreferrer"
                                                        className="text-muted-foreground hover:text-primary transition-colors text-sm"
                                                    >
                                                        {link.title}
                                                    </a>
                                                </li>
                                            );
                                        }

                                        if (isAnchor) {
                                            return (
                                                <li key={index}>
                                                    <a
                                                        href={link.href}
                                                        className="text-muted-foreground hover:text-primary transition-colors text-sm"
                                                    >
                                                        {link.title}
                                                    </a>
                                                </li>
                                            );
                                        }

                                        return (
                                            <li key={index}>
                                                <Link
                                                    href={link.href}
                                                    className="text-muted-foreground hover:text-primary transition-colors text-sm"
                                                >
                                                    {link.title}
                                                </Link>
                                            </li>
                                        );
                                    })}
                                </ul>
                            </div>
                        ))}
                    </div>
                </div>
                {/* Bottom Section */}
                <div className="py-6 border-t border-border/50">
                    <div className="flex flex-col md:flex-row justify-between items-center gap-4">
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <span>© {new Date().getFullYear()} Template SaaS. Todos os direitos reservados.</span>
                        </div>

                        <div className="flex items-center gap-4">
                            <Badge variant="outline" className="text-xs">
                                <Heart className="w-3 h-3 mr-1 text-red-500" />
                                Feito no Brasil
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                                v2.0.0
                            </Badge>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    )
}
