import { useState } from 'react';
import { CircleUserRoundIcon, XIcon, Upload, Loader2 } from "lucide-react";
import { useFileUpload } from "../../hooks/use-file-upload";
import { Button } from "../ui/button";
import { toast } from 'sonner';
import { optimizeImage, isValidImageFile, formatFileSize } from "../../lib/image-optimizer";

interface AvatarUploadProps {
  value?: File | string; // Pode ser File (novo) ou string (URL existente)
  onChange: (file: File | string) => void;
  disabled?: boolean;
}

export function AvatarUpload({ value, onChange, disabled }: AvatarUploadProps) {
  const [isOptimizing, setIsOptimizing] = useState(false);

  const [{ files }, { removeFile, openFileDialog, getInputProps }] = useFileUpload({
    accept: "image/*",
    maxSize: 10 * 1024 * 1024, // 10MB (antes da otimização)
    onFilesAdded: async (addedFiles) => {
      if (addedFiles.length > 0 && !disabled) {
        await handleFileSelect(addedFiles[0].file as File);
      }
    },
  });

  const handleFileSelect = async (file: File) => {
    if (disabled) return;

    // Validar se é uma imagem
    if (!isValidImageFile(file)) {
      toast.error('Tipo de arquivo não suportado. Use JPEG, PNG, GIF ou WebP.');
      return;
    }

    setIsOptimizing(true);
    try {
      // Otimizar imagem para WebP com qualidade máxima
      const optimizedFile = await optimizeImage(file, {
        maxWidth: 400,
        maxHeight: 400,
        quality: 1.0, // 100% de qualidade para preservar máxima qualidade da imagem
        format: 'webp'
      });

      // Atualizar o valor no formulário com o arquivo otimizado
      onChange(optimizedFile);

      toast.success(`Imagem otimizada! Tamanho reduzido de ${formatFileSize(file.size)} para ${formatFileSize(optimizedFile.size)}`);
    } catch (error) {
      console.error('Erro ao otimizar imagem:', error);
      toast.error('Erro ao processar imagem');
    } finally {
      setIsOptimizing(false);
    }
  };

  const handleRemove = () => {
    if (disabled) return;

    removeFile(files[0]?.id);
    onChange('');
  };

  // Determinar URL de preview
  const getPreviewUrl = () => {
    // Se há arquivo local, usar preview
    if (files[0]?.preview) {
      return files[0].preview;
    }

    // Se value é File, criar URL temporária
    if (value instanceof File) {
      return URL.createObjectURL(value);
    }

    // Se value é string (URL), usar diretamente
    if (typeof value === 'string' && value) {
      return value;
    }

    return null;
  };

  const previewUrl = getPreviewUrl();
  const fileName = files[0]?.file.name || (value instanceof File ? value.name : null);
  const showRemoveButton = previewUrl && !isOptimizing;

  return (
    <div className="flex flex-col items-center gap-2">
      <div className="relative inline-flex">
        <Button
          type="button"
          variant="outline"
          className="relative size-20 overflow-hidden p-0 shadow-none"
          onClick={openFileDialog}
          disabled={disabled || isOptimizing}
          aria-label={previewUrl ? "Alterar avatar" : "Selecionar avatar"}
        >
          {isOptimizing ? (
            <div className="flex items-center justify-center">
              <Loader2 className="size-6 animate-spin opacity-60" />
            </div>
          ) : previewUrl ? (
            <img
              className="size-full object-cover"
              src={previewUrl}
              alt="Preview do avatar"
              width={80}
              height={80}
              style={{ objectFit: "cover" }}
            />
          ) : (
            <div className="flex flex-col items-center gap-1" aria-hidden="true">
              <CircleUserRoundIcon className="size-6 opacity-60" />
              <Upload className="size-3 opacity-40" />
            </div>
          )}
        </Button>

        {showRemoveButton && (
          <Button
            type="button"
            onClick={handleRemove}
            size="icon"
            disabled={disabled}
            className="border-background focus-visible:border-background absolute -top-2 -right-2 size-6 rounded-full border-2 shadow-none"
            aria-label="Remover avatar"
          >
            <XIcon className="size-3.5" />
          </Button>
        )}

        <input
          {...getInputProps()}
          className="sr-only"
          aria-label="Upload de avatar"
          tabIndex={-1}
        />
      </div>

      {fileName && (
        <p className="text-muted-foreground text-xs max-w-[120px] truncate">
          {fileName}
        </p>
      )}

      <p className="text-muted-foreground text-xs text-center">
        {isOptimizing ? 'Otimizando imagem...' : 'Clique para selecionar avatar'}
        <br />
        <span className="text-[10px] opacity-70">
          JPEG, PNG, GIF (máx. 10MB) • Otimizado para WebP
        </span>
      </p>
    </div>
  );
}
