/**
 * Activity Filters Component
 * Provides filtering and search functionality for activities
 */

import React, { useState } from 'react';
import { CalendarIcon, FilterIcon, SearchIcon, XIcon } from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Badge } from '../ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '../ui/popover';
import {
  Calendar,
} from '../ui/calendar';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import type { ActivityFilters, ActivityType, ActivityAction, ActivityPriority } from '@/schemas/activities';

// ============================================================================
// FILTER OPTIONS
// ============================================================================

const ACTIVITY_TYPES: { value: ActivityType; label: string }[] = [
  { value: 'auth', label: 'Autenticação' },
  { value: 'client', label: 'Cliente' },
  { value: 'team', label: 'Equipe' },
  { value: 'chat', label: 'Chat' },
  { value: 'file', label: 'Arquivo' },
  { value: 'system', label: 'Sistema' },
  { value: 'admin', label: 'Admin' },
  { value: 'notification', label: 'Notificação' },
  { value: 'preference', label: 'Preferência' },
  { value: 'calendar', label: 'Calendário' },
  { value: 'document', label: 'Documento' },
];

const ACTIVITY_ACTIONS: { value: ActivityAction; label: string }[] = [
  { value: 'create', label: 'Criar' },
  { value: 'update', label: 'Atualizar' },
  { value: 'delete', label: 'Excluir' },
  { value: 'view', label: 'Visualizar' },
  { value: 'login', label: 'Login' },
  { value: 'logout', label: 'Logout' },
  { value: 'invite', label: 'Convidar' },
  { value: 'join', label: 'Entrar' },
  { value: 'leave', label: 'Sair' },
  { value: 'upload', label: 'Enviar' },
  { value: 'download', label: 'Baixar' },
  { value: 'share', label: 'Compartilhar' },
  { value: 'archive', label: 'Arquivar' },
  { value: 'restore', label: 'Restaurar' },
  { value: 'send', label: 'Enviar' },
  { value: 'receive', label: 'Receber' },
  { value: 'approve', label: 'Aprovar' },
  { value: 'reject', label: 'Rejeitar' },
  { value: 'export', label: 'Exportar' },
  { value: 'import', label: 'Importar' },
];

const PRIORITY_OPTIONS = [
  { value: 'low', label: 'Baixa' },
  { value: 'normal', label: 'Normal' },
  { value: 'high', label: 'Alta' },
  { value: 'critical', label: 'Crítica' },
];

// ============================================================================
// COMPONENTS
// ============================================================================

interface ActivityFiltersProps {
  filters: ActivityFilters;
  onFiltersChange: (filters: ActivityFilters) => void;
  onClearFilters: () => void;
  isLoading?: boolean;
}

export function ActivityFilters({
  filters,
  onFiltersChange,
  onClearFilters,
  isLoading = false
}: ActivityFiltersProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [dateFrom, setDateFrom] = useState<Date | undefined>(
    filters.dateFrom ? new Date(filters.dateFrom) : undefined
  );
  const [dateTo, setDateTo] = useState<Date | undefined>(
    filters.dateTo ? new Date(filters.dateTo) : undefined
  );

  const hasActiveFilters = Object.keys(filters).some(key => {
    const value = filters[key as keyof ActivityFilters];
    return value !== undefined && value !== '' && (Array.isArray(value) ? value.length > 0 : true);
  });

  const handleSearchChange = (value: string) => {
    onFiltersChange({ ...filters, search: value || undefined });
  };

  const handleTypeChange = (value: string) => {
    onFiltersChange({
      ...filters,
      type: value === 'all' ? undefined : value as ActivityType
    });
  };

  const handleActionChange = (value: string) => {
    onFiltersChange({
      ...filters,
      action: value === 'all' ? undefined : value as ActivityAction
    });
  };

  const handlePriorityChange = (value: string) => {
    onFiltersChange({
      ...filters,
      priority: value === 'all' ? undefined : value as ActivityPriority
    });
  };

  const handleDateFromChange = (date: Date | undefined) => {
    setDateFrom(date);
    onFiltersChange({
      ...filters,
      dateFrom: date ? date.toISOString() : undefined
    });
  };

  const handleDateToChange = (date: Date | undefined) => {
    setDateTo(date);
    onFiltersChange({
      ...filters,
      dateTo: date ? date.toISOString() : undefined
    });
  };

  return (
    <div className="space-y-4">
      {/* Search Bar */}
      <div className="relative">
        <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <Input
          placeholder="Buscar atividades..."
          value={filters.search || ''}
          onChange={(e) => handleSearchChange(e.target.value)}
          className="pl-10"
          disabled={isLoading}
        />
      </div>

      {/* Filter Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Popover open={isOpen} onOpenChange={setIsOpen}>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm">
                <FilterIcon className="h-4 w-4 mr-2" />
                Filtros
                {hasActiveFilters && (
                  <Badge variant="secondary" className="ml-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs">
                    !
                  </Badge>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80" align="start">
              <Card className="border-0 shadow-none">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm">Filtros de Atividade</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Type Filter */}
                  <div className="space-y-2">
                    <Label className="text-xs font-medium">Tipo</Label>
                    <Select
                      value={Array.isArray(filters.type) ? 'all' : (filters.type || 'all')}
                      onValueChange={handleTypeChange}
                    >
                      <SelectTrigger className="h-8">
                        <SelectValue>
                          {Array.isArray(filters.type) || !filters.type ? 'Tipo: Todos' :
                           `Tipo: ${ACTIVITY_TYPES.find(t => t.value === filters.type)?.label || 'Todos'}`}
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Tipo: Todos</SelectItem>
                        {ACTIVITY_TYPES.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            Tipo: {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Action Filter */}
                  <div className="space-y-2">
                    <Label className="text-xs font-medium">Ação</Label>
                    <Select
                      value={Array.isArray(filters.action) ? 'all' : (filters.action || 'all')}
                      onValueChange={handleActionChange}
                    >
                      <SelectTrigger className="h-8">
                        <SelectValue>
                          {Array.isArray(filters.action) || !filters.action ? 'Ação: Todas' :
                           `Ação: ${ACTIVITY_ACTIONS.find(a => a.value === filters.action)?.label || 'Todas'}`}
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Ação: Todas</SelectItem>
                        {ACTIVITY_ACTIONS.map((action) => (
                          <SelectItem key={action.value} value={action.value}>
                            Ação: {action.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Priority Filter */}
                  <div className="space-y-2">
                    <Label className="text-xs font-medium">Prioridade</Label>
                    <Select
                      value={filters.priority || 'all'}
                      onValueChange={handlePriorityChange}
                    >
                      <SelectTrigger className="h-8">
                        <SelectValue>
                          {!filters.priority ? 'Prioridade: Todas' :
                           `Prioridade: ${PRIORITY_OPTIONS.find(p => p.value === filters.priority)?.label || 'Todas'}`}
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Prioridade: Todas</SelectItem>
                        {PRIORITY_OPTIONS.map((priority) => (
                          <SelectItem key={priority.value} value={priority.value}>
                            Prioridade: {priority.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Date Range */}
                  <div className="space-y-2">
                    <Label className="text-xs font-medium">Período</Label>
                    <div className="grid grid-cols-2 gap-2">
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-8 justify-start text-left font-normal"
                          >
                            <CalendarIcon className="mr-2 h-3 w-3" />
                            {dateFrom ? format(dateFrom, 'dd/MM', { locale: ptBR }) : 'De'}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={dateFrom}
                            onSelect={handleDateFromChange}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>

                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-8 justify-start text-left font-normal"
                          >
                            <CalendarIcon className="mr-2 h-3 w-3" />
                            {dateTo ? format(dateTo, 'dd/MM', { locale: ptBR }) : 'Até'}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={dateTo}
                            onSelect={handleDateToChange}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>

                  {/* Clear Filters */}
                  {hasActiveFilters && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={onClearFilters}
                      className="w-full h-8"
                    >
                      <XIcon className="h-3 w-3 mr-2" />
                      Limpar Filtros
                    </Button>
                  )}
                </CardContent>
              </Card>
            </PopoverContent>
          </Popover>
        </div>

        {/* Active Filters Display */}
        {hasActiveFilters && (
          <div className="flex items-center space-x-2">
            <span className="text-xs text-gray-500">Filtros ativos:</span>
            <div className="flex flex-wrap gap-1">
              {filters.type && (
                <Badge variant="secondary" className="text-xs">
                  {ACTIVITY_TYPES.find(t => t.value === filters.type)?.label}
                </Badge>
              )}
              {filters.action && (
                <Badge variant="secondary" className="text-xs">
                  {ACTIVITY_ACTIONS.find(a => a.value === filters.action)?.label}
                </Badge>
              )}
              {filters.priority && (
                <Badge variant="secondary" className="text-xs">
                  {PRIORITY_OPTIONS.find(p => p.value === filters.priority)?.label}
                </Badge>
              )}
              {(filters.dateFrom || filters.dateTo) && (
                <Badge variant="secondary" className="text-xs">
                  Período
                </Badge>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
