/**
 * Event Calendar Component
 * Componente de calendário simplificado baseado no exemplo
 */

import React, { useState, useMemo } from 'react';
import { format, addMonths, addWeeks, addDays, subMonths, subWeeks, subDays } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import {
  ChevronLeft,
  ChevronRight,
  Calendar as CalendarIcon,
  Plus,
  Grid3X3,
  List,
  Columns,
  Square,
  Menu,
  Search,
  X
} from 'lucide-react';
import { cn } from '../../lib/utils';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import { useIsMobile } from '../../hooks/use-mobile';
import type { Event, CalendarEvent } from '@/schemas/events';
import { MonthView } from './MonthView';
import { WeekView } from './WeekView';
import { DayView } from './DayView';
import { AgendaView } from './AgendaView';
import { EventModal } from './EventModal';

export type CalendarView = 'month' | 'week' | 'day' | 'agenda';

export interface EventCalendarProps {
  events?: Event[];
  onEventAdd?: (event: Event) => void;
  onEventUpdate?: (event: Event) => void;
  onEventDelete?: (eventId: string) => void;
  onEventSelect?: (event: Event) => void;
  className?: string;
  initialView?: CalendarView;
  teamId?: string;
}

const viewIcons = {
  month: Grid3X3,
  week: Columns,
  day: Square,
  agenda: List,
};

const viewLabels = {
  month: 'Mês',
  week: 'Semana',
  day: 'Dia',
  agenda: 'Agenda',
};

export function EventCalendar({
  events = [],
  onEventAdd,
  onEventUpdate,
  onEventDelete,
  onEventSelect,
  className,
  initialView = 'month',
  teamId,
}: EventCalendarProps) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [view, setView] = useState<CalendarView>(initialView);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);

  // Modal state
  const [modalState, setModalState] = useState<{
    isOpen: boolean;
    mode: 'create' | 'edit' | 'view';
    event?: Event | null;
    initialDate?: Date;
    initialTime?: string;
  }>({
    isOpen: false,
    mode: 'create',
    event: null,
  });

  const isMobile = useIsMobile();

  // Navigation handlers
  const handlePrevious = () => {
    if (view === 'month') {
      setCurrentDate(subMonths(currentDate, 1));
    } else if (view === 'week') {
      setCurrentDate(subWeeks(currentDate, 1));
    } else if (view === 'day') {
      setCurrentDate(subDays(currentDate, 1));
    } else if (view === 'agenda') {
      setCurrentDate(subDays(currentDate, 30));
    }
  };

  const handleNext = () => {
    if (view === 'month') {
      setCurrentDate(addMonths(currentDate, 1));
    } else if (view === 'week') {
      setCurrentDate(addWeeks(currentDate, 1));
    } else if (view === 'day') {
      setCurrentDate(addDays(currentDate, 1));
    } else if (view === 'agenda') {
      setCurrentDate(addDays(currentDate, 30));
    }
  };

  const handleToday = () => {
    setCurrentDate(new Date());
  };

  // Filter events based on search query
  const filteredEvents = useMemo(() => {
    if (!searchQuery.trim()) {
      return events;
    }

    const query = searchQuery.toLowerCase().trim();
    return events.filter(event =>
      event.title.toLowerCase().includes(query) ||
      event.description?.toLowerCase().includes(query) ||
      event.location?.toLowerCase().includes(query) ||
      event.category?.toLowerCase().includes(query)
    );
  }, [events, searchQuery]);

  // Format title based on view
  const viewTitle = useMemo(() => {
    switch (view) {
      case 'day':
        return format(currentDate, "EEEE, dd 'de' MMMM 'de' yyyy", { locale: ptBR });
      case 'week':
        // Calculate week range
        const weekStart = new Date(currentDate);
        weekStart.setDate(currentDate.getDate() - currentDate.getDay());
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6);
        return `${format(weekStart, 'dd/MM', { locale: ptBR })} - ${format(weekEnd, 'dd/MM/yyyy', { locale: ptBR })}`;
      case 'month':
        return format(currentDate, "MMMM 'de' yyyy", { locale: ptBR });
      case 'agenda':
        return 'Agenda';
      default:
        return format(currentDate, "MMMM 'de' yyyy", { locale: ptBR });
    }
  }, [currentDate, view]);

  // Clear search
  const clearSearch = () => {
    setSearchQuery('');
    setShowSearch(false);
  };

  // Modal handlers
  const openCreateModal = (date?: Date, time?: string) => {
    setModalState({
      isOpen: true,
      mode: 'create',
      event: null,
      initialDate: date,
      initialTime: time,
    });
  };

  const openViewModal = (event: Event) => {
    setModalState({
      isOpen: true,
      mode: 'view',
      event,
    });
  };

  const openEditModal = (event: Event) => {
    setModalState({
      isOpen: true,
      mode: 'edit',
      event,
    });
  };

  const closeModal = () => {
    setModalState({
      isOpen: false,
      mode: 'create',
      event: null,
    });
  };

  const handleEventSave = async (eventData: Partial<Event>) => {
    try {
      if (modalState.mode === 'create') {
        if (onEventAdd) {
          await onEventAdd(eventData as Event);
        }
      } else if (modalState.mode === 'edit') {
        if (onEventUpdate) {
          await onEventUpdate(eventData as Event);
        }
      }
    } catch (error) {
      console.error('Error saving event:', error);
      throw error;
    }
  };

  const handleEventDelete = async (eventId: string) => {
    try {
      if (onEventDelete) {
        await onEventDelete(eventId);
      }
    } catch (error) {
      console.error('Error deleting event:', error);
      throw error;
    }
  };

  return (
    <div className={cn('flex flex-col h-full bg-background', className)}>
      {/* Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex items-center justify-between p-4">
          {/* Left Section - Navigation */}
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1">
              <Button
                variant="outline"
                size="icon"
                onClick={handlePrevious}
                className="h-8 w-8"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={handleNext}
                className="h-8 w-8"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={handleToday}
              className="hidden sm:inline-flex"
            >
              Hoje
            </Button>

            <div className="hidden md:block">
              <h2 className="text-lg font-semibold">{viewTitle}</h2>
            </div>
          </div>

          {/* Center Section - Mobile Title */}
          {isMobile && (
            <div className="flex-1 text-center">
              <h2 className="text-sm font-semibold truncate">{viewTitle}</h2>
            </div>
          )}

          {/* Right Section - Actions */}
          <div className="flex items-center gap-2">
            {/* Search Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSearch(!showSearch)}
              className={cn(
                "gap-2",
                showSearch && "bg-accent"
              )}
            >
              <Search className="h-4 w-4" />
              <span className="hidden sm:inline">Buscar</span>
            </Button>

            {/* View Selector */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="gap-2">
                  {React.createElement(viewIcons[view], {
                    className: "h-4 w-4"
                  })}
                  <span className="hidden sm:inline">{viewLabels[view]}</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {Object.entries(viewLabels).map(([viewKey, label]) => (
                  <DropdownMenuItem
                    key={viewKey}
                    onClick={() => setView(viewKey as CalendarView)}
                    className="gap-2"
                  >
                    {React.createElement(viewIcons[viewKey as CalendarView], {
                      className: "h-4 w-4"
                    })}
                    {label}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Add Event Button */}
            <Button size="sm" className="gap-2" onClick={() => openCreateModal()}>
              <Plus className="h-4 w-4" />
              <span className="hidden sm:inline">Novo Evento</span>
            </Button>
          </div>
        </div>

        {/* Search Bar */}
        {showSearch && (
          <div className="px-4 pb-4 border-t">
            <div className="relative mt-4">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Buscar eventos por título, descrição, local ou categoria..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-10"
              />
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearSearch}
                  className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>

            {/* Search Results Info */}
            {searchQuery && (
              <div className="mt-2 text-sm text-muted-foreground">
                {filteredEvents.length === 0 ? (
                  <span>Nenhum evento encontrado para "{searchQuery}"</span>
                ) : (
                  <span>
                    {filteredEvents.length} evento{filteredEvents.length !== 1 ? 's' : ''} encontrado{filteredEvents.length !== 1 ? 's' : ''} para "{searchQuery}"
                  </span>
                )}
              </div>
            )}
          </div>
        )}

        {/* Mobile Today Button */}
        {isMobile && !showSearch && (
          <div className="px-4 pb-4">
            <Button
              variant="outline"
              size="sm"
              onClick={handleToday}
              className="w-full"
            >
              Hoje
            </Button>
          </div>
        )}
      </div>

      {/* Calendar Content */}
      <div className="flex-1 p-4">
        <div className="h-full bg-card rounded-lg border overflow-hidden">
          {view === 'month' && (
            <MonthView
              currentDate={currentDate}
              events={filteredEvents}
              onEventSelect={openViewModal}
              onEventCreate={(date) => openCreateModal(date)}
            />
          )}

          {view === 'week' && (
            <WeekView
              currentDate={currentDate}
              events={filteredEvents}
              onEventSelect={openViewModal}
              onEventCreate={(date, hour) => {
                const timeString = `${hour.toString().padStart(2, '0')}:00`;
                openCreateModal(date, timeString);
              }}
            />
          )}

          {view === 'day' && (
            <DayView
              currentDate={currentDate}
              events={filteredEvents}
              onEventSelect={openViewModal}
              onEventCreate={(date, hour) => {
                const timeString = `${hour.toString().padStart(2, '0')}:00`;
                openCreateModal(date, timeString);
              }}
            />
          )}

          {view === 'agenda' && (
            <AgendaView
              currentDate={currentDate}
              events={filteredEvents}
              onEventSelect={openViewModal}
            />
          )}
        </div>
      </div>

      {/* Event Modal */}
      <EventModal
        isOpen={modalState.isOpen}
        onClose={closeModal}
        onSave={handleEventSave}
        onDelete={handleEventDelete}
        onEdit={openEditModal}
        event={modalState.event}
        initialDate={modalState.initialDate}
        initialTime={modalState.initialTime}
        mode={modalState.mode}
      />
    </div>
  );
}
