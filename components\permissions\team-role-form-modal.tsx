/**
 * Modal para criar/editar cargos do time
 */

import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Crown, Users, Palette, Shield, User, Eye } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../ui/form';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Button } from '../ui/button';
import { Switch } from '../ui/switch';
import { RadioGroup, RadioGroupItem } from '../ui/radio-group';
import { Label } from '../ui/label';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { useCreateTeamRole, useUpdateTeamRole } from '../../hooks/api/use-permissions';
import { PermissionSelector } from './permission-selector';
import type { TeamRole, CreateTeamRoleData, UpdateTeamRoleData } from '@/schemas/permissions';

// Schema de validação
const roleFormSchema = z.object({
  name: z.string().min(1, 'Nome é obrigatório').max(50, 'Nome muito longo'),
  description: z.string().max(200, 'Descrição muito longa').optional(),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Cor inválida'),
  userType: z.enum(['owner', 'admin', 'user', 'guest']),
  permissions: z.array(z.object({
    resource: z.enum(['dashboard', 'analytics', 'calendar', 'documents', 'kanban', 'clients', 'teams', 'team_chat', 'activities', 'reports', 'preferences', 'plans', 'help']),
    actions: z.array(z.enum(['view', 'create', 'edit', 'delete', 'manage'])),
  })),
  isDefault: z.boolean(),
});

type RoleFormData = z.infer<typeof roleFormSchema>;

interface TeamRoleFormModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  mode: 'create' | 'edit';
  teamId: string;
  role?: TeamRole | null;
  onClose?: () => void;
}

const PRESET_COLORS = [
  '#dc2626', // red
  '#ea580c', // orange
  '#ca8a04', // yellow
  '#16a34a', // green
  '#0891b2', // cyan
  '#2563eb', // blue
  '#7c3aed', // violet
  '#c026d3', // fuchsia
  '#64748b', // slate
  '#374151', // gray
];

export function TeamRoleFormModal({
  open,
  onOpenChange,
  mode,
  teamId,
  role,
  onClose,
}: TeamRoleFormModalProps) {
  const createRoleMutation = useCreateTeamRole();
  const updateRoleMutation = useUpdateTeamRole();

  const form = useForm<RoleFormData>({
    resolver: zodResolver(roleFormSchema),
    defaultValues: {
      name: '',
      description: '',
      color: '#3b82f6',
      userType: 'user',
      permissions: [],
      isDefault: false,
    },
  });

  // Resetar form quando modal abre/fecha ou role muda
  useEffect(() => {
    if (open && mode === 'edit' && role) {
      form.reset({
        name: role.name,
        description: role.description || '',
        color: role.color || '#3b82f6',
        userType: role.userType,
        permissions: role.permissions,
        isDefault: role.isDefault,
      });
    } else if (open && mode === 'create') {
      form.reset({
        name: '',
        description: '',
        color: '#3b82f6',
        userType: 'user',
        permissions: [],
        isDefault: false,
      });
    }
  }, [open, mode, role, form]);

  const handleSubmit = async (data: RoleFormData) => {
    try {
      if (mode === 'create') {
        const createData: CreateTeamRoleData = {
          name: data.name,
          description: data.description,
          color: data.color,
          userType: data.userType,
          permissions: data.permissions,
          isDefault: data.isDefault,
        };
        await createRoleMutation.mutateAsync(createData);
      } else if (mode === 'edit' && role) {
        const updateData: UpdateTeamRoleData = {
          name: data.name,
          description: data.description,
          color: data.color,
          permissions: data.permissions,
          isDefault: data.isDefault,
        };
        await updateRoleMutation.mutateAsync({ roleId: role.id, data: updateData });
      }

      onOpenChange(false);
      onClose?.();
    } catch (error) {
      // Error já é tratado nos hooks
    }
  };

  const isLoading = createRoleMutation.isPending || updateRoleMutation.isPending;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {mode === 'create' ? 'Criar Novo Cargo' : 'Editar Cargo'}
          </DialogTitle>
          <DialogDescription>
            {mode === 'create'
              ? 'Configure um novo cargo com permissões específicas para sua equipe.'
              : 'Atualize as configurações e permissões do cargo.'
            }
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Informações básicas */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Informações Básicas</h3>

              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nome do Cargo</FormLabel>
                    <FormControl>
                      <Input placeholder="Ex: Gerente, Analista, Desenvolvedor" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Descrição (opcional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Descreva as responsabilidades deste cargo..."
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="color"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Cor do Cargo</FormLabel>
                    <FormControl>
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <Input
                            type="color"
                            className="w-12 h-10 p-1 border rounded"
                            {...field}
                          />
                          <Input
                            placeholder="#3b82f6"
                            className="flex-1"
                            {...field}
                          />
                        </div>
                        <div className="flex flex-wrap gap-2">
                          {PRESET_COLORS.map((color) => (
                            <button
                              key={color}
                              type="button"
                              className="w-8 h-8 rounded border-2 border-transparent hover:border-gray-300"
                              style={{ backgroundColor: color }}
                              onClick={() => field.onChange(color)}
                            />
                          ))}
                        </div>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Separator />

            {/* Tipo de usuário */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Tipo de Usuário</h3>

              <FormField
                control={form.control}
                name="userType"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <RadioGroup
                        value={field.value}
                        onValueChange={field.onChange}
                        className="space-y-3"
                      >
                        <div className="flex items-center space-x-3 p-3 border rounded-lg">
                          <RadioGroupItem value="owner" id="owner" />
                          <Label htmlFor="owner" className="flex items-center gap-2 cursor-pointer">
                            <Crown className="h-4 w-4 text-red-500" />
                            <div>
                              <div className="font-medium">Proprietário</div>
                              <div className="text-sm text-muted-foreground">
                                Acesso total ao sistema e pode alterar preferências do time
                              </div>
                            </div>
                          </Label>
                        </div>
                        <div className="flex items-center space-x-3 p-3 border rounded-lg">
                          <RadioGroupItem value="admin" id="admin" />
                          <Label htmlFor="admin" className="flex items-center gap-2 cursor-pointer">
                            <Shield className="h-4 w-4 text-orange-500" />
                            <div>
                              <div className="font-medium">Administrador</div>
                              <div className="text-sm text-muted-foreground">
                                CRUD completo e pode convidar membros
                              </div>
                            </div>
                          </Label>
                        </div>
                        <div className="flex items-center space-x-3 p-3 border rounded-lg">
                          <RadioGroupItem value="user" id="user" />
                          <Label htmlFor="user" className="flex items-center gap-2 cursor-pointer">
                            <User className="h-4 w-4 text-blue-500" />
                            <div>
                              <div className="font-medium">Usuário</div>
                              <div className="text-sm text-muted-foreground">
                                Pode criar, ver e editar próprios itens
                              </div>
                            </div>
                          </Label>
                        </div>
                        <div className="flex items-center space-x-3 p-3 border rounded-lg">
                          <RadioGroupItem value="guest" id="guest" />
                          <Label htmlFor="guest" className="flex items-center gap-2 cursor-pointer">
                            <Eye className="h-4 w-4 text-gray-500" />
                            <div>
                              <div className="font-medium">Convidado</div>
                              <div className="text-sm text-muted-foreground">
                                Apenas visualização
                              </div>
                            </div>
                          </Label>
                        </div>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Separator />

            {/* Permissões (todos exceto owner) */}
            {form.watch('userType') !== 'owner' && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Permissões</h3>
                <FormField
                  control={form.control}
                  name="permissions"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <PermissionSelector
                          value={field.value}
                          onChange={field.onChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            <Separator />

            {/* Configurações */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Configurações</h3>

              <FormField
                control={form.control}
                name="isDefault"
                render={({ field }) => (
                  <FormItem className="flex items-center justify-between">
                    <div>
                      <FormLabel>Cargo Padrão</FormLabel>
                      <FormDescription>
                        Novos membros receberão este cargo automaticamente
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancelar
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Salvando...' : mode === 'create' ? 'Criar Cargo' : 'Salvar Alterações'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
