/**
 * Controller para Notificações
 * Usa subscribe do Valtio - sem useEffect, useState, nada
 */

import { useEffect, useRef } from 'react';
import { subscribe } from 'valtio';
import { useQueryClient } from '@tanstack/react-query';
import { realtimeStore } from '../../lib/realtime/store';
import { saveToIndexedDB } from '../../lib/cache-sync';

export function useNotificationsController() {
  const queryClient = useQueryClient();
  const unsubscribeRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    console.log('🎛️ Inicializando controller de notificações...');

    // Subscribe do Valtio para mudanças no store
    unsubscribeRef.current = subscribe(realtimeStore, () => {
      const notifications = realtimeStore.notifications;

      if (notifications.length === 0) return;

      console.log(`📦 Processando ${notifications.length} notificações do realtime...`);

      // Adicionar dados novos aos existentes no React Query
      notifications.forEach(notification => {
        console.log(`📝 Atualizando notificação: ${notification.$id}`);

        queryClient.setQueryData(['notifications'], (oldData: any) => {
          if (!oldData) return [notification];

          const exists = oldData.find((item: any) => item.$id === notification.$id);
          if (exists) {
            // Atualizar existente
            return oldData.map((item: any) =>
              item.$id === notification.$id ? notification : item
            );
          } else {
            // Adicionar novo
            return [...oldData, notification];
          }
        });

        // Salvar no IndexedDB
        saveToIndexedDB('notifications', notification, {
          collection: 'notifications',
          userId: notification.userId
        });
      });

      
    });

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    };
  }, [queryClient]);

  return {
    unsubscribe: () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    }
  };
}
