# 🔧 Scripts Simplificados

Scripts simplificados baseados no padrão `scripts_example` com apenas 5 arquivos essenciais.

## 📁 Estrutura

```
scripts/
├── const.cjs              # Definições das coleções
├── setup.cjs              # Script principal (executa tudo)
├── setup-collections.cjs  # Criação de coleções
├── setup-attributes.cjs   # Criação de atributos e índices
├── setup-relationships.cjs # Configuração de relações (chat)
├── seed-data.cjs          # Dados fictícios
└── README.md             # Esta documentação
```

## 🚀 Scripts Disponíveis

### Setup Completo
```bash
# Configuração completa (recomendado)
yarn setup

# Equivale a executar:
# 1. yarn setup:collections
# 2. yarn setup:attributes
```

### Setup Individual
```bash
# Apenas coleções
yarn setup:collections

# Apenas atributos e índices
yarn setup:attributes

# Apenas relações (chat)
yarn setup:relationships

# Dados fictícios
yarn seed
```

## 📋 Coleções Configuradas

### Coleções Principais
- **clients** - Gestão de clientes
- **notifications** - Sistema de notificações
- **public_profiles** - Perfis públicos de usuários
- **activity_logs** - Logs de atividade

### Coleções de Chat (Relações)
- **team_chats** - Chats de equipe (pai)
- **chat_messages** - Mensagens (filha)

## 🔄 Ordem de Execução

1. **Setup Completo**: `yarn setup`
   - Cria todas as coleções
   - Configura todos os atributos e índices

2. **Relações (OBRIGATÓRIO para chat)**: `yarn setup:relationships`
   - Cria relacionamentos entre team_chats e chat_messages
   - Configura cascade delete e two-way relationships
   - ⚠️ **IMPORTANTE**: Sem este passo, o chat não funcionará

3. **Dados Fictícios (Opcional)**: `yarn seed`
   - Popula com dados de exemplo
   - Útil para desenvolvimento e testes

## ⚙️ Configuração

### Variáveis de Ambiente Necessárias
```env
NEXT_PUBLIC_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
NEXT_PUBLIC_APPWRITE_PROJECT_ID=seu_project_id
NEXT_PUBLIC_APPWRITE_API_KEY=sua_api_key
NEXT_PUBLIC_APPWRITE_DATABASE_ID=seu_database_id
```

### IDs das Coleções (Atualizados Automaticamente)
```env
NEXT_PUBLIC_APPWRITE_CLIENTS_ID=
NEXT_PUBLIC_APPWRITE_NOTIFICATIONS_ID=
NEXT_PUBLIC_APPWRITE_PUBLIC_PROFILES_ID=
NEXT_PUBLIC_APPWRITE_ACTIVITY_LOGS_ID=
NEXT_PUBLIC_APPWRITE_TEAM_CHATS_ID=
NEXT_PUBLIC_APPWRITE_CHAT_MESSAGES_ID=
```

## 🔗 Relações Configuradas

### Chat-Mensagens (Two-Way Relationship)

**Configuração:**
- **team_chats.messages** → array de chat_messages (oneToMany)
- **chat_messages.chat** → referência para team_chats (manyToOne)
- **Two-Way**: Appwrite gerencia automaticamente ambos os lados
- **Cascade Delete**: Deletar chat remove todas as mensagens

**⚠️ IMPORTANTE:**
- Estes atributos de relacionamento **NÃO** estão definidos em `const.cjs`
- São criados automaticamente pelo `setup-relationships.cjs`
- Para funcionalidade completa de chat, execute `yarn setup:relationships`

## 📝 Adicionando Novas Coleções

1. **Edite `const.cjs`**:
```javascript
{
  name: "nova_colecao",
  envKey: "NEXT_PUBLIC_APPWRITE_NOVA_COLECAO_ID",
  attributes: [
    { name: "campo", type: "string", size: 100, required: true },
    // ... outros atributos
  ]
}
```

2. **Execute o setup**:
```bash
yarn setup
```

## 🔧 Adicionando Novas Relações

1. **Edite `setup-relationships.cjs`**
2. **Adicione nova configuração de relação**
3. **Execute**:
```bash
yarn setup:relationships
```

## 🐛 Troubleshooting

### Erro de Permissões
- Verifique se a API Key tem permissões de admin
- Confirme se o Database ID está correto

### Atributos Já Existem
- Normal em re-execuções
- Scripts são idempotentes (podem ser executados múltiplas vezes)

### Coleções Não Criadas
- Verifique variáveis de ambiente
- Execute `yarn setup:collections` primeiro

## 📊 Comparação com Scripts Antigos

| Antes | Depois |
|-------|--------|
| 8+ arquivos complexos | 5 arquivos simples |
| Múltiplos diretórios | Estrutura plana |
| Scripts específicos | Scripts modulares |
| Configuração fragmentada | Configuração centralizada |
| Difícil manutenção | Fácil manutenção |

## ✅ Vantagens da Nova Estrutura

- **Simplicidade**: Apenas 5 arquivos essenciais
- **Modularidade**: Cada script tem uma responsabilidade
- **Idempotência**: Pode ser executado múltiplas vezes
- **Centralização**: Configurações em `const.js`
- **Flexibilidade**: Scripts individuais ou completo
- **Manutenibilidade**: Código limpo e organizado
