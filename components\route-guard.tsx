'use client';

import { useEffect } from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import { useAuth } from '../hooks/use-auth';
import { Loader2 } from 'lucide-react';

interface RouteGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
}

/**
 * Route guard component to protect routes based on authentication status
 */
export function RouteGuard({
  children,
  requireAuth = true,
  redirectTo = '/login'
}: RouteGuardProps) {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    if (!isLoading) {
      if (requireAuth && !isAuthenticated) {
        // Save the current URL to redirect back after login
        const currentPath = pathname + (searchParams.toString() ? `?${searchParams.toString()}` : '');
        if (currentPath !== '/login' && currentPath !== '/register') {
          localStorage.setItem('redirectAfterLogin', currentPath);
        }
        router.replace(redirectTo);
      } else if (!requireAuth && isAuthenticated) {
        // Check if there's a saved redirect URL
        const savedRedirect = localStorage.getItem('redirectAfterLogin');
        if (savedRedirect) {
          localStorage.removeItem('redirectAfterLogin');
          router.replace(savedRedirect);
        } else {
          // Redirect authenticated users away from auth pages
          router.replace('/dashboard');
        }
      }
    }
  }, [isAuthenticated, isLoading, requireAuth, redirectTo, router, pathname, searchParams]);

  // Render content based on auth state without early returns
  const content = (() => {
    // Show loading spinner while checking auth
    if (isLoading) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="flex flex-col items-center gap-4">
            <Loader2 className="h-8 w-8 animate-spin" />
            <p className="text-muted-foreground">Verificando autenticação...</p>
          </div>
        </div>
      );
    }

    // Don't render children if auth requirements aren't met
    if (requireAuth && !isAuthenticated) {
      return null;
    }

    if (!requireAuth && isAuthenticated) {
      return null;
    }

    return <>{children}</>;
  })();

  return content;
}

/**
 * Higher-order component for protecting routes
 */
export function withRouteGuard<T extends object>(
  Component: React.ComponentType<T>,
  options: Omit<RouteGuardProps, 'children'> = {}
) {
  return function ProtectedComponent(props: T) {
    return (
      <RouteGuard {...options}>
        <Component {...props} />
      </RouteGuard>
    );
  };
}
