
'use client';

import * as React from "react";
import { cn } from "../../lib/utils";
import { ScrollArea } from "../ui/scroll-area";
import { ModernChatInput } from "./modern-chat-input";
import { ModernChatList } from "./modern-chat-bubble";
import { ReplyPreview } from "./reply-preview";
import { MentionInput } from "./mention-input";
import type { ChatMessage } from '@/schemas/chat';

export interface ModernChatMessage {
  id: string;
  content: string;
  isUser: boolean;
  timestamp?: Date;
  isLoading?: boolean;
  avatar?: string;
  userName?: string;
  status?: 'sending' | 'sent' | 'error';
  metadata?: {
    model?: string;
    [key: string]: any;
  };
  edited?: boolean;
  editedAt?: Date;
  chatId?: string;
  reactions?: string[];
  replyTo?: ChatMessage;
  mentions?: string[];
}

interface ModernChatProps {
  messages: ModernChatMessage[];
  onSendMessage: (message: string, mentions?: string[], replyTo?: string) => void;
  onFileUpload?: (fileData: { fileId: string; url: string; fileName: string; fileType: string; fileSize: number; }) => void;
  onEditMessage?: (messageId: string, content: string) => void;
  onDeleteMessage?: (messageId: string) => void;
  isLoading?: boolean;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
  height?: string;
  showFileUpload?: boolean;
  showEmojiPicker?: boolean;
  users?: Array<{
    $id: string;
    name: string;
    email?: string;
    prefs?: { avatar?: string };
  }>;
  enableMentions?: boolean;
  enableReply?: boolean;
}

export function ModernChat({
  messages,
  onSendMessage,
  onFileUpload,
  onEditMessage,
  onDeleteMessage,
  isLoading = false,
  disabled = false,
  placeholder = "Digite sua mensagem...",
  className,
  height = "600px",
  showFileUpload = true,
  showEmojiPicker = true,
  users = [],
  enableMentions = true,
  enableReply = true,
}: ModernChatProps) {
  const [inputValue, setInputValue] = React.useState("");
  const [mentions, setMentions] = React.useState<string[]>([]);
  const [replyingTo, setReplyingTo] = React.useState<ChatMessage | null>(null);

  const handleSend = React.useCallback(() => {
    if (!inputValue.trim() || disabled || isLoading) return;

    onSendMessage(inputValue.trim(), mentions, replyingTo?.$id);
    setInputValue("");
    setMentions([]);
    setReplyingTo(null);
  }, [inputValue, disabled, isLoading, onSendMessage, mentions, replyingTo]);

  const handleEmojiSelect = React.useCallback((emoji: string) => {
    setInputValue(prev => prev + emoji);
  }, []);

  const handleMentionChange = React.useCallback((value: string, newMentions: string[]) => {
    setInputValue(value);
    setMentions(newMentions);
  }, []);

  const handleReply = React.useCallback((message: ChatMessage) => {
    setReplyingTo(message);
  }, []);

  const handleCancelReply = React.useCallback(() => {
    setReplyingTo(null);
  }, []);

  return (
    <div
      className={cn(
        "flex flex-col bg-background border border-border rounded-lg shadow-sm overflow-hidden",
        className
      )}
      style={{ height }}
    >
      {/* Messages Area */}
      <div className="flex-1 min-h-0">
        <ScrollArea className="h-full">
          <ModernChatList
            messages={messages}
            onEditMessage={onEditMessage}
            onDeleteMessage={onDeleteMessage}
            onReplyMessage={enableReply ? handleReply : undefined}
          />
        </ScrollArea>
      </div>

      {/* Input Area */}
      <div className="border-t border-border bg-background/50">
        <div className="space-y-3 p-4">
          {/* Reply Preview */}
          {replyingTo && (
            <ReplyPreview
              replyingTo={replyingTo}
              onCancel={handleCancelReply}
            />
          )}

          {/* Input with Mentions */}
          {enableMentions ? (
            <MentionInput
              value={inputValue}
              onChange={handleMentionChange}
              onSend={handleSend}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSend();
                }
              }}
              onEmojiSelect={handleEmojiSelect}
              placeholder={placeholder}
              disabled={disabled}
              isLoading={isLoading}
              users={users}
              minHeight={60}
              maxHeight={200}
              showSendButton={true}
              showEmojiPicker={showEmojiPicker}
            />
          ) : (
            <ModernChatInput
              value={inputValue}
              onChange={setInputValue}
              onSend={handleSend}
              placeholder={placeholder}
              disabled={disabled}
              isLoading={isLoading}
              showFileUpload={showFileUpload}
              showEmojiPicker={showEmojiPicker}
              onFileUpload={onFileUpload}
              onEmojiSelect={handleEmojiSelect}
              minHeight={60}
              maxHeight={200}
            />
          )}
        </div>
      </div>
    </div>
  );
}

// Hook para usar com o chat moderno
export function useModernChat() {
  const [messages, setMessages] = React.useState<ModernChatMessage[]>([]);
  const [isLoading, setIsLoading] = React.useState(false);

  const addMessage = React.useCallback((message: Omit<ModernChatMessage, 'id'>) => {
    const newMessage: ModernChatMessage = {
      ...message,
      id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    };
    setMessages(prev => [...prev, newMessage]);
    return newMessage.id;
  }, []);

  const updateMessage = React.useCallback((id: string, updates: Partial<ModernChatMessage>) => {
    setMessages(prev => prev.map(msg =>
      msg.id === id ? { ...msg, ...updates } : msg
    ));
  }, []);

  const removeMessage = React.useCallback((id: string) => {
    setMessages(prev => prev.filter(msg => msg.id !== id));
  }, []);

  const clearMessages = React.useCallback(() => {
    setMessages([]);
  }, []);

  const sendMessage = React.useCallback(async (
    content: string,
    mentions?: string[],
    replyTo?: string,
    options?: {
      onResponse?: (response: string) => void;
      onError?: (error: Error) => void;
    }
  ) => {
    // Add user message
    const userMessageId = addMessage({
      content,
      isUser: true,
      timestamp: new Date(),
      status: 'sent',
      mentions: mentions || [],
      replyTo: replyTo ? { $id: replyTo } as ChatMessage : undefined,
    });

    // Add loading message for AI response
    const aiMessageId = addMessage({
      content: '',
      isUser: false,
      timestamp: new Date(),
      isLoading: true,
    });

    setIsLoading(true);

    try {
      // Simulate AI response - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const response = `Resposta para: "${content}"`;

      updateMessage(aiMessageId, {
        content: response,
        isLoading: false,
        status: 'sent',
        metadata: { model: 'AI Assistant' }
      });

      options?.onResponse?.(response);
    } catch (error) {
      updateMessage(aiMessageId, {
        content: 'Desculpe, ocorreu um erro ao processar sua mensagem.',
        isLoading: false,
        status: 'error',
      });

      options?.onError?.(error as Error);
    } finally {
      setIsLoading(false);
    }
  }, [addMessage, updateMessage]);

  return {
    messages,
    isLoading,
    addMessage,
    updateMessage,
    removeMessage,
    clearMessages,
    sendMessage,
  };
}

// Exemplo de uso
export function ModernChatExample() {
  const [messages, setMessages] = React.useState<ModernChatMessage[]>([
    {
      id: '1',
      content: 'Olá! Este é um exemplo do chat moderno.',
      isUser: false,
      timestamp: new Date(Date.now() - 300000),
      userName: 'Sistema',
      status: 'sent',
    },
    {
      id: '2',
      content: 'Você pode enviar mensagens, usar emojis e fazer upload de arquivos!',
      isUser: true,
      timestamp: new Date(Date.now() - 240000),
      userName: 'Você',
      status: 'sent',
    },
  ]);

  const [isLoading, setIsLoading] = React.useState(false);

  const handleSendMessage = React.useCallback((message: string) => {
    setIsLoading(true);

    const newMessage: ModernChatMessage = {
      id: `msg_${Date.now()}`,
      content: message,
      isUser: true,
      timestamp: new Date(),
      userName: 'Você',
      status: 'sending',
    };

    setMessages(prev => [...prev, newMessage]);

    // Simular resposta do sistema
    setTimeout(() => {
      setMessages(prev =>
        prev.map(msg =>
          msg.id === newMessage.id
            ? { ...msg, status: 'sent' as const }
            : msg
        )
      );

      // Adicionar resposta automática
      const responseMessage: ModernChatMessage = {
        id: `msg_${Date.now() + 1}`,
        content: `Recebi sua mensagem: "${message}"`,
        isUser: false,
        timestamp: new Date(),
        userName: 'Sistema',
        status: 'sent',
      };

      setMessages(prev => [...prev, responseMessage]);
      setIsLoading(false);
    }, 1000);
  }, []);

  return (
    <div className="max-w-4xl mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Chat Moderno</h1>
      <ModernChat
        messages={messages}
        onSendMessage={handleSendMessage}
        isLoading={isLoading}
        placeholder="Digite sua mensagem..."
        height="500px"
      />
    </div>
  );
}
