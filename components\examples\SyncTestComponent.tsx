/**
 * Componente para testar a sincronização de dados com soft delete
 * Demonstra como a sincronização funciona com documentos deletados
 */

import { useState } from 'react';
import { RefreshCw, Database, Trash2, RotateCcw, Eye } from 'lucide-react';
import { <PERSON><PERSON> } from '../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { useClients, useDeleteClient, useRestoreClient, useDeletedClients } from '../../hooks/api/use-clients';
import { syncUpdatedDataFromServer } from '../../lib/cache-sync';
import { useAuth } from '../../hooks/use-auth';
import { useQueryClient } from '@tanstack/react-query';
import type { Client } from '@/schemas/clients';

export function SyncTestComponent() {
  const [isManualSyncing, setIsManualSyncing] = useState(false);
  const [syncLogs, setSyncLogs] = useState<string[]>([]);
  const { user } = useAuth();
  const queryClient = useQueryClient();
  
  const { data: activeClients, isLoading: loadingActive } = useClients();
  const { data: deletedClients, isLoading: loadingDeleted } = useDeletedClients();
  const deleteClient = useDeleteClient();
  const restoreClient = useRestoreClient();

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setSyncLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 9)]);
  };

  const handleManualSync = async () => {
    if (!user?.$id) return;
    
    setIsManualSyncing(true);
    addLog('🔄 Iniciando sincronização manual...');

    try {
      await syncUpdatedDataFromServer<Client>(
        'clients',
        user.$id,
        (activeData, deletedData) => {
          addLog(`✅ Sincronização concluída: ${activeData.length} ativos, ${deletedData.length} deletados`);
          
          if (activeData.length > 0) {
            addLog(`📝 Documentos ativos atualizados: ${activeData.map(d => d.name).join(', ')}`);
          }
          
          if (deletedData.length > 0) {
            addLog(`🗑️ Documentos removidos do cache: ${deletedData.map(d => d.name).join(', ')}`);
          }

          // Atualizar cache do React Query
          queryClient.setQueryData<Client[]>(['clients', user.$id], (oldData) => {
            if (!oldData) return activeData;

            let mergedData = [...oldData];

            // Remover documentos deletados
            if (deletedData.length > 0) {
              const deletedIds = deletedData.map(doc => doc.$id);
              mergedData = mergedData.filter(item => !deletedIds.includes(item.$id));
            }

            // Merge dos dados ativos
            activeData.forEach(updatedItem => {
              const existingIndex = mergedData.findIndex(item => item.$id === updatedItem.$id);
              if (existingIndex >= 0) {
                mergedData[existingIndex] = updatedItem;
              } else {
                mergedData.push(updatedItem);
              }
            });

            return mergedData;
          });
        }
      );
    } catch (error) {
      addLog(`❌ Erro na sincronização: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    } finally {
      setIsManualSyncing(false);
    }
  };

  const handleSoftDelete = async (client: Client) => {
    addLog(`🗑️ Soft delete iniciado para: ${client.name}`);
    try {
      await deleteClient.mutateAsync(client.$id);
      addLog(`✅ Cliente ${client.name} movido para lixeira`);
    } catch (error) {
      addLog(`❌ Erro ao deletar ${client.name}: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  };

  const handleRestore = async (client: Client) => {
    addLog(`♻️ Restauração iniciada para: ${client.name}`);
    try {
      await restoreClient.mutateAsync(client.$id);
      addLog(`✅ Cliente ${client.name} restaurado com sucesso`);
    } catch (error) {
      addLog(`❌ Erro ao restaurar ${client.name}: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  };

  return (
    <div className="space-y-6">
      {/* Controles de Sincronização */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RefreshCw className="h-5 w-5" />
            Teste de Sincronização com Soft Delete
          </CardTitle>
          <CardDescription>
            Teste como a sincronização funciona com documentos soft deleted
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-4">
            <Button
              onClick={handleManualSync}
              disabled={isManualSyncing}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${isManualSyncing ? 'animate-spin' : ''}`} />
              {isManualSyncing ? 'Sincronizando...' : 'Sincronizar Manualmente'}
            </Button>
            
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Database className="h-4 w-4" />
              <span>
                {activeClients?.length || 0} ativos, {deletedClients?.length || 0} na lixeira
              </span>
            </div>
          </div>

          {/* Logs de Sincronização */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium mb-2 flex items-center gap-2">
              <Eye className="h-4 w-4" />
              Logs de Sincronização
            </h4>
            <div className="space-y-1 max-h-40 overflow-y-auto">
              {syncLogs.length === 0 ? (
                <p className="text-sm text-gray-500">Nenhum log ainda...</p>
              ) : (
                syncLogs.map((log, index) => (
                  <div key={index} className="text-xs font-mono bg-white p-2 rounded border">
                    {log}
                  </div>
                ))
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Clientes Ativos */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Clientes Ativos ({activeClients?.length || 0})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loadingActive ? (
            <div className="text-center py-4">Carregando...</div>
          ) : activeClients?.length === 0 ? (
            <div className="text-center py-4 text-gray-500">Nenhum cliente ativo</div>
          ) : (
            <div className="space-y-2">
              {activeClients?.slice(0, 5).map((client) => (
                <div key={client.$id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <h4 className="font-medium">{client.name}</h4>
                    <p className="text-sm text-gray-600">{client.email}</p>
                    <Badge variant="secondary" className="text-xs mt-1">
                      {client.status}
                    </Badge>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSoftDelete(client)}
                    disabled={deleteClient.isPending}
                    className="flex items-center gap-2"
                  >
                    <Trash2 className="h-4 w-4" />
                    Soft Delete
                  </Button>
                </div>
              ))}
              {(activeClients?.length || 0) > 5 && (
                <p className="text-sm text-gray-500 text-center">
                  ... e mais {(activeClients?.length || 0) - 5} clientes
                </p>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Clientes na Lixeira */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5" />
            Lixeira ({deletedClients?.length || 0})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loadingDeleted ? (
            <div className="text-center py-4">Carregando...</div>
          ) : deletedClients?.length === 0 ? (
            <div className="text-center py-4 text-gray-500">Lixeira vazia</div>
          ) : (
            <div className="space-y-2">
              {deletedClients?.slice(0, 5).map((client) => (
                <div key={client.$id} className="flex items-center justify-between p-3 border rounded-lg bg-red-50">
                  <div>
                    <h4 className="font-medium text-gray-700">{client.name}</h4>
                    <p className="text-sm text-gray-600">{client.email}</p>
                    <Badge variant="destructive" className="text-xs mt-1">
                      Deletado
                    </Badge>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleRestore(client)}
                    disabled={restoreClient.isPending}
                    className="flex items-center gap-2"
                  >
                    <RotateCcw className="h-4 w-4" />
                    Restaurar
                  </Button>
                </div>
              ))}
              {(deletedClients?.length || 0) > 5 && (
                <p className="text-sm text-gray-500 text-center">
                  ... e mais {(deletedClients?.length || 0) - 5} clientes deletados
                </p>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Instruções */}
      <Card>
        <CardHeader>
          <CardTitle>💡 Como Testar</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm">
          <p>1. <strong>Soft Delete:</strong> Clique em "Soft Delete" em um cliente ativo</p>
          <p>2. <strong>Observe:</strong> O cliente desaparece da lista ativa e aparece na lixeira</p>
          <p>3. <strong>Sincronização:</strong> Clique em "Sincronizar Manualmente" para testar</p>
          <p>4. <strong>Restaurar:</strong> Clique em "Restaurar" para mover de volta para ativos</p>
          <p>5. <strong>Logs:</strong> Acompanhe os logs para ver o que acontece internamente</p>
        </CardContent>
      </Card>
    </div>
  );
}
