
import { useCallback, useMemo } from "react"
import { IconTrendingDown, IconTrendingUp, IconCalendarEvent, IconLayoutKanban, IconChecklist, IconUsersGroup } from "@tabler/icons-react"

import { Badge } from "./ui/badge"
import {
  Card,
  CardAction,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "./ui/card"
import { Skeleton } from "./ui/skeleton"
import { useAnalytics } from "../hooks/use-analytics"
import { DashboardErrorWrapper } from "./dashboard-error-wrapper"

export function SectionCards() {
  const { data: analytics, isLoading } = useAnalytics();

  // Função para formatar moeda
  const formatCurrency = useCallback((value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  }, []);

  // Função para formatar números
  const formatNumber = useCallback((value: number) => {
    return new Intl.NumberFormat('pt-BR').format(value);
  }, []);

  // Mover toda a lógica para useMemo para evitar early returns
  const content = useMemo(() => {
    if (isLoading) {
      return (
        <div className="grid grid-cols-1 gap-4 px-4 lg:px-6 @xl/main:grid-cols-2 @5xl/main:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Skeleton key={i} className="h-32" />
          ))}
        </div>
      );
    }

    if (!analytics) {
      return null;
    }

    const cards = [
      {
        title: "Total de Eventos",
        value: formatNumber(analytics.totalEvents),
        growth: analytics.eventsGrowth,
        icon: IconCalendarEvent,
        description: "eventos cadastrados",
        trend: analytics.eventsGrowth >= 0 ? "up" : "down",
      },
      {
        title: "Boards Kanban",
        value: formatNumber(analytics.totalKanbanBoards),
        growth: analytics.tasksGrowth,
        icon: IconLayoutKanban,
        description: "boards ativos",
        trend: analytics.tasksGrowth >= 0 ? "up" : "down",
      },
      {
        title: "Tarefas Concluídas",
        value: formatNumber(analytics.completedTasks),
        growth: Math.round(((analytics.completedTasks / Math.max(analytics.totalKanbanTasks, 1)) * 100 - 50) * 10) / 10,
        icon: IconChecklist,
        description: "tarefas finalizadas",
        trend: analytics.completedTasks > (analytics.totalKanbanTasks / 2) ? "up" : "down",
      },
      {
        title: "Total de Clientes",
        value: formatNumber(analytics.totalClients),
        growth: analytics.clientGrowth,
        icon: IconUsersGroup,
        description: "clientes cadastrados",
        trend: analytics.clientGrowth >= 0 ? "up" : "down",
      },
    ];

    return (
      <DashboardErrorWrapper
        fallbackTitle="Erro nos Cards do Dashboard"
        fallbackDescription="Não foi possível carregar as métricas do dashboard."
        showSkeleton={true}
      >
        <div className="*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 px-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs lg:px-6 @xl/main:grid-cols-2 @5xl/main:grid-cols-4">
          {cards.map((card, index) => {
            const Icon = card.icon;
            const TrendIcon = card.trend === "up" ? IconTrendingUp : IconTrendingDown;
            const isPositiveGrowth = card.growth >= 0;

            return (
              <Card key={index} className="@container/card">
                <CardHeader>
                  <CardDescription>{card.title}</CardDescription>
                  <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
                    {card.value}
                  </CardTitle>
                  <CardAction>
                    <Badge variant="outline" className={isPositiveGrowth ? "text-green-600" : "text-red-600"}>
                      <TrendIcon className="h-3 w-3" />
                      {isPositiveGrowth ? "+" : ""}{card.growth}%
                    </Badge>
                  </CardAction>
                </CardHeader>
                <CardFooter className="flex-col items-start gap-1.5 text-sm">
                  <div className="line-clamp-1 flex gap-2 font-medium">
                    {card.trend === "up" ? "Crescendo este mês" : "Precisa de atenção"}
                    <TrendIcon className="size-4" />
                  </div>
                  <div className="text-muted-foreground">
                    {card.description}
                  </div>
                </CardFooter>
              </Card>
            );
          })}
        </div>
      </DashboardErrorWrapper>
    );
  }, [analytics, isLoading, formatCurrency, formatNumber]);

  return content;
}
