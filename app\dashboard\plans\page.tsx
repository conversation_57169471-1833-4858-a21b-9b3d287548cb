'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/use-auth';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Check, Crown, Zap, Building, CreditCard, Settings } from 'lucide-react';
import { PLANS, formatPrice, calculateYearlyDiscount } from '@/lib/plans';
import { toast } from 'sonner';
import { useUpgradePlan, useManageSubscription, usePaymentReturn, PaymentUtils } from '@/hooks/use-payments';
import type { Metadata } from 'next';

// Função para determinar o plano do usuário baseado nas labels
function getUserPlan(userLabels: string[] = []): string {
  // Verifica se o usuário tem labels específicas de planos
  if (userLabels.includes('enterprise') || userLabels.includes('plan-enterprise')) {
    return 'enterprise';
  }
  if (userLabels.includes('pro') || userLabels.includes('plan-pro') || userLabels.includes('professional')) {
    return 'pro';
  }
  // Se não tem nenhuma label específica, assume plano básico
  return 'free';
}

// Função para obter o ícone do plano
function getPlanIcon(planId: string) {
  switch (planId) {
    case 'free':
      return <Zap className="h-5 w-5" />;
    case 'pro':
      return <Crown className="h-5 w-5" />;
    case 'enterprise':
      return <Building className="h-5 w-5" />;
    default:
      return <Zap className="h-5 w-5" />;
  }
}

export default function PlansPage() {
  const { user } = useAuth();
  const [isYearly, setIsYearly] = useState(false);

  const upgradePlan = useUpgradePlan();
  const manageSubscription = useManageSubscription();
  const { processReturn } = usePaymentReturn();

  // Determina o plano atual do usuário
  const userLabels = user?.labels || [];
  const currentPlan = getUserPlan(userLabels);
  const hasPaidPlan = currentPlan !== 'free';
  const paymentsConfigured = PaymentUtils.isConfigured();

  // Processar retorno do Stripe
  useEffect(() => {
    processReturn();
  }, [processReturn]);

  const handlePlanChange = (planId: string) => {
    if (planId === currentPlan) {
      toast.info('Você já está neste plano');
      return;
    }

    if (planId === 'free') {
      if (hasPaidPlan) {
        // Redirecionar para portal do Stripe para cancelar
        manageSubscription.mutate(window.location.href);
      } else {
        toast.info('Você já está no plano gratuito');
      }
      return;
    }

    if (!paymentsConfigured) {
      toast.error('Sistema de pagamentos não configurado');
      return;
    }

    // Fazer upgrade
    upgradePlan.mutate({
      planId,
      billingCycle: isYearly ? 'yearly' : 'monthly',
    });
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex flex-col gap-4 p-4 md:p-6 border-b md:flex-row md:items-center md:justify-between">
        <div className="min-w-0">
          <h1 className="text-xl md:text-2xl font-bold">Planos e Assinatura</h1>
          <p className="text-muted-foreground text-sm md:text-base">
            Gerencie sua assinatura e escolha o plano ideal para suas necessidades.
          </p>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 p-4 md:p-6">
        <div className="space-y-6">
          {/* Current Plan Status */}
          <div className="flex items-center gap-4 p-4 bg-muted/50 rounded-lg">
            <div className="flex items-center gap-2">
              {getPlanIcon(currentPlan)}
              <span className="font-medium">Plano Atual:</span>
            </div>
            <Badge variant="secondary" className="text-sm">
              {PLANS.find(p => p.id === currentPlan)?.name || 'Básico'}
            </Badge>
            {currentPlan !== 'free' && (
              <span className="text-sm text-muted-foreground">
                • Renovação automática
              </span>
            )}
          </div>

          {/* Billing Toggle */}
          <div className="flex items-center justify-center gap-4">
            <Label htmlFor="billing-toggle" className={!isYearly ? 'font-medium' : ''}>
              Mensal
            </Label>
            <Switch
              id="billing-toggle"
              checked={isYearly}
              onCheckedChange={setIsYearly}
            />
            <Label htmlFor="billing-toggle" className={isYearly ? 'font-medium' : ''}>
              Anual
            </Label>
            {isYearly && (
              <Badge variant="secondary" className="ml-2">
                Economize até 17%
              </Badge>
            )}
          </div>

          {/* Plans Grid */}
          <div className="grid gap-6 md:grid-cols-3">
            {PLANS.map((plan) => {
              const isCurrentPlan = plan.id === currentPlan;
              const price = isYearly ? plan.price.yearly : plan.price.monthly;
              const period = isYearly ? plan.period.yearly : plan.period.monthly;
              const discount = calculateYearlyDiscount(plan.price.monthly, plan.price.yearly);

              return (
                <Card
                  key={plan.id}
                  className={`relative ${plan.popular ? 'border-primary shadow-lg' : ''} ${isCurrentPlan ? 'ring-2 ring-primary' : ''}`}
                >
                  {plan.popular && (
                    <div className="absolute -top-3 left-1/2 -translate-x-1/2">
                      <Badge className="bg-primary text-primary-foreground">
                        Mais Popular
                      </Badge>
                    </div>
                  )}

                  {isCurrentPlan && (
                    <div className="absolute -top-3 right-4">
                      <Badge variant="secondary">
                        Plano Atual
                      </Badge>
                    </div>
                  )}

                  <CardHeader className="text-center pb-4">
                    <div className="flex justify-center mb-2">
                      {getPlanIcon(plan.id)}
                    </div>
                    <CardTitle className="text-xl">{plan.name}</CardTitle>
                    <CardDescription>{plan.description}</CardDescription>

                    <div className="mt-4">
                      <div className="flex items-baseline justify-center gap-1">
                        <span className="text-3xl font-bold">
                          {formatPrice(price, plan.currency)}
                        </span>
                        {price > 0 && (
                          <span className="text-muted-foreground">{period}</span>
                        )}
                      </div>
                      {isYearly && discount > 0 && (
                        <p className="text-sm text-green-600 mt-1">
                          Economize {discount}% no plano anual
                        </p>
                      )}
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    <Button
                      className="w-full"
                      variant={plan.buttonVariant as "default" | "outline"}
                      disabled={isCurrentPlan || upgradePlan.isPending || manageSubscription.isPending}
                      onClick={() => handlePlanChange(plan.id)}
                    >
                      {upgradePlan.isPending || manageSubscription.isPending ? (
                        <>
                          <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                          Processando...
                        </>
                      ) : isCurrentPlan ? (
                        <>
                          <Check className="mr-2 h-4 w-4" />
                          Plano Atual
                        </>
                      ) : plan.id === 'free' && hasPaidPlan ? (
                        <>
                          <Settings className="mr-2 h-4 w-4" />
                          Gerenciar Assinatura
                        </>
                      ) : plan.id !== 'free' && paymentsConfigured ? (
                        <>
                          <CreditCard className="mr-2 h-4 w-4" />
                          {plan.buttonText}
                        </>
                      ) : (
                        plan.buttonText
                      )}
                    </Button>

                    <div className="space-y-3">
                      {plan.features.map((feature, index) => (
                        <div key={index} className="flex items-start gap-3">
                          <Check
                            className={`h-4 w-4 mt-0.5 flex-shrink-0 ${
                              feature.included
                                ? 'text-green-500'
                                : 'text-muted-foreground'
                            }`}
                          />
                          <span
                            className={`text-sm ${
                              feature.included
                                ? 'text-foreground'
                                : 'text-muted-foreground line-through'
                            }`}
                          >
                            {feature.name}
                          </span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Additional Info */}
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Informações de Cobrança</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <p className="text-sm text-muted-foreground">
                  • Todos os planos incluem 14 dias de teste gratuito
                </p>
                <p className="text-sm text-muted-foreground">
                  • Cancele a qualquer momento sem taxas
                </p>
                <p className="text-sm text-muted-foreground">
                  • Suporte técnico incluído em todos os planos
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Precisa de Ajuda?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <p className="text-sm text-muted-foreground">
                  Nossa equipe está aqui para ajudar você a escolher o melhor plano.
                </p>
                <Button variant="outline" className="w-full">
                  Falar com Suporte
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
