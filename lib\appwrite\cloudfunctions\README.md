# 🚀 Cloud Functions - Template Completo

Este diretório contém as cloud functions essenciais para o template, seguindo a documentação oficial do Appwrite. Inclui funções administrativas e utilitários seguros.

**⚠️ IMPORTANTE**: O gerenciamento de times foi migrado para **client-side** usando a API nativa do Appwrite Teams. Não são mais necessárias cloud functions para times.

## ✅ Atualizações Baseadas na Documentação Oficial

- **ES6 Modules**: Uso de `import/export` em vez de `require/module.exports` ✅
- **Headers corretos**: Uso de `x-appwrite-user-jwt` para autenticação ✅
- **Body parsing**: Uso de `req.bodyJson` conforme documentação ✅
- **Estrutura moderna**: Seguindo padrões oficiais do Appwrite ✅
- **Configurações corrigidas**: Paths e entrypoints no `appwrite.json` ✅
- **Validação TypeScript**: Todos os erros corrigidos ✅

## 📋 Funções Disponíveis

### 1. **admin-users**
Função para listar usuários do projeto (apenas para admins).

**Endpoint:** `/functions/admin-users/executions`
**Método:** `GET`
**Autenticação:** Requerida (JWT)
**Permissões:** Apenas usuários com label `admin`

**Parâmetros:**
- `limit` (opcional): Número máximo de usuários (default: 25, max: 100)
- `offset` (opcional): Offset para paginação (default: 0)
- `search` (opcional): Termo de busca para filtrar por nome ou email

### 2. **admin-user-management**
Função para gerenciamento avançado de usuários (promover/demover roles).

**Endpoint:** `/functions/admin-user-management/executions`
**Método:** `POST`
**Autenticação:** Requerida (JWT)
**Permissões:** Apenas usuários com label `admin`

**Ações Disponíveis:**
- `promote_to_admin`: Promover usuário a administrador
- `promote_to_moderator`: Promover usuário a moderador
- `demote_to_user`: Demover usuário para usuário comum
- `update_permissions`: Atualizar permissões específicas
- `bulk_update_roles`: Atualização em lote de roles

**Exemplo de Uso:**
```javascript
{
  "action": "promote_to_admin",
  "userId": "user-id-here"
}
```

## 🏢 Gerenciamento de Times - Migrado para Client-Side

**⚠️ IMPORTANTE**: Todas as funções de gerenciamento de times foram **removidas** e migradas para **client-side** usando a API nativa do Appwrite Teams.

### Operações Disponíveis (Client-Side):
- ✅ **Criar time**: `teams.create()`
- ✅ **Listar times**: `teams.list()`
- ✅ **Obter time**: `teams.get()`
- ✅ **Atualizar time**: `teams.updateName()`
- ✅ **Deletar time**: `teams.delete()`
- ✅ **Convidar membro**: `teams.createMembership()`
- ✅ **Remover membro**: `teams.deleteMembership()`
- ✅ **Atualizar role**: `teams.updateMembershipRoles()`
- ✅ **Listar membros**: `teams.listMemberships()`

### Vantagens da Migração:
- 🚀 **Melhor Performance**: Chamadas diretas à API
- 🔧 **Menos Complexidade**: Sem necessidade de cloud functions
- 💰 **Menor Custo**: Redução no uso de cloud functions
- 🛠️ **Mais Simples**: Manutenção facilitada

## 🔧 Funções Utilitárias

### 3. **utility-send-email**
Envia emails usando provedores de email seguros.

**Endpoint:** `/functions/utility-send-email/executions`
**Método:** `POST`
**Autenticação:** Opcional (dependendo do uso)

**Body:**
```javascript
{
  "to": "<EMAIL>",
  "subject": "Assunto do Email",
  "template": "welcome", // ou "password-reset", "team-invite", "custom"
  "data": {
    "name": "Nome do Usuário",
    "loginUrl": "https://app.com/login"
  },
  "html": "HTML content", // apenas para template custom
  "text": "Text content" // opcional
}
```

**Templates Disponíveis:**
- `welcome`: Email de boas-vindas
- `password-reset`: Reset de senha
- `team-invite`: Convite para time
- `notification`: Notificação geral
- `custom`: HTML customizado

### 4. **webhook-handler**
Manipula webhooks de serviços externos.

**Endpoint:** `/functions/webhook-handler/executions`
**Método:** `POST`
**Autenticação:** Não requerida (webhooks externos)

**Headers:**
- `X-Webhook-Source`: stripe | paypal | sendgrid | custom
- `X-Webhook-Signature`: Assinatura do webhook (para verificação)

**Provedores Suportados:**
- ✅ Stripe (pagamentos e assinaturas)
- ✅ PayPal (pagamentos)
- ✅ SendGrid (status de emails)
- ✅ Mailgun (status de emails)
- ✅ Webhooks customizados

## 🛠️ Como Configurar

### 1. **Instalar Appwrite CLI**
```bash
npm install -g appwrite-cli
```

### 2. **Fazer Login no Appwrite**
```bash
appwrite login
```

### 3. **Configurar Projeto**
```bash
appwrite init project
```

### 4. **Deploy das Funções**
```bash
# Na raiz do projeto
appwrite deploy function

# Ou deploy específico
appwrite deploy function --functionId admin-users
```

### 5. **Configurar Variáveis de Ambiente**
No console do Appwrite, configure as seguintes variáveis para a função:

- `APPWRITE_FUNCTION_ENDPOINT`: `https://cloud.appwrite.io/v1`
- `APPWRITE_FUNCTION_PROJECT_ID`: Seu Project ID
- `APPWRITE_FUNCTION_API_KEY`: Sua API Key (com permissões de usuários)

### 6. **Configurar Permissões**
- Execute: `["role:admin"]`
- Runtime: `node-18.0`
- Timeout: `15` segundos

## 🔧 Desenvolvimento Local

### 1. **Testar Localmente**
```bash
# Instalar dependências
cd src/lib/appwrite/cloudfunctions
npm install node-appwrite

# Testar função
node admin-users.js
```

### 2. **Debug**
As funções incluem logs detalhados. Verifique os logs no console do Appwrite.

## 🚨 Fallbacks

O sistema foi projetado para funcionar mesmo sem as cloud functions configuradas:

1. **Dados Mock**: Se a cloud function não estiver disponível, o sistema usa dados mock
2. **Graceful Degradation**: Funcionalidades que dependem das cloud functions mostram mensagens apropriadas
3. **Error Handling**: Todos os erros são tratados adequadamente

## 📝 Notas Importantes

- As cloud functions são **obrigatórias** para acessar a API de usuários do Appwrite
- Sem as cloud functions, o painel admin funcionará com dados limitados/mock
- Certifique-se de que apenas usuários com label `admin` tenham acesso
- As funções incluem validação de autenticação e autorização

## 🔐 Segurança

- ✅ Validação de JWT
- ✅ Verificação de permissões admin
- ✅ Sanitização de dados de resposta
- ✅ Rate limiting (configurável no Appwrite)
- ✅ Logs de auditoria

## 🆘 Troubleshooting

### Erro: "Cloud function não configurada"
- Verifique se a função foi deployada corretamente
- Confirme as variáveis de ambiente
- Verifique as permissões da função

### Erro: "Token inválido ou expirado"
- Usuário precisa fazer login novamente
- Verifique se o JWT está sendo enviado corretamente

### Erro: "Acesso negado"
- Usuário não tem label `admin`
- Adicione o label `admin` ao usuário no console do Appwrite

## 📚 Próximos Passos

Para funcionalidade completa de gerenciamento de usuários, considere implementar:

1. **admin-update-user**: Atualizar labels e status de usuários
2. **admin-delete-user**: Deletar usuários
3. **admin-create-user**: Criar novos usuários
4. **admin-user-stats**: Estatísticas detalhadas de usuários
