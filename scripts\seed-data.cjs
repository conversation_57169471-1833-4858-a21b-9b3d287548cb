// Script para gerar dados fictícios para o template
console.log("🌱 Iniciando script de seed...");
require('dotenv').config();
console.log("✅ Dotenv configurado");

const { Client, Databases, ID } = require('node-appwrite');
console.log("✅ Appwrite importado");

const { faker } = require('@faker-js/faker');
console.log("✅ Faker importado");

// Configurar faker para português brasileiro
faker.locale = 'pt_BR';

const client = new Client()
    .setEndpoint(process.env.APPWRITE_ENDPOINT)
    .setProject(process.env.APPWRITE_PROJECT_ID)
    .setKey(process.env.APPWRITE_API_KEY);

const databases = new Databases(client);
const DATABASE_ID = process.env.APPWRITE_DATABASE_ID;

// Configuração base para ownership
const BASE_USER_ID = process.env.APPWRITE_USER_ID || '6848a1cd001ab7e3b29b';
const BASE_TEAM_ID = process.env.APPWRITE_TEAM_ID || '6848ad26000cb7f5a9dc';

// Funções auxiliares
function getRandomInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

function getRandomElement(array) {
    return array[Math.floor(Math.random() * array.length)];
}

// Gerar clientes fictícios
function generateClients(count = 10) {
    const clients = [];
    const tipos = ['pessoa_fisica', 'pessoa_juridica'];
    const statuses = ['ativo', 'inativo', 'prospecto', 'arquivado'];
    const priorities = ['baixa', 'media', 'alta'];

    for (let i = 0; i < count; i++) {
        const tipo = getRandomElement(tipos);
        const client = {
            // Ownership e auditoria
            userId: BASE_USER_ID,
            createdBy: BASE_USER_ID,
            teamId: Math.random() > 0.5 ? BASE_TEAM_ID : undefined,

            // Informações básicas
            name: tipo === 'pessoa_fisica' ? faker.person.fullName() : faker.company.name(),
            email: faker.internet.email(),
            phone: faker.phone.number('(##) #####-####'),
            document: tipo === 'pessoa_fisica' ?
                faker.string.numeric(11) : // CPF
                faker.string.numeric(14),  // CNPJ
            type: tipo,
            status: getRandomElement(statuses),
            priority: getRandomElement(priorities),

            // Campos opcionais (compatíveis com const.cjs)
            avatar: faker.image.avatar(),
            tags: Array.from({ length: getRandomInt(1, 4) }, () => faker.lorem.word()),

            // Campos de empresa (se pessoa jurídica)
            company: tipo === 'pessoa_juridica' ? faker.company.name() : undefined,
            companyDocument: tipo === 'pessoa_juridica' ? faker.string.numeric(14) : undefined
        };

        clients.push(client);
    }

    return clients;
}

// Gerar notificações fictícias
function generateNotifications(count = 20) {
    const notifications = [];
    // Tipos válidos conforme const.cjs
    const types = ['mention', 'assignment', 'comment', 'update', 'deadline', 'system', 'invite', 'alert', 'report', 'summary', 'success', 'info', 'warning', 'error'];

    for (let i = 0; i < count; i++) {
        const notification = {
            // Ownership (obrigatório conforme const.cjs)
            userId: BASE_USER_ID,
            createdBy: BASE_USER_ID,

            // Informações básicas
            type: getRandomElement(types),
            title: faker.lorem.sentence(4),
            message: faker.lorem.sentence(8),

            // Status
            read: faker.datatype.boolean(),

            // Campos opcionais conforme const.cjs
            content: faker.lorem.sentence(8), // Campo alias para compatibilidade
            actionUrl: Math.random() > 0.5 ? faker.internet.url() : undefined,
            relatedId: Math.random() > 0.5 ? faker.string.uuid() : undefined,
            relatedType: Math.random() > 0.5 ? getRandomElement(['client', 'user', 'system', 'payment']) : undefined
        };

        notifications.push(notification);
    }

    return notifications;
}

// Função de perfis públicos removida (não precisa de seed)

// Gerar logs de atividade fictícios
function generateActivityLogs(count = 30) {
    const logs = [];
    const actions = ['create', 'update', 'delete', 'view', 'login', 'logout', 'export', 'import'];
    const resources = ['client', 'notification', 'profile', 'user', 'system', 'chat', 'event', 'kanban'];
    // Tipos válidos conforme const.cjs
    const types = ['auth', 'client', 'team', 'chat', 'file', 'system', 'admin', 'notification', 'preference', 'calendar', 'document'];
    const priorities = ['low', 'normal', 'high', 'critical'];
    const categories = ['authentication', 'data_management', 'user_interaction', 'system_operation'];

    for (let i = 0; i < count; i++) {
        const log = {
            // Ownership
            userId: BASE_USER_ID,
            createdBy: BASE_USER_ID,
            teamId: Math.random() > 0.5 ? BASE_TEAM_ID : undefined,

            // Atividade básica
            action: getRandomElement(actions),
            resource: getRandomElement(resources),
            resourceId: faker.string.uuid(),

            // Detalhes expandidos
            type: getRandomElement(types),
            title: faker.lorem.sentence(3),
            description: faker.lorem.sentence(6),
            priority: getRandomElement(priorities),
            category: getRandomElement(categories),

            // Dados técnicos
            details: JSON.stringify({
                oldValue: faker.lorem.word(),
                newValue: faker.lorem.word(),
                affectedFields: Array.from({ length: getRandomInt(1, 3) }, () => faker.lorem.word())
            }),

            // Metadados
            metadata: JSON.stringify({
                source: 'seed_script',
                environment: 'development',
                version: '1.0.0'
            }),

            // Informações técnicas
            ipAddress: faker.internet.ip(),
            userAgent: faker.internet.userAgent(),

            // Tags
            tags: Array.from({ length: getRandomInt(1, 3) }, () => faker.lorem.word())
        };

        logs.push(log);
    }

    return logs;
}

// Funções de team chats e mensagens removidas (não precisam de seed)

// Gerar eventos fictícios
function generateEvents(count = 10) {
    const events = [];
    // Tipos válidos conforme const.cjs
    const types = ['meeting', 'task', 'reminder', 'appointment', 'deadline', 'personal', 'work', 'other'];
    const priorities = ['baixa', 'media', 'alta', 'critica'];
    const statuses = ['agendado', 'em_andamento', 'concluido', 'cancelado', 'adiado'];

    for (let i = 0; i < count; i++) {
        const startDate = faker.date.future();
        const endDate = new Date(startDate.getTime() + (getRandomInt(1, 4) * 60 * 60 * 1000)); // 1-4 horas depois

        const event = {
            // Ownership
            userId: BASE_USER_ID,
            createdBy: BASE_USER_ID,
            teamId: Math.random() > 0.5 ? BASE_TEAM_ID : undefined,

            // Informações básicas
            title: faker.lorem.sentence(3),
            description: faker.lorem.paragraph(),

            // Data e hora
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
            allDay: faker.datatype.boolean(),
            timezone: 'America/Sao_Paulo',

            // Classificação conforme const.cjs
            type: getRandomElement(types),
            priority: getRandomElement(priorities),
            status: getRandomElement(statuses),
            category: faker.lorem.word(),
            color: faker.color.rgb(),

            // Localização
            location: faker.location.streetAddress(),
            locationUrl: faker.datatype.boolean() ? faker.internet.url() : undefined,

            // Recorrência conforme const.cjs
            recurrenceType: getRandomElement(['none', 'daily', 'weekly', 'monthly', 'yearly']),
            recurrenceInterval: getRandomInt(1, 4),

            // Participantes
            attendees: [BASE_USER_ID],
            isPublic: faker.datatype.boolean(),
            allowGuestInvites: faker.datatype.boolean(),

            // Lembretes conforme const.cjs
            reminderType: getRandomElement(['none', '5min', '15min', '30min', '1hour', '2hours', '1day', '1week']),
            reminderSent: false,

            // Anexos e links
            attachments: [],
            links: JSON.stringify([
                {
                    title: faker.lorem.words(2),
                    url: faker.internet.url()
                }
            ]),

            // Metadata
            tags: Array.from({ length: getRandomInt(1, 3) }, () => faker.lorem.word()),
            notes: faker.lorem.paragraph()
        };

        events.push(event);
    }

    return events;
}

// Gerar boards de kanban fictícios
function generateKanbanBoards(count = 2) {
    const boards = [];
    const visibilities = ['private', 'team', 'public'];

    for (let i = 0; i < count; i++) {
        const board = {
            // Ownership
            userId: BASE_USER_ID,
            createdBy: BASE_USER_ID,
            teamId: BASE_TEAM_ID,

            // Informações básicas
            title: `Board ${i + 1} - ${faker.lorem.words(2)}`,
            description: faker.lorem.paragraph(),

            // Configurações conforme const.cjs
            visibility: getRandomElement(visibilities),
            allowComments: true,
            allowAttachments: true,
            enableTimeTracking: faker.datatype.boolean(),

            // Configurações visuais
            backgroundColor: faker.color.rgb(),

            // Metadados
            isTemplate: false,
            isArchived: false,
            isFavorite: faker.datatype.boolean()
        };

        boards.push(board);
    }

    return boards;
}

// Gerar colunas de kanban fictícias
function generateKanbanColumns(count = 4, boardIds = []) {
    const columns = [];
    const defaultColumns = ['A Fazer', 'Em Progresso', 'Em Revisão', 'Concluído'];

    for (let i = 0; i < count; i++) {
        const column = {
            // Ownership
            userId: BASE_USER_ID,
            createdBy: BASE_USER_ID,
            teamId: BASE_TEAM_ID,

            // Relacionamento
            boardId: boardIds.length > 0 ? getRandomElement(boardIds) : 'board_placeholder',

            // Informações básicas
            title: defaultColumns[i] || `Coluna ${i + 1}`,
            description: faker.lorem.sentence(),

            // Configurações
            position: i,
            color: faker.color.rgb(),

            // Configurações conforme const.cjs
            taskLimit: getRandomInt(3, 10),
            isCollapsed: false,

            // Metadados
            isArchived: false
        };

        columns.push(column);
    }

    return columns;
}

// Gerar tarefas de kanban fictícias
function generateKanbanTasks(count = 15, boardIds = [], columnIds = []) {
    const tasks = [];
    const statuses = ['todo', 'in_progress', 'review', 'done'];
    const priorities = ['baixa', 'media', 'alta', 'critica'];

    for (let i = 0; i < count; i++) {
        const dueDate = Math.random() > 0.5 ? faker.date.future() : null;

        const task = {
            // Ownership
            userId: BASE_USER_ID,
            createdBy: BASE_USER_ID,
            teamId: BASE_TEAM_ID,

            // Relacionamentos
            boardId: boardIds.length > 0 ? getRandomElement(boardIds) : 'board_placeholder',
            columnId: columnIds.length > 0 ? getRandomElement(columnIds) : 'column_placeholder',

            // Informações básicas
            title: faker.lorem.sentence(4),
            description: faker.lorem.paragraph(),

            // Status e prioridade
            status: getRandomElement(statuses),
            priority: getRandomElement(priorities),

            // Datas
            dueDate: dueDate ? dueDate.toISOString() : undefined,
            completedAt: getRandomElement(statuses) === 'done' ? faker.date.recent().toISOString() : undefined,

            // Organização
            position: i,
            tags: Array.from({ length: getRandomInt(1, 3) }, () => faker.lorem.word()),

            // Atribuição
            assignedTo: Math.random() > 0.5 ? BASE_USER_ID : undefined,
            estimatedHours: getRandomInt(1, 40),
            actualHours: getRandomInt(1, 35),

            // Metadados
            isArchived: false
        };

        tasks.push(task);
    }

    return tasks;
}

// Função principal para executar o seed
async function seedData() {
    try {
        console.log("🚀 Iniciando seed de dados...");
        console.log("📋 Verificando variáveis de ambiente...");
        console.log("DATABASE_ID:", DATABASE_ID);
        console.log("BASE_USER_ID:", BASE_USER_ID);
        console.log("BASE_TEAM_ID:", BASE_TEAM_ID);
        console.log("CLIENTS_ID:", process.env.VITE_APPWRITE_CLIENTS_ID);
        console.log("NOTIFICATIONS_ID:", process.env.VITE_APPWRITE_NOTIFICATIONS_ID);

        // Arrays para armazenar IDs criados para relacionamentos
        const createdIds = {
            clients: [],
            boards: [],
            columns: [],
            events: []
        };

        // Criar clientes
        if (process.env.VITE_APPWRITE_CLIENTS_ID) {
            console.log("\n👥 Criando clientes...");
            const clients = generateClients(10);
            console.log(`Gerados ${clients.length} clientes`);

            for (const client of clients) {
                try {
                    console.log(`Criando cliente: ${client.name}`);
                    const result = await databases.createDocument(
                        DATABASE_ID,
                        process.env.VITE_APPWRITE_CLIENTS_ID,
                        ID.unique(),
                        client
                    );
                    createdIds.clients.push(result.$id);
                    console.log(`✅ Cliente criado com ID: ${result.$id}`);
                } catch (error) {
                    console.error(`❌ Erro ao criar cliente ${client.name}:`, error.message);
                }
            }
        }

        // Criar notificações
        if (process.env.VITE_APPWRITE_NOTIFICATIONS_ID) {
            console.log("\n🔔 Criando notificações...");
            const notifications = generateNotifications(20);
            console.log(`Geradas ${notifications.length} notificações`);

            for (const notification of notifications) {
                try {
                    console.log(`Criando notificação: ${notification.title}`);
                    const result = await databases.createDocument(
                        DATABASE_ID,
                        process.env.VITE_APPWRITE_NOTIFICATIONS_ID,
                        ID.unique(),
                        notification
                    );
                    console.log(`✅ Notificação criada com ID: ${result.$id}`);
                } catch (error) {
                    console.error(`❌ Erro ao criar notificação:`, error.message);
                }
            }
        }

        // Perfis públicos não precisam de seed (conforme solicitado)

        // Criar logs de atividade
        if (process.env.VITE_APPWRITE_ACTIVITY_LOGS_ID) {
            console.log("\n📝 Criando logs de atividade...");
            const logs = generateActivityLogs(30);
            console.log(`Gerados ${logs.length} logs de atividade`);

            for (const log of logs) {
                try {
                    const result = await databases.createDocument(
                        DATABASE_ID,
                        process.env.VITE_APPWRITE_ACTIVITY_LOGS_ID,
                        ID.unique(),
                        log
                    );
                    console.log(`✅ Log criado com ID: ${result.$id}`);
                } catch (error) {
                    console.error(`❌ Erro ao criar log:`, error.message);
                }
            }
        }

        // Team chats e mensagens não precisam de seed (conforme solicitado)

        // Criar eventos
        if (process.env.VITE_APPWRITE_EVENTS_ID) {
            console.log("\n📅 Criando eventos...");
            const events = generateEvents(10);
            console.log(`Gerados ${events.length} eventos`);

            for (const event of events) {
                try {
                    console.log(`Criando evento: ${event.title}`);
                    const result = await databases.createDocument(
                        DATABASE_ID,
                        process.env.VITE_APPWRITE_EVENTS_ID,
                        ID.unique(),
                        event
                    );
                    createdIds.events.push(result.$id);
                    console.log(`✅ Evento criado com ID: ${result.$id}`);
                } catch (error) {
                    console.error(`❌ Erro ao criar evento ${event.title}:`, error.message);
                }
            }
        }

        // Criar boards de kanban
        if (process.env.VITE_APPWRITE_KANBAN_BOARDS_ID) {
            console.log("\n📋 Criando boards de kanban...");
            const boards = generateKanbanBoards(2);
            console.log(`Gerados ${boards.length} boards`);

            for (const board of boards) {
                try {
                    console.log(`Criando board: ${board.title}`);
                    const result = await databases.createDocument(
                        DATABASE_ID,
                        process.env.VITE_APPWRITE_KANBAN_BOARDS_ID,
                        ID.unique(),
                        board
                    );
                    createdIds.boards.push(result.$id);
                    console.log(`✅ Board criado com ID: ${result.$id}`);
                } catch (error) {
                    console.error(`❌ Erro ao criar board ${board.title}:`, error.message);
                }
            }
        }

        // Criar colunas de kanban (apenas se há boards criados)
        if (process.env.VITE_APPWRITE_KANBAN_COLUMNS_ID && createdIds.boards.length > 0) {
            console.log("\n📊 Criando colunas de kanban...");
            const columns = generateKanbanColumns(4, createdIds.boards);
            console.log(`Geradas ${columns.length} colunas`);

            for (const column of columns) {
                try {
                    console.log(`Criando coluna: ${column.title}`);
                    const result = await databases.createDocument(
                        DATABASE_ID,
                        process.env.VITE_APPWRITE_KANBAN_COLUMNS_ID,
                        ID.unique(),
                        column
                    );
                    createdIds.columns.push(result.$id);
                    console.log(`✅ Coluna criada com ID: ${result.$id}`);
                } catch (error) {
                    console.error(`❌ Erro ao criar coluna ${column.title}:`, error.message);
                }
            }
        }

        // Criar tarefas de kanban (apenas se há boards e colunas criados)
        if (process.env.VITE_APPWRITE_KANBAN_TASKS_ID && createdIds.boards.length > 0 && createdIds.columns.length > 0) {
            console.log("\n✅ Criando tarefas de kanban...");
            const tasks = generateKanbanTasks(15, createdIds.boards, createdIds.columns);
            console.log(`Geradas ${tasks.length} tarefas`);

            for (const task of tasks) {
                try {
                    console.log(`Criando tarefa: ${task.title}`);
                    const result = await databases.createDocument(
                        DATABASE_ID,
                        process.env.VITE_APPWRITE_KANBAN_TASKS_ID,
                        ID.unique(),
                        task
                    );
                    console.log(`✅ Tarefa criada com ID: ${result.$id}`);
                } catch (error) {
                    console.error(`❌ Erro ao criar tarefa ${task.title}:`, error.message);
                }
            }
        }

        console.log("\n🎉 Seed de dados concluído com sucesso!");
        console.log(`📊 Resumo:`);
        console.log(`   - ${createdIds.clients.length} clientes criados`);
        console.log(`   - ${createdIds.events.length} eventos criados`);
        console.log(`   - ${createdIds.boards.length} boards kanban criados`);
        console.log(`   - ${createdIds.columns.length} colunas kanban criadas`);
        console.log(`   - Notificações e logs de atividade criados conforme disponibilidade`);
        console.log(`   - Todos os dados usam userId: ${BASE_USER_ID}`);
        console.log(`   - Team ID base: ${BASE_TEAM_ID}`);

    } catch (error) {
        console.error("❌ Erro ao executar seed:", error);
        process.exit(1);
    }
}

// Verificar se as variáveis de ambiente estão definidas
console.log("🔍 Verificando variáveis de ambiente...");
console.log("ENDPOINT:", process.env.APPWRITE_ENDPOINT);
console.log("PROJECT_ID:", process.env.APPWRITE_PROJECT_ID);
console.log("API_KEY:", process.env.APPWRITE_API_KEY ? "***" : "não definido");
console.log("DATABASE_ID:", process.env.APPWRITE_DATABASE_ID);

// Executar se chamado diretamente
if (require.main === module) {
    seedData();
}

module.exports = { seedData };
