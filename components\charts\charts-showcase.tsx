"use client"

import { AreaChartGradient } from './area-chart-gradient'
import { AreaChartInteractive } from './area-chart-interactive'
import { BarChartMultiple } from './bar-chart-multiple'
import { PieChartDonut } from './pie-chart-donut'

// Dados de exemplo para demonstração
const sampleData = [
  { month: "Janeiro", desktop: 186, mobile: 80 },
  { month: "Fevereiro", desktop: 305, mobile: 200 },
  { month: "Março", desktop: 237, mobile: 120 },
  { month: "Abril", desktop: 173, mobile: 190 },
  { month: "Maio", desktop: 209, mobile: 130 },
  { month: "Junho", desktop: 214, mobile: 140 },
]

const pieData = [
  { browser: "chrome", visitors: 275, fill: "var(--color-chrome)" },
  { browser: "safari", visitors: 200, fill: "var(--color-safari)" },
  { browser: "firefox", visitors: 287, fill: "var(--color-firefox)" },
  { browser: "edge", visitors: 173, fill: "var(--color-edge)" },
  { browser: "other", visitors: 190, fill: "var(--color-other)" },
]

const interactiveData = [
  { date: "2024-04-01", desktop: 222, mobile: 150 },
  { date: "2024-04-02", desktop: 97, mobile: 180 },
  { date: "2024-04-03", desktop: 167, mobile: 120 },
  { date: "2024-04-04", desktop: 242, mobile: 260 },
  { date: "2024-04-05", desktop: 373, mobile: 290 },
  { date: "2024-04-06", desktop: 301, mobile: 340 },
  { date: "2024-04-07", desktop: 245, mobile: 180 },
  { date: "2024-04-08", desktop: 409, mobile: 320 },
  { date: "2024-04-09", desktop: 59, mobile: 110 },
  { date: "2024-04-10", desktop: 261, mobile: 190 },
  { date: "2024-04-11", desktop: 327, mobile: 350 },
  { date: "2024-04-12", desktop: 292, mobile: 210 },
  { date: "2024-04-13", desktop: 342, mobile: 380 },
  { date: "2024-04-14", desktop: 137, mobile: 220 },
  { date: "2024-04-15", desktop: 120, mobile: 170 },
  { date: "2024-04-16", desktop: 138, mobile: 190 },
  { date: "2024-04-17", desktop: 446, mobile: 360 },
  { date: "2024-04-18", desktop: 364, mobile: 410 },
  { date: "2024-04-19", desktop: 243, mobile: 180 },
  { date: "2024-04-20", desktop: 89, mobile: 150 },
]

interface ChartsShowcaseProps {
  className?: string
}

export function ChartsShowcase({ className }: ChartsShowcaseProps) {
  return (
    <div className={className}>
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold tracking-tight mb-2">Charts Modernos</h2>
          <p className="text-muted-foreground">
            Componentes de gráficos baseados nos padrões do Shadcn UI com gradientes, interatividade e design moderno.
          </p>
        </div>

        <div className="grid gap-6">
          {/* Gráfico de Área Interativo */}
          <AreaChartInteractive 
            data={interactiveData}
            title="Gráfico de Área Interativo"
            description="Selecione diferentes períodos para visualizar os dados"
          />

          {/* Grid com 2 colunas */}
          <div className="grid gap-6 md:grid-cols-2">
            {/* Gráfico de Área com Gradiente */}
            <AreaChartGradient 
              data={sampleData}
              title="Área com Gradiente"
              description="Visualização com gradientes suaves"
              footerText="Tendência positiva"
            />

            {/* Gráfico de Pizza Donut */}
            <PieChartDonut 
              data={pieData}
              title="Pizza Donut"
              description="Distribuição por categoria"
              footerText="Dados atualizados"
            />
          </div>

          {/* Gráfico de Barras Múltiplo */}
          <BarChartMultiple 
            data={sampleData}
            title="Barras Múltiplas"
            description="Comparação entre diferentes métricas"
            footerText="Performance melhorada"
          />
        </div>

        <div className="mt-8 p-4 bg-muted/50 rounded-lg">
          <h3 className="font-semibold mb-2">Características dos Novos Charts:</h3>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• Gradientes suaves para melhor visualização</li>
            <li>• Interatividade com seletores de período</li>
            <li>• Footers informativos com ícones</li>
            <li>• Tooltips customizados</li>
            <li>• Design consistente com o sistema Shadcn</li>
            <li>• Responsividade automática</li>
            <li>• Acessibilidade melhorada</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
