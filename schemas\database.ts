/**
 * Database type definitions for Appwrite collections
 * Provides comprehensive type safety for all database operations
 */

import type { Models } from 'appwrite';
import type {
  Team,
  TeamMembership,
  CreateTeamData,
  UpdateTeamData,
  InviteMemberData,
  UpdateMembershipData,
  TeamsListResponse,
  MembershipsListResponse
} from './teams';
import type {
  Client,
  ClientStatus,
  ClientType,
  ClientPriority,
  ClientAddress,
  UpdateClientData,
  ClientFormData,
} from './clients';

// Re-export team types for easier importing (Appwrite Teams nativo)
export type {
  Team,
  TeamMembership,
  CreateTeamData,
  UpdateTeamData,
  InviteMemberData,
  UpdateMembershipData,
  TeamsListResponse,
  MembershipsListResponse
};

// Re-export client types for easier importing
export type {
  Client,
  ClientStatus,
  ClientType,
  ClientPriority,
  ClientAddress,
  UpdateClientData,
  ClientFormData,
};

// Re-export websocket types for database hooks
// Removido: export type { DatabaseRealtimeEvent, RealtimeMessage } from './websocket';
// Tipos de realtime agora estão em app/lib/realtime/types.ts

// Base document interface
export interface BaseDocument extends Models.Document {
  $id: string;
  $collectionId: string;
  $databaseId: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  isDeleted?: boolean; // Soft delete field
}

// Public Profile types (optional collection for public profiles)
export interface PublicProfile extends BaseDocument {
  userId: string; // Reference to Appwrite user ID
  displayName: string;
  avatar?: string;
  bio?: string;
  isPublic: boolean;
  socialLinks?: SocialLinks;
  skills?: string[];
  location?: string;
  website?: string;
  joinedAt: string;
  lastActiveAt?: string;
}

export type UserRole = 'admin' | 'moderator' | 'user' | 'guest';
export type UserStatus = 'active' | 'suspended' | 'pending' | 'inactive';

export interface SocialLinks {
  twitter?: string;
  linkedin?: string;
  github?: string;
  instagram?: string;
  facebook?: string;
  youtube?: string;
  website?: string;
}

// User preferences (stored in Appwrite account preferences)
export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  timezone: string;
  notifications: NotificationPreferences;
  privacy: PrivacySettings;
}

export interface NotificationPreferences {
  email: boolean;
  push: boolean;
  sms: boolean;
  marketing: boolean;
  updates: boolean;
}

export interface PrivacySettings {
  profileVisible: boolean;
  activityVisible: boolean;
  contactInfoVisible: boolean;
}

// Product-related types
export interface Product extends BaseDocument {
  userId: string;
  status: ProductStatus;
  name: string;
  slug: string;
  description?: string;
  shortDescription?: string;
  categoryId?: string;
  brand?: string;
  sku: string;
  price: number;
  salePrice?: number;
  cost?: number;
  currency: Currency;
  inventory: ProductInventory;
  weight?: number;
  dimensions?: ProductDimensions;
  images: ProductImage[];
  hasVariants: boolean;
  variants?: ProductVariant[];
  tags: string[];
  seoTitle?: string;
  seoDescription?: string;
  featured: boolean;
  trending: boolean;
}

export type ProductStatus = 'active' | 'inactive' | 'draft' | 'archived';
export type Currency = 'USD' | 'EUR' | 'GBP' | 'BRL';

export interface ProductInventory {
  trackQuantity: boolean;
  quantity: number;
  lowStockThreshold: number;
  allowBackorders: boolean;
  stockStatus: 'in_stock' | 'out_of_stock' | 'on_backorder';
}

export interface ProductDimensions {
  length: number;
  width: number;
  height: number;
  unit: 'cm' | 'in';
}

export interface ProductImage {
  id: string;
  url: string;
  alt: string;
  isPrimary: boolean;
  sortOrder: number;
}

export interface ProductVariant {
  id: string;
  name: string;
  sku: string;
  price: number;
  inventory: ProductInventory;
  attributes: Record<string, string>;
}


export type OrderStatus = 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
export type PaymentStatus = 'pending' | 'paid' | 'failed' | 'refunded' | 'partially_refunded';

export interface OrderItem {
  productId: string;
  variantId?: string;
  name: string;
  sku: string;
  quantity: number;
  price: number;
  total: number;
}

export interface Address {
  firstName: string;
  lastName: string;
  company?: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone?: string;
}

export interface PaymentMethod {
  type: 'credit_card' | 'debit_card' | 'paypal' | 'stripe' | 'bank_transfer';
  last4?: string;
  brand?: string;
  expiryMonth?: number;
  expiryYear?: number;
}

// Review types
export interface Review extends BaseDocument {
  userId: string;
  productId: string;
  rating: number;
  title: string;
  content: string;
  verified: boolean;
  helpful: number;
  reported: number;
  status: ReviewStatus;
  images?: string[];
}

export type ReviewStatus = 'pending' | 'approved' | 'rejected' | 'spam';

// Notification types
export interface Notification extends BaseDocument {
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: Record<string, unknown>;
  read: boolean;
  readAt?: string;
  actionUrl?: string;
  priority: NotificationPriority;
  expiresAt?: string;
}

export type NotificationType = 'info' | 'success' | 'warning' | 'error' | 'order' | 'promotion' | 'system';
export type NotificationPriority = 'low' | 'normal' | 'high' | 'urgent';

// Activity Log types - Enhanced for comprehensive logging
export interface ActivityLog extends BaseDocument {
  userId: string;
  createdBy: string;
  teamId?: string;
  action: string;
  resource: string;
  resourceId?: string;
  details?: string; // JSON string in database
  ipAddress?: string;
  userAgent?: string;
  // Additional fields from activities.ts
  type?: string;
  title?: string;
  description?: string;
  priority?: string;
  tags?: string[];
  category?: string;
  metadata?: string; // JSON string in database
}

// Campaign types
export interface Campaign extends BaseDocument {
  name: string;
  type: CampaignType;
  status: CampaignStatus;
  startDate: string;
  endDate: string;
  targetAudience: TargetAudience;
  content: CampaignContent;
  metrics: CampaignMetrics;
}

export type CampaignType = 'email' | 'push' | 'sms' | 'banner' | 'popup';
export type CampaignStatus = 'draft' | 'scheduled' | 'active' | 'paused' | 'completed' | 'cancelled';

export interface TargetAudience {
  userRoles: UserRole[];
  userStatus: UserStatus[];
  tags: string[];
  customFilters: Record<string, unknown>;
}

export interface CampaignContent {
  subject?: string;
  title: string;
  message: string;
  imageUrl?: string;
  actionUrl?: string;
  actionText?: string;
}

export interface CampaignMetrics {
  sent: number;
  delivered: number;
  opened: number;
  clicked: number;
  converted: number;
  unsubscribed: number;
}

// Coupon types
export interface Coupon extends BaseDocument {
  code: string;
  name: string;
  description?: string;
  type: CouponType;
  value: number;
  minimumAmount?: number;
  maximumDiscount?: number;
  usageLimit?: number;
  usageCount: number;
  userLimit?: number;
  startDate: string;
  endDate: string;
  isActive: boolean;
  applicableProducts: string[];
  applicableCategories: string[];
}

export type CouponType = 'percentage' | 'fixed_amount' | 'free_shipping';

// Generic query types
export interface QueryOptions {
  limit?: number;
  offset?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
  filters?: Record<string, unknown>;
  search?: string;
  [key: string]: unknown; // Index signature for compatibility
}

export interface PaginatedResponse<T> {
  documents: T[];
  total: number;
  limit: number;
  offset: number;
  hasMore: boolean;
}

// Collection names type
export type CollectionName =
  | 'public_profiles'
  | 'notifications'
  | 'activity_logs'
  | 'clients'
  | 'chat_messages'
  | 'team_chats';

// Document type mapping
export interface DocumentTypeMap {
  public_profiles: PublicProfile;
  notifications: Notification;
  activity_logs: ActivityLog;
  clients: Client;
  chat_messages: import('./chat').ChatMessage;
  team_chats: import('./chat').TeamChat;
}

// Validation schemas for create/update operations
export interface CreatePublicProfileData {
  userId: string;
  displayName: string;
  avatar?: string;
  bio?: string;
  isPublic?: boolean;
  socialLinks?: Partial<SocialLinks>;
  skills?: string[];
  location?: string;
  website?: string;
  [key: string]: unknown; // Index signature for compatibility
}

export interface UpdatePublicProfileData {
  displayName?: string;
  avatar?: string;
  bio?: string;
  isPublic?: boolean;
  socialLinks?: Partial<SocialLinks>;
  skills?: string[];
  location?: string;
  website?: string;
}

// Chat-related validation schemas are in types/chat.ts
