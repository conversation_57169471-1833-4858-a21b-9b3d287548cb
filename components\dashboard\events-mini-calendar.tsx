"use client"

import { useState } from "react"
import { Calendar } from "@/components/ui/calendar"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CalendarIcon, Clock } from "lucide-react"
import { useEvents } from "@/hooks/api/use-events"
import { format, isSameDay, addDays } from "date-fns"
import { ptBR } from "date-fns/locale"

// Dados padrão para quando não há eventos reais
const createDefaultEvents = () => {
  const today = new Date()
  return [
    {
      $id: 'default-1',
      title: 'Reunião de Equipe',
      startDate: addDays(today, 1).toISOString(),
    },
    {
      $id: 'default-2',
      title: 'Apresentação Cliente',
      startDate: addDays(today, 3).toISOString(),
    },
    {
      $id: 'default-3',
      title: 'Workshop Técnico',
      startDate: addDays(today, 5).toISOString(),
    },
  ]
}

export function EventsMiniCalendar() {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date())
  const { data: events } = useEvents()

  // Usar dados reais ou padrão
  const hasRealData = events && events.length > 0
  const displayEvents = hasRealData ? events : createDefaultEvents()

  // Processar eventos para o calendário
  const eventDates = displayEvents.map(event => new Date(event.startDate))

  // Eventos do dia selecionado
  const selectedDateEvents = displayEvents.filter(event =>
    selectedDate && isSameDay(new Date(event.startDate), selectedDate)
  )

  // Próximos eventos (próximos 7 dias)
  const upcomingEvents = displayEvents.filter(event => {
    const eventDate = new Date(event.startDate)
    const today = new Date()
    const nextWeek = new Date()
    nextWeek.setDate(today.getDate() + 7)
    return eventDate >= today && eventDate <= nextWeek
  }).slice(0, 3)

  // Modifiers para destacar dias com eventos
  const modifiers = {
    hasEvents: eventDates,
  }

  const modifiersStyles = {
    hasEvents: {
      backgroundColor: 'hsl(var(--primary))',
      color: 'hsl(var(--primary-foreground))',
      borderRadius: '6px',
    },
  }

  return (
    <Card className="flex flex-col">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2">
          <CalendarIcon className="h-4 w-4" />
          Eventos
        </CardTitle>
        <CardDescription>
          Calendário com próximos eventos
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Mini Calendar */}
        <Calendar
          mode="single"
          selected={selectedDate}
          onSelect={setSelectedDate}
          modifiers={modifiers}
          modifiersStyles={modifiersStyles}
          className="w-full"
          locale={ptBR}
        />

        {/* Eventos do dia selecionado */}
        {selectedDate && selectedDateEvents.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">
              {format(selectedDate, "d 'de' MMMM", { locale: ptBR })}
            </h4>
            <div className="space-y-1">
              {selectedDateEvents.map(event => (
                <div key={event.$id} className="flex items-center gap-2 text-xs">
                  <Clock className="h-3 w-3 text-muted-foreground" />
                  <span className="truncate">{event.title}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Próximos eventos */}
        {upcomingEvents.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Próximos Eventos</h4>
            <div className="space-y-2">
              {upcomingEvents.map(event => (
                <div key={event.$id} className="flex items-center justify-between gap-2">
                  <span className="text-xs truncate">{event.title}</span>
                  <Badge variant="outline" className="text-xs">
                    {format(new Date(event.startDate), "dd/MM", { locale: ptBR })}
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Estatísticas */}
        <div className="pt-2 border-t">
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Total de eventos</span>
            <span>{displayEvents.length}</span>
          </div>
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Próximos 7 dias</span>
            <span>{upcomingEvents.length}</span>
          </div>
          {!hasRealData && (
            <div className="text-xs text-muted-foreground mt-1">
              Dados de exemplo
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
