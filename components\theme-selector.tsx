import { useThemeConfig } from "./active-theme";
import { Label } from "./ui/label";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectSeparator,
  SelectTrigger,
  SelectValue,
} from "./ui/select";

const DEFAULT_THEMES = [
  {
    name: "Default",
    value: "default",
    description: "Tema padrão neutro",
  },
  {
    name: "Blue",
    value: "blue",
    description: "Tema azul profissional",
  },
  {
    name: "Green",
    value: "green",
    description: "Tema verde natural",
  },
  {
    name: "Amber",
    value: "amber",
    description: "Tema âmbar caloroso",
  },
  {
    name: "E-commerce",
    value: "ecommerce",
    description: "Tema moderno para e-commerce",
  },
];

const CREATIVE_THEMES = [
  {
    name: "Candyland",
    value: "candyland",
    description: "Tema colorido e vibrante",
  },
  {
    name: "Cyberpunk",
    value: "cyberpunk",
    description: "Tema futurista com cores neon",
  },
  {
    name: "Sunset",
    value: "sunset",
    description: "Tema com cores quentes de pôr do sol",
  },
  {
    name: "Ocean",
    value: "ocean",
    description: "Tema azul/verde oceânico",
  },
  {
    name: "Nature",
    value: "nature",
    description: "Tema verde natural e orgânico",
  },
];

const SCALED_THEMES = [
  {
    name: "Default Compacto",
    value: "default-scaled",
    description: "Tema padrão com interface compacta",
  },
  {
    name: "Blue Compacto",
    value: "blue-scaled",
    description: "Tema azul com interface compacta",
  },
];

const MONO_THEMES = [
  {
    name: "Mono",
    value: "mono-scaled",
    description: "Tema minimalista monocromático",
  },
];

export function ThemeSelector() {
  const { activeTheme, setActiveTheme } = useThemeConfig();

  const allThemes = [
    ...DEFAULT_THEMES,
    ...CREATIVE_THEMES,
    ...SCALED_THEMES,
    ...MONO_THEMES,
  ];

  const currentTheme = allThemes.find(theme => theme.value === activeTheme);

  return (
    <div className="space-y-2">
      <Label htmlFor="theme-selector">Tema da Interface</Label>
      <Select value={activeTheme} onValueChange={setActiveTheme}>
        <SelectTrigger id="theme-selector">
          <SelectValue placeholder="Selecione um tema">
            {currentTheme?.name || "Tema personalizado"}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectLabel>Temas Padrão</SelectLabel>
            {DEFAULT_THEMES.map((theme) => (
              <SelectItem key={theme.value} value={theme.value}>
                <div className="flex flex-col">
                  <span>{theme.name}</span>
                  <span className="text-xs text-muted-foreground">
                    {theme.description}
                  </span>
                </div>
              </SelectItem>
            ))}
          </SelectGroup>

          <SelectSeparator />

          <SelectGroup>
            <SelectLabel>Temas Criativos</SelectLabel>
            {CREATIVE_THEMES.map((theme) => (
              <SelectItem key={theme.value} value={theme.value}>
                <div className="flex flex-col">
                  <span>{theme.name}</span>
                  <span className="text-xs text-muted-foreground">
                    {theme.description}
                  </span>
                </div>
              </SelectItem>
            ))}
          </SelectGroup>

          <SelectSeparator />

          <SelectGroup>
            <SelectLabel>Temas Compactos</SelectLabel>
            {SCALED_THEMES.map((theme) => (
              <SelectItem key={theme.value} value={theme.value}>
                <div className="flex flex-col">
                  <span>{theme.name}</span>
                  <span className="text-xs text-muted-foreground">
                    {theme.description}
                  </span>
                </div>
              </SelectItem>
            ))}
          </SelectGroup>

          <SelectSeparator />

          <SelectGroup>
            <SelectLabel>Temas Especiais</SelectLabel>
            {MONO_THEMES.map((theme) => (
              <SelectItem key={theme.value} value={theme.value}>
                <div className="flex flex-col">
                  <span>{theme.name}</span>
                  <span className="text-xs text-muted-foreground">
                    {theme.description}
                  </span>
                </div>
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
  );
}
