import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import Link from 'next/link';
import {
  Clock,
  DollarSign,
  Rocket,
  Shield,
  Users,
  Zap,
  CheckCircle,
  TrendingUp,
  Code,
  Heart
} from 'lucide-react';

const benefits = [
  {
    icon: Clock,
    title: 'Economize 3-6 Meses',
    description: 'Não perca tempo configurando autenticação, dashboard e infraestrutura básica.',
    stats: '90% menos tempo de setup',
    color: 'bg-blue-500'
  },
  {
    icon: DollarSign,
    title: 'Reduza Custos de Desenvolvimento',
    description: 'Evite contratar desenvolvedores para funcionalidades que já estão prontas.',
    stats: 'Até R$ 50.000 em economia',
    color: 'bg-green-500'
  },
  {
    icon: Rocket,
    title: '<PERSON>',
    description: 'Foque no seu produto principal enquanto a base já está construída.',
    stats: 'Time-to-market 70% menor',
    color: 'bg-purple-500'
  },
  {
    icon: Shield,
    title: 'Segurança Garantida',
    description: 'Implementações testadas e seguindo as melhores práticas de segurança.',
    stats: 'Conformidade LGPD/GDPR',
    color: 'bg-red-500'
  }
];

const features = [
  'Autenticação completa com OAuth',
  'Sistema de teams e permissões',
  'Dashboard administrativo',
  'PWA com notificações push',
  'Cache local-first inteligente',
  'Chat em tempo real',
  'Cloud functions serverless',
  'UI/UX moderna e responsiva',
  'TypeScript 100% tipado',
  'Documentação completa',
  'Scripts de setup automatizados',
  'Suporte técnico incluído'
];

export function BenefitsSection() {
  return (
    <section className="py-16 md:py-24 bg-muted/30">
      <div className="container mx-auto px-4 max-w-7xl">
        {/* Header */}
        <div className="text-center mb-16">
          <Badge variant="outline" className="mb-4">
            Benefícios
          </Badge>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
            Por que escolher nosso template?
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
            Mais de 1000+ desenvolvedores já economizaram tempo e dinheiro usando nosso template.
            Veja como você também pode acelerar seu projeto.
          </p>
        </div>

        {/* Benefits Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {benefits.map((benefit, index) => {
            const Icon = benefit.icon;
            return (
              <Card key={index} className="text-center group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                <CardHeader>
                  <div className={`w-16 h-16 ${benefit.color} rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}>
                    <Icon className="w-8 h-8 text-white" />
                  </div>
                  <CardTitle className="text-xl group-hover:text-primary transition-colors">
                    {benefit.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base mb-4">
                    {benefit.description}
                  </CardDescription>
                  <Badge variant="secondary" className="text-sm font-medium">
                    {benefit.stats}
                  </Badge>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Features Comparison */}
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left side - Features list */}
          <div>
            <h3 className="text-2xl md:text-3xl font-bold mb-6">
              O que você recebe no template
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center gap-3">
                  <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                  <span className="text-sm">{feature}</span>
                </div>
              ))}
            </div>

            <div className="mt-8 p-6 bg-primary/5 rounded-lg border border-primary/20">
              <div className="flex items-center gap-3 mb-3">
                <Heart className="w-6 h-6 text-primary" />
                <h4 className="font-semibold text-primary">Garantia de Satisfação</h4>
              </div>
              <p className="text-sm text-muted-foreground">
                Se você não ficar satisfeito com o template nos primeiros 30 dias,
                devolvemos 100% do seu dinheiro. Sem perguntas, sem complicações.
              </p>
            </div>
          </div>

          {/* Right side - Stats */}
          <div className="space-y-6">
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-lg font-semibold">Tempo de Desenvolvimento</h4>
                <TrendingUp className="w-6 h-6 text-green-500" />
              </div>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Sem Template</span>
                    <span className="text-red-500">6 meses</span>
                  </div>
                  <div className="w-full bg-red-100 rounded-full h-2">
                    <div className="bg-red-500 h-2 rounded-full w-full"></div>
                  </div>
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Com Template</span>
                    <span className="text-green-500">2 semanas</span>
                  </div>
                  <div className="w-full bg-green-100 rounded-full h-2">
                    <div className="bg-green-500 h-2 rounded-full w-1/6"></div>
                  </div>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-lg font-semibold">Custo de Desenvolvimento</h4>
                <DollarSign className="w-6 h-6 text-green-500" />
              </div>
              <div className="space-y-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-red-500 mb-1">R$ 80.000</div>
                  <div className="text-sm text-muted-foreground">Desenvolvimento do zero</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-500 mb-1">R$ 297</div>
                  <div className="text-sm text-muted-foreground">Com nosso template</div>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="text-lg font-semibold text-green-700">
                    Economia de 99.6%
                  </div>
                </div>
              </div>
            </Card>

            <div className="text-center">
              <Button size="lg" asChild className="w-full">
                <Link href="/register">
                  <Zap className="w-5 h-5 mr-2" />
                  Começar Agora
                </Link>
              </Button>
              <p className="text-xs text-muted-foreground mt-2">
                Setup completo em menos de 5 minutos
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
