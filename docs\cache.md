# 🗂️ Sistema de Cache Local-First

Este documento explica o sistema de cache avançado do template, que implementa uma estratégia local-first usando IndexedDB e TanStack Query.

## 📋 Visão Geral

O sistema de cache é projetado para:

- **Performance** - Dados servidos instantaneamente do cache local
- **Offline-first** - Funciona sem conexão com internet
- **Sincronização** - Atualiza automaticamente com o servidor
- **Type Safety** - TypeScript completo em toda a stack
- **Simplicidade** - API limpa e fácil de usar

## 🏗️ Arquitetura

### Camadas do Sistema
```
┌─────────────────┐
│   React Query   │ ← Estado e invalidação
├─────────────────┤
│   Cache Hooks   │ ← Hooks customizados
├─────────────────┤
│   Unified Cache │ ← Lógica de cache unificada
├─────────────────┤
│   IndexedDB     │ ← Armazenamento local
└─────────────────┘
```

### Estratégia Local-First
1. **Verificar IndexedDB** primeiro
2. **Servir dados locais** se disponíveis
3. **Buscar do servidor** apenas se necessário
4. **Sincronizar** em background
5. **Invalidar** quando necessário

## ⚙️ Configuração

### Cache Config
```typescript
interface SimpleCacheConfig {
  enabled: boolean;
  defaultTTL: number; // em milissegundos
  dbName: string;
  version: number;
}

const CACHE_CONFIG: SimpleCacheConfig = {
  enabled: import.meta.env.NEXT_PUBLIC_CACHE_ENABLED !== 'false',
  defaultTTL: 5 * 60 * 1000, // 5 minutos
  dbName: 'ReactQueryCache',
  version: 1,
};
```

### Variáveis de Ambiente
```env
# Cache configuration
NEXT_PUBLIC_CACHE_ENABLED=true
NEXT_PUBLIC_CACHE_DB_NAME=ReactQueryCache
```

## 🔧 Implementação

### Cache Item Structure
```typescript
interface CacheItem<T = any> {
  data: T;
  timestamp: number;
  ttl: number;
  queryKey: readonly unknown[];
}
```

### IndexedDB Schema
```typescript
// Stores criados automaticamente:
// - cache: dados do React Query
// - clients: dados de clientes
// - notifications: notificações
// - activity_logs: logs de atividade
// - cache_metadata: metadados do cache
```

## 🪝 Hooks de Cache

### useSimplifiedCache
Hook principal para estratégia local-first:

```typescript
interface SimplifiedCacheOptions<T> {
  queryKey: (string | undefined)[];
  collection: string;
  userId?: string;
  fetchFn: () => Promise<T[]>;
  enabled?: boolean;
  staleTime?: number;
}

function useSimplifiedCache<T>({
  queryKey,
  collection,
  userId,
  fetchFn,
  enabled = true
}: SimplifiedCacheOptions<T>) {
  return useQuery({
    queryKey,
    queryFn: async () => {
      // 1. Verificar IndexedDB
      const hasData = await hasDataInIndexedDB(collection, userId);
      
      if (hasData) {
        // 2. Servir dados locais
        const cachedData = await getFromIndexedDB<T>(collection, userId);
        console.log(`🗄️ Dados de ${collection} carregados do cache local`);
        return cachedData;
      }
      
      // 3. Buscar do servidor
      const serverData = await fetchFn();
      
      // 4. Salvar no cache
      await saveToIndexedDB(collection, serverData, { collection, userId });
      
      return serverData;
    },
    enabled,
    staleTime: 0, // Sempre considerar stale para permitir background updates
  });
}
```

### useUnifiedCache
Hook avançado com controle granular:

```typescript
interface UnifiedCacheOptions<T> {
  queryKey: readonly unknown[];
  queryFn: () => Promise<T>;
  cacheTTL?: number;
  enableLocalCache?: boolean;
  onCacheHit?: (data: T) => void;
  onCacheMiss?: () => void;
}

function useUnifiedCache<T>(options: UnifiedCacheOptions<T>) {
  const query = useQuery({
    queryKey: options.queryKey,
    queryFn: async () => {
      // Tentar cache local primeiro
      const cachedData = await getCache<T>(options.queryKey);
      
      if (cachedData !== null) {
        options.onCacheHit?.(cachedData);
        
        // Background validation
        options.queryFn().then(async (freshData) => {
          await setCache(options.queryKey, freshData, options.cacheTTL);
        });
        
        return cachedData;
      }
      
      // Buscar do servidor
      const data = await options.queryFn();
      await setCache(options.queryKey, data, options.cacheTTL);
      
      return data;
    }
  });
  
  return {
    ...query,
    invalidateCache: () => deleteCache(options.queryKey),
    clearLocalCache: () => cacheManager.clear()
  };
}
```

## 🔄 Sincronização

### Sync After Mutation
Sincroniza automaticamente após operações CRUD:

```typescript
async function syncAfterMutation<T>(
  collection: string,
  operation: 'create' | 'update' | 'delete',
  data: T | string,
  userId?: string
) {
  try {
    if (operation === 'delete') {
      await removeFromIndexedDB(collection, data as string);
    } else {
      await saveToIndexedDB(collection, data as T, { collection, userId });
    }
    
    console.log(`✅ Sincronização ${operation} concluída para ${collection}`);
  } catch (error) {
    console.error(`❌ Erro na sincronização:`, error);
  }
}
```

### Background Sync
Sincroniza dados atualizados em background:

```typescript
async function syncUpdatedDataFromServer<T>(
  collection: string,
  userId?: string,
  onDataUpdated?: (activeData: T[], deletedData: T[]) => void
) {
  // Verificar se IndexedDB tem dados
  const hasData = await hasDataInIndexedDB(collection, userId);
  if (!hasData) return;
  
  // Buscar timestamp da última sincronização
  const lastTimestamp = await getLastSyncTimestamp(collection, userId);
  
  // Buscar dados atualizados (incluindo soft deleted)
  const updatedData = await fetchUpdatedDataFromServer<T>(
    collection,
    lastTimestamp,
    userId
  );
  
  if (updatedData.length > 0) {
    // Separar dados ativos e deletados
    const activeDocuments = updatedData.filter(doc => !doc.isDeleted);
    const deletedDocuments = updatedData.filter(doc => doc.isDeleted);
    
    // Atualizar cache local
    for (const doc of activeDocuments) {
      await saveToIndexedDB(collection, doc, { collection, userId });
    }
    
    // Remover documentos deletados
    for (const doc of deletedDocuments) {
      await removeFromIndexedDB(collection, doc.$id);
    }
    
    onDataUpdated?.(activeDocuments, deletedDocuments);
  }
}
```

## 📊 Operações CRUD

### Create
```typescript
const createMutation = useMutation({
  mutationFn: async (data: CreateClientData) => {
    const result = await createClient(data);
    
    // Sincronizar com cache local
    await syncAfterMutation('clients', 'create', result, userId);
    
    return result;
  },
  onSuccess: () => {
    // Invalidar queries relacionadas
    queryClient.invalidateQueries({ queryKey: ['clients'] });
    
    // Sincronizar dados atualizados em background
    syncUpdatedDataFromServer('clients', userId);
  }
});
```

### Read
```typescript
const { data: clients, isLoading } = useSimplifiedCache({
  queryKey: ['clients', userId],
  collection: 'clients',
  userId,
  fetchFn: () => listClients({ userId }),
  enabled: !!userId
});
```

### Update
```typescript
const updateMutation = useMutation({
  mutationFn: async ({ id, data }: { id: string; data: UpdateClientData }) => {
    const result = await updateClient(id, data);
    
    // Sincronizar com cache local
    await syncAfterMutation('clients', 'update', result, userId);
    
    return result;
  },
  onSuccess: () => {
    queryClient.invalidateQueries({ queryKey: ['clients'] });
    syncUpdatedDataFromServer('clients', userId);
  }
});
```

### Delete (Soft Delete)
```typescript
const deleteMutation = useMutation({
  mutationFn: async (id: string) => {
    const result = await softDeleteClient(id);
    
    // Remover do cache local
    await syncAfterMutation('clients', 'delete', id, userId);
    
    return result;
  },
  onSuccess: () => {
    queryClient.invalidateQueries({ queryKey: ['clients'] });
  }
});
```

## 🔍 Debugging e Monitoramento

### Cache Status
```typescript
// Verificar se cache está habilitado
const isCacheEnabled = () => {
  return typeof window !== 'undefined' && CACHE_CONFIG.enabled;
};

// Obter estatísticas do cache
const getCacheStats = async () => {
  const db = await getIndexedDB();
  const stats = {};
  
  for (const storeName of db.objectStoreNames) {
    const count = await dbManager.count(storeName);
    stats[storeName] = count;
  }
  
  return stats;
};
```

### Logs de Cache
```typescript
// Logs automáticos em desenvolvimento
if (import.meta.env.DEV) {
  console.log('🗄️ Dados carregados do cache local');
  console.log('🔄 Sincronizando com servidor...');
  console.log('✅ Cache atualizado com sucesso');
}
```

## 🛠️ Utilitários

### Cache Manager
```typescript
class CacheManager {
  async get<T>(key: string): Promise<CacheItem<T> | null> {
    // Implementação IndexedDB
  }
  
  async set<T>(key: string, value: CacheItem<T>): Promise<void> {
    // Implementação IndexedDB
  }
  
  async delete(key: string): Promise<void> {
    // Implementação IndexedDB
  }
  
  async clear(): Promise<void> {
    // Limpar todo o cache
  }
}
```

### Query Key Generation
```typescript
function generateCacheKey(queryKey: readonly unknown[]): string {
  return queryKey
    .map(key => typeof key === 'string' ? key : JSON.stringify(key))
    .join('_');
}
```

## ⚡ Performance

### Otimizações
- **Lazy Loading** - IndexedDB inicializado apenas quando necessário
- **Batch Operations** - Múltiplas operações em uma transação
- **Background Sync** - Sincronização não bloqueia UI
- **TTL Inteligente** - Cache expira baseado no uso

### Métricas
```typescript
// Tempo de resposta do cache
const cacheResponseTime = performance.now() - startTime;

// Taxa de cache hit
const cacheHitRate = cacheHits / totalRequests;

// Tamanho do cache
const cacheSize = await getCacheStats();
```

## ✅ Boas Práticas

### 1. **Query Keys Consistentes**
```typescript
// ✅ Bom - chaves consistentes
const queryKey = ['clients', userId, filters];

// ❌ Ruim - chaves inconsistentes
const queryKey = [userId, 'clients', Math.random()];
```

### 2. **Invalidação Inteligente**
```typescript
// ✅ Bom - invalidação específica
queryClient.invalidateQueries({ queryKey: ['clients', userId] });

// ❌ Ruim - invalidação muito ampla
queryClient.invalidateQueries();
```

### 3. **Error Handling**
```typescript
try {
  const data = await getFromCache(queryKey);
  return data;
} catch (error) {
  console.warn('Cache miss, falling back to server');
  return await fetchFromServer();
}
```

### 4. **Memory Management**
```typescript
// Configurar garbage collection
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      gcTime: 10 * 60 * 1000, // 10 minutos
      staleTime: 5 * 60 * 1000, // 5 minutos
    }
  }
});
```

---

**📖 Próximos Passos:**
- [hooks.md](./hooks.md) - Hooks que usam cache
- [database.md](./database.md) - Integração com banco
- [realtime.md](./realtime.md) - Updates em tempo real
