# 🎛️ Controladores de Realtime

Sistema de controladores que sincronizam dados do Appwrite Realtime com React Query e IndexedDB.

## 🏗️ Arquitetura

### Padrão dos Controladores

Todos os controladores seguem o mesmo padrão baseado no `use-clients-controller.ts`:

```typescript
export function useExampleController() {
  const queryClient = useQueryClient();
  const unsubscribeRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    console.log('🎛️ Inicializando controller...');

    unsubscribeRef.current = subscribe(realtimeStore.example, () => {
      const items = realtimeStore.example;
      if (items.length === 0) return;

      items.forEach(item => {
        // Atualizar React Query
        queryClient.setQueryData(['example'], (oldData: any) => {
          // Lógica de merge
        });

        // Salvar no IndexedDB
        saveToIndexedDB('example', item, {
          collection: 'example',
          userId: item.userId
        });
      });

      // Limpar store
      realtimeStore.example = [];
    });

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
    };
  }, [queryClient]);

  return {};
}
```

## 📋 Controladores Disponíveis

### 1. useClientsController
- **Collection**: `clients`
- **Funcionalidade**: Sincroniza clientes em tempo real
- **Cache**: Por userId
- **IndexedDB**: Salva com collection + userId

### 2. useNotificationsController
- **Collection**: `notifications`
- **Funcionalidade**: Sincroniza notificações em tempo real
- **Cache**: Global
- **IndexedDB**: Salva com collection + userId

### 3. useChatsController
- **Collection**: `team_chats`
- **Funcionalidade**: Sincroniza chats de equipe
- **Cache**: Por teamId
- **IndexedDB**: Salva com collection + teamId

### 4. useMessagesController
- **Collection**: `chat_messages`
- **Funcionalidade**: Sincroniza mensagens de chat
- **Cache**: Por chatId
- **IndexedDB**: Salva com collection + chatId

### 5. useEventsController
- **Collection**: `events`
- **Funcionalidade**: Sincroniza eventos do calendário
- **Cache**: Por userId + teamId
- **IndexedDB**: Salva com collection + userId
- **Invalidações**: events, eventStats

### 6. useKanbanController
- **Collections**: `kanban_boards`, `kanban_columns`, `kanban_tasks`
- **Funcionalidade**: Sincroniza boards, colunas e tasks
- **Cache**: Boards por userId, invalidações em cascata
- **IndexedDB**: Salva cada tipo separadamente
- **Relacionamentos**: Tasks/Columns invalidam board pai

### 7. useActivitiesController
- **Collection**: `activity_logs`
- **Funcionalidade**: Sincroniza logs de atividade
- **Cache**: Global, por usuário, por equipe
- **IndexedDB**: Salva com collection + userId
- **Invalidações**: activities, activityStats, recentActivities

### 8. useTeamsController
- **Service**: Appwrite Teams (não collection)
- **Funcionalidade**: Invalida queries relacionadas a teams
- **Cache**: Apenas invalidações
- **Nota**: Teams são gerenciados pelo serviço nativo do Appwrite

### 9. useStorageController
- **Service**: Appwrite Storage (não collection)
- **Funcionalidade**: Invalida queries relacionadas a files
- **Cache**: Apenas invalidações
- **Nota**: Files são gerenciados pelo serviço de Storage do Appwrite

## 🚀 Como Usar

### Inicialização Automática

Os controladores são inicializados automaticamente pelo `RealtimeInitializer`:

```tsx
// app/components/realtime-initializer.tsx
export function RealtimeInitializer() {
  useClientsController();
  useNotificationsController();
  useChatsController();
  useMessagesController();
  useEventsController();
  useKanbanController();
  useActivitiesController();
  useTeamsController();
  useStorageController();
  
  // ... lógica de conexão
}
```

### Importação Manual

```typescript
import {
  useClientsController,
  useEventsController,
  useKanbanController
} from '@/hooks/controllers';
```

## 🔄 Fluxo de Dados

1. **Appwrite Realtime** → Evento recebido
2. **Listener** (`realtime.ts`) → Atualiza Valtio store
3. **Controller** → Subscribe detecta mudança
4. **React Query** → Dados atualizados via setQueryData
5. **IndexedDB** → Cache local atualizado
6. **UI** → Re-render automático

## 🛠️ Configuração

### Store Valtio

```typescript
// app/lib/realtime/store.ts
export const realtimeStore = proxy({
  clients: [] as Client[],
  notifications: [] as Notification[],
  events: [] as Event[],
  boards: [] as Board[],
  // ... outros arrays
});
```

### Listener Realtime

```typescript
// app/lib/realtime/realtime.ts
function updateStore(collection: string, action: string, payload: any) {
  switch (collection) {
    case 'clients':
      updateArray('clients', action, payload);
      break;
    case 'events':
      updateArray('events', action, payload);
      break;
    // ... outros casos
  }
}
```

## 📝 Notas Importantes

- **Cleanup Automático**: Controllers fazem cleanup via useEffect
- **Type Safety**: Todos os controladores são tipados
- **Performance**: Apenas create/update são processados (delete ignorado)
- **Error Handling**: IndexedDB errors não interrompem o fluxo
- **Deduplicação**: Merge inteligente evita duplicatas
- **Invalidações**: Queries relacionadas são invalidadas automaticamente

## 🔧 Troubleshooting

### Controller não funciona
1. Verificar se está no `RealtimeInitializer`
2. Verificar se collection ID está no `.env`
3. Verificar se listener está mapeando a collection

### Dados não sincronizam
1. Verificar conexão realtime
2. Verificar se eventos estão chegando no listener
3. Verificar se store Valtio está sendo atualizado

### Performance issues
1. Verificar se cleanup está funcionando
2. Verificar se não há loops infinitos
3. Verificar se invalidações não são excessivas
