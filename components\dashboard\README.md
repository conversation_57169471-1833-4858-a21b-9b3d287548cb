# Dashboard Charts Components

Esta pasta contém os componentes de gráficos modernos e bonitos para a dashboard, baseados nos charts do Shadcn UI e inspirados nos exemplos da pasta `components`.

## Componentes Disponíveis

### 1. DashboardRevenueChart
- **Tipo**: Gráfico de Barras com gradiente
- **Funcionalidade**: Mostra receita mensal (MRR) e anual (ARR) com toggle
- **Features**:
  - Toggle entre MRR/ARR
  - Gradientes visuais
  - Badges de crescimento
  - Tooltips customizados

### 2. DashboardGrowthChart
- **Tipo**: Gráfico de Linha
- **Funcionalidade**: Crescimento de usuários e clientes ao longo do tempo
- **Features**:
  - <PERSON><PERSON><PERSON><PERSON> linhas (usuários e clientes)
  - Pontos interativos
  - Badges de crescimento percentual
  - Ícones representativos

### 3. DashboardActivityChart
- **Tipo**: Gráfico de Barras Empilhadas
- **Funcionalidade**: Atividades do sistema (tarefas, eventos, atividades, notificações)
- **Features**:
  - Barras empilhadas com gradientes
  - Resumo por categoria
  - Cores diferenciadas por tipo

### 4. DashboardDistributionChart
- **Tipo**: Gráfico de Barras Empilhadas
- **Funcionalidade**: Distribuição de planos (Individual, Equipe, Empresarial)
- **Features**:
  - Percentuais por categoria
  - Ícones representativos
  - Gradientes por tipo de plano

### 5. DashboardProgressChart
- **Tipo**: Barras de Progresso Customizadas
- **Funcionalidade**: Progresso das metas mensais
- **Features**:
  - Barras de progresso visuais
  - Resumo de metas atingidas
  - Cores baseadas no desempenho
  - Ícones por categoria

### 6. DashboardPieChart
- **Tipo**: Gráfico de Pizza
- **Funcionalidade**: Status dos projetos
- **Features**:
  - Gráfico de pizza interativo
  - Legenda customizada
  - Estatísticas resumidas
  - Hover effects

### 7. DashboardAreaChart
- **Tipo**: Gráfico de Área Interativo
- **Funcionalidade**: Métricas de performance, engajamento e satisfação
- **Features**:
  - Toggle entre métricas
  - Gradientes suaves
  - Pontos interativos
  - Resumo comparativo

### 8. DashboardChartsSection
- **Tipo**: Container Principal
- **Funcionalidade**: Organiza todos os gráficos em um layout responsivo
- **Features**:
  - Grid responsivo
  - Layout otimizado para mobile
  - Título e descrição da seção

## Tecnologias Utilizadas

- **Recharts**: Biblioteca principal para gráficos
- **Shadcn UI**: Sistema de design e componentes
- **Radix UI**: Componentes primitivos
- **Tailwind CSS**: Estilização
- **Lucide React**: Ícones

## Características dos Gráficos

### Design
- **Cores consistentes**: Uso do sistema de cores do Shadcn (`--chart-1` a `--chart-6`)
- **Gradientes**: Aplicados para dar profundidade visual
- **Responsividade**: Adaptação automática para diferentes tamanhos de tela
- **Acessibilidade**: Suporte a leitores de tela e navegação por teclado

### Interatividade
- **Tooltips**: Informações detalhadas ao passar o mouse
- **Hover effects**: Feedback visual nas interações
- **Toggles**: Alternância entre diferentes visualizações
- **Badges**: Indicadores de tendência e crescimento

### Performance
- **Lazy loading**: Carregamento otimizado
- **Memoização**: Prevenção de re-renders desnecessários
- **Skeleton loading**: Estados de carregamento elegantes

## Como Usar

```tsx
import { DashboardChartsSection } from '@/components/dashboard";

export default function Dashboard() {
  return (
    <div>
      {/* Outros componentes da dashboard */}
      <DashboardChartsSection />
    </div>
  );
}
```

## Customização

Cada componente pode ser customizado através de:
- **Props**: Dados e configurações específicas
- **CSS Variables**: Cores e espaçamentos
- **Tailwind Classes**: Estilos adicionais

## Integração com Dados Reais

Os gráficos agora utilizam dados reais da aplicação:

### Hooks Utilizados
- **useAnalytics**: Dados principais de analytics e métricas
- **useClients**: Lista de clientes para distribuição e receita
- **useEvents**: Eventos para cálculos de atividade
- **useBoards**: Boards Kanban para tarefas e progresso
- **useNotifications**: Notificações para métricas de engajamento
- **useActivities**: Log de atividades do sistema

### Dados Reais por Gráfico

#### DashboardRevenueChart
- **Receita**: Calculada baseada no número de clientes × valor médio (R$ 150)
- **Crescimento**: Usa `analytics.revenueGrowth` dos dados reais
- **Variação sazonal**: Aplicada para simular flutuações mensais

#### DashboardGrowthChart
- **Usuários**: `analytics.totalUsers` e `analytics.userGrowth`
- **Clientes**: `analytics.totalClients` e `analytics.clientGrowth`
- **Dados históricos**: `analytics.chartData` com evolução mensal

#### DashboardActivityChart
- **Tarefas**: Baseado em `analytics.totalKanbanTasks`
- **Eventos**: Baseado em `analytics.totalEvents`
- **Atividades**: Contagem real de `activities.length`
- **Notificações**: `analytics.notificationsCount`

#### DashboardDistributionChart
- **Distribuição**: Baseada no número real de clientes
- **Tipos**: Individual (40%), Equipe (45%), Empresarial (15%)
- **Crescimento**: `analytics.clientGrowth`

#### DashboardProgressChart
- **Metas dinâmicas**: Calculadas baseadas nos dados atuais
- **Usuários ativos**: `analytics.activeUsers`
- **Progresso real**: Percentuais baseados em dados reais

#### DashboardPieChart
- **Status de clientes**: Filtrados por `client.status`
- **Distribuição real**: Clientes ativos, eventos, boards, etc.
- **Dados filtrados**: Remove categorias com valor 0

#### DashboardAreaChart
- **Performance**: Calculada baseada na quantidade total de itens
- **Engajamento**: Baseado na taxa de conversão e atividade
- **Satisfação**: Derivada da performance com variações

## Estrutura de Arquivos

```
app/components/dashboard/
├── dashboard-revenue-chart.tsx
├── dashboard-growth-chart.tsx
├── dashboard-activity-chart.tsx
├── dashboard-distribution-chart.tsx
├── dashboard-progress-chart.tsx
├── dashboard-pie-chart.tsx
├── dashboard-area-chart.tsx
├── dashboard-charts-section.tsx
├── index.ts
└── README.md
```

## Próximos Passos

1. **Integração com dados reais**: Conectar com APIs do backend
2. **Filtros avançados**: Adicionar filtros por período, categoria, etc.
3. **Exportação**: Funcionalidade para exportar gráficos
4. **Animações**: Transições suaves entre estados
5. **Temas**: Suporte a temas dark/light aprimorado
