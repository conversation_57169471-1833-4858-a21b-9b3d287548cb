/**
 * Chatbox expandível com Gemini AI
 * Assistente inteligente com contexto do projeto
 */

import React, { useState, useRef, useEffect } from 'react';
import { Send, Bot, X, Trash2, Plus, MessageSquare } from 'lucide-react';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import { ExpandableChat } from './expandable-chat';
import { ModernChat } from './modern-chat';
import type { ModernChatMessage } from './modern-chat';
import { useGeminiChat } from '../../hooks/use-gemini-chat';
import type { GeminiChatMessage } from '@/schemas/chat';

interface GeminiChatboxProps {
  position?: 'bottom-right' | 'bottom-left';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export function GeminiChatbox({
  position = 'bottom-right',
  size = 'lg',
  className
}: GeminiChatboxProps) {
  const {
    currentSession,
    sessions,
    isLoading,
    isGeminiReady,
    sendMessage,
    createSession,
    selectSession,
    deleteSession,
    clearCurrentSession,
    isError,
    error,
  } = useGeminiChat();

  const messages = currentSession?.messages || [];

  // Converter mensagens do Gemini para o formato ModernChat
  const modernMessages: ModernChatMessage[] = messages.map((msg) => ({
    id: msg.id,
    content: msg.content,
    isUser: msg.role === 'user',
    timestamp: new Date(msg.timestamp),
    isLoading: msg.status === 'sending',
    status: msg.status === 'error' ? 'error' : msg.status === 'sending' ? 'sending' : 'sent',
    metadata: msg.metadata,
  }));

  // Criar sessão inicial se não existir
  useEffect(() => {
    if (!currentSession && sessions.length === 0) {
      createSession('Assistente AI', undefined);
    }
  }, [currentSession, sessions.length, createSession]);

  // Enviar mensagem usando o novo sistema
  const handleSendMessage = async (messageContent: string) => {
    if (!messageContent.trim() || isLoading) return;

    try {
      await sendMessage(messageContent.trim());
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
    }
  };



  return (
    <ExpandableChat
      position={position}
      size={size}
      className={className}
      icon={<Bot className="h-5 w-5" />}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-muted/50">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <Bot className="h-5 w-5 text-primary" />
            <h3 className="font-semibold">Assistente AI</h3>
          </div>
          <Badge
            variant={!isGeminiReady ? 'destructive' : isLoading ? 'secondary' : 'default'}
            className="text-xs"
          >
            {!isGeminiReady ? 'Não configurado' : isLoading ? 'Digitando...' : 'Online'}
          </Badge>
        </div>

        <div className="flex items-center gap-1">
          {/* Seletor de Sessão */}
          {sessions.length > 1 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <MessageSquare className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                {sessions.map((session) => (
                  <DropdownMenuItem
                    key={session.id}
                    onClick={() => selectSession(session.id)}
                    className={currentSession?.id === session.id ? 'bg-muted' : ''}
                  >
                    <div className="flex flex-col gap-1 flex-1">
                      <span className="text-sm font-medium truncate">
                        {session.title}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {session.messages.length} mensagens
                      </span>
                    </div>
                  </DropdownMenuItem>
                ))}
                <Separator />
                <DropdownMenuItem onClick={() => createSession()}>
                  <Plus className="h-4 w-4 mr-2" />
                  Nova conversa
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          {/* Menu de Opções */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <Bot className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => createSession()}>
                <Plus className="h-4 w-4 mr-2" />
                Nova conversa
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={clearCurrentSession}
                disabled={!currentSession || messages.length === 0}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Limpar conversa
              </DropdownMenuItem>
              {currentSession && sessions.length > 1 && (
                <DropdownMenuItem
                  onClick={() => deleteSession(currentSession.id)}
                  className="text-destructive"
                >
                  <X className="h-4 w-4 mr-2" />
                  Deletar conversa
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Chat Moderno */}
      <div className="flex-1 min-h-0">
        {!isGeminiReady ? (
          <div className="flex items-center justify-center h-full p-8">
            <div className="text-center text-muted-foreground max-w-sm">
              <Bot className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">⚠️ Configuração Necessária</p>
              <p className="text-sm">
                Para usar o assistente AI, adicione sua chave do Google Gemini no arquivo .env:
              </p>
              <code className="block mt-2 p-2 bg-muted rounded text-xs">
                NEXT_PUBLIC_GEMINI_API_KEY=sua-chave-aqui
              </code>
            </div>
          </div>
        ) : (
          <ModernChat
            messages={modernMessages}
            onSendMessage={handleSendMessage}
            isLoading={isLoading}
            placeholder="Pergunte algo sobre o projeto..."
            height="100%"
            showFileUpload={false}
            showEmojiPicker={false}
            className="border-0 rounded-none"
          />
        )}
      </div>

      {/* Informações da sessão */}
      {currentSession && isGeminiReady && (
        <div className="flex items-center justify-between px-4 py-2 border-t bg-muted/30 text-xs text-muted-foreground">
          <span>
            Modelo: {currentSession.settings.model}
          </span>
          <span>
            {messages.length} mensagens
          </span>
        </div>
      )}
    </ExpandableChat>
  );
}
