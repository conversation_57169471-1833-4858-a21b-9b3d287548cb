import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import { Badge } from '../ui/badge';
import { Users } from 'lucide-react';
import { useTeams } from '../../hooks/use-api';

interface TeamSelectorProps {
  selectedTeamId?: string;
  onTeamSelect: (teamId: string) => void;
  className?: string;
}

export function TeamSelector({
  selectedTeamId,
  onTeamSelect,
  className
}: TeamSelectorProps) {
  const { data: teams = [], isLoading } = useTeams();

  const selectedTeam = teams.find(team => team.$id === selectedTeamId);

  if (isLoading) {
    return (
      <div className={`flex items-center gap-2 ${className || ''}`}>
        <div className="h-10 w-48 bg-muted animate-pulse rounded"></div>
      </div>
    );
  }

  if (teams.length === 0) {
    return (
      <div className={`flex items-center gap-2 ${className || ''}`}>
        <div className="flex items-center gap-2 text-muted-foreground">
          <Users className="h-4 w-4" />
          <span className="text-sm">Nenhum time encontrado</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex items-center gap-2 ${className || ''}`}>
      <Select value={selectedTeamId} onValueChange={onTeamSelect}>
        <SelectTrigger className="w-64">
          <SelectValue placeholder="Selecione um time">
            {selectedTeam && (
              <div className="flex items-center gap-2">
                <div className="flex items-center justify-center w-6 h-6 bg-primary/10 rounded">
                  <Users className="h-3 w-3 text-primary" />
                </div>
                <span className="font-medium">{selectedTeam.name}</span>
                <Badge variant="outline" className="ml-auto">
                  {selectedTeam.total} membro{selectedTeam.total !== 1 ? 's' : ''}
                </Badge>
              </div>
            )}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {teams.map((team) => (
            <SelectItem key={team.$id} value={team.$id}>
              <div className="flex items-center gap-3 py-1">
                <div className="flex items-center justify-center w-8 h-8 bg-primary/10 rounded">
                  <Users className="h-4 w-4 text-primary" />
                </div>
                <div className="flex-1">
                  <div className="font-medium">{team.name}</div>
                  <div className="text-xs text-muted-foreground">
                    {team.total} membro{team.total !== 1 ? 's' : ''}
                  </div>
                </div>
                {/* Role badge - seria necessário buscar membership do usuário */}
                <Badge variant="outline" className="text-xs">
                  Membro
                </Badge>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
