import { useState } from 'react'
import { CalendarIcon, FilterIcon, RefreshCwIcon } from 'lucide-react'
import { Button } from '../ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Label } from '../ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '../ui/popover'
import { Calendar } from '../ui/calendar'
import { cn } from '../../lib/utils'
import { format } from 'date-fns'
import { ptBR } from 'date-fns/locale'

export interface ReportsFilters {
  dateRange: {
    from: Date | undefined
    to: Date | undefined
  }
  reportType: string
  status: string
  format: string
  category: string
}

interface ReportsFiltersProps {
  filters: ReportsFilters
  onFiltersChange: (filters: ReportsFilters) => void
  onApplyFilters: () => void
  onResetFilters: () => void
}

export function ReportsFiltersComponent({
  filters,
  onFiltersChange,
  onApplyFilters,
  onResetFilters
}: ReportsFiltersProps) {
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false)

  const updateFilter = <K extends keyof ReportsFilters>(key: K, value: ReportsFilters[K]) => {
    onFiltersChange({
      ...filters,
      [key]: value
    })
  }

  const updateDateRange = (range: { from: Date | undefined; to: Date | undefined }) => {
    updateFilter('dateRange', range)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FilterIcon className="size-5" />
          Filtros de Relatórios
        </CardTitle>
        <CardDescription>
          Configure os filtros para personalizar a visualização dos relatórios
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">

          {/* Período */}
          <div className="space-y-2">
            <Label>Período</Label>
            <Popover open={isDatePickerOpen} onOpenChange={setIsDatePickerOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !filters.dateRange.from && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {filters.dateRange.from ? (
                    filters.dateRange.to ? (
                      <>
                        {format(filters.dateRange.from, "dd/MM/yyyy", { locale: ptBR })} -{" "}
                        {format(filters.dateRange.to, "dd/MM/yyyy", { locale: ptBR })}
                      </>
                    ) : (
                      format(filters.dateRange.from, "dd/MM/yyyy", { locale: ptBR })
                    )
                  ) : (
                    "Selecione o período"
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  initialFocus
                  mode="range"
                  defaultMonth={filters.dateRange.from}
                  selected={{
                    from: filters.dateRange.from,
                    to: filters.dateRange.to,
                  }}
                  onSelect={(range) => {
                    updateDateRange({
                      from: range?.from,
                      to: range?.to,
                    })
                  }}
                  numberOfMonths={2}
                  locale={ptBR}
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Tipo de Relatório */}
          <div className="space-y-2">
            <Label>Tipo de Relatório</Label>
            <Select
              value={filters.reportType}
              onValueChange={(value) => updateFilter('reportType', value)}
            >
              <SelectTrigger>
                <SelectValue>
                  {filters.reportType === 'all' ? 'Tipo: Todos' :
                   filters.reportType === 'financial' ? 'Tipo: Financeiro' :
                   filters.reportType === 'clients' ? 'Tipo: Clientes' :
                   filters.reportType === 'teams' ? 'Tipo: Equipes' :
                   filters.reportType === 'performance' ? 'Tipo: Performance' :
                   filters.reportType === 'analytics' ? 'Tipo: Analytics' :
                   filters.reportType === 'custom' ? 'Tipo: Personalizado' :
                   'Tipo'}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tipo: Todos</SelectItem>
                <SelectItem value="financial">Tipo: Financeiro</SelectItem>
                <SelectItem value="clients">Tipo: Clientes</SelectItem>
                <SelectItem value="teams">Tipo: Equipes</SelectItem>
                <SelectItem value="performance">Tipo: Performance</SelectItem>
                <SelectItem value="analytics">Tipo: Analytics</SelectItem>
                <SelectItem value="custom">Tipo: Personalizado</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Status */}
          <div className="space-y-2">
            <Label>Status</Label>
            <Select
              value={filters.status}
              onValueChange={(value) => updateFilter('status', value)}
            >
              <SelectTrigger>
                <SelectValue>
                  {filters.status === 'all' ? 'Status: Todos' :
                   filters.status === 'generated' ? 'Status: Gerado' :
                   filters.status === 'processing' ? 'Status: Processando' :
                   filters.status === 'scheduled' ? 'Status: Agendado' :
                   filters.status === 'failed' ? 'Status: Falhou' :
                   filters.status === 'archived' ? 'Status: Arquivado' :
                   'Status'}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Status: Todos</SelectItem>
                <SelectItem value="generated">Status: Gerado</SelectItem>
                <SelectItem value="processing">Status: Processando</SelectItem>
                <SelectItem value="scheduled">Status: Agendado</SelectItem>
                <SelectItem value="failed">Status: Falhou</SelectItem>
                <SelectItem value="archived">Status: Arquivado</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Formato */}
          <div className="space-y-2">
            <Label>Formato</Label>
            <Select
              value={filters.format}
              onValueChange={(value) => updateFilter('format', value)}
            >
              <SelectTrigger>
                <SelectValue>
                  {filters.format === 'all' ? 'Formato: Todos' :
                   filters.format === 'pdf' ? 'Formato: PDF' :
                   filters.format === 'excel' ? 'Formato: Excel' :
                   filters.format === 'csv' ? 'Formato: CSV' :
                   filters.format === 'json' ? 'Formato: JSON' :
                   'Formato'}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Formato: Todos</SelectItem>
                <SelectItem value="pdf">Formato: PDF</SelectItem>
                <SelectItem value="excel">Formato: Excel</SelectItem>
                <SelectItem value="csv">Formato: CSV</SelectItem>
                <SelectItem value="json">Formato: JSON</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Categoria */}
          <div className="space-y-2">
            <Label>Categoria</Label>
            <Select
              value={filters.category}
              onValueChange={(value) => updateFilter('category', value)}
            >
              <SelectTrigger>
                <SelectValue>
                  {filters.category === 'all' ? 'Categoria: Todas' :
                   filters.category === 'operational' ? 'Categoria: Operacional' :
                   filters.category === 'strategic' ? 'Categoria: Estratégico' :
                   filters.category === 'compliance' ? 'Categoria: Compliance' :
                   filters.category === 'marketing' ? 'Categoria: Marketing' :
                   filters.category === 'sales' ? 'Categoria: Vendas' :
                   'Categoria'}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Categoria: Todas</SelectItem>
                <SelectItem value="operational">Categoria: Operacional</SelectItem>
                <SelectItem value="strategic">Categoria: Estratégico</SelectItem>
                <SelectItem value="compliance">Categoria: Compliance</SelectItem>
                <SelectItem value="marketing">Categoria: Marketing</SelectItem>
                <SelectItem value="sales">Categoria: Vendas</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Botões de Ação */}
        <div className="flex flex-col gap-2 pt-4 sm:flex-row">
          <Button onClick={onApplyFilters} className="flex-1">
            <FilterIcon className="mr-2 size-4" />
            Aplicar Filtros
          </Button>
          <Button variant="outline" onClick={onResetFilters} className="flex-1">
            <RefreshCwIcon className="mr-2 size-4" />
            Limpar Filtros
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
