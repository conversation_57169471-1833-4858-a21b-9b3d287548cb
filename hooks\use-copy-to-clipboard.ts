import { useState } from 'react';
import { toast } from 'sonner';

/**
 * Hook para copiar texto para a área de transferência
 */
export function useCopyToClipboard() {
  const [isCopied, setIsCopied] = useState(false);

  const copyToClipboard = async (text: string, successMessage?: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setIsCopied(true);
      toast.success(successMessage || 'Copiado para a área de transferência!');
      
      // Reset após 2 segundos
      setTimeout(() => setIsCopied(false), 2000);
      
      return true;
    } catch (error) {
      console.error('Erro ao copiar para área de transferência:', error);
      toast.error('Erro ao copiar texto');
      return false;
    }
  };

  return { copyToClipboard, isCopied };
}
