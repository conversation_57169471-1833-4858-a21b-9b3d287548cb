/**
 * Kanban React Query Hooks
 * Estrutura otimizada com apenas 2 coleções para máxima performance
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useAuth } from '../use-auth';
import { useTeamContext } from '../../contexts/team-context';
import { useDocumentPermissions } from '../use-document-permissions';
import { useIndexedDB } from '../use-indexeddb';
import { isCacheEnabled } from '../../lib/cache-config';
import type {
  OptimizedWorkspace,
  OptimizedBoard,
  CreateWorkspaceData,
  CreateBoardData,
  CreateColumnData,
  CreateTaskData,
  CreateLabelData,
  CreateChecklistData,
  CreateCommentData,
  CreateAttachmentData,
  UpdateWorkspaceData,
  UpdateBoardData,
  UpdateColumnData,
  UpdateTaskData,
  UpdateCommentData,
} from '@/schemas/kanban';
import * as kanban from '../../lib/appwrite/functions/kanban';

// ============================================================================
// QUERY KEYS
// ============================================================================

export const kanbanKeys = {
  all: ['kanban'] as const,
  workspaces: () => [...kanbanKeys.all, 'workspaces'] as const,
  workspace: (id: string) => [...kanbanKeys.workspaces(), id] as const,
  userWorkspaces: (userId: string) => [...kanbanKeys.workspaces(), 'user', userId] as const,
  boards: () => [...kanbanKeys.all, 'boards'] as const,
  board: (id: string) => [...kanbanKeys.boards(), id] as const,
  userBoards: (userId: string) => [...kanbanKeys.boards(), 'user', userId] as const,
  workspaceBoards: (workspaceId: string) => [...kanbanKeys.boards(), 'workspace', workspaceId] as const,
  boardStats: (boardId: string) => [...kanbanKeys.board(boardId), 'stats'] as const,
};

// ============================================================================
// WORKSPACE HOOKS
// ============================================================================

export function useWorkspace(workspaceId: string) {
  return useQuery({
    queryKey: kanbanKeys.workspace(workspaceId),
    queryFn: () => kanban.getWorkspace(workspaceId),
    enabled: !!workspaceId,
  });
}

export function useUserWorkspaces(userId?: string) {
  const { getCache, setCache } = useIndexedDB();

  return useQuery({
    queryKey: kanbanKeys.userWorkspaces(userId || ''),
    queryFn: async () => {
      if (!userId) throw new Error('User ID required');

      // Verificar cache local primeiro
      if (isCacheEnabled()) {
        const cached = await getCache('workspaces', userId);
        if (cached && Array.isArray(cached) && cached.length > 0) {
          console.log('📦 Cache hit: workspaces');
          return cached as OptimizedWorkspace[];
        }
      }

      // Buscar do servidor
      console.log('🌐 Buscando workspaces do servidor');
      const result = await kanban.listUserWorkspaces(userId);
      const workspacesData = result.documents as unknown as OptimizedWorkspace[];

      // Salvar no cache se habilitado
      if (isCacheEnabled() && workspacesData.length > 0) {
        await setCache('workspaces', workspacesData, userId);
      }

      return workspacesData;
    },
    enabled: !!userId,
    staleTime: isCacheEnabled() ? 0 : 5 * 60 * 1000,
    refetchOnWindowFocus: false,
  });
}

export function useCreateWorkspace() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateWorkspaceData) => kanban.createWorkspace(data),
    onSuccess: (workspace, variables) => {
      // Invalidate user workspaces
      queryClient.invalidateQueries({
        queryKey: kanbanKeys.userWorkspaces(variables.userId)
      });

      // Add to cache
      queryClient.setQueryData(
        kanbanKeys.workspace(workspace.$id),
        workspace
      );

      toast.success('Workspace criado com sucesso!');
    },
    onError: (error) => {
      console.error('Erro ao criar workspace:', error);
      toast.error('Erro ao criar workspace. Tente novamente.');
    },
  });
}

export function useUpdateWorkspace() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ workspaceId, data }: { workspaceId: string; data: UpdateWorkspaceData }) =>
      kanban.updateWorkspace(workspaceId, data),
    onSuccess: (workspace, { workspaceId }) => {
      // Update cache
      queryClient.setQueryData(
        kanbanKeys.workspace(workspaceId),
        workspace
      );

      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: kanbanKeys.userWorkspaces(workspace.userId)
      });

      toast.success('Workspace atualizado com sucesso!');
    },
    onError: (error) => {
      console.error('Erro ao atualizar workspace:', error);
      toast.error('Erro ao atualizar workspace. Tente novamente.');
    },
  });
}

export function useDeleteWorkspace() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ workspaceId, userId }: { workspaceId: string; userId: string }) =>
      kanban.deleteWorkspace(workspaceId),
    onSuccess: (_, { workspaceId, userId }) => {
      // Remove from cache
      queryClient.removeQueries({
        queryKey: kanbanKeys.workspace(workspaceId)
      });

      // Invalidate user workspaces
      queryClient.invalidateQueries({
        queryKey: kanbanKeys.userWorkspaces(userId)
      });

      toast.success('Workspace excluído com sucesso!');
    },
    onError: (error) => {
      console.error('Erro ao excluir workspace:', error);
      toast.error('Erro ao excluir workspace. Tente novamente.');
    },
  });
}

// ============================================================================
// BOARD HOOKS
// ============================================================================

export function useBoard(boardId: string) {
  const { getCache, setCache } = useIndexedDB();
  const { user } = useAuth();

  return useQuery({
    queryKey: kanbanKeys.board(boardId),
    queryFn: async () => {
      if (!user?.$id) throw new Error('User not authenticated');

      // Verificar cache local primeiro
      if (isCacheEnabled()) {
        const cached = await getCache('board', boardId);
        if (cached) {
          console.log('📦 Cache hit: board');
          return cached as any;
        }
      }

      // Buscar do servidor - UMA ÚNICA QUERY RETORNA TUDO!
      console.log('🌐 Buscando board do servidor');
      const boardData = await kanban.getBoardWithData(boardId);

      // Salvar no cache se habilitado
      if (isCacheEnabled()) {
        await setCache('board', boardData as any, boardId);
      }

      return boardData;
    },
    enabled: !!user?.$id && !!boardId,
    staleTime: isCacheEnabled() ? 0 : 2 * 60 * 1000,
    refetchOnWindowFocus: false,
  });
}



export function useUserBoards(userId?: string, workspaceId?: string) {
  const { getCache, setCache } = useIndexedDB();

  return useQuery({
    queryKey: workspaceId
      ? kanbanKeys.workspaceBoards(workspaceId)
      : kanbanKeys.userBoards(userId || ''),
    queryFn: async () => {
      if (!userId) throw new Error('User ID required');

      // Verificar cache local primeiro
      const cacheKey = workspaceId ? `workspace_boards_${workspaceId}` : 'boards';
      if (isCacheEnabled()) {
        const cached = await getCache(cacheKey, userId);
        if (cached && Array.isArray(cached) && cached.length > 0) {
          console.log('📦 Cache hit: boards');
          return cached as OptimizedBoard[];
        }
      }

      // Buscar do servidor
      console.log('🌐 Buscando boards do servidor');
      const result = await kanban.listBoards(userId, undefined, workspaceId);
      const boardsData = result.documents as unknown as OptimizedBoard[];

      // Salvar no cache se habilitado
      if (isCacheEnabled() && boardsData.length > 0) {
        await setCache(cacheKey, boardsData as any, userId);
      }

      return boardsData;
    },
    enabled: !!userId,
    staleTime: isCacheEnabled() ? 0 : 5 * 60 * 1000,
    refetchOnWindowFocus: false,
  });
}

export function useCreateBoard() {
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const { permissionContext } = useTeamContext();
  const { teamId } = useDocumentPermissions();

  return useMutation({
    mutationFn: (data: CreateBoardData) => {
      // Adicionar opções de permissão aos dados
      const boardData: CreateBoardData = {
        ...data,
        teamId: teamId || data.teamId,
        permissionOptions: {
          userId: user?.$id || data.userId,
          teamId,
          permissionContext,
          isPublic: data.visibility === 'public',
        },
      };

      return kanban.createBoard(boardData);
    },
    onSuccess: (board, variables) => {
      // Add to cache
      queryClient.setQueryData(
        kanbanKeys.board(board.$id),
        board as any
      );

      // Invalidate user boards
      queryClient.invalidateQueries({
        queryKey: kanbanKeys.userBoards(variables.userId)
      });

      // Invalidate workspace boards if applicable
      if (variables.workspaceId) {
        queryClient.invalidateQueries({
          queryKey: kanbanKeys.workspaceBoards(variables.workspaceId)
        });
      }

      toast.success('Board criado com sucesso!');
    },
    onError: (error) => {
      console.error('Erro ao criar board:', error);
      toast.error('Erro ao criar board. Tente novamente.');
    },
  });
}

export function useUpdateBoard() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ boardId, data }: { boardId: string; data: UpdateBoardData }) =>
      kanban.updateBoard(boardId, data),
    onSuccess: (board, { boardId }) => {
      // Update cache
      queryClient.setQueryData(
        kanbanKeys.board(boardId),
        board as any
      );

      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: kanbanKeys.userBoards((board as any).userId)
      });

      if ((board as any).workspaceId) {
        queryClient.invalidateQueries({
          queryKey: kanbanKeys.workspaceBoards((board as any).workspaceId)
        });
      }

      toast.success('Board atualizado com sucesso!');
    },
    onError: (error) => {
      console.error('Erro ao atualizar board:', error);
      toast.error('Erro ao atualizar board. Tente novamente.');
    },
  });
}

export function useDeleteBoard() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ boardId, userId }: { boardId: string; userId: string }) =>
      kanban.deleteBoard(boardId),
    onSuccess: (_, { boardId, userId }) => {
      // Remove from cache
      queryClient.removeQueries({
        queryKey: kanbanKeys.board(boardId)
      });

      // Invalidate user boards
      queryClient.invalidateQueries({
        queryKey: kanbanKeys.userBoards(userId)
      });

      toast.success('Board excluído com sucesso!');
    },
    onError: (error) => {
      console.error('Erro ao excluir board:', error);
      toast.error('Erro ao excluir board. Tente novamente.');
    },
  });
}

// ============================================================================
// COLUMN HOOKS
// ============================================================================

export function useAddColumn() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      boardId,
      columnData,
      userId
    }: {
      boardId: string;
      columnData: CreateColumnData;
      userId: string;
    }) => kanban.addColumnToBoard(boardId, columnData, userId),
    onSuccess: (updatedBoard, { boardId }) => {
      // Update board cache
      queryClient.setQueryData(
        kanbanKeys.board(boardId),
        updatedBoard as any
      );

      toast.success('Coluna criada com sucesso!');
    },
    onError: (error) => {
      console.error('Erro ao criar coluna:', error);
      toast.error('Erro ao criar coluna. Tente novamente.');
    },
  });
}

export function useUpdateColumn() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      boardId,
      columnId,
      columnData,
      userId
    }: {
      boardId: string;
      columnId: string;
      columnData: UpdateColumnData;
      userId: string;
    }) => kanban.updateColumnInBoard(boardId, columnId, columnData, userId),
    onSuccess: (updatedBoard, { boardId }) => {
      // Update board cache
      queryClient.setQueryData(
        kanbanKeys.board(boardId),
        updatedBoard as any
      );

      toast.success('Coluna atualizada com sucesso!');
    },
    onError: (error) => {
      console.error('Erro ao atualizar coluna:', error);
      toast.error('Erro ao atualizar coluna. Tente novamente.');
    },
  });
}

export function useDeleteColumn() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      boardId,
      columnId,
      userId
    }: {
      boardId: string;
      columnId: string;
      userId: string;
    }) => kanban.deleteColumnFromBoard(boardId, columnId, userId),
    onSuccess: (updatedBoard, { boardId }) => {
      // Update board cache
      queryClient.setQueryData(
        kanbanKeys.board(boardId),
        updatedBoard as any
      );

      toast.success('Coluna excluída com sucesso!');
    },
    onError: (error) => {
      console.error('Erro ao excluir coluna:', error);
      toast.error('Erro ao excluir coluna. Tente novamente.');
    },
  });
}

// ============================================================================
// TASK HOOKS
// ============================================================================

export function useAddTask() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      boardId,
      columnId,
      taskData,
      userId
    }: {
      boardId: string;
      columnId: string;
      taskData: CreateTaskData;
      userId: string;
    }) => kanban.addTaskToColumn(boardId, columnId, taskData, userId),
    onSuccess: (updatedBoard, { boardId }) => {
      // Update board cache
      queryClient.setQueryData(
        kanbanKeys.board(boardId),
        updatedBoard as any
      );

      toast.success('Tarefa criada com sucesso!');
    },
    onError: (error) => {
      console.error('Erro ao criar tarefa:', error);
      toast.error('Erro ao criar tarefa. Tente novamente.');
    },
  });
}

export function useUpdateTask() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      boardId,
      taskId,
      taskData,
      userId
    }: {
      boardId: string;
      taskId: string;
      taskData: UpdateTaskData;
      userId: string;
    }) => kanban.updateTaskInBoard(boardId, taskId, taskData, userId),
    onSuccess: (updatedBoard, { boardId }) => {
      // Update board cache
      queryClient.setQueryData(
        kanbanKeys.board(boardId),
        updatedBoard as any
      );

      toast.success('Tarefa atualizada com sucesso!');
    },
    onError: (error) => {
      console.error('Erro ao atualizar tarefa:', error);
      toast.error('Erro ao atualizar tarefa. Tente novamente.');
    },
  });
}

export function useDeleteTask() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      boardId,
      taskId,
      userId
    }: {
      boardId: string;
      taskId: string;
      userId: string;
    }) => kanban.deleteTaskFromBoard(boardId, taskId, userId),
    onSuccess: (updatedBoard, { boardId }) => {
      // Update board cache
      queryClient.setQueryData(
        kanbanKeys.board(boardId),
        updatedBoard as any
      );

      toast.success('Tarefa excluída com sucesso!');
    },
    onError: (error) => {
      console.error('Erro ao excluir tarefa:', error);
      toast.error('Erro ao excluir tarefa. Tente novamente.');
    },
  });
}

export function useMoveTask() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      boardId,
      taskId,
      newColumnId,
      newPosition,
      userId
    }: {
      boardId: string;
      taskId: string;
      newColumnId: string;
      newPosition: number;
      userId: string;
    }) => kanban.moveTaskBetweenColumns(boardId, taskId, '', newColumnId, newPosition, userId),
    onSuccess: (updatedBoard, { boardId }) => {
      // Update board cache
      queryClient.setQueryData(
        kanbanKeys.board(boardId),
        updatedBoard as any
      );
    },
    onError: (error) => {
      console.error('Erro ao mover tarefa:', error);
      toast.error('Erro ao mover tarefa. Tente novamente.');
    },
  });
}

// ============================================================================
// HOOKS PARA COMPATIBILIDADE COM COMPONENTES ANTIGOS
// ============================================================================

export function useBoards(userId?: string, workspaceId?: string) {
  return useUserBoards(userId, workspaceId);
}

export function useBoardsSummary(userId?: string) {
  const { data: boards } = useUserBoards(userId);

  return {
    data: boards ? {
      total: boards.length,
      active: boards.filter(b => !b.isArchived).length,
      archived: boards.filter(b => b.isArchived).length,
      templates: boards.filter(b => b.isTemplate).length,
    } : undefined
  };
}

export function useCreateColumn() {
  return useAddColumn();
}

export function useCreateTask() {
  return useAddTask();
}

// Alias para compatibilidade
export const useBoardWithData = useBoard;
