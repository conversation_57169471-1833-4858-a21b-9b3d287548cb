import { ID, Query } from 'appwrite';
import { databases, DATABASE_ID, COLLECTIONS, account } from '../config';
import { listTeams } from './teams';
import { createDocumentPermissions } from '../../document-permissions';
import type { UserPermissionContext } from '@/schemas/permissions';

/**
 * Simple Appwrite database functions
 * Direct calls without unnecessary complexity
 */

export interface CreateDocumentOptions {
  userId: string;
  teamId?: string | null;
  permissionContext?: UserPermissionContext | null;
  isPublic?: boolean;
  customPermissions?: string[];
}

/**
 * Create a document with proper permissions
 */
export async function createDocument(
  collectionId: string,
  data: Record<string, any>,
  options?: CreateDocumentOptions,
  documentId?: string
) {
  let permissions: string[] = [];

  // Se foram fornecidas permissões customizadas, usar elas
  if (options?.customPermissions) {
    permissions = options.customPermissions;
  }
  // <PERSON><PERSON>o contrário, gerar permissões baseadas no usuário e team
  else if (options?.userId) {
    permissions = createDocumentPermissions(
      options.userId,
      options.teamId,
      options.permissionContext,
      options.isPublic || false
    );
  }

  const result = await databases.createDocument(
    DATABASE_ID,
    collectionId,
    documentId || ID.unique(),
    data,
    permissions.length > 0 ? permissions : undefined
  );

  return result;
}

/**
 * Create a document (legacy function for backward compatibility)
 */
export async function createDocumentLegacy(collectionId: string, data: Record<string, any>, documentId?: string) {
  const result = await databases.createDocument(
    DATABASE_ID,
    collectionId,
    documentId || ID.unique(),
    data
  );

  return result;
}

/**
 * Get a document by ID (filters out soft deleted documents if supported)
 */
export async function getDocument(collectionId: string, documentId: string) {
  const result = await databases.getDocument(DATABASE_ID, collectionId, documentId);

  // Check if document is soft deleted (only for collections that support it)
  if (hasSoftDeleteField(collectionId) && result.isDeleted === true) {
    throw new Error('Document not found');
  }

  return result;
}

/**
 * Update a document
 */
export async function updateDocument(collectionId: string, documentId: string, data: Record<string, any>) {
  const result = await databases.updateDocument(DATABASE_ID, collectionId, documentId, data);
  return result;
}

/**
 * Soft delete a document (sets isDeleted to true, only for collections that support it)
 */
export async function softDeleteDocument(collectionId: string, documentId: string) {
  if (!hasSoftDeleteField(collectionId)) {
    throw new Error(`Collection ${collectionId} does not support soft delete. Use hardDeleteDocument instead.`);
  }

  const result = await databases.updateDocument(DATABASE_ID, collectionId, documentId, {
    isDeleted: true
  });
  return result;
}

/**
 * Hard delete a document (permanently removes from database)
 * Use with caution - this is irreversible
 */
export async function hardDeleteDocument(collectionId: string, documentId: string) {
  const result = await databases.deleteDocument(DATABASE_ID, collectionId, documentId);
  return result;
}

/**
 * Restore a soft deleted document (sets isDeleted to false, only for collections that support it)
 */
export async function restoreDocument(collectionId: string, documentId: string) {
  if (!hasSoftDeleteField(collectionId)) {
    throw new Error(`Collection ${collectionId} does not support soft delete.`);
  }

  const result = await databases.updateDocument(DATABASE_ID, collectionId, documentId, {
    isDeleted: false
  });
  return result;
}

/**
 * Delete a document (uses soft delete if supported, otherwise hard delete)
 */
export async function deleteDocument(collectionId: string, documentId: string) {
  if (hasSoftDeleteField(collectionId)) {
    return softDeleteDocument(collectionId, documentId);
  } else {
    return hardDeleteDocument(collectionId, documentId);
  }
}

/**
 * Collections that have both teamId and isDeleted fields
 */
const COLLECTIONS_WITH_TEAM_AND_SOFT_DELETE = [
  COLLECTIONS.CLIENTS,
  COLLECTIONS.ACTIVITY_LOGS,
  COLLECTIONS.TEAM_CHATS,
  COLLECTIONS.CHAT_MESSAGES,
  COLLECTIONS.EVENTS,
  COLLECTIONS.EVENT_CATEGORIES,
  COLLECTIONS.KANBAN_BOARDS_OPTIMIZED,
];

/**
 * Collections that have isDeleted but no teamId
 */
const COLLECTIONS_WITH_SOFT_DELETE_ONLY = [
  COLLECTIONS.NOTIFICATIONS,
  COLLECTIONS.PUBLIC_PROFILES,
];

/**
 * Check if collection has teamId field
 */
function hasTeamIdField(collectionId: string): boolean {
  return COLLECTIONS_WITH_TEAM_AND_SOFT_DELETE.includes(collectionId);
}

/**
 * Check if collection has isDeleted field
 */
function hasSoftDeleteField(collectionId: string): boolean {
  return COLLECTIONS_WITH_TEAM_AND_SOFT_DELETE.includes(collectionId) ||
         COLLECTIONS_WITH_SOFT_DELETE_ONLY.includes(collectionId);
}

/**
 * List documents with appropriate queries based on collection schema
 */
export async function listDocuments(collectionId: string) {
  const user = await account.get();
  const queries = [Query.orderDesc('$createdAt')];

  // Add user filter
  queries.push(Query.equal('userId', user.$id));

  // Add team filter if collection supports it
  if (hasTeamIdField(collectionId)) {
    const teams = await listTeams();
    const teamId = teams.teams[0]?.$id || '';
    if (teamId) {
      // Replace user filter with OR condition for user or team
      queries[1] = Query.or([
        Query.equal('userId', user.$id),
        Query.equal('teamId', teamId)
      ]);
    }
  }

  // Add soft delete filter if collection supports it
  if (hasSoftDeleteField(collectionId)) {
    queries.push(Query.equal('isDeleted', false));
  }

  const result = await databases.listDocuments(DATABASE_ID, collectionId, queries);
  return result;
}

/**
 * List only soft deleted documents (only for collections that support soft delete)
 */
export async function listDeletedDocuments(collectionId: string) {
  if (!hasSoftDeleteField(collectionId)) {
    throw new Error(`Collection ${collectionId} does not support soft delete`);
  }

  const user = await account.get();
  const queries = [Query.orderDesc('$createdAt')];

  // Add user filter
  queries.push(Query.equal('userId', user.$id));

  // Add team filter if collection supports it
  if (hasTeamIdField(collectionId)) {
    const teams = await listTeams();
    const teamId = teams.teams[0]?.$id || '';
    if (teamId) {
      // Replace user filter with OR condition for user or team
      queries[1] = Query.or([
        Query.equal('userId', user.$id),
        Query.equal('teamId', teamId)
      ]);
    }
  }

  // Add soft delete filter for deleted documents only
  queries.push(Query.equal('isDeleted', true));

  const result = await databases.listDocuments(DATABASE_ID, collectionId, queries);
  return result;
}

/**
 * List all documents (including soft deleted ones if supported)
 */
export async function listAllDocuments(collectionId: string) {
  const user = await account.get();
  const queries = [Query.orderDesc('$createdAt')];

  // Add user filter
  queries.push(Query.equal('userId', user.$id));

  // Add team filter if collection supports it
  if (hasTeamIdField(collectionId)) {
    const teams = await listTeams();
    const teamId = teams.teams[0]?.$id || '';
    if (teamId) {
      // Replace user filter with OR condition for user or team
      queries[1] = Query.or([
        Query.equal('userId', user.$id),
        Query.equal('teamId', teamId)
      ]);
    }
  }

  // Note: No isDeleted filter here - we want all documents

  const result = await databases.listDocuments(DATABASE_ID, collectionId, queries);
  return result;
}

export const clients = {
  create: (data: Record<string, any>, options?: CreateDocumentOptions) =>
    createDocument(COLLECTIONS.CLIENTS, data, options),
  get: (id: string) => getDocument(COLLECTIONS.CLIENTS, id),
  update: (id: string, data: Record<string, any>) => updateDocument(COLLECTIONS.CLIENTS, id, data),
  delete: (id: string) => deleteDocument(COLLECTIONS.CLIENTS, id),
  softDelete: (id: string) => softDeleteDocument(COLLECTIONS.CLIENTS, id),
  hardDelete: (id: string) => hardDeleteDocument(COLLECTIONS.CLIENTS, id),
  restore: (id: string) => restoreDocument(COLLECTIONS.CLIENTS, id),
  list: () => listDocuments(COLLECTIONS.CLIENTS),
  listDeleted: () => listDeletedDocuments(COLLECTIONS.CLIENTS),
  listAll: () => listAllDocuments(COLLECTIONS.CLIENTS),
};



/**
 * Notifications collection functions
 */
export const notifications = {
  create: (data: Record<string, any>) => createDocument(COLLECTIONS.NOTIFICATIONS, data),
  get: (id: string) => getDocument(COLLECTIONS.NOTIFICATIONS, id),
  update: (id: string, data: Record<string, any>) => updateDocument(COLLECTIONS.NOTIFICATIONS, id, data),
  delete: (id: string) => deleteDocument(COLLECTIONS.NOTIFICATIONS, id),
  softDelete: (id: string) => softDeleteDocument(COLLECTIONS.NOTIFICATIONS, id),
  hardDelete: (id: string) => hardDeleteDocument(COLLECTIONS.NOTIFICATIONS, id),
  restore: (id: string) => restoreDocument(COLLECTIONS.NOTIFICATIONS, id),
  list: () => listDocuments(COLLECTIONS.NOTIFICATIONS),
  listDeleted: () => listDeletedDocuments(COLLECTIONS.NOTIFICATIONS),
  listAll: () => listAllDocuments(COLLECTIONS.NOTIFICATIONS),
};

/**
 * Activity logs collection functions
 */
export const activityLogs = {
  create: (data: Record<string, any>) => createDocument(COLLECTIONS.ACTIVITY_LOGS, data),
  get: (id: string) => getDocument(COLLECTIONS.ACTIVITY_LOGS, id),
  update: (id: string, data: Record<string, any>) => updateDocument(COLLECTIONS.ACTIVITY_LOGS, id, data),
  delete: (id: string) => deleteDocument(COLLECTIONS.ACTIVITY_LOGS, id),
  softDelete: (id: string) => softDeleteDocument(COLLECTIONS.ACTIVITY_LOGS, id),
  hardDelete: (id: string) => hardDeleteDocument(COLLECTIONS.ACTIVITY_LOGS, id),
  restore: (id: string) => restoreDocument(COLLECTIONS.ACTIVITY_LOGS, id),
  list: () => listDocuments(COLLECTIONS.ACTIVITY_LOGS),
  listDeleted: () => listDeletedDocuments(COLLECTIONS.ACTIVITY_LOGS),
  listAll: () => listAllDocuments(COLLECTIONS.ACTIVITY_LOGS),
};

/**
 * Chat messages collection functions
 */
export const chatMessages = {
  create: (data: Record<string, any>, options?: CreateDocumentOptions, documentId?: string) =>
    createDocument(COLLECTIONS.CHAT_MESSAGES, data, options, documentId),
  get: (id: string) => getDocument(COLLECTIONS.CHAT_MESSAGES, id),
  update: (id: string, data: Record<string, any>) => updateDocument(COLLECTIONS.CHAT_MESSAGES, id, data),
  delete: (id: string) => deleteDocument(COLLECTIONS.CHAT_MESSAGES, id),
  softDelete: (id: string) => softDeleteDocument(COLLECTIONS.CHAT_MESSAGES, id),
  hardDelete: (id: string) => hardDeleteDocument(COLLECTIONS.CHAT_MESSAGES, id),
  restore: (id: string) => restoreDocument(COLLECTIONS.CHAT_MESSAGES, id),
  list: () => listDocuments(COLLECTIONS.CHAT_MESSAGES),
  listDeleted: () => listDeletedDocuments(COLLECTIONS.CHAT_MESSAGES),
  listAll: () => listAllDocuments(COLLECTIONS.CHAT_MESSAGES),
  // Função específica para buscar mensagens por chatId
  getByChatId: async (chatId: string) => {
    const result = await databases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.CHAT_MESSAGES,
      [
        Query.equal('chat', chatId),
        Query.equal('isDeleted', false),
        Query.orderAsc('$createdAt') // Mudança: ordenar ascendente para cronológico
      ]
    );
    return result.documents;
  },
  // Função para buscar mensagens com sincronização (incluindo deletadas para sync)
  getByChatIdWithDeleted: async (chatId: string) => {
    const result = await databases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.CHAT_MESSAGES,
      [
        Query.equal('chat', chatId),
        Query.orderAsc('$createdAt')
      ]
    );
    return result.documents;
  },
};

/**
 * Team chats collection functions
 */
export const teamChats = {
  create: (data: Record<string, any>) => createDocument(COLLECTIONS.TEAM_CHATS, data),
  get: (id: string) => getDocument(COLLECTIONS.TEAM_CHATS, id),
  update: (id: string, data: Record<string, any>) => updateDocument(COLLECTIONS.TEAM_CHATS, id, data),
  delete: (id: string) => deleteDocument(COLLECTIONS.TEAM_CHATS, id),
  softDelete: (id: string) => softDeleteDocument(COLLECTIONS.TEAM_CHATS, id),
  hardDelete: (id: string) => hardDeleteDocument(COLLECTIONS.TEAM_CHATS, id),
  restore: (id: string) => restoreDocument(COLLECTIONS.TEAM_CHATS, id),
  list: () => listDocuments(COLLECTIONS.TEAM_CHATS),
  listDeleted: () => listDeletedDocuments(COLLECTIONS.TEAM_CHATS),
  listAll: () => listAllDocuments(COLLECTIONS.TEAM_CHATS),
  // Função específica para buscar chat por teamId
  getByTeamId: async (teamId: string) => {
    const result = await databases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.TEAM_CHATS,
      [
        Query.equal('teamId', teamId),
        Query.equal('isActive', true),
        Query.equal('isDeleted', false)
      ]
    );
    return result.documents.length > 0 ? result.documents[0] : null;
  },
  // Função simplificada - agora mensagens são buscadas separadamente
  // Esta função é mantida para compatibilidade mas não é mais recomendada
  getWithMessages: async (chatId: string) => {
    console.warn('⚠️ getWithMessages está deprecated. Use chatMessages.getByChatId() separadamente.');
    // Buscar apenas chat básico
    const chat = await getDocument(COLLECTIONS.TEAM_CHATS, chatId);
    return {
      ...chat,
      messages: [] // Array vazio - mensagens devem ser buscadas separadamente
    };
  },
};

// Export Query for convenience
export { Query };
