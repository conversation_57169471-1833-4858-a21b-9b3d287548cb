/**
 * Ka<PERSON>ban Due Date Checker Hook
 * Automatically checks for tasks with approaching or overdue dates
 */

import { useEffect, useRef } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from './use-auth';
import { useBoards } from './api/use-kanban';
import { useDueDateChecker } from './api/use-kanban-notifications';
import type { Task } from '@/schemas/kanban';

interface DueDateCheckConfig {
  enabled?: boolean;
  checkInterval?: number; // in minutes
  notifyHoursBefore?: number[];
}

const DEFAULT_CONFIG: DueDateCheckConfig = {
  enabled: true,
  checkInterval: 30, // Check every 30 minutes
  notifyHoursBefore: [24, 4, 1], // Notify 24h, 4h, and 1h before due
};

/**
 * Hook to automatically check for tasks with approaching due dates
 */
export function useKanbanDueDateChecker(config: DueDateCheckConfig = {}) {
  const { user } = useAuth();
  const { data: boards } = useBoards();
  const { checkTasksDueDates } = useDueDateChecker();
  const lastCheckRef = useRef<Record<string, number>>({});

  const finalConfig = { ...DEFAULT_CONFIG, ...config };

  // Get all tasks from all boards
  const allTasks = useQuery({
    queryKey: ['kanban', 'all-tasks', user?.$id],
    queryFn: async (): Promise<{ tasks: Task[]; boardTitles: Record<string, string> }> => {
      if (!boards || boards.length === 0) {
        return { tasks: [], boardTitles: {} };
      }

      const allTasks: Task[] = [];
      const boardTitles: Record<string, string> = {};

      // For now, we'll just return empty since we'd need to fetch all board data
      // In a real implementation, you might want to have a separate API endpoint
      // that returns all tasks for the user across all boards

      boards.forEach(board => {
        boardTitles[board.$id] = board.title;
      });

      return { tasks: allTasks, boardTitles };
    },
    enabled: !!user?.$id && !!boards && finalConfig.enabled,
    refetchInterval: (finalConfig.checkInterval || 30) * 60 * 1000, // Convert to milliseconds
    refetchOnWindowFocus: false,
  });

  // Check due dates when tasks data changes
  useEffect(() => {
    if (!allTasks.data || !finalConfig.enabled) return;

    const { tasks, boardTitles } = allTasks.data;
    const now = Date.now();

    // Group tasks by board for efficient processing
    const tasksByBoard: Record<string, Task[]> = {};

    tasks.forEach(task => {
      if (!task.dueDate || !task.assignedTo || task.status === 'done') return;

      if (!tasksByBoard[task.boardId!]) {
        tasksByBoard[task.boardId!] = [];
      }
      tasksByBoard[task.boardId!].push(task);
    });

    // Check each board's tasks
    Object.entries(tasksByBoard).forEach(([boardId, boardTasks]) => {
      const boardTitle = boardTitles[boardId] || 'Unknown Board';

      // Check if we should notify for this board
      const lastCheck = lastCheckRef.current[boardId] || 0;
      const timeSinceLastCheck = now - lastCheck;
      const minInterval = 60 * 60 * 1000; // Minimum 1 hour between notifications for same board

      if (timeSinceLastCheck >= minInterval) {
        checkTasksDueDates(boardTasks, boardTitle);
        lastCheckRef.current[boardId] = now;
      }
    });
  }, [allTasks.data, finalConfig.enabled, checkTasksDueDates]);

  return {
    isEnabled: finalConfig.enabled,
    lastCheck: lastCheckRef.current,
    tasksCount: allTasks.data?.tasks.length || 0,
    isLoading: allTasks.isLoading,
  };
}

/**
 * Hook to manually check due dates for specific board tasks
 */
export function useManualDueDateCheck() {
  const { checkTasksDueDates } = useDueDateChecker();

  const checkBoardTasks = async (tasks: Task[], boardTitle: string) => {
    const tasksWithDueDates = tasks.filter(
      task => task.dueDate && task.assignedTo && task.status !== 'done'
    );

    if (tasksWithDueDates.length > 0) {
      await checkTasksDueDates(tasksWithDueDates, boardTitle);
    }
  };

  return { checkBoardTasks };
}

/**
 * Hook to get tasks that are due soon or overdue
 */
export function useDueTasks() {
  const { user } = useAuth();
  const { data: boards } = useBoards();

  return useQuery({
    queryKey: ['kanban', 'due-tasks', user?.$id],
    queryFn: async () => {
      if (!boards || boards.length === 0) return [];

      const now = new Date();
      const dueTasks: Array<Task & { boardTitle: string; hoursUntilDue: number }> = [];

      // This would need to be implemented to fetch all tasks
      // For now, returning empty array

      return dueTasks.sort((a, b) => a.hoursUntilDue - b.hoursUntilDue);
    },
    enabled: !!user?.$id && !!boards,
    refetchInterval: 5 * 60 * 1000, // Refresh every 5 minutes
  });
}

/**
 * Hook to get overdue tasks count for dashboard
 */
export function useOverdueTasksCount() {
  const dueTasks = useDueTasks();

  const overdueCount = dueTasks.data?.filter(task => task.hoursUntilDue < 0).length || 0;
  const dueSoonCount = dueTasks.data?.filter(task =>
    task.hoursUntilDue >= 0 && task.hoursUntilDue <= 24
  ).length || 0;

  return {
    overdueCount,
    dueSoonCount,
    totalCount: overdueCount + dueSoonCount,
    isLoading: dueTasks.isLoading,
  };
}

/**
 * Hook to enable/disable due date checking
 */
export function useDueDateCheckingSettings() {
  const setEnabled = (enabled: boolean) => {
    // This could be stored in user preferences
    localStorage.setItem('kanban-due-date-checking', enabled.toString());
  };

  const getEnabled = (): boolean => {
    const stored = localStorage.getItem('kanban-due-date-checking');
    return stored !== null ? stored === 'true' : true; // Default to enabled
  };

  const setCheckInterval = (minutes: number) => {
    localStorage.setItem('kanban-check-interval', minutes.toString());
  };

  const getCheckInterval = (): number => {
    const stored = localStorage.getItem('kanban-check-interval');
    return stored ? parseInt(stored, 10) : 30; // Default to 30 minutes
  };

  return {
    enabled: getEnabled(),
    checkInterval: getCheckInterval(),
    setEnabled,
    setCheckInterval,
  };
}
