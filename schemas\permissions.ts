/**
 * Sistema de Permissões e Cargos Customizáveis
 * Define tipos para permissões granulares e cargos personalizados por time
 * Utiliza as preferências do Appwrite Teams para armazenar os dados
 */



// ============================================================================
// TIPOS BASE DE USUÁRIO
// ============================================================================

/**
 * Tipos de usuário no sistema
 */
export type UserType = 'owner' | 'admin' | 'user' | 'guest';

/**
 * Recursos disponíveis no sistema
 */
export type SystemResource =
  | 'dashboard'
  | 'analytics'
  | 'calendar'
  | 'documents'
  | 'kanban'
  | 'clients'
  | 'teams'
  | 'team_chat'
  | 'activities'
  | 'reports'
  | 'preferences'
  | 'plans'
  | 'help';

/**
 * Ações disponíveis para cada recurso
 */
export type ResourceAction = 'view' | 'create' | 'edit' | 'delete' | 'manage';

/**
 * Permissão específica para um recurso
 */
export interface ResourcePermission {
  resource: SystemResource;
  actions: ResourceAction[];
}

// ============================================================================
// CARGOS CUSTOMIZÁVEIS (armazenados nas preferências do time)
// ============================================================================

/**
 * Cargo customizado criado pelo time
 */
export interface TeamRole {
  // Identificação
  id: string; // UUID único
  name: string;
  description?: string;
  color?: string;

  // Tipo base do usuário (owner tem acesso total)
  userType: UserType;

  // Permissões específicas (apenas para collaborators)
  permissions: ResourcePermission[];

  // Configurações
  isDefault: boolean; // Se é o cargo padrão para novos membros
  isActive: boolean;

  // Auditoria
  createdBy: string;
  createdAt: string;
  updatedBy?: string;
  updatedAt?: string;
}

/**
 * Dados para criar um novo cargo
 */
export interface CreateTeamRoleData {
  name: string;
  description?: string;
  color?: string;
  userType: UserType;
  permissions: ResourcePermission[];
  isDefault?: boolean;
}

/**
 * Dados para atualizar um cargo
 */
export interface UpdateTeamRoleData {
  name?: string;
  description?: string;
  color?: string;
  permissions?: ResourcePermission[];
  isDefault?: boolean;
  isActive?: boolean;
}

// ============================================================================
// ATRIBUIÇÃO DE CARGOS (armazenado nas preferências do time)
// ============================================================================

/**
 * Atribuição de cargo a um membro do time
 */
export interface TeamMemberRole {
  userId: string;
  roleId: string;

  // Dados desnormalizados para performance
  roleName: string;
  userType: UserType;

  // Auditoria
  assignedBy: string;
  assignedAt: string;
}

/**
 * Dados para atribuir cargo a um membro
 */
export interface AssignRoleData {
  userId: string;
  roleId: string;
}

// ============================================================================
// ESTRUTURA DAS PREFERÊNCIAS DO TIME
// ============================================================================

/**
 * Estrutura completa das preferências do time incluindo permissões
 */
export interface TeamPermissionPreferences {
  // Configurações básicas existentes
  description?: string;
  color?: string;
  timezone?: string;
  notifyOnMention?: boolean;
  notifyOnMessage?: boolean;
  notifyOnJoin?: boolean;
  emailNotifications?: boolean;
  allowGifs?: boolean;
  allowEmojis?: boolean;
  allowFileUploads?: boolean;
  isPublic?: boolean;
  allowInvites?: boolean;
  requireApproval?: boolean;
  updatedAt?: string;

  // Novas configurações de permissões
  roles?: TeamRole[];
  memberRoles?: TeamMemberRole[];

  // Configurações de permissões
  permissionsVersion?: string; // Para versionamento
  lastPermissionUpdate?: string;
}

// ============================================================================
// CONTEXTO DE PERMISSÕES
// ============================================================================

/**
 * Contexto completo de permissões do usuário
 */
export interface UserPermissionContext {
  userId: string;
  currentTeamId?: string;
  userType: UserType;
  roleId?: string;
  roleName?: string;
  permissions: ResourcePermission[];

  // Cache de verificações rápidas
  hasFullAccess: boolean; // true se for owner
  accessibleResources: SystemResource[];
}

/**
 * Resultado de verificação de permissão
 */
export interface PermissionCheck {
  allowed: boolean;
  reason?: string;
  requiredPermission?: {
    resource: SystemResource;
    action: ResourceAction;
  };
}

// ============================================================================
// CONFIGURAÇÕES PADRÃO
// ============================================================================

/**
 * Permissões padrão para owners (acesso total)
 */
export const OWNER_PERMISSIONS: ResourcePermission[] = [
  { resource: 'dashboard', actions: ['view', 'create', 'edit', 'delete', 'manage'] },
  { resource: 'analytics', actions: ['view', 'create', 'edit', 'delete', 'manage'] },
  { resource: 'calendar', actions: ['view', 'create', 'edit', 'delete', 'manage'] },
  { resource: 'documents', actions: ['view', 'create', 'edit', 'delete', 'manage'] },
  { resource: 'kanban', actions: ['view', 'create', 'edit', 'delete', 'manage'] },
  { resource: 'clients', actions: ['view', 'create', 'edit', 'delete', 'manage'] },
  { resource: 'teams', actions: ['view', 'create', 'edit', 'delete', 'manage'] },
  { resource: 'team_chat', actions: ['view', 'create', 'edit', 'delete', 'manage'] },
  { resource: 'activities', actions: ['view', 'create', 'edit', 'delete', 'manage'] },
  { resource: 'reports', actions: ['view', 'create', 'edit', 'delete', 'manage'] },
  { resource: 'preferences', actions: ['view', 'create', 'edit', 'delete', 'manage'] },
  { resource: 'plans', actions: ['view', 'create', 'edit', 'delete', 'manage'] },
  { resource: 'help', actions: ['view'] },
];

/**
 * Permissões padrão para Guest - apenas visualizar
 */
export const DEFAULT_GUEST_PERMISSIONS: ResourcePermission[] = [
  { resource: 'dashboard', actions: ['view'] },
  { resource: 'activities', actions: ['view'] },
  { resource: 'help', actions: ['view'] },
];

/**
 * Permissões padrão para User - pode criar, ver, editar próprios itens
 */
export const DEFAULT_USER_PERMISSIONS: ResourcePermission[] = [
  { resource: 'dashboard', actions: ['view'] },
  { resource: 'analytics', actions: ['view'] },
  { resource: 'calendar', actions: ['view', 'create', 'edit'] },
  { resource: 'documents', actions: ['view', 'create', 'edit'] },
  { resource: 'kanban', actions: ['view', 'create', 'edit'] },
  { resource: 'clients', actions: ['view', 'create', 'edit'] },
  { resource: 'activities', actions: ['view'] },
  { resource: 'help', actions: ['view'] },
];

/**
 * Permissões padrão para Admin - CRUD completo + convidar membros
 */
export const DEFAULT_ADMIN_PERMISSIONS: ResourcePermission[] = [
  { resource: 'dashboard', actions: ['view', 'create', 'edit', 'delete', 'manage'] },
  { resource: 'analytics', actions: ['view', 'create', 'edit', 'delete', 'manage'] },
  { resource: 'calendar', actions: ['view', 'create', 'edit', 'delete', 'manage'] },
  { resource: 'documents', actions: ['view', 'create', 'edit', 'delete', 'manage'] },
  { resource: 'kanban', actions: ['view', 'create', 'edit', 'delete', 'manage'] },
  { resource: 'clients', actions: ['view', 'create', 'edit', 'delete', 'manage'] },
  { resource: 'teams', actions: ['view', 'manage'] }, // Pode convidar membros mas não alterar preferências
  { resource: 'activities', actions: ['view', 'create', 'edit', 'delete'] },
  { resource: 'reports', actions: ['view', 'create', 'edit', 'delete'] },
  { resource: 'help', actions: ['view'] },
];

/**
 * Cargo padrão para Owner
 */
export const DEFAULT_OWNER_ROLE: Omit<TeamRole, 'id' | 'createdBy' | 'createdAt'> = {
  name: 'Proprietário',
  description: 'Acesso total ao sistema e pode alterar preferências do time',
  color: '#dc2626',
  userType: 'owner',
  permissions: OWNER_PERMISSIONS,
  isDefault: false,
  isActive: true,
};

/**
 * Cargo padrão para Admin
 */
export const DEFAULT_ADMIN_ROLE: Omit<TeamRole, 'id' | 'createdBy' | 'createdAt'> = {
  name: 'Administrador',
  description: 'CRUD completo e pode convidar membros',
  color: '#f59e0b',
  userType: 'admin',
  permissions: DEFAULT_ADMIN_PERMISSIONS,
  isDefault: false,
  isActive: true,
};

/**
 * Cargo padrão para User
 */
export const DEFAULT_USER_ROLE: Omit<TeamRole, 'id' | 'createdBy' | 'createdAt'> = {
  name: 'Usuário',
  description: 'Pode criar, ver e editar próprios itens',
  color: '#3b82f6',
  userType: 'user',
  permissions: DEFAULT_USER_PERMISSIONS,
  isDefault: true,
  isActive: true,
};

/**
 * Cargo padrão para Guest
 */
export const DEFAULT_GUEST_ROLE: Omit<TeamRole, 'id' | 'createdBy' | 'createdAt'> = {
  name: 'Convidado',
  description: 'Apenas visualização',
  color: '#6b7280',
  userType: 'guest',
  permissions: DEFAULT_GUEST_PERMISSIONS,
  isDefault: false,
  isActive: true,
};

/**
 * Mapeamento de recursos para ícones e labels
 */
export const RESOURCE_CONFIG: Record<SystemResource, {
  label: string;
  description: string;
  icon: string;
  category: 'core' | 'data' | 'communication' | 'settings';
}> = {
  dashboard: {
    label: 'Dashboard',
    description: 'Visão geral e métricas principais',
    icon: 'IconDashboard',
    category: 'core'
  },
  analytics: {
    label: 'Analytics',
    description: 'Relatórios e análises avançadas',
    icon: 'IconChartLine',
    category: 'core'
  },
  calendar: {
    label: 'Calendário',
    description: 'Agendamentos e eventos',
    icon: 'IconCalendar',
    category: 'core'
  },
  documents: {
    label: 'Documentos',
    description: 'Gerenciamento de arquivos',
    icon: 'IconFolder',
    category: 'data'
  },
  kanban: {
    label: 'Kanban',
    description: 'Quadros de tarefas',
    icon: 'IconLayoutKanban',
    category: 'core'
  },
  clients: {
    label: 'Clientes',
    description: 'Gerenciamento de clientes',
    icon: 'IconUsersGroup',
    category: 'data'
  },
  teams: {
    label: 'Times',
    description: 'Gerenciamento de equipes',
    icon: 'IconUsersGroup',
    category: 'settings'
  },
  team_chat: {
    label: 'Chat de Time',
    description: 'Comunicação interna',
    icon: 'IconMessage',
    category: 'communication'
  },
  activities: {
    label: 'Atividades',
    description: 'Log de ações do sistema',
    icon: 'IconActivity',
    category: 'core'
  },
  reports: {
    label: 'Relatórios',
    description: 'Relatórios personalizados',
    icon: 'IconChartBar',
    category: 'core'
  },
  preferences: {
    label: 'Preferências',
    description: 'Configurações pessoais',
    icon: 'IconSettings',
    category: 'settings'
  },
  plans: {
    label: 'Planos',
    description: 'Assinatura e faturamento',
    icon: 'IconCreditCard',
    category: 'settings'
  },
  help: {
    label: 'Ajuda',
    description: 'Documentação e suporte',
    icon: 'IconHelp',
    category: 'settings'
  },
};

/**
 * Labels para ações
 */
export const ACTION_LABELS: Record<ResourceAction, string> = {
  view: 'Visualizar',
  create: 'Criar',
  edit: 'Editar',
  delete: 'Excluir',
  manage: 'Gerenciar',
};
