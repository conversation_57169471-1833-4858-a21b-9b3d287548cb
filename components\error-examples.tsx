import { useState } from 'react';
import { But<PERSON> } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { ErrorPage } from './error-page';
import { NotFoundPage } from './not-found-page';
import { Badge } from './ui/badge';

/**
 * Componente de exemplo para demonstrar diferentes tipos de erro
 * Útil para desenvolvimento e testes
 */
export function ErrorExamples() {
  const [currentError, setCurrentError] = useState<string | null>(null);

  const errorTypes = [
    {
      id: '404',
      title: 'Página não encontrada',
      description: 'Simula erro 404',
      component: <NotFoundPage />
    },
    {
      id: '403',
      title: 'Acesso negado',
      description: 'Simula erro 403',
      component: (
        <ErrorPage
          statusCode={403}
          title="Acesso negado"
          description="Você não tem permissão para acessar este recurso."
          error={new Error('Forbidden access')}
        />
      )
    },
    {
      id: '500',
      title: 'Erro interno',
      description: 'Simula erro 500',
      component: (
        <ErrorPage
          statusCode={500}
          title="Erro interno do servidor"
          description="Ocorreu um erro interno no servidor."
          error={new Error('Internal server error')}
        />
      )
    },
    {
      id: 'network',
      title: 'Erro de rede',
      description: 'Simula erro de conexão',
      component: (
        <ErrorPage
          title="Erro de conexão"
          description="Não foi possível conectar ao servidor."
          error={new Error('Network connection failed')}
        />
      )
    },
    {
      id: 'javascript',
      title: 'Erro JavaScript',
      description: 'Simula erro de aplicação',
      component: (
        <ErrorPage
          title="Erro na aplicação"
          description="Ocorreu um erro inesperado na aplicação."
          error={new Error('TypeError: Cannot read property of undefined')}
          onRetry={() => setCurrentError(null)}
        />
      )
    }
  ];

  if (currentError) {
    const errorType = errorTypes.find(e => e.id === currentError);
    return errorType?.component || null;
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Exemplos de Páginas de Erro</h1>
        <p className="text-muted-foreground">
          Demonstração dos diferentes tipos de erro implementados
        </p>
        <Badge variant="outline">Apenas para desenvolvimento</Badge>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {errorTypes.map((errorType) => (
          <Card key={errorType.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <CardTitle className="text-lg">{errorType.title}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-muted-foreground">
                {errorType.description}
              </p>
              <Button
                onClick={() => setCurrentError(errorType.id)}
                className="w-full"
                variant="outline"
              >
                Visualizar Erro
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="text-center pt-6">
        <p className="text-sm text-muted-foreground">
          💡 Dica: Estes componentes são automaticamente usados pelo ErrorBoundary do React Router
        </p>
      </div>
    </div>
  );
}
