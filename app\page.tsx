import HeroSection from "@/components/hero-section";
import { FeaturesSection } from "@/components/features-section";
import { BenefitsSection } from "@/components/benefits-section";
import { StatsSection } from "@/components/stats-section";
import { TestimonialsSection } from "@/components/testimonials-section";
import { PricingSection } from "@/components/pricing-section";
import { FAQSection } from "@/components/faq-section";
import { CTASection } from "@/components/cta-section";
import FooterSection from "@/components/footer";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Template SaaS Completo - Lance seu produto em 2 semanas",
  description: "Template completo para SaaS com autenticação, dashboard, teams, PWA, chat em tempo real e tudo que você precisa. Economize meses de desenvolvimento.",
  keywords: "template saas, react, typescript, appwrite, dashboard, autenticação, pwa, chat tempo real",
  openGraph: {
    title: "Template SaaS Completo - Lance seu produto em 2 semanas",
    description: "Template completo para SaaS com autenticação, dashboard, teams, PWA, chat em tempo real e tudo que você precisa. Economize meses de desenvolvimento.",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Template SaaS Completo - Lance seu produto em 2 semanas",
    description: "Template completo para SaaS com autenticação, dashboard, teams, PWA, chat em tempo real e tudo que você precisa. Economize meses de desenvolvimento.",
  },
};

export default function LandingPage() {
  return (
    <>
      <HeroSection />
      <section id="features">
        <FeaturesSection />
      </section>
      <BenefitsSection />
      <StatsSection />
      <section id="testimonials">
        <TestimonialsSection />
      </section>
      <section id="pricing">
        <PricingSection />
      </section>
      <FAQSection />
      <CTASection />
      <FooterSection />
    </>
  );
}
