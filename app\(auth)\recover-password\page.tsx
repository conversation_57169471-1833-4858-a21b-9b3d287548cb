'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2, ArrowLeft, CheckCircle } from 'lucide-react';
import { toast } from 'sonner';
import { sendPasswordRecovery } from '@/lib/appwrite/functions/auth';
import { log } from '@/lib/logger';

export default function RecoverPasswordPage() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email.trim()) {
      toast.error('Por favor, digite seu email');
      return;
    }

    setIsLoading(true);

    try {
      await sendPasswordRecovery(email.trim());
      setEmailSent(true);
      toast.success('Email de recuperação enviado!');
    } catch (error) {
      log.error('Password recovery error', error, { email: email.trim() });
      toast.error(
        error instanceof Error
          ? error.message
          : 'Erro ao enviar email de recuperação. Tente novamente.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  if (emailSent) {
    return (
      <div className="flex flex-col gap-6">
        <div className="flex flex-col items-center gap-4 text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">Email enviado!</h1>
            <p className="text-muted-foreground text-sm text-balance mt-2">
              Enviamos um link de recuperação para <strong>{email}</strong>
            </p>
          </div>
        </div>

        <div className="space-y-4">
          <div className="text-sm text-muted-foreground space-y-2">
            <p>• Verifique sua caixa de entrada e spam</p>
            <p>• O link expira em 1 hora</p>
            <p>• Se não receber, tente novamente</p>
          </div>

          <Button
            variant="outline"
            className="w-full"
            onClick={() => {
              setEmailSent(false);
              setEmail('');
            }}
          >
            Tentar outro email
          </Button>
        </div>

        <div className="text-center text-sm">
          <Link
            href="/login"
            className="text-primary hover:text-primary/80 transition-colors inline-flex items-center gap-1"
          >
            <ArrowLeft className="w-4 h-4" />
            Voltar ao login
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col items-center gap-2 text-center">
        <h1 className="text-2xl font-bold">Recuperar senha</h1>
        <p className="text-muted-foreground text-sm text-balance">
          Digite seu email para receber um link de recuperação
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            disabled={isLoading}
            required
          />
        </div>

        <Button
          type="submit"
          className="w-full"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Enviando...
            </>
          ) : (
            'Enviar link de recuperação'
          )}
        </Button>
      </form>

      <div className="text-center text-sm">
        <span className="text-muted-foreground">Lembrou da senha? </span>
        <Link
          href="/login"
          className="text-primary hover:text-primary/80 transition-colors font-medium"
        >
          Fazer login
        </Link>
      </div>

      <div className="text-center text-sm">
        <Link
          href="/register"
          className="text-primary hover:text-primary/80 transition-colors inline-flex items-center gap-1"
        >
          <ArrowLeft className="w-4 h-4" />
          Voltar ao início
        </Link>
      </div>
    </div>
  );
}
