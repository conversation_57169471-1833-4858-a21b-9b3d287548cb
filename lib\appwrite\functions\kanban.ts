/**
 * Kanban Appwrite Functions
 * Nova estrutura com apenas 2 coleções: WORKSPACES e KANBAN_BOARDS
 * Todas as operações CRUD otimizadas para performance
 */

import { databases, DATABASE_ID, COLLECTIONS } from '../config';
import { ID, Query } from 'appwrite';
import type {
  OptimizedWorkspace,
  OptimizedBoard,
  EmbeddedColumn,
  EmbeddedTask,
  EmbeddedLabel,
  EmbeddedChecklist,
  EmbeddedComment,
  EmbeddedAttachment,
  CreateWorkspaceData,
  CreateBoardData,
  CreateColumnData,
  CreateTaskData,
  CreateLabelData,
  CreateChecklistData,
  CreateCommentData,
  CreateAttachmentData,
  UpdateWorkspaceData,
  UpdateBoardData,
  UpdateColumnData,
  UpdateTaskData,
  UpdateCommentData,
} from '@/schemas/kanban';

import {
  findTaskInBoard,
  findColumnInBoard
} from '@/schemas/kanban';

// ============================================================================
// WORKSPACE FUNCTIONS - Mantém estrutura atual
// ============================================================================

export async function createWorkspace(data: CreateWorkspaceData) {
  return await databases.createDocument(
    DATABASE_ID,
    COLLECTIONS.WORKSPACES,
    ID.unique(),
    {
      ...data,
      createdBy: data.userId,
      updatedBy: data.userId,
    }
  );
}

export async function getWorkspace(workspaceId: string) {
  return await databases.getDocument(
    DATABASE_ID,
    COLLECTIONS.WORKSPACES,
    workspaceId
  );
}

export async function updateWorkspace(workspaceId: string, data: UpdateWorkspaceData) {
  return await databases.updateDocument(
    DATABASE_ID,
    COLLECTIONS.WORKSPACES,
    workspaceId,
    data
  );
}

export async function deleteWorkspace(workspaceId: string) {
  return await databases.deleteDocument(
    DATABASE_ID,
    COLLECTIONS.WORKSPACES,
    workspaceId
  );
}

export async function listUserWorkspaces(userId: string) {
  return await databases.listDocuments(
    DATABASE_ID,
    COLLECTIONS.WORKSPACES,
    [
      Query.or([
        Query.equal('userId', userId),
        Query.search('members', userId)
      ]),
      Query.equal('isArchived', false),
      Query.orderDesc('$createdAt')
    ]
  );
}

// ============================================================================
// BOARD FUNCTIONS - Estrutura otimizada
// ============================================================================

export async function createBoard(data: CreateBoardData) {
  // Filtrar apenas campos que existem no banco de dados
  const boardData = {
    // Ownership e auditoria
    userId: data.userId,
    teamId: data.teamId,
    createdBy: data.userId,
    updatedBy: data.userId,

    // Relacionamentos
    workspaceId: data.workspaceId,

    // Informações básicas
    title: data.title,
    description: data.description,

    // Configurações de visibilidade
    visibility: data.visibility || 'private',

    // Configurações visuais
    backgroundColor: data.backgroundColor,
    backgroundImage: data.backgroundImage || '',

    // Membros
    members: data.members || [],

    // Configurações funcionais
    allowComments: data.allowComments ?? true,
    allowAttachments: data.allowAttachments ?? true,
    enableTimeTracking: data.enableTimeTracking ?? false,
    enableLabels: data.enableLabels ?? true,
    enableChecklists: data.enableChecklists ?? true,
    enableDueDates: data.enableDueDates ?? true,

    // Configurações de automação
    enableAutomation: data.enableAutomation ?? false,
    automationRules: JSON.stringify([]),

    // DADOS EMBARCADOS - Inicializar com arrays vazios como JSON strings
    labels: JSON.stringify([]),
    columns: JSON.stringify([]),

    // Metadados
    isTemplate: data.isTemplate ?? false,
    isArchived: data.isArchived ?? false,
    isFavorite: data.isFavorite ?? false,
    isStarred: data.isStarred ?? false,

    // Estatísticas
    tasksCount: 0,
    membersCount: data.members?.length || 0,
    lastActivity: new Date().toISOString(),

    // Soft delete
    isDeleted: false,
  };

  // Usar a função createDocument com permissões se options foram fornecidas
  if (data.permissionOptions) {
    const { createDocument } = await import('./database');
    return await createDocument(COLLECTIONS.KANBAN_BOARDS_OPTIMIZED, boardData, data.permissionOptions, ID.unique());
  }

  // Fallback para método antigo
  return await databases.createDocument(
    DATABASE_ID,
    COLLECTIONS.KANBAN_BOARDS_OPTIMIZED,
    ID.unique(),
    boardData
  );
}

export async function getBoard(boardId: string): Promise<OptimizedBoard> {
  const board = await databases.getDocument(
    DATABASE_ID,
    COLLECTIONS.KANBAN_BOARDS_OPTIMIZED,
    boardId
  );

  // Parse JSON strings para objetos
  const parsedBoard = {
    ...board,
    labels: board.labels ? JSON.parse(board.labels) : [],
    columns: board.columns ? JSON.parse(board.columns) : [],
    automationRules: board.automationRules ? JSON.parse(board.automationRules) : [],
  };

  return parsedBoard as unknown as OptimizedBoard;
}

export async function updateBoard(boardId: string, data: UpdateBoardData) {
  const updateData: any = {
    ...data,
    updatedBy: data.updatedBy,
    lastActivity: new Date().toISOString(),
  };

  // Serializar arrays para JSON strings se presentes
  if (data.labels) {
    updateData.labels = JSON.stringify(data.labels);
  }
  if (data.columns) {
    updateData.columns = JSON.stringify(data.columns);
  }
  if (data.automationRules) {
    updateData.automationRules = JSON.stringify(data.automationRules);
  }

  const result = await databases.updateDocument(
    DATABASE_ID,
    COLLECTIONS.KANBAN_BOARDS_OPTIMIZED,
    boardId,
    updateData
  );

  // Parse de volta para retornar objeto estruturado
  return {
    ...result,
    labels: result.labels ? JSON.parse(result.labels) : [],
    columns: result.columns ? JSON.parse(result.columns) : [],
    automationRules: result.automationRules ? JSON.parse(result.automationRules) : [],
  };
}

export async function deleteBoard(boardId: string) {
  return await databases.deleteDocument(
    DATABASE_ID,
    COLLECTIONS.KANBAN_BOARDS_OPTIMIZED,
    boardId
  );
}

export async function listBoards(userId?: string, teamId?: string, workspaceId?: string) {
  const queries = [];

  if (userId) {
    queries.push(Query.or([
      Query.equal('userId', userId),
      Query.search('members', userId)
    ]));
  }

  if (teamId) {
    queries.push(Query.equal('teamId', teamId));
  }

  if (workspaceId) {
    queries.push(Query.equal('workspaceId', workspaceId));
  }

  queries.push(Query.equal('isArchived', false));
  queries.push(Query.orderDesc('$createdAt'));

  const result = await databases.listDocuments(
    DATABASE_ID,
    COLLECTIONS.KANBAN_BOARDS_OPTIMIZED,
    queries
  );

  // Parse JSON strings para objetos em todos os boards
  const parsedDocuments = result.documents.map(board => ({
    ...board,
    labels: board.labels ? JSON.parse(board.labels) : [],
    columns: board.columns ? JSON.parse(board.columns) : [],
    automationRules: board.automationRules ? JSON.parse(board.automationRules) : [],
  }));

  return {
    ...result,
    documents: parsedDocuments,
  };
}

export async function getBoardWithData(boardId: string): Promise<OptimizedBoard> {
  // Com a nova estrutura, uma única query retorna tudo!
  return await getBoard(boardId);
}

// ============================================================================
// COLUMN FUNCTIONS - Operações em arrays embarcados
// ============================================================================

export async function addColumnToBoard(boardId: string, columnData: CreateColumnData, userId: string) {
  const board = await getBoard(boardId);

  // Calcular nova posição
  const maxPosition = board.columns.length > 0
    ? Math.max(...board.columns.map(col => col.position))
    : -1;

  const newColumn: EmbeddedColumn = {
    id: ID.unique(),
    ...columnData,
    position: maxPosition + 1,
    tasks: [],
    createdAt: new Date().toISOString(),
    createdBy: userId,
  };

  const updatedColumns = [...board.columns, newColumn];

  return await updateBoard(boardId, {
    columns: updatedColumns,
    updatedBy: userId,
  });
}

export async function updateColumnInBoard(boardId: string, columnId: string, columnData: UpdateColumnData, userId: string) {
  const board = await getBoard(boardId);

  const updatedColumns = board.columns.map(col =>
    col.id === columnId
      ? {
          ...col,
          ...columnData,
          updatedAt: new Date().toISOString(),
          updatedBy: userId
        }
      : col
  );

  return await updateBoard(boardId, {
    columns: updatedColumns,
    updatedBy: userId,
  });
}

export async function deleteColumnFromBoard(boardId: string, columnId: string, userId: string) {
  const board = await getBoard(boardId);

  const updatedColumns = board.columns.filter(col => col.id !== columnId);

  return await updateBoard(boardId, {
    columns: updatedColumns,
    updatedBy: userId,
  });
}

export async function reorderColumnsInBoard(boardId: string, columnIds: string[], userId: string) {
  const board = await getBoard(boardId);

  const updatedColumns = columnIds.map((columnId, index) => {
    const column = board.columns.find(col => col.id === columnId);
    if (!column) throw new Error(`Column ${columnId} not found`);

    return {
      ...column,
      position: index,
      updatedAt: new Date().toISOString(),
      updatedBy: userId,
    };
  });

  return await updateBoard(boardId, {
    columns: updatedColumns,
    updatedBy: userId,
  });
}

// ============================================================================
// TASK FUNCTIONS - Operações em arrays embarcados
// ============================================================================

export async function addTaskToColumn(boardId: string, columnId: string, taskData: CreateTaskData, userId: string) {
  const board = await getBoard(boardId);
  const column = findColumnInBoard(board, columnId);

  if (!column) {
    throw new Error(`Column ${columnId} not found`);
  }

  // Calcular nova posição
  const maxPosition = column.tasks.length > 0
    ? Math.max(...column.tasks.map(task => task.position))
    : -1;

  const newTask: EmbeddedTask = {
    id: ID.unique(),
    ...taskData,
    position: maxPosition + 1,
    checklists: [],
    comments: [],
    attachments: [],
    createdAt: new Date().toISOString(),
    createdBy: userId,
  };

  const updatedColumns = board.columns.map(col =>
    col.id === columnId
      ? {
          ...col,
          tasks: [...col.tasks, newTask],
          updatedAt: new Date().toISOString(),
          updatedBy: userId,
        }
      : col
  );

  // Atualizar contagem de tasks
  const tasksCount = updatedColumns.reduce((acc, col) => acc + col.tasks.length, 0);

  return await updateBoard(boardId, {
    columns: updatedColumns,
    tasksCount,
    updatedBy: userId,
  });
}

export async function updateTaskInBoard(boardId: string, taskId: string, taskData: UpdateTaskData, userId: string) {
  const board = await getBoard(boardId);
  const taskLocation = findTaskInBoard(board, taskId);

  if (!taskLocation) {
    throw new Error(`Task ${taskId} not found`);
  }

  const updatedColumns = board.columns.map(col =>
    col.id === taskLocation.columnId
      ? {
          ...col,
          tasks: col.tasks.map(task =>
            task.id === taskId
              ? {
                  ...task,
                  ...taskData,
                  updatedAt: new Date().toISOString(),
                  updatedBy: userId,
                }
              : task
          ),
          updatedAt: new Date().toISOString(),
          updatedBy: userId,
        }
      : col
  );

  return await updateBoard(boardId, {
    columns: updatedColumns,
    updatedBy: userId,
  });
}

export async function moveTaskBetweenColumns(
  boardId: string,
  taskId: string,
  sourceColumnId: string,
  targetColumnId: string,
  newPosition: number,
  userId: string
) {
  const board = await getBoard(boardId);
  const taskLocation = findTaskInBoard(board, taskId);

  if (!taskLocation) {
    throw new Error(`Task ${taskId} not found`);
  }

  const task = taskLocation.task;

  let updatedColumns = board.columns.map(col => {
    if (col.id === sourceColumnId) {
      // Remover task da coluna origem
      return {
        ...col,
        tasks: col.tasks.filter(t => t.id !== taskId),
        updatedAt: new Date().toISOString(),
        updatedBy: userId,
      };
    }
    if (col.id === targetColumnId) {
      // Adicionar task na coluna destino
      const updatedTasks = [...col.tasks];
      updatedTasks.splice(newPosition, 0, {
        ...task,
        position: newPosition,
        updatedAt: new Date().toISOString(),
        updatedBy: userId,
      });

      // Reordenar posições
      updatedTasks.forEach((t, index) => {
        t.position = index;
      });

      return {
        ...col,
        tasks: updatedTasks,
        updatedAt: new Date().toISOString(),
        updatedBy: userId,
      };
    }
    return col;
  });

  return await updateBoard(boardId, {
    columns: updatedColumns,
    updatedBy: userId,
  });
}

export async function deleteTaskFromBoard(boardId: string, taskId: string, userId: string) {
  const board = await getBoard(boardId);
  const taskLocation = findTaskInBoard(board, taskId);

  if (!taskLocation) {
    throw new Error(`Task ${taskId} not found`);
  }

  const updatedColumns = board.columns.map(col =>
    col.id === taskLocation.columnId
      ? {
          ...col,
          tasks: col.tasks.filter(task => task.id !== taskId),
          updatedAt: new Date().toISOString(),
          updatedBy: userId,
        }
      : col
  );

  // Atualizar contagem de tasks
  const tasksCount = updatedColumns.reduce((acc, col) => acc + col.tasks.length, 0);

  return await updateBoard(boardId, {
    columns: updatedColumns,
    tasksCount,
    updatedBy: userId,
  });
}

// ============================================================================
// LABEL FUNCTIONS - Operações em arrays embarcados
// ============================================================================

export async function addLabelToBoard(boardId: string, labelData: CreateLabelData, userId: string) {
  const board = await getBoard(boardId);

  const newLabel: EmbeddedLabel = {
    id: ID.unique(),
    ...labelData,
    createdAt: new Date().toISOString(),
    createdBy: userId,
  };

  const updatedLabels = [...board.labels, newLabel];

  return await updateBoard(boardId, {
    labels: updatedLabels,
    updatedBy: userId,
  });
}

export async function updateLabelInBoard(boardId: string, labelId: string, labelData: Partial<CreateLabelData>, userId: string) {
  const board = await getBoard(boardId);

  const updatedLabels = board.labels.map(label =>
    label.id === labelId
      ? { ...label, ...labelData }
      : label
  );

  return await updateBoard(boardId, {
    labels: updatedLabels,
    updatedBy: userId,
  });
}

export async function deleteLabelFromBoard(boardId: string, labelId: string, userId: string) {
  const board = await getBoard(boardId);

  const updatedLabels = board.labels.filter(label => label.id !== labelId);

  // Remover label de todas as tasks
  const updatedColumns = board.columns.map(col => ({
    ...col,
    tasks: col.tasks.map(task => ({
      ...task,
      labelIds: task.labelIds.filter(id => id !== labelId),
    })),
  }));

  return await updateBoard(boardId, {
    labels: updatedLabels,
    columns: updatedColumns,
    updatedBy: userId,
  });
}
