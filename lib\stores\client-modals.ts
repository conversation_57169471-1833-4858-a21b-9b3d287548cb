import { proxy } from 'valtio';
import type { Client } from '@/schemas/clients';

/**
 * Store Valtio para gerenciar estado dos modais de clientes
 * Uso simples: clientModalsStore.create.isOpen = true
 */

export interface ClientModalsState {
  // Modal de criação
  create: {
    isOpen: boolean;
  };

  // Modal de edição
  edit: {
    isOpen: boolean;
    client: Client | null;
  };

  // Modal de visualização
  view: {
    isOpen: boolean;
    client: Client | null;
  };

  // Modal de exclusão
  delete: {
    isOpen: boolean;
    client: Client | null;
  };

  // Modal de importação
  import: {
    isOpen: boolean;
  };

  // Modal de exportação
  export: {
    isOpen: boolean;
  };
}

/**
 * Store reativo usando Valtio
 * Edite diretamente: clientModalsStore.edit.isOpen = true
 */
export const clientModalsStore = proxy<ClientModalsState>({
  create: {
    isOpen: false,
  },
  edit: {
    isOpen: false,
    client: null,
  },
  view: {
    isOpen: false,
    client: null,
  },
  delete: {
    isOpen: false,
    client: null,
  },
  import: {
    isOpen: false,
  },
  export: {
    isOpen: false,
  },
});

// Helper functions para facilitar o uso
export const clientModalsActions = {
  openCreateModal: () => {
    clientModalsStore.create.isOpen = true;
  },
  openEditModal: (client: Client) => {
    clientModalsStore.edit.client = client;
    clientModalsStore.edit.isOpen = true;
  },
  openViewModal: (client: Client) => {
    clientModalsStore.view.client = client;
    clientModalsStore.view.isOpen = true;
  },
  openDeleteModal: (client: Client) => {
    clientModalsStore.delete.client = client;
    clientModalsStore.delete.isOpen = true;
  },
  openImportModal: () => {
    clientModalsStore.import.isOpen = true;
  },
  openExportModal: () => {
    clientModalsStore.export.isOpen = true;
  },
};
