/**
 * React Big Calendar Styles
 * Estilos customizados para o calendário
 */

/* Import do CSS base do react-big-calendar */
@import 'react-big-calendar/lib/css/react-big-calendar.css';

/* ============================================================================
   VARIÁVEIS CSS CUSTOMIZADAS
   ============================================================================ */

.rbc-calendar {
  --rbc-bg-color: hsl(var(--background));
  --rbc-border-color: hsl(var(--border));
  --rbc-off-range-bg-color: hsl(var(--muted));
  --rbc-off-range-color: hsl(var(--muted-foreground));
  --rbc-header-color: hsl(var(--foreground));
  --rbc-event-bg: hsl(var(--primary));
  --rbc-event-border: hsl(var(--primary));
  --rbc-event-color: hsl(var(--primary-foreground));
  --rbc-event-selected-bg: hsl(var(--accent));
  --rbc-slot-selection-bg: hsl(var(--accent));
  --rbc-today-bg: hsl(var(--accent) / 0.1);
}

/* ============================================================================
   LAYOUT PRINCIPAL
   ============================================================================ */

.rbc-calendar {
  background-color: var(--rbc-bg-color);
  border: 1px solid var(--rbc-border-color);
  border-radius: 8px;
  font-family: inherit;
  height: 100%;
  min-height: 500px;
}

/* ============================================================================
   HEADER E NAVEGAÇÃO
   ============================================================================ */

.rbc-header {
  background-color: var(--rbc-bg-color);
  border-bottom: 1px solid var(--rbc-border-color);
  color: var(--rbc-header-color);
  font-weight: 600;
  padding: 12px 8px;
  text-align: center;
  font-size: 14px;
}

.rbc-header + .rbc-header {
  border-left: 1px solid var(--rbc-border-color);
}

.rbc-month-header {
  background-color: hsl(var(--muted) / 0.5);
}

/* ============================================================================
   CÉLULAS E SLOTS
   ============================================================================ */

.rbc-month-view,
.rbc-time-view {
  border: 1px solid var(--rbc-border-color);
  border-radius: 6px;
  overflow: hidden;
}

.rbc-month-row {
  border-bottom: 1px solid var(--rbc-border-color);
}

.rbc-month-row:last-child {
  border-bottom: none;
}

.rbc-date-cell {
  border-right: 1px solid var(--rbc-border-color);
  min-height: 120px;
  padding: 8px;
}

.rbc-date-cell:last-child {
  border-right: none;
}

.rbc-off-range-bg {
  background-color: var(--rbc-off-range-bg-color);
  color: var(--rbc-off-range-color);
}

.rbc-today {
  background-color: var(--rbc-today-bg);
}

.rbc-current-time-indicator {
  background-color: hsl(var(--destructive));
  height: 2px;
  z-index: 3;
}

/* ============================================================================
   EVENTOS
   ============================================================================ */

.rbc-event {
  background-color: var(--rbc-event-bg);
  border: 1px solid var(--rbc-event-border);
  border-radius: 4px;
  color: var(--rbc-event-color);
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  margin: 1px 2px;
  padding: 2px 6px;
  transition: all 0.2s ease;
}

.rbc-event:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.rbc-event.rbc-selected {
  background-color: var(--rbc-event-selected-bg);
  border-color: hsl(var(--ring));
  box-shadow: 0 0 0 2px hsl(var(--ring));
}

.rbc-event-label {
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.rbc-event-continues-after,
.rbc-event-continues-before {
  opacity: 0.8;
}

/* ============================================================================
   VISUALIZAÇÃO SEMANAL E DIÁRIA
   ============================================================================ */

.rbc-time-view .rbc-time-gutter {
  background-color: hsl(var(--muted) / 0.3);
  border-right: 1px solid var(--rbc-border-color);
  font-size: 12px;
  color: hsl(var(--muted-foreground));
}

.rbc-time-view .rbc-time-content {
  border-left: 1px solid var(--rbc-border-color);
}

.rbc-time-slot {
  border-bottom: 1px solid hsl(var(--border) / 0.3);
}

.rbc-time-slot.rbc-now {
  background-color: var(--rbc-today-bg);
}

.rbc-timeslot-group {
  border-bottom: 1px solid var(--rbc-border-color);
}

.rbc-day-slot .rbc-time-slot {
  border-right: 1px solid hsl(var(--border) / 0.2);
}

/* ============================================================================
   VISUALIZAÇÃO DE AGENDA
   ============================================================================ */

.rbc-agenda-view {
  border: 1px solid var(--rbc-border-color);
  border-radius: 6px;
}

.rbc-agenda-view table {
  width: 100%;
  border-collapse: collapse;
}

.rbc-agenda-view .rbc-agenda-date-cell,
.rbc-agenda-view .rbc-agenda-time-cell,
.rbc-agenda-view .rbc-agenda-event-cell {
  border-bottom: 1px solid var(--rbc-border-color);
  padding: 12px;
  vertical-align: top;
}

.rbc-agenda-view .rbc-agenda-date-cell {
  background-color: hsl(var(--muted) / 0.3);
  font-weight: 600;
  width: 120px;
}

.rbc-agenda-view .rbc-agenda-time-cell {
  background-color: hsl(var(--muted) / 0.1);
  font-size: 12px;
  width: 100px;
  color: hsl(var(--muted-foreground));
}

.rbc-agenda-view .rbc-agenda-event-cell {
  width: auto;
}

/* ============================================================================
   SELEÇÃO E INTERAÇÃO
   ============================================================================ */

.rbc-slot-selection {
  background-color: var(--rbc-slot-selection-bg);
  border: 2px solid hsl(var(--primary));
  border-radius: 4px;
  opacity: 0.5;
}

.rbc-slot-selecting {
  cursor: crosshair;
}

.rbc-addons-dnd .rbc-addons-dnd-drag-preview {
  background-color: hsl(var(--primary) / 0.8);
  border: 2px dashed hsl(var(--primary));
  border-radius: 4px;
  opacity: 0.7;
}

.rbc-addons-dnd .rbc-addons-dnd-over {
  background-color: hsl(var(--accent) / 0.3);
}

/* ============================================================================
   POPUP E OVERLAY
   ============================================================================ */

.rbc-overlay {
  background-color: var(--rbc-bg-color);
  border: 1px solid var(--rbc-border-color);
  border-radius: 6px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  max-height: 300px;
  overflow-y: auto;
  padding: 8px;
  z-index: 1000;
}

.rbc-overlay-header {
  border-bottom: 1px solid var(--rbc-border-color);
  font-weight: 600;
  margin-bottom: 8px;
  padding-bottom: 8px;
}

/* ============================================================================
   RESPONSIVIDADE
   ============================================================================ */

@media (max-width: 768px) {
  .rbc-calendar {
    font-size: 12px;
  }
  
  .rbc-header {
    font-size: 12px;
    padding: 8px 4px;
  }
  
  .rbc-date-cell {
    min-height: 80px;
    padding: 4px;
  }
  
  .rbc-event {
    font-size: 10px;
    margin: 1px;
    padding: 1px 4px;
  }
  
  .rbc-time-view .rbc-time-gutter {
    font-size: 10px;
    width: 50px;
  }
  
  .rbc-agenda-view .rbc-agenda-date-cell {
    width: 80px;
  }
  
  .rbc-agenda-view .rbc-agenda-time-cell {
    width: 70px;
  }
}

/* ============================================================================
   TEMA ESCURO
   ============================================================================ */

@media (prefers-color-scheme: dark) {
  .rbc-calendar {
    --rbc-bg-color: hsl(var(--background));
    --rbc-border-color: hsl(var(--border));
    --rbc-off-range-bg-color: hsl(var(--muted));
    --rbc-off-range-color: hsl(var(--muted-foreground));
    --rbc-header-color: hsl(var(--foreground));
    --rbc-today-bg: hsl(var(--accent) / 0.2);
  }
}

/* ============================================================================
   CUSTOMIZAÇÕES ESPECÍFICAS
   ============================================================================ */

/* Melhorar contraste dos eventos */
.rbc-event-content {
  font-weight: 500;
  line-height: 1.2;
}

/* Estilo para eventos de dia inteiro */
.rbc-event.rbc-event-allday {
  background: linear-gradient(135deg, var(--rbc-event-bg) 0%, hsl(var(--primary) / 0.8) 100%);
  border-left: 4px solid hsl(var(--primary));
}

/* Estilo para eventos em andamento */
.rbc-event.event-in-progress {
  background: linear-gradient(135deg, hsl(var(--warning)) 0%, hsl(var(--warning) / 0.8) 100%);
  border-left: 4px solid hsl(var(--warning));
  color: hsl(var(--warning-foreground));
}

/* Estilo para eventos concluídos */
.rbc-event.event-completed {
  background: linear-gradient(135deg, hsl(var(--success)) 0%, hsl(var(--success) / 0.8) 100%);
  border-left: 4px solid hsl(var(--success));
  color: hsl(var(--success-foreground));
  opacity: 0.8;
}

/* Estilo para eventos cancelados */
.rbc-event.event-cancelled {
  background: linear-gradient(135deg, hsl(var(--muted)) 0%, hsl(var(--muted) / 0.6) 100%);
  border-left: 4px solid hsl(var(--muted-foreground));
  color: hsl(var(--muted-foreground));
  opacity: 0.6;
  text-decoration: line-through;
}

/* Animações suaves */
.rbc-event,
.rbc-date-cell,
.rbc-time-slot {
  transition: all 0.2s ease;
}

.rbc-date-cell:hover {
  background-color: hsl(var(--accent) / 0.1);
}

/* Melhorar acessibilidade */
.rbc-event:focus {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

.rbc-calendar *:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}
