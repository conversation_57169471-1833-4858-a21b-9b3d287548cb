/**
 * Dashboard Activities Panel Component
 * Displays recent activities for admin/enterprise users only
 */

import React from 'react';
import Link from 'next/link';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from './ui/card';
import { Badge } from './ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { Button } from './ui/button';
import { Skeleton } from './ui/skeleton';
import { UpgradeAlert } from './upgrade-alert';
import {
  Activity,
  ArrowRight,
  Lock,
  User,
  Users,
  MessageSquare,
  File,
  Settings,
  Bell,
  Calendar,
  Folder,
  Shield,
  LogIn,
  Plus,
  Edit,
  Trash,
  Eye,
} from 'lucide-react';
import { useAuth } from '../hooks/use-auth';
import { useRecentActivities } from '../hooks/use-api';
import { canViewActivities, getActivityPermissionInfo } from '../lib/activity-permissions';
import type { Activity as ActivityType, ActivityType as ActivityTypeEnum } from '@/schemas/activities';

// ============================================================================
// TYPES & CONSTANTS
// ============================================================================

interface DashboardActivitiesPanelProps {
  limit?: number;
  showHeader?: boolean;
  className?: string;
}

const ACTIVITY_ICONS: Record<ActivityTypeEnum, React.ComponentType<any>> = {
  auth: LogIn,
  client: User,
  team: Users,
  chat: MessageSquare,
  file: File,
  system: Settings,
  admin: Shield,
  notification: Bell,
  preference: Settings,
  calendar: Calendar,
  document: Folder,
};

const ACTIVITY_COLORS: Record<ActivityTypeEnum, string> = {
  auth: 'text-blue-600 bg-blue-100',
  client: 'text-green-600 bg-green-100',
  team: 'text-purple-600 bg-purple-100',
  chat: 'text-orange-600 bg-orange-100',
  file: 'text-indigo-600 bg-indigo-100',
  system: 'text-gray-600 bg-gray-100',
  admin: 'text-red-600 bg-red-100',
  notification: 'text-yellow-600 bg-yellow-100',
  preference: 'text-teal-600 bg-teal-100',
  calendar: 'text-pink-600 bg-pink-100',
  document: 'text-cyan-600 bg-cyan-100',
};

// ============================================================================
// COMPONENTS
// ============================================================================

function ActivityItem({ activity }: { activity: ActivityType }) {
  const IconComponent = ACTIVITY_ICONS[activity.type] || Activity;
  const colorClass = ACTIVITY_COLORS[activity.type] || 'text-gray-600 bg-gray-100';

  return (
    <div className="flex items-start space-x-3 p-3 hover:bg-muted/50 rounded-lg transition-colors">
      <div className={`p-2 rounded-full ${colorClass} flex-shrink-0`}>
        <IconComponent className="h-4 w-4" />
      </div>

      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2">
          <p className="text-sm font-medium truncate">{activity.title}</p>
          <Badge variant="outline" className="text-xs">
            {activity.type}
          </Badge>
        </div>

        {activity.description && (
          <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
            {activity.description}
          </p>
        )}

        <div className="flex items-center gap-2 mt-2">
          <div className="text-xs text-muted-foreground">
            {formatDistanceToNow(new Date(activity.$createdAt), {
              addSuffix: true,
              locale: ptBR,
            })}
          </div>

          {activity.priority !== 'normal' && (
            <Badge
              variant={activity.priority === 'high' || activity.priority === 'critical' ? 'destructive' : 'secondary'}
              className="text-xs"
            >
              {activity.priority}
            </Badge>
          )}
        </div>
      </div>
    </div>
  );
}

function ActivitiesListSkeleton({ count = 5 }: { count?: number }) {
  return (
    <div className="space-y-3">
      {Array.from({ length: count }).map((_, i) => (
        <div key={i} className="flex items-start space-x-3 p-3">
          <Skeleton className="h-8 w-8 rounded-full" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-48" />
            <Skeleton className="h-3 w-32" />
            <Skeleton className="h-3 w-24" />
          </div>
        </div>
      ))}
    </div>
  );
}

function UpgradePrompt() {
  return (
    <UpgradeAlert
      title="Funcionalidade Premium"
      description="O log de atividades está disponível apenas para usuários Enterprise ou administradores."
      requiredPlan="enterprise"
      benefits={[
        'Histórico completo de atividades',
        'Filtros avançados por tipo e data',
        'Relatórios detalhados de auditoria',
        'Monitoramento em tempo real'
      ]}
    />
  );
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function DashboardActivitiesPanel({
  limit = 6,
  showHeader = true,
  className = ""
}: DashboardActivitiesPanelProps) {
  const { user } = useAuth();

  // Check permissions
  const hasViewPermission = React.useMemo(() => canViewActivities(user), [user]);
  const permissionInfo = React.useMemo(() => getActivityPermissionInfo(user), [user]);

  // Only fetch activities if user has permission
  const {
    data: activities = [],
    isLoading,
    error
  } = useRecentActivities(hasViewPermission ? limit : 0);

  // Don't render anything if user doesn't have permission
  if (!hasViewPermission) {
    return (
      <Card className={className}>
        {showHeader && (
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <Lock className="h-5 w-5 text-muted-foreground" />
              <CardTitle className="text-lg">Atividades Recentes</CardTitle>
            </div>
            <CardDescription>
              Log de atividades do sistema
            </CardDescription>
          </CardHeader>
        )}

        <CardContent className={showHeader ? "pt-0" : "p-6"}>
          <UpgradePrompt />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <Activity className="h-8 w-8 mx-auto mb-2" />
            <p>Erro ao carregar atividades</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      {showHeader && (
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-primary" />
              <div>
                <CardTitle className="text-lg">Atividades Recentes</CardTitle>
                <CardDescription>
                  {isLoading ? 'Carregando...' : `${activities.length} atividade(s) recente(s)`}
                </CardDescription>
              </div>
            </div>

            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/activities">
                Ver todas
                <ArrowRight className="h-4 w-4 ml-1" />
              </Link>
            </Button>
          </div>
        </CardHeader>
      )}

      <CardContent className={showHeader ? "pt-0" : "p-6"}>
        {isLoading ? (
          <ActivitiesListSkeleton count={limit} />
        ) : activities.length === 0 ? (
          <div className="text-center py-8">
            <Activity className="h-12 w-12 mx-auto text-muted-foreground mb-3" />
            <h3 className="text-sm font-medium text-muted-foreground mb-2">
              Nenhuma atividade registrada
            </h3>
            <p className="text-xs text-muted-foreground">
              As atividades aparecerão aqui conforme você usar o sistema
            </p>
          </div>
        ) : (
          <div className="space-y-1">
            {activities.map((activity) => (
              <ActivityItem key={activity.$id} activity={activity} />
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
