/**
 * Hook para chat com Gemini AI
 * Integra com cloud function do Gemini e gerencia sessões de chat
 */

import { useState, useCallback, useEffect } from 'react';
import { useMutation } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useAuth } from './use-auth';
import { usePathname } from 'next/navigation';
import { sendMessageToGemini, isGeminiConfigured } from '../lib/gemini/chat';
import { generateFullContext, generateSummaryContext, detectQuestionType } from '../lib/gemini/context';
import type {
  GeminiChatMessage,
  GeminiChatSession,
  ProjectContext,
} from '@/schemas/chat';

// Configurações
const MAX_MESSAGES_HISTORY = 50;
const SESSION_STORAGE_KEY = 'gemini-chat-sessions';

/**
 * Hook principal para chat com Gemini
 */
export function useGeminiChat() {
  const { user } = useAuth();
  const pathname = usePathname();
  const [currentSession, setCurrentSession] = useState<GeminiChatSession | null>(null);
  const [sessions, setSessions] = useState<GeminiChatSession[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isGeminiReady, setIsGeminiReady] = useState(false);

  // Verificar se Gemini está configurado
  useEffect(() => {
    setIsGeminiReady(isGeminiConfigured());
  }, []);

  // Carregar sessões do localStorage
  useEffect(() => {
    if (!user?.$id) return;

    try {
      const stored = localStorage.getItem(`${SESSION_STORAGE_KEY}-${user.$id}`);
      if (stored) {
        const parsedSessions = JSON.parse(stored) as GeminiChatSession[];
        setSessions(parsedSessions);

        // Carregar última sessão ativa
        const lastSession = parsedSessions.find(s => s.id === localStorage.getItem(`last-session-${user.$id}`));
        if (lastSession) {
          setCurrentSession(lastSession);
        }
      }
    } catch (error) {
      console.error('Erro ao carregar sessões do chat:', error);
    }
  }, [user?.$id]);

  // Função para obter contexto do projeto baseado na página atual
  const getProjectContext = useCallback((): string => {
    return generateFullContext(pathname);
  }, [pathname]);

  // Salvar sessões no localStorage
  const saveSessions = useCallback((updatedSessions: GeminiChatSession[]) => {
    if (!user?.$id) return;

    try {
      localStorage.setItem(`${SESSION_STORAGE_KEY}-${user.$id}`, JSON.stringify(updatedSessions));
      setSessions(updatedSessions);
    } catch (error) {
      console.error('Erro ao salvar sessões:', error);
    }
  }, [user?.$id]);

  // Criar nova sessão
  const createSession = useCallback((title?: string, context?: string) => {
    if (!user) return null;

    const newSession: GeminiChatSession = {
      id: `session-${Date.now()}`,
      userId: user.$id,
      title: title || `Chat ${new Date().toLocaleDateString()}`,
      messages: [],
      context: context || getProjectContext(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      settings: {
        model: 'gemini-1.5-flash',
        temperature: 0.7,
        maxTokens: 2048,
        includeProjectContext: true,
      },
    };

    const updatedSessions = [newSession, ...sessions].slice(0, 10); // Manter apenas 10 sessões
    saveSessions(updatedSessions);
    setCurrentSession(newSession);

    if (user.$id) {
      localStorage.setItem(`last-session-${user.$id}`, newSession.id);
    }

    return newSession;
  }, [user, sessions, saveSessions, getProjectContext]);

  // Selecionar sessão
  const selectSession = useCallback((sessionId: string) => {
    const session = sessions.find(s => s.id === sessionId);
    if (session) {
      setCurrentSession(session);
      if (user?.$id) {
        localStorage.setItem(`last-session-${user.$id}`, sessionId);
      }
    }
  }, [sessions, user?.$id]);

  // Deletar sessão
  const deleteSession = useCallback((sessionId: string) => {
    const updatedSessions = sessions.filter(s => s.id !== sessionId);
    saveSessions(updatedSessions);

    if (currentSession?.id === sessionId) {
      const nextSession = updatedSessions[0] || null;
      setCurrentSession(nextSession);
      if (user?.$id && nextSession) {
        localStorage.setItem(`last-session-${user.$id}`, nextSession.id);
      }
    }
  }, [sessions, currentSession, saveSessions, user?.$id]);

  // Mutation para enviar mensagem
  const sendMessageMutation = useMutation({
    mutationFn: async ({ message, sessionId }: { message: string; sessionId: string }) => {
      if (!user) throw new Error('Usuário não autenticado');
      if (!isGeminiReady) throw new Error('Gemini AI não está configurado. Adicione NEXT_PUBLIC_GEMINI_API_KEY no arquivo .env');

      const session = sessions.find(s => s.id === sessionId);
      if (!session) throw new Error('Sessão não encontrada');

      // Detectar tipo de pergunta para otimizar contexto
      const questionType = detectQuestionType(message);

      // Preparar contexto baseado no tipo de pergunta
      let contextToUse = '';
      if (session.settings.includeProjectContext) {
        if (questionType.suggestedContext === 'full') {
          contextToUse = generateFullContext(pathname);
        } else {
          contextToUse = generateSummaryContext();
        }
      }

      // Preparar histórico da conversa
      const conversationHistory = session.messages.slice(-5).map(m => ({
        role: m.role,
        content: m.content,
      }));

      // Enviar para Gemini
      const response = await sendMessageToGemini(message, {
        model: session.settings.model,
        temperature: session.settings.temperature,
        maxTokens: session.settings.maxTokens,
        includeContext: session.settings.includeProjectContext,
        context: contextToUse,
        conversationHistory,
      });

      return {
        userMessage: message,
        assistantMessage: response.content,
        metadata: {
          model: response.model,
          tokens: response.tokens,
          processingTime: response.processingTime,
          context: questionType.type,
        },
      };
    },
    onMutate: ({ message, sessionId }) => {
      // Adicionar mensagem do usuário imediatamente (optimistic update)
      const userMessage: GeminiChatMessage = {
        id: `msg-${Date.now()}`,
        content: message,
        role: 'user',
        timestamp: new Date().toISOString(),
        status: 'sending',
      };

      const updatedSessions = sessions.map(s => {
        if (s.id === sessionId) {
          const updatedSession = {
            ...s,
            messages: [...s.messages, userMessage],
            updatedAt: new Date().toISOString(),
          };

          if (currentSession?.id === sessionId) {
            setCurrentSession(updatedSession);
          }

          return updatedSession;
        }
        return s;
      });

      saveSessions(updatedSessions);
      setIsLoading(true);
    },
    onSuccess: ({ userMessage, assistantMessage, metadata }, { sessionId }) => {
      // Adicionar resposta do assistente
      const assistantMsg: GeminiChatMessage = {
        id: `msg-${Date.now()}-assistant`,
        content: assistantMessage,
        role: 'assistant',
        timestamp: new Date().toISOString(),
        status: 'sent',
        metadata,
      };

      const updatedSessions = sessions.map(s => {
        if (s.id === sessionId) {
          const messages = s.messages.map(m =>
            m.status === 'sending' ? { ...m, status: 'sent' as const } : m
          );

          const updatedSession = {
            ...s,
            messages: [...messages, assistantMsg].slice(-MAX_MESSAGES_HISTORY),
            updatedAt: new Date().toISOString(),
          };

          if (currentSession?.id === sessionId) {
            setCurrentSession(updatedSession);
          }

          return updatedSession;
        }
        return s;
      });

      saveSessions(updatedSessions);
      setIsLoading(false);
    },
    onError: (error, { sessionId }) => {
      console.error('Erro ao enviar mensagem para Gemini:', error);

      // Marcar mensagem como erro
      const updatedSessions = sessions.map(s => {
        if (s.id === sessionId) {
          const messages = s.messages.map(m =>
            m.status === 'sending' ? { ...m, status: 'error' as const } : m
          );

          const updatedSession = {
            ...s,
            messages,
            updatedAt: new Date().toISOString(),
          };

          if (currentSession?.id === sessionId) {
            setCurrentSession(updatedSession);
          }

          return updatedSession;
        }
        return s;
      });

      saveSessions(updatedSessions);
      setIsLoading(false);
      toast.error('Erro ao enviar mensagem para o assistente');
    },
  });

  // Enviar mensagem
  const sendMessage = useCallback((message: string) => {
    if (!currentSession) {
      const newSession = createSession();
      if (newSession) {
        sendMessageMutation.mutate({ message, sessionId: newSession.id });
      }
    } else {
      sendMessageMutation.mutate({ message, sessionId: currentSession.id });
    }
  }, [currentSession, createSession, sendMessageMutation]);

  // Limpar sessão atual
  const clearCurrentSession = useCallback(() => {
    if (!currentSession) return;

    const clearedSession = {
      ...currentSession,
      messages: [],
      updatedAt: new Date().toISOString(),
    };

    const updatedSessions = sessions.map(s =>
      s.id === currentSession.id ? clearedSession : s
    );

    saveSessions(updatedSessions);
    setCurrentSession(clearedSession);
  }, [currentSession, sessions, saveSessions]);

  return {
    // Estado
    currentSession,
    sessions,
    isLoading: isLoading || sendMessageMutation.isPending,
    isGeminiReady,

    // Ações
    sendMessage,
    createSession,
    selectSession,
    deleteSession,
    clearCurrentSession,

    // Estado da mutation
    isError: sendMessageMutation.isError,
    error: sendMessageMutation.error,
  };
}


