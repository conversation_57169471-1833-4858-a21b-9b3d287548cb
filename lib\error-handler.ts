/**
 * Sistema centralizado de tratamento de erros
 * Padroniza o tratamento de erros em toda a aplicação
 */

import { toast } from 'sonner';
import { log } from './logger';

export enum ErrorType {
  NETWORK = 'NETWORK',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  VALIDATION = 'VALIDATION',
  NOT_FOUND = 'NOT_FOUND',
  SERVER = 'SERVER',
  CLIENT = 'CLIENT',
  UNKNOWN = 'UNKNOWN',
  BUSINESS_LOGIC = 'BUSINESS_LOGIC',
  EXTERNAL_SERVICE = 'EXTERNAL_SERVICE',
}

export interface AppError extends Error {
  type: ErrorType;
  code?: string;
  statusCode?: number;
  context?: Record<string, unknown>;
  userMessage?: string;
  retryable?: boolean;
  timestamp: string;
}

export interface ErrorHandlerConfig {
  showToast: boolean;
  logError: boolean;
  reportToService: boolean;
  fallbackMessage: string;
  language: 'pt-BR' | 'en';
  enableDetailedErrors: boolean;
}

class ErrorHandler {
  private config: ErrorHandlerConfig;

  constructor(config: Partial<ErrorHandlerConfig> = {}) {
    this.config = {
      showToast: true,
      logError: true,
      reportToService: process.env.NODE_ENV === "production",
      fallbackMessage: 'Ocorreu um erro inesperado. Tente novamente.',
      language: 'pt-BR',
      enableDetailedErrors: process.env.NODE_ENV === "development",
      ...config,
    };
  }

  /**
   * Mensagens de erro localizadas
   */
  private getErrorMessages() {
    const messages = {
      'pt-BR': {
        [ErrorType.NETWORK]: 'Erro de conexão. Verifique sua internet.',
        [ErrorType.AUTHENTICATION]: 'Erro de autenticação. Faça login novamente.',
        [ErrorType.AUTHORIZATION]: 'Você não tem permissão para esta ação.',
        [ErrorType.VALIDATION]: 'Dados inválidos. Verifique os campos.',
        [ErrorType.NOT_FOUND]: 'Recurso não encontrado.',
        [ErrorType.SERVER]: 'Erro interno do servidor. Tente novamente.',
        [ErrorType.CLIENT]: 'Erro na aplicação. Recarregue a página.',
        [ErrorType.UNKNOWN]: 'Erro inesperado. Tente novamente.',
        [ErrorType.BUSINESS_LOGIC]: 'Erro na lógica de negócio. Verifique os dados.',
        [ErrorType.EXTERNAL_SERVICE]: 'Erro no serviço externo. Tente novamente.',
        fallback: 'Ocorreu um erro inesperado. Tente novamente.',
      },
      'en': {
        [ErrorType.NETWORK]: 'Connection error. Check your internet.',
        [ErrorType.AUTHENTICATION]: 'Authentication error. Please login again.',
        [ErrorType.AUTHORIZATION]: 'You do not have permission for this action.',
        [ErrorType.VALIDATION]: 'Invalid data. Check the fields.',
        [ErrorType.NOT_FOUND]: 'Resource not found.',
        [ErrorType.SERVER]: 'Internal server error. Try again.',
        [ErrorType.CLIENT]: 'Application error. Reload the page.',
        [ErrorType.UNKNOWN]: 'Unexpected error. Try again.',
        [ErrorType.BUSINESS_LOGIC]: 'Business logic error. Check the data.',
        [ErrorType.EXTERNAL_SERVICE]: 'External service error. Try again.',
        fallback: 'An unexpected error occurred. Please try again.',
      },
    };

    return messages[this.config.language];
  }

  /**
   * Cria um erro padronizado da aplicação
   */
  createError(
    message: string,
    type: ErrorType = ErrorType.UNKNOWN,
    options: Partial<AppError> = {}
  ): AppError {
    const error = new Error(message) as AppError;
    const errorMessages = this.getErrorMessages();

    error.type = type;
    error.timestamp = new Date().toISOString();
    error.code = options.code;
    error.statusCode = options.statusCode;
    error.context = options.context;
    error.retryable = options.retryable ?? false;

    // Usar mensagem localizada se não foi fornecida uma mensagem personalizada
    error.userMessage = options.userMessage || errorMessages[type] || errorMessages.fallback;

    return error;
  }

  /**
   * Converte erros do Appwrite para erros padronizados
   */
  fromAppwriteError(error: any): AppError {
    const message = error.message || 'Erro do Appwrite';
    let type = ErrorType.SERVER;
    let userMessage = this.config.fallbackMessage;
    let retryable = false;

    // Mapear códigos específicos do Appwrite
    switch (error.code) {
      case 401:
        type = ErrorType.AUTHENTICATION;
        userMessage = 'Você precisa fazer login para continuar.';
        break;
      case 403:
        type = ErrorType.AUTHORIZATION;
        userMessage = 'Você não tem permissão para realizar esta ação.';
        break;
      case 404:
        type = ErrorType.NOT_FOUND;
        userMessage = 'O recurso solicitado não foi encontrado.';
        break;
      case 409:
        type = ErrorType.VALIDATION;
        userMessage = 'Já existe um recurso com essas informações.';
        break;
      case 429:
        type = ErrorType.SERVER;
        userMessage = 'Muitas tentativas. Tente novamente em alguns minutos.';
        retryable = true;
        break;
      case 500:
      case 502:
      case 503:
        type = ErrorType.SERVER;
        userMessage = 'Erro no servidor. Tente novamente em alguns instantes.';
        retryable = true;
        break;
      default:
        if (error.code >= 400 && error.code < 500) {
          type = ErrorType.CLIENT;
          userMessage = 'Dados inválidos. Verifique as informações e tente novamente.';
        }
    }

    return this.createError(message, type, {
      code: error.code?.toString(),
      statusCode: error.code,
      userMessage,
      retryable,
      context: {
        originalError: error,
        appwriteType: error.type,
      },
    });
  }

  /**
   * Converte erros de rede para erros padronizados
   */
  fromNetworkError(error: any): AppError {
    let userMessage = 'Problema de conexão. Verifique sua internet.';
    let retryable = true;

    if (error.name === 'AbortError') {
      userMessage = 'Operação cancelada.';
      retryable = false;
    } else if (error.name === 'TimeoutError') {
      userMessage = 'Operação demorou muito para responder. Tente novamente.';
    }

    return this.createError(error.message || 'Erro de rede', ErrorType.NETWORK, {
      userMessage,
      retryable,
      context: { originalError: error },
    });
  }

  /**
   * Converte erros de validação para erros padronizados
   */
  fromValidationError(errors: Record<string, string[]>): AppError {
    const firstError = Object.values(errors)[0]?.[0];
    const message = firstError || 'Dados inválidos';

    return this.createError(message, ErrorType.VALIDATION, {
      userMessage: 'Verifique os dados informados e tente novamente.',
      context: { validationErrors: errors },
    });
  }

  /**
   * Trata um erro de forma padronizada
   */
  async handle(error: unknown, context?: Record<string, unknown>): Promise<AppError> {
    let appError: AppError;

    // Converter diferentes tipos de erro para AppError
    if (this.isAppError(error)) {
      appError = error;
    } else if (this.isAppwriteError(error)) {
      appError = this.fromAppwriteError(error);
    } else if (this.isNetworkError(error)) {
      appError = this.fromNetworkError(error);
    } else if (error instanceof Error) {
      appError = this.createError(error.message, ErrorType.UNKNOWN, {
        userMessage: this.config.fallbackMessage,
        context: { originalError: error },
      });
    } else {
      appError = this.createError(
        'Erro desconhecido',
        ErrorType.UNKNOWN,
        {
          userMessage: this.config.fallbackMessage,
          context: { originalError: error },
        }
      );
    }

    // Adicionar contexto adicional
    if (context) {
      appError.context = { ...appError.context, ...context };
    }

    // Log do erro
    if (this.config.logError) {
      log.error(appError.message, appError, appError.context);
    }

    // Mostrar toast para o usuário
    if (this.config.showToast && appError.userMessage) {
      toast.error(appError.userMessage);
    }

    // Reportar para serviço externo (Sentry, etc.)
    if (this.config.reportToService) {
      await this.reportError(appError);
    }

    return appError;
  }

  /**
   * Reporta erro para serviço externo
   */
  private async reportError(error: AppError): Promise<void> {
    try {
      // Aqui você integraria com Sentry, LogRocket, etc.
      // Exemplo com Sentry:
      // Sentry.captureException(error, {
      //   tags: { type: error.type },
      //   extra: error.context,
      // });

      log.info('Error reported to external service', {
        errorId: error.timestamp,
        type: error.type,
      });
    } catch (reportError) {
      log.warn('Failed to report error to external service', { reportError });
    }
  }

  /**
   * Verifica se é um erro da aplicação
   */
  private isAppError(error: unknown): error is AppError {
    return error instanceof Error && 'type' in error && 'timestamp' in error;
  }

  /**
   * Verifica se é um erro do Appwrite
   */
  private isAppwriteError(error: unknown): boolean {
    return (
      typeof error === 'object' &&
      error !== null &&
      'code' in error &&
      'message' in error
    );
  }

  /**
   * Verifica se é um erro de rede
   */
  private isNetworkError(error: unknown): boolean {
    return (
      error instanceof Error &&
      (error.name === 'NetworkError' ||
        error.name === 'AbortError' ||
        error.name === 'TimeoutError' ||
        error.message.includes('fetch'))
    );
  }

  /**
   * Cria um wrapper para funções que podem gerar erros
   */
  withErrorHandling<T extends (...args: any[]) => any>(
    fn: T,
    context?: Record<string, unknown>
  ): T {
    return (async (...args: any[]) => {
      try {
        return await fn(...args);
      } catch (error) {
        throw await this.handle(error, context);
      }
    }) as T;
  }

  /**
   * Retry automático para operações que falharam
   */
  async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000,
    context?: Record<string, unknown>
  ): Promise<T> {
    let lastError: AppError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = await this.handle(error, { ...context, attempt });

        // Não tentar novamente se o erro não for retryable
        if (!lastError.retryable || attempt === maxRetries) {
          throw lastError;
        }

        // Aguardar antes da próxima tentativa (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, attempt - 1)));
      }
    }

    throw lastError!;
  }
}

// Instância global do error handler
export const errorHandler = new ErrorHandler();

/**
 * Função global simplificada para tratamento de erros
 * Substitui o padrão catch(error) { console.error(error); toast.error(error); }
 */
export async function loggerError(
  error: unknown,
  context?: Record<string, unknown>,
  options?: {
    showToast?: boolean;
    logError?: boolean;
    customMessage?: string;
    type?: ErrorType;
  }
): Promise<AppError> {
  const config = {
    showToast: options?.showToast ?? true,
    logError: options?.logError ?? true,
    ...options,
  };

  // Criar um error handler temporário com configurações específicas
  const tempHandler = new ErrorHandler({
    showToast: config.showToast,
    logError: config.logError,
    reportToService: false, // Não reportar automaticamente
  });

  let appError: AppError;

  // Se foi fornecido um tipo específico e mensagem customizada
  if (options?.type && options?.customMessage) {
    appError = tempHandler.createError(options.customMessage, options.type, { context });

    if (config.logError) {
      log.error(appError.message, error as Error, appError.context);
    }

    if (config.showToast && appError.userMessage) {
      toast.error(appError.userMessage);
    }
  } else {
    // Usar o tratamento padrão
    appError = await tempHandler.handle(error, context);
  }

  return appError;
}

/**
 * Wrapper para funções que podem gerar erros
 * Automatiza o tratamento de erros com loggerError
 */
export function withErrorHandling<T extends (...args: any[]) => any>(
  fn: T,
  context?: Record<string, unknown>,
  options?: Parameters<typeof loggerError>[2]
): T {
  return (async (...args: any[]) => {
    try {
      return await fn(...args);
    } catch (error) {
      throw await loggerError(error, context, options);
    }
  }) as T;
}

/**
 * Decorator para tratamento automático de erros em métodos
 */
export function HandleErrors(
  context?: Record<string, unknown>,
  options?: Parameters<typeof loggerError>[2]
) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const methodName = `${target.constructor.name}.${propertyKey}`;

    descriptor.value = async function (...args: any[]) {
      try {
        return await originalMethod.apply(this, args);
      } catch (error) {
        throw await loggerError(error, {
          ...context,
          method: methodName,
          args: args.length > 0 ? args : undefined
        }, options);
      }
    };

    return descriptor;
  };
}

// Hook para usar o error handler em componentes React
export function useErrorHandler() {
  return errorHandler;
}
