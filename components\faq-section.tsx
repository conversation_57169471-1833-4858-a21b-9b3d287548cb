import React from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from './ui/accordion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import Link from 'next/link';
import { MessageCircle, HelpCircle, Mail } from 'lucide-react';

const faqs = [
  {
    id: 'item-1',
    question: 'O que está incluído no template?',
    answer: 'O template inclui autenticação completa com React Router v7, dashboard moderno, sistema de teams client-side, gerenciamento de clientes, cache local-first IndexedDB, PWA configurável, cloud functions híbridas, chat real-time, sistema de planos e muito mais. Tudo 100% TypeScript e pronto para produção.',
  },
  {
    id: 'item-2',
    question: 'Como funciona o sistema de planos?',
    answer: 'O sistema usa labels do Appwrite para determinar o plano do usuário: Gratuito (sem teams), Pro (1 team, 5 membros) e Enterprise (ilimitado). As verificações são feitas automaticamente usando getUserPlan() e getUserLimitsInfo().',
  },
  {
    id: 'item-3',
    question: 'Como configurar o cache local-first?',
    answer: 'O cache IndexedDB está configurado automaticamente em app/lib/cache-config.ts. Você pode ajustar TTL, estratégias (local-first, server-first) e tipos de storage. Os hooks React Query já integram com o cache automaticamente.',
  },
  {
    id: 'item-4',
    question: 'Como ativar o PWA?',
    answer: 'Configure NEXT_PUBLIC_PWA_ENABLED=true no .env e reinicie o servidor. O sistema gerencia automaticamente service workers, cache, notificações e install prompts. Quando desabilitado, remove tudo automaticamente.',
  },
  {
    id: 'item-5',
    question: 'Como usar cloud functions?',
    answer: 'Configure as URLs em app/lib/appwrite/cloudfunctions/const.ts e use useCloudFunction(). O template inclui 5 funções essenciais: admin, auth, gemini processors e stripe. Todas têm fallback gracioso.',
  },
  {
    id: 'item-6',
    question: 'Como configurar o Appwrite?',
    answer: 'Execute "yarn setup" para configurar automaticamente todas as coleções, atributos, índices e permissões. O script cria: clients, notifications, public_profiles e activity_logs com configuração completa.',
  },
  {
    id: 'item-7',
    question: 'Como funciona o sistema de teams?',
    answer: 'O sistema usa 100% APIs nativas do Appwrite (teams, memberships). Inclui criação de teams, convites, gerenciamento de membros, chat real-time e verificação automática de limites por plano.',
  },
  {
    id: 'item-8',
    question: 'Como usar os hooks customizados?',
    answer: 'Todos os hooks estão em app/hooks/api/ organizados por funcionalidade. Use useClients(), useTeams(), useChat(), etc. Todos integram React Query com cache automático e são 100% type-safe.',
  },
  {
    id: 'item-9',
    question: 'Como funciona o sistema de import/export?',
    answer: 'O sistema suporta CSV, Excel e PDF com processamento via IA (Gemini). Inclui filtros avançados, validação de dados e geração de PDFs profissionais. Disponível apenas para planos pagos.',
  },
  {
    id: 'item-10',
    question: 'Como acessar a documentação?',
    answer: 'A documentação completa está em /docs/INDEX com 26 arquivos cobrindo todas as funcionalidades: autenticação, banco, cache, PWA, cloud functions, teams, chat, API hooks e muito mais.',
  },
];

export function FAQSection() {
  return (
    <section className="py-16 md:py-24 bg-muted/30">
      <div className="container mx-auto px-4 max-w-4xl">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Perguntas Frequentes
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Encontre respostas para as dúvidas mais comuns sobre nosso template
          </p>
        </div>

        <div className="mb-12">
          <Accordion type="single" collapsible className="w-full space-y-4">
            {faqs.map((faq) => (
              <AccordionItem
                key={faq.id}
                value={faq.id}
                className="border rounded-lg px-6 bg-background/50 backdrop-blur-sm"
              >
                <AccordionTrigger className="text-left hover:no-underline py-6">
                  <span className="font-medium">{faq.question}</span>
                </AccordionTrigger>
                <AccordionContent className="pb-6 text-muted-foreground">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>

        {/* Seção de Suporte */}
        <div className="grid md:grid-cols-3 gap-6">
          <Card className="text-center">
            <CardHeader>
              <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                <HelpCircle className="w-6 h-6 text-primary" />
              </div>
              <CardTitle className="text-lg">Central de Ajuda</CardTitle>
              <CardDescription>
                Acesse nossa documentação completa
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button variant="outline" asChild className="w-full">
                <Link href="/dashboard/help">
                  Ver Documentação
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                <MessageCircle className="w-6 h-6 text-primary" />
              </div>
              <CardTitle className="text-lg">Chat de Suporte</CardTitle>
              <CardDescription>
                Converse com nossa equipe em tempo real
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button variant="outline" className="w-full">
                Iniciar Chat
              </Button>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                <Mail className="w-6 h-6 text-primary" />
              </div>
              <CardTitle className="text-lg">Email</CardTitle>
              <CardDescription>
                Entre em contato por email
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button variant="outline" className="w-full" asChild>
                <a href="mailto:<EMAIL>">
                  Enviar Email
                </a>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}
