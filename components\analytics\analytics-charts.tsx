/**
 * Componente de gráficos de analytics melhorado com padrões do Shadcn
 */

import { TrendingUp, Users, DollarSign, CheckSquare } from "lucide-react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "../ui/card";
import { ChartContainer, ChartTooltip, ChartTooltipContent, type ChartConfig } from "../ui/chart";
import { AreaChart, Area, BarChart, Bar, LineChart, Line, XAxis, YAxis, CartesianGrid } from "recharts";
import type { AnalyticsData } from '@/hooks/use-analytics';

interface AnalyticsChartsProps {
  data?: AnalyticsData;
}

const chartConfig = {
  users: {
    label: "Usuários",
    color: "hsl(var(--chart-1))",
  },
  clients: {
    label: "Clientes",
    color: "hsl(var(--chart-2))",
  },
  revenue: {
    label: "<PERSON><PERSON><PERSON>",
    color: "hsl(var(--chart-3))",
  },
  events: {
    label: "Eventos",
    color: "hsl(var(--chart-4))",
  },
  tasks: {
    label: "Tarefas",
    color: "hsl(var(--chart-5))",
  },
} satisfies ChartConfig;

export function AnalyticsCharts({ data }: AnalyticsChartsProps) {
  if (!data) {
    return null;
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  return (
    <div>
      <div className="grid gap-4 md:gap-6 lg:grid-cols-2">

        {/* Gráfico de Área - Usuários e Clientes */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Crescimento ao Longo do Tempo
            </CardTitle>
            <CardDescription>
              Evolução de usuários e clientes nos últimos 12 meses
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig}>
              <AreaChart
                accessibilityLayer
                data={data.chartData}
                margin={{
                  left: 12,
                  right: 12,
                }}
              >
                <defs>
                  <linearGradient id="fillUsers" x1="0" y1="0" x2="0" y2="1">
                    <stop
                      offset="5%"
                      stopColor="var(--color-users)"
                      stopOpacity={0.8}
                    />
                    <stop
                      offset="95%"
                      stopColor="var(--color-users)"
                      stopOpacity={0.1}
                    />
                  </linearGradient>
                  <linearGradient id="fillClients" x1="0" y1="0" x2="0" y2="1">
                    <stop
                      offset="5%"
                      stopColor="var(--color-clients)"
                      stopOpacity={0.8}
                    />
                    <stop
                      offset="95%"
                      stopColor="var(--color-clients)"
                      stopOpacity={0.1}
                    />
                  </linearGradient>
                </defs>
                <CartesianGrid vertical={false} />
                <XAxis
                  dataKey="month"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                  tickFormatter={(value) => value.slice(0, 3)}
                />
                <YAxis
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                />
                <ChartTooltip
                  cursor={false}
                  content={<ChartTooltipContent indicator="dot" />}
                />
                <Area
                  dataKey="clients"
                  type="natural"
                  fill="url(#fillClients)"
                  fillOpacity={0.4}
                  stroke="var(--color-clients)"
                  stackId="a"
                />
                <Area
                  dataKey="users"
                  type="natural"
                  fill="url(#fillUsers)"
                  fillOpacity={0.4}
                  stroke="var(--color-users)"
                  stackId="a"
                />
              </AreaChart>
            </ChartContainer>
          </CardContent>
          <CardFooter>
            <div className="flex w-full items-start gap-2 text-sm">
              <div className="grid gap-2">
                <div className="flex items-center gap-2 font-medium leading-none">
                  Crescimento consistente <TrendingUp className="h-4 w-4" />
                </div>
                <div className="flex items-center gap-2 leading-none text-muted-foreground">
                  Dados dos últimos 12 meses
                </div>
              </div>
            </div>
          </CardFooter>
        </Card>

        {/* Gráfico de Barras - Receita */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Receita Mensal
            </CardTitle>
            <CardDescription>
              Receita acumulada nos últimos 6 meses
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig}>
              <BarChart
                accessibilityLayer
                data={data.chartData.slice(-6)}
                margin={{
                  left: 12,
                  right: 12,
                }}
              >
                <CartesianGrid vertical={false} />
                <XAxis
                  dataKey="month"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                  tickFormatter={(value) => String(value).slice(0, 3)}
                />
                <YAxis
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                  tickFormatter={(value) => formatCurrency(value)}
                />
                <ChartTooltip
                  cursor={false}
                  content={<ChartTooltipContent
                    hideLabel
                    formatter={(value) => [formatCurrency(Number(value)), "Receita"]}
                  />}
                />
                <Bar
                  dataKey="revenue"
                  fill="var(--color-revenue)"
                  radius={4}
                />
              </BarChart>
            </ChartContainer>
          </CardContent>
          <CardFooter className="flex-col items-start gap-2 text-sm">
            <div className="flex gap-2 font-medium leading-none">
              Receita em crescimento <TrendingUp className="h-4 w-4" />
            </div>
            <div className="leading-none text-muted-foreground">
              Últimos 6 meses
            </div>
          </CardFooter>
        </Card>

        {/* Gráfico de Linha - Tarefas */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckSquare className="h-5 w-5" />
              Tarefas Kanban
            </CardTitle>
            <CardDescription>
              Evolução de tarefas nos últimos 6 meses
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig}>
              <LineChart
                accessibilityLayer
                data={data.chartData.slice(-6)}
                margin={{
                  left: 12,
                  right: 12,
                }}
              >
                <CartesianGrid vertical={false} />
                <XAxis
                  dataKey="month"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                  tickFormatter={(value) => String(value).slice(0, 3)}
                />
                <YAxis
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                />
                <ChartTooltip
                  cursor={false}
                  content={<ChartTooltipContent
                    hideLabel
                    formatter={(value) => [value, "Tarefas"]}
                  />}
                />
                <Line
                  dataKey="tasks"
                  type="monotone"
                  stroke="var(--color-tasks)"
                  strokeWidth={3}
                  dot={{
                    fill: "var(--color-tasks)",
                    strokeWidth: 2,
                    r: 4,
                  }}
                  activeDot={{
                    r: 6,
                    stroke: "var(--color-tasks)",
                    strokeWidth: 2,
                  }}
                />
              </LineChart>
            </ChartContainer>
          </CardContent>
          <CardFooter className="flex-col items-start gap-2 text-sm">
            <div className="flex gap-2 font-medium leading-none">
              Produtividade em alta <TrendingUp className="h-4 w-4" />
            </div>
            <div className="leading-none text-muted-foreground">
              Últimos 6 meses
            </div>
          </CardFooter>
        </Card>

      </div>

      {/* Comparação de Períodos */}
      <div className="mt-6">
        <Card>
          <CardHeader>
            <CardTitle>Comparação Mensal</CardTitle>
            <CardDescription>
              Comparação entre o mês atual e anterior
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div className="space-y-2">
                <p className="text-sm font-medium">Usuários</p>
                <div className="space-y-1">
                  <p className="text-2xl font-bold">{data.monthlyStats.currentMonth.users}</p>
                  <p className="text-xs text-muted-foreground">
                    Anterior: {data.monthlyStats.previousMonth.users}
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <p className="text-sm font-medium">Clientes</p>
                <div className="space-y-1">
                  <p className="text-2xl font-bold">{data.monthlyStats.currentMonth.clients}</p>
                  <p className="text-xs text-muted-foreground">
                    Anterior: {data.monthlyStats.previousMonth.clients}
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <p className="text-sm font-medium">Receita</p>
                <div className="space-y-1">
                  <p className="text-2xl font-bold">{formatCurrency(data.monthlyStats.currentMonth.revenue)}</p>
                  <p className="text-xs text-muted-foreground">
                    Anterior: {formatCurrency(data.monthlyStats.previousMonth.revenue)}
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <p className="text-sm font-medium">Eventos</p>
                <div className="space-y-1">
                  <p className="text-2xl font-bold">{data.monthlyStats.currentMonth.events}</p>
                  <p className="text-xs text-muted-foreground">
                    Anterior: {data.monthlyStats.previousMonth.events}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
