/**
 * Sistema de Verificação de Limites por Plano
 * Verifica se o usuário pode executar ações baseado no seu plano
 */

import type { Models } from 'appwrite';
import { PLANS } from './plans';

/**
 * Determina o plano do usuário baseado nas labels
 */
export function getUserPlan(userLabels: string[] = []): string {
  // Verifica se o usuário tem labels específicas de planos
  if (userLabels.includes('enterprise') || userLabels.includes('plan-enterprise')) {
    return 'enterprise';
  }
  if (userLabels.includes('pro') || userLabels.includes('plan-pro') || userLabels.includes('professional')) {
    return 'pro';
  }
  // Se não tem nenhuma label específica, assume plano básico
  return 'free';
}

/**
 * Obtém os limites do plano do usuário
 */
export function getPlanLimits(planId: string) {
  const plan = PLANS.find(p => p.id === planId);
  return plan?.limits || {
    teams: 0,
    membersPerTeam: 0,
    storage: '0GB',
    apiCalls: 0,
  };
}

/**
 * Verifica se o usuário pode criar times
 */
export function canCreateTeam(user: Models.User<Models.Preferences>, currentTeamsCount: number = 0): {
  allowed: boolean;
  reason?: string;
  planRequired?: string;
} {
  const userPlan = getUserPlan(user.labels || []);
  const limits = getPlanLimits(userPlan);

  // Plano free não pode criar times
  if (userPlan === 'free') {
    return {
      allowed: false,
      reason: 'Plano gratuito não permite criação de times',
      planRequired: 'pro',
    };
  }

  // Verifica limite de times (-1 = ilimitado)
  if (limits.teams !== -1 && currentTeamsCount >= limits.teams) {
    return {
      allowed: false,
      reason: `Limite de ${limits.teams} time(s) atingido para o plano ${userPlan}`,
      planRequired: userPlan === 'pro' ? 'enterprise' : undefined,
    };
  }

  return { allowed: true };
}

/**
 * Verifica se o usuário pode adicionar membros ao time
 */
export function canAddTeamMember(
  user: Models.User<Models.Preferences>,
  currentMembersCount: number = 0
): {
  allowed: boolean;
  reason?: string;
  planRequired?: string;
} {
  const userPlan = getUserPlan(user.labels || []);
  const limits = getPlanLimits(userPlan);

  // Plano free não tem times
  if (userPlan === 'free') {
    return {
      allowed: false,
      reason: 'Plano gratuito não permite times',
      planRequired: 'pro',
    };
  }

  // Verifica limite de membros por time (-1 = ilimitado)
  if (limits.membersPerTeam !== -1 && currentMembersCount >= limits.membersPerTeam) {
    return {
      allowed: false,
      reason: `Limite de ${limits.membersPerTeam} membro(s) por time atingido para o plano ${userPlan}`,
      planRequired: userPlan === 'pro' ? 'enterprise' : undefined,
    };
  }

  return { allowed: true };
}

/**
 * Verifica se o usuário tem acesso a funcionalidades de times
 */
export function hasTeamAccess(user: Models.User<Models.Preferences>): boolean {
  const userPlan = getUserPlan(user.labels || []);
  return userPlan !== 'free';
}

/**
 * Obtém informações completas sobre os limites do usuário
 */
export function getUserLimitsInfo(user: Models.User<Models.Preferences>) {
  const userPlan = getUserPlan(user.labels || []);
  const limits = getPlanLimits(userPlan);
  const plan = PLANS.find(p => p.id === userPlan);

  return {
    plan: userPlan,
    planName: plan?.name || 'Desconhecido',
    limits,
    hasTeamAccess: hasTeamAccess(user),
    canCreateTeams: userPlan !== 'free',
    isUnlimited: {
      teams: limits.teams === -1,
      membersPerTeam: limits.membersPerTeam === -1,
      apiCalls: limits.apiCalls === -1,
    },
  };
}

/**
 * Formata os limites para exibição
 */
export function formatLimit(value: number | string, type: 'teams' | 'members' | 'storage' | 'api'): string {
  if (typeof value === 'string') return value;
  if (value === -1) return 'Ilimitado';
  if (value === 0) return 'Não disponível';

  switch (type) {
    case 'teams':
      return `${value} time${value !== 1 ? 's' : ''}`;
    case 'members':
      return `${value} membro${value !== 1 ? 's' : ''} por time`;
    case 'api':
      return `${value.toLocaleString()} chamadas/mês`;
    default:
      return value.toString();
  }
}

/**
 * Verifica se o usuário pode usar funcionalidades de import/export de dados
 */
export function canUseDataImportExport(user: Models.User<Models.Preferences>): {
  allowed: boolean;
  reason?: string;
  planRequired?: string;
} {
  const userPlan = getUserPlan(user.labels || []);

  // Apenas planos pagos podem usar import/export
  if (userPlan === 'free') {
    return {
      allowed: false,
      reason: 'Import/Export de dados disponível apenas para planos pagos',
      planRequired: 'pro',
    };
  }

  return { allowed: true };
}

/**
 * Verifica se o usuário pode exportar dados (todos podem exportar)
 */
export function canExportData(user: Models.User<Models.Preferences>): {
  allowed: boolean;
  reason?: string;
  planRequired?: string;
} {
  // Todos os usuários podem exportar dados
  return { allowed: true };
}

/**
 * Verifica se o usuário pode importar dados (apenas usuários pagos)
 */
export function canImportData(user: Models.User<Models.Preferences>): {
  allowed: boolean;
  reason?: string;
  planRequired?: string;
} {
  const userPlan = getUserPlan(user.labels || []);

  // Apenas planos pagos podem importar dados
  if (userPlan === 'free') {
    return {
      allowed: false,
      reason: 'Importação de dados disponível apenas para planos pagos',
      planRequired: 'pro',
    };
  }

  return { allowed: true };
}

/**
 * Verifica se o usuário pode usar IA para processamento de dados
 */
export function canUseAIProcessing(user: Models.User<Models.Preferences>): {
  allowed: boolean;
  reason?: string;
  planRequired?: string;
} {
  const userPlan = getUserPlan(user.labels || []);

  // Apenas planos pagos podem usar IA
  if (userPlan === 'free') {
    return {
      allowed: false,
      reason: 'Processamento com IA disponível apenas para planos pagos',
      planRequired: 'pro',
    };
  }

  return { allowed: true };
}

/**
 * Verifica se o upgrade é necessário para uma ação
 */
export function getUpgradeInfo(currentPlan: string, requiredPlan?: string) {
  if (!requiredPlan) return null;

  const current = PLANS.find(p => p.id === currentPlan);
  const required = PLANS.find(p => p.id === requiredPlan);

  if (!current || !required) return null;

  return {
    currentPlan: current.name,
    requiredPlan: required.name,
    requiredPlanId: requiredPlan,
    message: `Upgrade para o plano ${required.name} necessário`,
    benefits: required.features.filter(f => f.included).map(f => f.name),
  };
}
