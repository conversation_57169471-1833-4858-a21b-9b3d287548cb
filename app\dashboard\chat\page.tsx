'use client';

/**
 * Página de demonstração do chat moderno
 * Mostra os novos componentes de chat baseados no v0-ai-chat
 */

import { ModernChatExample } from "@/components/chat/modern-chat";

export default function ChatDemoPage() {
  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">Chat Moderno</h1>
        <p className="text-muted-foreground">
          Demonstração do novo sistema de chat baseado no v0-ai-chat com design moderno e funcionalidades aprimoradas.
        </p>
      </div>

      <div className="grid gap-8">
        {/* Exemplo do Chat Moderno */}
        <div className="space-y-4">
          <div>
            <h2 className="text-xl font-semibold">Chat Moderno</h2>
            <p className="text-sm text-muted-foreground">
              Interface moderna com suporte a markdown, código, anexos e muito mais.
            </p>
          </div>
          
          <div className="border rounded-lg overflow-hidden">
            <ModernChatExample />
          </div>
        </div>

        {/* Recursos do Chat */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Recursos Disponíveis</h2>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="p-4 border rounded-lg">
              <h3 className="font-medium mb-2">💬 Mensagens em Tempo Real</h3>
              <p className="text-sm text-muted-foreground">
                Sistema de chat com WebSocket para mensagens instantâneas
              </p>
            </div>
            
            <div className="p-4 border rounded-lg">
              <h3 className="font-medium mb-2">📝 Suporte a Markdown</h3>
              <p className="text-sm text-muted-foreground">
                Formatação rica com markdown, código e links
              </p>
            </div>
            
            <div className="p-4 border rounded-lg">
              <h3 className="font-medium mb-2">📎 Anexos</h3>
              <p className="text-sm text-muted-foreground">
                Envio de arquivos, imagens e documentos
              </p>
            </div>
            
            <div className="p-4 border rounded-lg">
              <h3 className="font-medium mb-2">😊 Emojis e Reações</h3>
              <p className="text-sm text-muted-foreground">
                Picker de emojis e sistema de reações
              </p>
            </div>
            
            <div className="p-4 border rounded-lg">
              <h3 className="font-medium mb-2">🔍 Busca</h3>
              <p className="text-sm text-muted-foreground">
                Busca avançada no histórico de mensagens
              </p>
            </div>
            
            <div className="p-4 border rounded-lg">
              <h3 className="font-medium mb-2">👥 Chat em Equipe</h3>
              <p className="text-sm text-muted-foreground">
                Canais de equipe e mensagens diretas
              </p>
            </div>
          </div>
        </div>

        {/* Exemplos de Uso */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Casos de Uso</h2>
          
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h3 className="font-medium">💼 Comunicação Empresarial</h3>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Canais de equipe organizados</li>
                <li>• Mensagens diretas entre colaboradores</li>
                <li>• Compartilhamento de arquivos</li>
                <li>• Histórico completo de conversas</li>
              </ul>
            </div>
            
            <div className="space-y-3">
              <h3 className="font-medium">🎯 Suporte ao Cliente</h3>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Chat em tempo real com clientes</li>
                <li>• Sistema de tickets integrado</li>
                <li>• Transferência entre agentes</li>
                <li>• Métricas de atendimento</li>
              </ul>
            </div>
            
            <div className="space-y-3">
              <h3 className="font-medium">🤖 Integração com IA</h3>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Chatbots inteligentes</li>
                <li>• Respostas automáticas</li>
                <li>• Análise de sentimento</li>
                <li>• Sugestões de resposta</li>
              </ul>
            </div>
            
            <div className="space-y-3">
              <h3 className="font-medium">📱 Multi-plataforma</h3>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Responsivo para mobile</li>
                <li>• PWA com notificações</li>
                <li>• Sincronização em tempo real</li>
                <li>• Modo offline</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
