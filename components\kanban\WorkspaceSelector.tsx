/**
 * WorkspaceSelector Component
 * Allows users to select and manage workspaces (similar to Trello workspaces)
 */

import React, { useState } from 'react';
import { 
  Building2, 
  Plus, 
  Settings, 
  Users, 
  ChevronDown, 
  Star,
  Lock,
  Globe,
  Eye
} from 'lucide-react';
import { <PERSON><PERSON> } from '../ui/button';
import { Badge } from '../ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import { cn } from '../../lib/utils';
import type { Workspace } from '@/schemas/kanban';

interface WorkspaceSelectorProps {
  workspaces: Workspace[];
  selectedWorkspace?: Workspace;
  onSelectWorkspace: (workspace: Workspace | null) => void;
  onCreateWorkspace?: () => void;
  onManageWorkspace?: (workspace: Workspace) => void;
  className?: string;
}

export function WorkspaceSelector({
  workspaces,
  selectedWorkspace,
  onSelectWorkspace,
  onCreateWorkspace,
  onManageWorkspace,
  className
}: WorkspaceSelectorProps) {
  const getVisibilityIcon = (visibility: string) => {
    switch (visibility) {
      case 'private':
        return <Lock className="h-3 w-3" />;
      case 'team':
        return <Users className="h-3 w-3" />;
      case 'public':
        return <Globe className="h-3 w-3" />;
      default:
        return <Eye className="h-3 w-3" />;
    }
  };

  const getVisibilityLabel = (visibility: string) => {
    switch (visibility) {
      case 'private':
        return 'Privado';
      case 'team':
        return 'Equipe';
      case 'public':
        return 'Público';
      default:
        return 'Visível';
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "justify-between min-w-[200px] max-w-[300px]",
            className
          )}
        >
          <div className="flex items-center gap-2 min-w-0">
            {selectedWorkspace ? (
              <>
                <div
                  className="w-4 h-4 rounded-sm flex-shrink-0"
                  style={{ 
                    backgroundColor: selectedWorkspace.color || '#6b7280' 
                  }}
                />
                <span className="truncate">{selectedWorkspace.name}</span>
              </>
            ) : (
              <>
                <Building2 className="h-4 w-4 flex-shrink-0" />
                <span className="truncate">Todos os boards</span>
              </>
            )}
          </div>
          <ChevronDown className="h-4 w-4 flex-shrink-0" />
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent align="start" className="w-80">
        {/* All Boards Option */}
        <DropdownMenuItem
          onClick={() => onSelectWorkspace(null)}
          className={cn(
            "flex items-center gap-3 p-3",
            !selectedWorkspace && "bg-accent"
          )}
        >
          <Building2 className="h-5 w-5 text-muted-foreground" />
          <div className="flex-1">
            <p className="font-medium">Todos os boards</p>
            <p className="text-xs text-muted-foreground">
              Ver todos os boards de todos os workspaces
            </p>
          </div>
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        {/* Workspaces List */}
        <div className="max-h-64 overflow-y-auto">
          {workspaces.map((workspace) => (
            <DropdownMenuItem
              key={workspace.$id}
              onClick={() => onSelectWorkspace(workspace)}
              className={cn(
                "flex items-center gap-3 p-3",
                selectedWorkspace?.$id === workspace.$id && "bg-accent"
              )}
            >
              <div
                className="w-5 h-5 rounded-sm flex-shrink-0"
                style={{ 
                  backgroundColor: workspace.color || '#6b7280' 
                }}
              />
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <p className="font-medium truncate">{workspace.name}</p>
                  {getVisibilityIcon(workspace.visibility)}
                </div>
                
                {workspace.description && (
                  <p className="text-xs text-muted-foreground line-clamp-1">
                    {workspace.description}
                  </p>
                )}
                
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="outline" className="text-xs">
                    {getVisibilityLabel(workspace.visibility)}
                  </Badge>
                  
                  {workspace.members.length > 0 && (
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <Users className="h-3 w-3" />
                      <span>{workspace.members.length}</span>
                    </div>
                  )}
                </div>
              </div>

              {onManageWorkspace && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    onManageWorkspace(workspace);
                  }}
                  className="opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <Settings className="h-3 w-3" />
                </Button>
              )}
            </DropdownMenuItem>
          ))}
        </div>

        {/* Create Workspace */}
        {onCreateWorkspace && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={onCreateWorkspace}
              className="flex items-center gap-3 p-3 text-primary"
            >
              <Plus className="h-5 w-5" />
              <div>
                <p className="font-medium">Criar workspace</p>
                <p className="text-xs text-muted-foreground">
                  Organize seus boards em um novo workspace
                </p>
              </div>
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Componente compacto para exibir workspace atual
interface WorkspaceDisplayProps {
  workspace?: Workspace;
  showMembers?: boolean;
  className?: string;
}

export function WorkspaceDisplay({ 
  workspace, 
  showMembers = false, 
  className 
}: WorkspaceDisplayProps) {
  if (!workspace) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <Building2 className="h-4 w-4 text-muted-foreground" />
        <span className="text-sm text-muted-foreground">Todos os boards</span>
      </div>
    );
  }

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <div
        className="w-4 h-4 rounded-sm"
        style={{ backgroundColor: workspace.color || '#6b7280' }}
      />
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium truncate">
            {workspace.name}
          </span>
          
          <Badge variant="outline" className="text-xs">
            {workspace.visibility === 'private' && <Lock className="h-2 w-2 mr-1" />}
            {workspace.visibility === 'team' && <Users className="h-2 w-2 mr-1" />}
            {workspace.visibility === 'public' && <Globe className="h-2 w-2 mr-1" />}
            {workspace.visibility === 'private' ? 'Privado' : 
             workspace.visibility === 'team' ? 'Equipe' : 'Público'}
          </Badge>
        </div>
        
        {workspace.description && (
          <p className="text-xs text-muted-foreground line-clamp-1">
            {workspace.description}
          </p>
        )}
      </div>

      {showMembers && workspace.members.length > 0 && (
        <div className="flex items-center gap-1">
          <div className="flex -space-x-1">
            {workspace.members.slice(0, 3).map((memberId, index) => (
              <Avatar key={memberId} className="h-6 w-6 border-2 border-background">
                <AvatarImage src={`/api/avatar/${memberId}`} />
                <AvatarFallback className="text-xs">
                  {memberId.slice(0, 2).toUpperCase()}
                </AvatarFallback>
              </Avatar>
            ))}
          </div>
          
          {workspace.members.length > 3 && (
            <span className="text-xs text-muted-foreground ml-1">
              +{workspace.members.length - 3}
            </span>
          )}
        </div>
      )}
    </div>
  );
}

// Hook para gerenciar estado do workspace selecionado
export function useWorkspaceSelection(workspaces: Workspace[]) {
  const [selectedWorkspace, setSelectedWorkspace] = useState<Workspace | null>(null);

  const selectWorkspace = (workspace: Workspace | null) => {
    setSelectedWorkspace(workspace);
  };

  const getFilteredBoards = (allBoards: any[]) => {
    if (!selectedWorkspace) {
      return allBoards;
    }
    
    return allBoards.filter(board => board.workspaceId === selectedWorkspace.$id);
  };

  return {
    selectedWorkspace,
    selectWorkspace,
    getFilteredBoards,
  };
}
