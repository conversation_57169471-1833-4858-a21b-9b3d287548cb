/**
 * Kanban Store - Valtio State Management
 * Gerencia estado dos modais e UI do sistema kanban
 */

import { proxy } from 'valtio';
import type { Board, Column, Task } from '@/schemas/kanban';

// ============================================================================
// TYPES
// ============================================================================

interface ModalState {
  isOpen: boolean;
  data?: any;
}

interface KanbanStore {
  // Board modals
  boardCreate: ModalState;
  boardEdit: ModalState & { board?: Board };
  boardView: ModalState & { board?: Board };

  // Column modals
  columnCreate: ModalState & { boardId?: string };
  columnEdit: ModalState & { column?: Column };

  // Task modals
  taskCreate: ModalState & { columnId?: string; boardId?: string };
  taskEdit: ModalState & { task?: Task };
  taskView: ModalState & { task?: Task };

  // Filter modals
  filtersModal: ModalState;
  searchModal: ModalState;

  // UI state
  selectedBoardId: string | null;
  draggedTask: Task | null;
  isDragging: boolean;

  // Filters
  filters: {
    search: string;
    priority: string[];
    status: string[];
    assignedTo: string[];
    tags: string[];
    showArchived: boolean;
  };

  // View preferences
  viewMode: 'board' | 'list';
  compactMode: boolean;
  showTaskDetails: boolean;
}

// ============================================================================
// STORE
// ============================================================================

export const kanbanStore = proxy<KanbanStore>({
  // Board modals
  boardCreate: {
    isOpen: false,
  },
  boardEdit: {
    isOpen: false,
    board: undefined,
  },
  boardView: {
    isOpen: false,
    board: undefined,
  },

  // Column modals
  columnCreate: {
    isOpen: false,
    boardId: undefined,
  },
  columnEdit: {
    isOpen: false,
    column: undefined,
  },

  // Task modals
  taskCreate: {
    isOpen: false,
    columnId: undefined,
    boardId: undefined,
  },
  taskEdit: {
    isOpen: false,
    task: undefined,
  },
  taskView: {
    isOpen: false,
    task: undefined,
  },

  // Filter modals
  filtersModal: {
    isOpen: false,
  },
  searchModal: {
    isOpen: false,
  },

  // UI state
  selectedBoardId: null,
  draggedTask: null,
  isDragging: false,

  // Filters
  filters: {
    search: '',
    priority: [],
    status: [],
    assignedTo: [],
    tags: [],
    showArchived: false,
  },

  // View preferences
  viewMode: 'board',
  compactMode: false,
  showTaskDetails: true,
});

// ============================================================================
// ACTIONS
// ============================================================================

export const kanbanActions = {
  // Board actions
  openBoardCreate() {
    kanbanStore.boardCreate.isOpen = true;
  },

  closeBoardCreate() {
    kanbanStore.boardCreate.isOpen = false;
  },

  openBoardEdit(board: Board) {
    kanbanStore.boardEdit.isOpen = true;
    kanbanStore.boardEdit.board = board;
  },

  closeBoardEdit() {
    kanbanStore.boardEdit.isOpen = false;
    kanbanStore.boardEdit.board = undefined;
  },

  openBoardView(board: Board) {
    kanbanStore.boardView.isOpen = true;
    kanbanStore.boardView.board = board;
  },

  closeBoardView() {
    kanbanStore.boardView.isOpen = false;
    kanbanStore.boardView.board = undefined;
  },

  // Column actions
  openColumnCreate(boardId: string) {
    kanbanStore.columnCreate.isOpen = true;
    kanbanStore.columnCreate.boardId = boardId;
  },

  closeColumnCreate() {
    kanbanStore.columnCreate.isOpen = false;
    kanbanStore.columnCreate.boardId = undefined;
  },

  openColumnEdit(column: Column) {
    kanbanStore.columnEdit.isOpen = true;
    kanbanStore.columnEdit.column = column;
  },

  closeColumnEdit() {
    kanbanStore.columnEdit.isOpen = false;
    kanbanStore.columnEdit.column = undefined;
  },

  // Task actions
  openTaskCreate(columnId: string, boardId: string) {
    kanbanStore.taskCreate.isOpen = true;
    kanbanStore.taskCreate.columnId = columnId;
    kanbanStore.taskCreate.boardId = boardId;
  },

  closeTaskCreate() {
    kanbanStore.taskCreate.isOpen = false;
    kanbanStore.taskCreate.columnId = undefined;
    kanbanStore.taskCreate.boardId = undefined;
  },

  openTaskEdit(task: Task) {
    kanbanStore.taskEdit.isOpen = true;
    kanbanStore.taskEdit.task = task;
  },

  closeTaskEdit() {
    kanbanStore.taskEdit.isOpen = false;
    kanbanStore.taskEdit.task = undefined;
  },

  openTaskView(task: Task) {
    kanbanStore.taskView.isOpen = true;
    kanbanStore.taskView.task = task;
  },

  closeTaskView() {
    kanbanStore.taskView.isOpen = false;
    kanbanStore.taskView.task = undefined;
  },

  // Filter modal actions
  openFiltersModal() {
    kanbanStore.filtersModal.isOpen = true;
  },

  closeFiltersModal() {
    kanbanStore.filtersModal.isOpen = false;
  },

  openSearchModal() {
    kanbanStore.searchModal.isOpen = true;
  },

  closeSearchModal() {
    kanbanStore.searchModal.isOpen = false;
  },

  // UI actions
  setSelectedBoard(boardId: string | null) {
    kanbanStore.selectedBoardId = boardId;
  },

  setDraggedTask(task: Task | null) {
    kanbanStore.draggedTask = task;
    kanbanStore.isDragging = !!task;
  },

  // Filter actions
  setSearch(search: string) {
    kanbanStore.filters.search = search;
  },

  setPriorityFilter(priorities: string[]) {
    kanbanStore.filters.priority = priorities;
  },

  setStatusFilter(statuses: string[]) {
    kanbanStore.filters.status = statuses;
  },

  setAssignedToFilter(assignedTo: string[]) {
    kanbanStore.filters.assignedTo = assignedTo;
  },

  setTagsFilter(tags: string[]) {
    kanbanStore.filters.tags = tags;
  },

  toggleShowArchived() {
    kanbanStore.filters.showArchived = !kanbanStore.filters.showArchived;
  },

  clearFilters() {
    kanbanStore.filters = {
      search: '',
      priority: [],
      status: [],
      assignedTo: [],
      tags: [],
      showArchived: false,
    };
  },

  // View actions
  setViewMode(mode: 'board' | 'list') {
    kanbanStore.viewMode = mode;
  },

  toggleCompactMode() {
    kanbanStore.compactMode = !kanbanStore.compactMode;
  },

  toggleTaskDetails() {
    kanbanStore.showTaskDetails = !kanbanStore.showTaskDetails;
  },

  // Utility actions
  closeAllModals() {
    kanbanStore.boardCreate.isOpen = false;
    kanbanStore.boardEdit.isOpen = false;
    kanbanStore.boardEdit.board = undefined;
    kanbanStore.boardView.isOpen = false;
    kanbanStore.boardView.board = undefined;

    kanbanStore.columnCreate.isOpen = false;
    kanbanStore.columnCreate.boardId = undefined;
    kanbanStore.columnEdit.isOpen = false;
    kanbanStore.columnEdit.column = undefined;

    kanbanStore.taskCreate.isOpen = false;
    kanbanStore.taskCreate.columnId = undefined;
    kanbanStore.taskCreate.boardId = undefined;
    kanbanStore.taskEdit.isOpen = false;
    kanbanStore.taskEdit.task = undefined;
    kanbanStore.taskView.isOpen = false;
    kanbanStore.taskView.task = undefined;

    kanbanStore.filtersModal.isOpen = false;
    kanbanStore.searchModal.isOpen = false;
  },

  resetStore() {
    kanbanStore.selectedBoardId = null;
    kanbanStore.draggedTask = null;
    kanbanStore.isDragging = false;
    kanbanStore.filters = {
      search: '',
      priority: [],
      status: [],
      assignedTo: [],
      tags: [],
      showArchived: false,
    };
    kanbanStore.viewMode = 'board';
    kanbanStore.compactMode = false;
    kanbanStore.showTaskDetails = true;
    kanbanActions.closeAllModals();
  },
};

// ============================================================================
// SELECTORS
// ============================================================================

export const kanbanSelectors = {
  // Check if any modal is open
  isAnyModalOpen: () => {
    return (
      kanbanStore.boardCreate.isOpen ||
      kanbanStore.boardEdit.isOpen ||
      kanbanStore.boardView.isOpen ||
      kanbanStore.columnCreate.isOpen ||
      kanbanStore.columnEdit.isOpen ||
      kanbanStore.taskCreate.isOpen ||
      kanbanStore.taskEdit.isOpen ||
      kanbanStore.taskView.isOpen ||
      kanbanStore.filtersModal.isOpen ||
      kanbanStore.searchModal.isOpen
    );
  },

  // Check if filters are active
  hasActiveFilters: () => {
    const { filters } = kanbanStore;
    return (
      filters.search.length > 0 ||
      filters.priority.length > 0 ||
      filters.status.length > 0 ||
      filters.assignedTo.length > 0 ||
      filters.tags.length > 0 ||
      filters.showArchived
    );
  },

  // Get current board
  getCurrentBoard: () => {
    return kanbanStore.boardEdit.board || kanbanStore.boardView.board;
  },

  // Get current task
  getCurrentTask: () => {
    return kanbanStore.taskEdit.task || kanbanStore.taskView.task;
  },

  // Get current column
  getCurrentColumn: () => {
    return kanbanStore.columnEdit.column;
  },
};
