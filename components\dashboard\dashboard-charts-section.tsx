import { DashboardRevenueChart } from "./dashboard-revenue-chart";
import { DashboardGrowthChart } from "./dashboard-growth-chart";
import { DashboardActivityChart } from "./dashboard-activity-chart";
import { DashboardDistributionChart } from "./dashboard-distribution-chart";
import { DashboardProgressChart } from "./dashboard-progress-chart";
import { DashboardPieChart } from "./dashboard-pie-chart";
import { DashboardAreaChart } from "./dashboard-area-chart";
import { useIsMobile } from "../../hooks/use-mobile";

export function DashboardChartsSection() {
  const isMobile = useIsMobile();

  return (
    <div className="space-y-6">
      {/* T<PERSON><PERSON><PERSON> da se<PERSON> */}
      <div className="px-4 lg:px-6">
        <div className="space-y-2">
          <h2 className="text-xl font-semibold tracking-tight">
            <PERSON><PERSON><PERSON><PERSON> e Métricas
          </h2>
          <p className="text-muted-foreground">
            Acompanhe o desempenho e crescimento do seu negócio em tempo real.
          </p>
        </div>
      </div>

      {/* Grid principal de gráficos */}
      <div className="px-4 lg:px-6">
        <div className="grid gap-6">
          {/* Primeira linha - Gráficos principais */}
          <div className={`grid gap-6 ${isMobile ? 'grid-cols-1' : 'grid-cols-1 lg:grid-cols-2'}`}>
            <DashboardRevenueChart />
            <DashboardGrowthChart />
          </div>

          {/* Segunda linha - Gráfico de atividades (largura completa) */}
          <div className="grid gap-6">
            <DashboardActivityChart />
          </div>

          {/* Terceira linha - Distribuição, Progresso e Pizza */}
          <div className={`grid gap-6 ${isMobile ? 'grid-cols-1' : 'grid-cols-1 lg:grid-cols-3'}`}>
            <DashboardDistributionChart />
            <DashboardProgressChart />
            <DashboardPieChart />
          </div>

          {/* Quarta linha - Gráfico de Área */}
          <div className="grid gap-6">
            <DashboardAreaChart />
          </div>
        </div>
      </div>
    </div>
  );
}
