/**
 * Página de exemplo para demonstrar o sistema de chat em tempo real
 */

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  MessageCircle, 
  Zap, 
  Users, 
  Settings,
  Code,
  BookOpen,
  ExternalLink,
  Info
} from 'lucide-react';
import { ChatRealtimeDemo } from '@/components/examples/chat-realtime-demo';
import { Button } from '@/components/ui/button';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Chat em Tempo Real - Exemplo',
  description: 'Demonstração do sistema de chat em tempo real com Appwrite',
};

export default function ChatRealtimeExamplePage() {
  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-2">
          <MessageCircle className="h-8 w-8 text-primary" />
          <h1 className="text-4xl font-bold">Chat em Tempo Real</h1>
        </div>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          Demonstração do sistema de chat integrado com Appwrite Realtime para comunicação instantânea
        </p>
        <div className="flex items-center justify-center gap-2">
          <Badge variant="secondary" className="gap-1">
            <Zap className="h-3 w-3" />
            Tempo Real
          </Badge>
          <Badge variant="outline" className="gap-1">
            <Users className="h-3 w-3" />
            Multi-usuário
          </Badge>
          <Badge variant="outline" className="gap-1">
            <Settings className="h-3 w-3" />
            Configurável
          </Badge>
        </div>
      </div>

      <Separator />

      {/* Informações Técnicas */}
      <div className="grid md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Code className="h-5 w-5" />
              Tecnologias Utilizadas
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <span>Appwrite Realtime</span>
              <Badge variant="default">WebSocket</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span>React Query</span>
              <Badge variant="secondary">Cache</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span>TypeScript</span>
              <Badge variant="outline">Type Safety</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span>Shadcn/ui</span>
              <Badge variant="outline">Components</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              Recursos Implementados
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <span>Mensagens em Tempo Real</span>
              <Badge variant="default">✓</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span>Indicador de Digitação</span>
              <Badge variant="secondary">Parcial</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span>Status de Conexão</span>
              <Badge variant="default">✓</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span>Cache Otimista</span>
              <Badge variant="default">✓</Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Alertas Importantes */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>Configuração Necessária:</strong> Para funcionar corretamente, certifique-se de que as variáveis de ambiente 
          do Appwrite estão configuradas e que as coleções de chat foram criadas. 
          Consulte a documentação em <code>docs/chat-realtime.md</code> para mais detalhes.
        </AlertDescription>
      </Alert>

      {/* Demonstração Interativa */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">Demonstração Interativa</h2>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" asChild>
              <a href="/docs/chat-realtime.md" target="_blank" rel="noopener noreferrer">
                <BookOpen className="h-4 w-4 mr-2" />
                Documentação
                <ExternalLink className="h-3 w-3 ml-1" />
              </a>
            </Button>
          </div>
        </div>
        
        <ChatRealtimeDemo teamId="demo-team-123" />
      </div>

      {/* Código de Exemplo */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Code className="h-5 w-5" />
            Exemplo de Uso
          </CardTitle>
          <CardDescription>
            Como implementar o chat em tempo real em seus componentes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <pre className="bg-muted p-4 rounded-lg overflow-x-auto text-sm">
            <code>{`import { useChatMessages, useSendMessage, useChatRealtime } from '@/hooks/use-api';

function ChatComponent({ teamId }: { teamId: string }) {
  const { data: messagesData } = useChatMessages(teamId);
  const sendMessageMutation = useSendMessage();
  const { isConnected, sendTypingIndicator } = useChatRealtime(teamId);

  const handleSendMessage = async (content: string) => {
    await sendMessageMutation.mutateAsync({
      content,
      teamId,
      type: 'text',
    });
  };

  return (
    <div>
      <div>Status: {isConnected ? 'Conectado' : 'Desconectado'}</div>
      {messagesData?.messages?.map(message => (
        <div key={message.$id}>
          <strong>{message.senderName}:</strong> {message.content}
        </div>
      ))}
      {/* Input para nova mensagem */}
    </div>
  );
}`}</code>
          </pre>
        </CardContent>
      </Card>

      {/* Próximos Passos */}
      <Card>
        <CardHeader>
          <CardTitle>Próximos Passos</CardTitle>
          <CardDescription>
            Funcionalidades planejadas para futuras versões
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 bg-yellow-500 rounded-full" />
            <span>Indicadores de digitação via Cloud Functions</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 bg-yellow-500 rounded-full" />
            <span>Status de presença online dos usuários</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 bg-yellow-500 rounded-full" />
            <span>Notificações push para mensagens</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 bg-yellow-500 rounded-full" />
            <span>Suporte a anexos e imagens</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 bg-yellow-500 rounded-full" />
            <span>Reações às mensagens</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 bg-yellow-500 rounded-full" />
            <span>Histórico de mensagens com paginação</span>
          </div>
        </CardContent>
      </Card>

      {/* Footer */}
      <div className="text-center text-muted-foreground">
        <p>
          Este exemplo demonstra a integração com Appwrite Realtime para comunicação em tempo real.
          Para implementar em produção, configure as permissões adequadas e considere as limitações de rate limiting.
        </p>
      </div>
    </div>
  );
}
