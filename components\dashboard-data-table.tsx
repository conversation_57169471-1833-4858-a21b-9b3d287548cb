/**
 * Componente de tabela de dados para o dashboard com dados reais
 */

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card";
import { Badge } from "./ui/badge";
import { <PERSON><PERSON> } from "./ui/button";
import { Input } from "./ui/input";
import { Skeleton } from "./ui/skeleton";
import { Search, Eye, Edit, Trash2 } from "lucide-react";
import { useClients } from "../hooks/api/use-clients";
import { useActivities } from "../hooks/api/use-activities";
import { formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";
import { DashboardErrorWrapper } from "./dashboard-error-wrapper";
import React from "react";

export function DashboardDataTable() {
  const [searchTerm, setSearchTerm] = useState("");
  const { data: clients = [], isLoading: clientsLoading } = useClients();
  const { data: activities = [], isLoading: activitiesLoading } = useActivities();

  // Combinar dados de clientes e atividades recentes
  const dashboardData = React.useMemo(() => [
    // Clientes recentes
    ...clients.slice(0, 5).map(client => ({
      id: client.$id,
      type: "client" as const,
      title: client.name,
      subtitle: client.email,
      status: "Ativo",
      category: "Cliente",
      lastUpdate: client.$updatedAt,
      priority: "normal" as const,
    })),
    // Atividades recentes
    ...activities.slice(0, 5).map(activity => ({
      id: activity.$id,
      type: "activity" as const,
      title: activity.action,
      subtitle: activity.details || "Atividade do sistema",
      status: "Concluído",
      category: "Atividade",
      lastUpdate: activity.$createdAt,
      priority: activity.action.includes("delete") ? "high" as const : "normal" as const,
    })),
  ].sort((a, b) => new Date(b.lastUpdate).getTime() - new Date(a.lastUpdate).getTime()), [clients, activities]);

  // Filtrar dados baseado na busca
  const filteredData = React.useMemo(() => dashboardData.filter(item =>
    item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (item.subtitle && typeof item.subtitle === 'string' && item.subtitle.toLowerCase().includes(searchTerm.toLowerCase())) ||
    item.category.toLowerCase().includes(searchTerm.toLowerCase())
  ), [dashboardData, searchTerm]);

  const isLoading = clientsLoading || activitiesLoading;

  // Mover toda a lógica de renderização para useMemo
  const content = React.useMemo(() => {
    if (isLoading) {
      return (
        <div className="px-4 lg:px-6">
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-48" />
              <Skeleton className="h-4 w-64" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Array.from({ length: 5 }).map((_, i) => (
                  <Skeleton key={i} className="h-16 w-full" />
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return (
      <DashboardErrorWrapper
        fallbackTitle="Erro na Tabela de Dados"
        fallbackDescription="Não foi possível carregar a tabela de atividades recentes."
        showSkeleton={true}
      >
        <div className="px-4 lg:px-6">
          <Card>
          <CardHeader>
            <CardTitle>Atividade Recente</CardTitle>
            <CardDescription>
              Últimos clientes cadastrados e atividades do sistema
            </CardDescription>

            {/* Barra de busca */}
            <div className="flex items-center space-x-2 pt-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="Buscar por nome, email ou categoria..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </CardHeader>

          <CardContent>
            {filteredData.length === 0 ? (
              <div className="flex items-center justify-center py-8">
                <p className="text-muted-foreground">
                  {searchTerm ? "Nenhum resultado encontrado" : "Nenhum dado disponível"}
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredData.map((item) => (
                  <div
                    key={item.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex-1 space-y-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium">{item.title}</h4>
                        <Badge
                          variant={item.priority === "high" ? "destructive" : "secondary"}
                          className="text-xs"
                        >
                          {item.category}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {typeof item.subtitle === 'string' ? item.subtitle : "Sem descrição"}
                      </p>
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <span>Status: {item.status}</span>
                        <span>
                          Atualizado {formatDistanceToNow(new Date(item.lastUpdate), {
                            addSuffix: true,
                            locale: ptBR
                          })}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                      {item.type === "client" && (
                        <>
                          <Button variant="ghost" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Rodapé com estatísticas */}
            <div className="flex items-center justify-between pt-6 mt-6 border-t">
              <div className="text-sm text-muted-foreground">
                Mostrando {filteredData.length} de {dashboardData.length} itens
              </div>
              <div className="flex gap-4 text-sm">
                <span className="text-muted-foreground">
                  {clients.length} clientes
                </span>
                <span className="text-muted-foreground">
                  {activities.length} atividades
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      </DashboardErrorWrapper>
    );
  }, [isLoading, filteredData, dashboardData, clients, activities, searchTerm, setSearchTerm]);

  return content;
}
