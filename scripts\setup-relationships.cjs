/**
 * Script exclusivo para configurar RELACIONAMENTOS entre collections
 *
 * ⚠️ IMPORTANTE: SEPARAÇÃO DE RESPONSABILIDADES
 * - const.cjs: Define collections e atributos básicos (string, enum, boolean, etc.)
 * - setup-relationships.cjs: Define APENAS relacionamentos entre collections
 *
 * ORDEM DE EXECUÇÃO:
 * 1. yarn setup (executa setup-collections.cjs + setup-attributes.cjs)
 * 2. yarn setup:relationships (executa este script)
 *
 * RELACIONAMENTOS CONFIGURADOS:
 * 1. Chat-Mensagens: Team Chats (1) ←→ (N) Chat Messages com CASCADE DELETE
 *    - Cria atributo 'messages' em team_chats (array de chat_messages)
 *    - Cria atributo 'chat' em chat_messages (referência para team_chats)
 *    - TWO-WAY: Appwrite gerencia automaticamente ambos os lados
 *    - CASCADE DELETE: Deletar chat remove todas as mensagens
 * 2. Futuras relações podem ser adicionadas aqui
 *
 * Execute: node scripts/setup-relationships.cjs
 */

require('dotenv').config({ path: '../.env' });
const { Client, Databases } = require('node-appwrite');

// Configuração do cliente Appwrite
const client = new Client();
client
  .setEndpoint(process.env.APPWRITE_ENDPOINT)
  .setProject(process.env.APPWRITE_PROJECT_ID)
  .setKey(process.env.APPWRITE_API_KEY);

const databases = new Databases(client);
const DATABASE_ID = process.env.APPWRITE_DATABASE_ID;

// Definição dos relacionamentos a serem criados
const relationships = [
  {
    name: 'Chat-Messages Two-Way Relationship',
    description: 'Team Chats (1) ←→ (N) Chat Messages com CASCADE DELETE (TWO-WAY)',
    parentCollection: 'VITE_APPWRITE_TEAM_CHATS_ID',
    childCollection: 'VITE_APPWRITE_CHAT_MESSAGES_ID',
    parentAttribute: 'messages', // atributo no chat que vai conter array de mensagens
    childAttribute: 'chat', // atributo na mensagem que vai referenciar o chat
    type: 'oneToMany',
    twoWay: true, // ✅ TWO-WAY: Appwrite gerencia automaticamente ambos os lados
    onDelete: 'cascade'
  }
  // Futuras relações podem ser adicionadas aqui
];

/**
 * Função para criar um relacionamento entre collections
 */
async function createRelationship(relationshipConfig) {
  try {
    console.log(`\n🔗 Criando relacionamento: ${relationshipConfig.name}`);
    console.log(`📝 Descrição: ${relationshipConfig.description}`);

    // Buscar IDs das collections do .env
    const parentCollectionId = process.env[relationshipConfig.parentCollection];
    const childCollectionId = process.env[relationshipConfig.childCollection];

    if (!parentCollectionId || !childCollectionId) {
      throw new Error(`Collections não encontradas no .env: ${relationshipConfig.parentCollection} ou ${relationshipConfig.childCollection}`);
    }

    console.log(`📋 Collection Pai: ${parentCollectionId}`);
    console.log(`📋 Collection Filha: ${childCollectionId}`);
    console.log(`📋 Database ID: ${DATABASE_ID}`);

    // Criar atributo de relacionamento
    console.log('🔧 Chamando createRelationshipAttribute...');
    await databases.createRelationshipAttribute(
      DATABASE_ID,
      parentCollectionId,
      childCollectionId, // relatedCollectionId
      relationshipConfig.type, // type
      relationshipConfig.twoWay, // twoWay
      relationshipConfig.parentAttribute, // key
      relationshipConfig.childAttribute, // twoWayKey
      relationshipConfig.onDelete // onDelete
    );

    console.log('✅ Relacionamento TWO-WAY criado com sucesso!');
    console.log(`   - ${parentCollectionId}.${relationshipConfig.parentAttribute} → array de ${childCollectionId}`);
    console.log(`   - ${childCollectionId}.${relationshipConfig.childAttribute} → referência para ${parentCollectionId}`);
    console.log(`   - TWO-WAY: Appwrite gerencia automaticamente ambos os lados`);
    console.log(`   - ON DELETE: ${relationshipConfig.onDelete.toUpperCase()}`);

    return true;
  } catch (error) {
    if (error.code === 409) {
      console.log('ℹ️ Relacionamento já existe');
      return true;
    } else {
      console.error('❌ Erro ao criar relacionamento:', error.message);
      throw error;
    }
  }
}

async function setupChatRelationships() {
  try {
    console.log('🚀 Iniciando configuração APENAS dos relacionamentos...\n');

    // Verificar configuração
    if (!process.env.APPWRITE_ENDPOINT || !process.env.APPWRITE_PROJECT_ID || !process.env.APPWRITE_API_KEY) {
      throw new Error('❌ Variáveis de ambiente do Appwrite não configuradas');
    }

    console.log('📋 RELACIONAMENTOS A SEREM CRIADOS:');
    relationships.forEach((rel, index) => {
      console.log(`   ${index + 1}. ${rel.name}`);
      console.log(`      ${rel.description}`);
    });
    console.log('');

    // Verificar se as collections existem
    console.log('🔍 Verificando se as collections existem...');
    for (const rel of relationships) {
      const parentId = process.env[rel.parentCollection];
      const childId = process.env[rel.childCollection];

      if (!parentId) {
        throw new Error(`❌ Collection pai não encontrada: ${rel.parentCollection} não está definida no .env`);
      }
      if (!childId) {
        throw new Error(`❌ Collection filha não encontrada: ${rel.childCollection} não está definida no .env`);
      }

      console.log(`   ✅ ${rel.parentCollection}: ${parentId}`);
      console.log(`   ✅ ${rel.childCollection}: ${childId}`);
    }

    // Criar relacionamentos
    console.log('\n🔗 Criando relacionamentos...');

    for (const relationshipConfig of relationships) {
      await createRelationship(relationshipConfig);
    }

    console.log('\n🎉 Configuração dos relacionamentos concluída com sucesso!');
    console.log('\n📝 PRÓXIMOS PASSOS:');
    console.log('   1. Os relacionamentos estão prontos para uso');
    console.log('   2. Use team_chats.messages para buscar mensagens via relacionamento');
    console.log('   3. CASCADE DELETE configurado automaticamente');
    console.log('   4. Teste o relacionamento com o script test-chat-relationship.cjs');

  } catch (error) {
    console.error('❌ Erro ao configurar relacionamentos:', error);
    process.exit(1);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  setupChatRelationships();
}

module.exports = { setupChatRelationships };
