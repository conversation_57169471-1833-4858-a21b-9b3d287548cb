# 👥 Sistema de Teams

Este documento explica o sistema completo de teams (equipes) do template, incluindo gerenciamento de membros, permissões granulares e integração com documentos.

## 📋 Visão Geral

O sistema de teams utiliza a API nativa do Appwrite Teams com extensões customizadas para:

- **Gerenciamento de membros** com roles flexíveis
- **Permissões granulares** por recurso e ação
- **Cargos customizáveis** definidos por team
- **Integração automática** com documentos e recursos
- **Contexto global** para facilitar o desenvolvimento

## 🏗️ Arquitetura

### Componentes Principais

```
app/
├── contexts/team-context.tsx       # Contexto global de teams
├── hooks/api/use-teams.ts          # Hooks para operações de teams
├── hooks/api/use-permissions.ts    # Hooks para gerenciar permissões
├── lib/permissions.ts              # Sistema de permissões
├── lib/team-permissions.ts         # Permissões específicas de teams
├── lib/document-permissions.ts     # Permissões para documentos
└── types/
    ├── teams.ts                    # Tipos de teams (Appwrite nativo)
    └── permissions.ts              # Tipos de permissões customizadas
```

## 🔧 Configuração Básica

### 1. Contexto de Teams

O `TeamProvider` deve envolver sua aplicação:

```typescript
import { TeamProvider } from '@/contexts/team-context';

function App() {
  return (
    <TeamProvider>
      {/* Sua aplicação */}
    </TeamProvider>
  );
}
```

### 2. Hook Principal

```typescript
import { useTeamContext } from '@/contexts/team-context';

function MyComponent() {
  const {
    currentTeam,           // Team ativo
    currentTeamId,         // ID do team ativo
    teams,                 // Lista de teams do usuário
    currentUserMembership, // Membership do usuário no team atual
    permissionContext,     // Contexto de permissões
    switchTeam,           // Função para trocar de team
    isLoadingTeams        // Estado de carregamento
  } = useTeamContext();
}
```

## 👥 Gerenciamento de Teams

### Criar Team

```typescript
import { useCreateTeam } from '@/hooks/api/use-teams';

function CreateTeamForm() {
  const createTeam = useCreateTeam();

  const handleSubmit = async (data: { name: string }) => {
    await createTeam.mutateAsync({
      name: data.name,
      roles: ['owner', 'admin', 'member', 'guest'] // Roles padrão
    });
  };
}
```

### Convidar Membros

```typescript
import { useInviteToTeam } from '@/hooks/api/use-teams';

function InviteMemberForm() {
  const inviteToTeam = useInviteToTeam();

  const handleInvite = async (data: { email: string; roles: string[] }) => {
    await inviteToTeam.mutateAsync({
      teamId: currentTeamId,
      email: data.email,
      roles: data.roles,
      url: `${window.location.origin}/dashboard/teams`
    });
  };
}
```

### Gerenciar Membros

```typescript
import { useTeamMembers, useRemoveTeamMember } from '@/hooks/api/use-teams';

function TeamMembersList() {
  const { data: members } = useTeamMembers(teamId);
  const removeTeamMember = useRemoveTeamMember();

  const handleRemoveMember = async (membershipId: string) => {
    await removeTeamMember.mutateAsync({
      teamId,
      membershipId
    });
  };
}
```

## 🔐 Sistema de Permissões

### Tipos de Usuário

```typescript
type UserType = 'owner' | 'admin' | 'user' | 'guest';
```

- **Owner**: Acesso total, pode alterar preferências do team
- **Admin**: CRUD completo + convidar membros
- **User**: Pode criar, ver, editar próprios itens
- **Guest**: Apenas visualizar

### Recursos do Sistema

```typescript
type SystemResource = 
  | 'dashboard'
  | 'analytics'
  | 'calendar'
  | 'documents'
  | 'kanban'
  | 'clients'
  | 'teams'
  | 'team_chat'
  | 'activities'
  | 'reports'
  | 'preferences'
  | 'plans'
  | 'help';
```

### Ações Disponíveis

```typescript
type ResourceAction = 'view' | 'create' | 'edit' | 'delete' | 'manage';
```

### Verificar Permissões

```typescript
import { useHasPermission } from '@/contexts/team-context';

function MyComponent() {
  const hasPermission = useHasPermission();

  const canCreateClients = hasPermission('clients', 'create');
  const canManageTeams = hasPermission('teams', 'manage');

  return (
    <div>
      {canCreateClients && (
        <Button onClick={createClient}>Criar Cliente</Button>
      )}
      {canManageTeams && (
        <Button onClick={manageTeam}>Gerenciar Team</Button>
      )}
    </div>
  );
}
```

## 🎯 Cargos Customizáveis

### Criar Cargo Customizado

```typescript
import { useCreateTeamRole } from '@/hooks/api/use-permissions';

function CreateRoleForm() {
  const createRole = useCreateTeamRole();

  const handleSubmit = async (data: CreateTeamRoleData) => {
    await createRole.mutateAsync({
      teamId: currentTeamId,
      roleData: {
        name: 'Gerente de Vendas',
        description: 'Acesso total a clientes e relatórios',
        color: '#3b82f6',
        userType: 'user',
        permissions: [
          { resource: 'clients', actions: ['view', 'create', 'edit', 'delete'] },
          { resource: 'reports', actions: ['view', 'create'] },
          { resource: 'dashboard', actions: ['view'] }
        ],
        isDefault: false
      }
    });
  };
}
```

### Atribuir Cargo a Membro

```typescript
import { useAssignRoleToMember } from '@/hooks/api/use-permissions';

function AssignRoleForm() {
  const assignRole = useAssignRoleToMember();

  const handleAssign = async (userId: string, roleId: string) => {
    await assignRole.mutateAsync({
      teamId: currentTeamId,
      userId,
      roleId
    });
  };
}
```

## 📄 Permissões de Documentos

### Sistema Automático

O sistema aplica automaticamente permissões do Appwrite aos documentos:

```typescript
import { useDocumentPermissions } from '@/hooks/use-document-permissions';

function CreateDocumentForm() {
  const { permissions, teamId } = useDocumentPermissions();

  const handleSubmit = async (data: any) => {
    // As permissões são aplicadas automaticamente
    await createClient(data, {
      userId: user.$id,
      teamId,
      permissionContext,
      isPublic: false
    });
  };
}
```

### Verificar Acesso a Documentos

```typescript
import { useDocumentAccess } from '@/hooks/use-document-permissions';

function DocumentActions({ document }) {
  const { canRead, canWrite, canDelete } = useDocumentAccess(document.$permissions);

  return (
    <div>
      {canRead && <Button>Visualizar</Button>}
      {canWrite && <Button>Editar</Button>}
      {canDelete && <Button>Deletar</Button>}
    </div>
  );
}
```

### Lógica de Permissões

1. **Criador**: Sempre tem acesso total (read, write, delete)
2. **Team**: Acesso baseado nas permissões do cargo/role
3. **Público**: Se `isPublic: true`, qualquer um pode ler

## 🔄 Seleção de Team

### Prioridade de Seleção

1. **selectedTeam** (localStorage) - Team selecionado pelo usuário
2. **Primeiro team** do usuário - Se não há seleção
3. **userId** como fallback - Se não há teams

### Trocar de Team

```typescript
import { useTeamContext } from '@/contexts/team-context';

function TeamSelector() {
  const { teams, currentTeamId, switchTeam } = useTeamContext();

  return (
    <Select value={currentTeamId} onValueChange={switchTeam}>
      {teams.map(team => (
        <SelectItem key={team.$id} value={team.$id}>
          {team.name}
        </SelectItem>
      ))}
    </Select>
  );
}
```

## 🛠️ Hooks Utilitários

### useCurrentTeam

```typescript
import { useCurrentTeam } from '@/contexts/team-context';

function TeamInfo() {
  const { team, teamId, membership, isOwner } = useCurrentTeam();

  return (
    <div>
      <h2>{team?.name}</h2>
      <p>Você é {isOwner ? 'proprietário' : 'membro'}</p>
    </div>
  );
}
```

### useHasAccess

```typescript
import { useHasAccess } from '@/contexts/team-context';

function ProtectedComponent() {
  const hasClientsAccess = useHasAccess('clients');

  if (!hasClientsAccess) {
    return <div>Sem acesso a clientes</div>;
  }

  return <ClientsList />;
}
```

## 📊 Estrutura de Dados

### Team (Appwrite Nativo)

```typescript
interface Team extends Models.Team<Models.Preferences> {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  name: string;
  total: number;
  prefs: TeamPermissionPreferences; // Preferências customizadas
}
```

### TeamMembership

```typescript
interface TeamMembership extends Models.Membership {
  $id: string;
  userId: string;
  userName: string;
  userEmail: string;
  teamId: string;
  teamName: string;
  roles: string[];
  invited: string;
  joined: string;
  confirm: boolean;
}
```

### Preferências do Team

```typescript
interface TeamPermissionPreferences {
  // Configurações básicas
  description?: string;
  color?: string;
  timezone?: string;
  
  // Configurações de permissões
  roles?: TeamRole[];
  memberRoles?: TeamMemberRole[];
  
  // Configurações de notificação
  notifyOnMention?: boolean;
  notifyOnMessage?: boolean;
  emailNotifications?: boolean;
}
```

## 🔍 Troubleshooting

### Problemas Comuns

1. **Erro "Phone authentication budget cap"**
   - Desabilite autenticação por telefone no console Appwrite
   - Configure domínios permitidos

2. **Permissões não aplicadas**
   - Verifique se o TeamProvider está configurado
   - Confirme que o usuário tem membership no team

3. **Team não carrega**
   - Verifique as variáveis de ambiente
   - Confirme que o usuário está autenticado

### Debug

```typescript
import { useTeamContext } from '@/contexts/team-context';

function DebugTeams() {
  const context = useTeamContext();
  
  console.log('Team Context:', {
    currentTeamId: context.currentTeamId,
    teams: context.teams,
    permissionContext: context.permissionContext
  });
}
```

## 📚 Exemplos Práticos

### Componente com Permissões

```typescript
import { useTeamContext, useHasPermission } from '@/contexts/team-context';

function ClientsPage() {
  const { currentTeam } = useTeamContext();
  const hasPermission = useHasPermission();
  
  const canCreate = hasPermission('clients', 'create');
  const canManage = hasPermission('clients', 'manage');

  return (
    <div>
      <h1>Clientes - {currentTeam?.name}</h1>
      
      {canCreate && (
        <Button onClick={openCreateModal}>
          Novo Cliente
        </Button>
      )}
      
      <ClientsList showActions={canManage} />
    </div>
  );
}
```

### Proteção de Rotas

```typescript
import { useHasAccess } from '@/contexts/team-context';
import { Navigate } from 'react-router-dom';

function ProtectedRoute({ children, resource }: { 
  children: React.ReactNode; 
  resource: string; 
}) {
  const hasAccess = useHasAccess(resource);

  if (!hasAccess) {
    return <Navigate to="/dashboard" replace />;
  }

  return <>{children}</>;
}
```

## 🎯 Próximos Passos

1. **Configure o TeamProvider** na sua aplicação
2. **Implemente verificações de permissão** nos componentes
3. **Customize os cargos** conforme sua necessidade
4. **Teste o sistema** com diferentes tipos de usuário
5. **Configure notificações** de team conforme necessário

Para mais detalhes sobre implementação, consulte:
- [permissions.md](./permissions.md) - Sistema de permissões detalhado
- [hooks.md](./hooks.md) - Hooks disponíveis
- [types.md](./types.md) - Definições de tipos
