import React, { Component, type ReactNode, type ErrorInfo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import { Skeleton } from './ui/skeleton';

interface DashboardErrorWrapperState {
  hasError: boolean;
  error: Error | null;
  retryCount: number;
}

interface DashboardErrorWrapperProps {
  children: ReactNode;
  fallbackTitle?: string;
  fallbackDescription?: string;
  showSkeleton?: boolean;
  maxRetries?: number;
}

/**
 * Error wrapper específico para componentes do dashboard
 * Fornece fallbacks mais elegantes e retry automático
 */
export class DashboardErrorWrapper extends Component<
  DashboardErrorWrapperProps,
  DashboardErrorWrapperState
> {
  private retryTimeout: NodeJS.Timeout | null = null;

  constructor(props: DashboardErrorWrapperProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): DashboardErrorWrapperState {
    return {
      hasError: true,
      error,
      retryCount: 0,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Dashboard component error:', error);
    console.error('Error info:', errorInfo);

    // Auto-retry para certos tipos de erro
    if (this.shouldAutoRetry(error) && this.state.retryCount < (this.props.maxRetries || 2)) {
      this.scheduleRetry();
    }
  }

  componentWillUnmount() {
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
    }
  }

  private shouldAutoRetry(error: Error): boolean {
    const retryableErrors = [
      'Failed to fetch',
      'Network request failed',
      'IndexedDB',
      'Transaction',
      'AbortError',
    ];

    return retryableErrors.some(errorType => 
      error.message.includes(errorType) || error.name.includes(errorType)
    );
  }

  private scheduleRetry = () => {
    const delay = Math.min(1000 * Math.pow(2, this.state.retryCount), 5000);
    
    this.retryTimeout = setTimeout(() => {
      this.setState(prevState => ({
        hasError: false,
        error: null,
        retryCount: prevState.retryCount + 1,
      }));
    }, delay);
  };

  private handleManualRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      retryCount: 0,
    });
  };

  render() {
    if (this.state.hasError) {
      // Mostrar skeleton durante retry automático
      if (this.retryTimeout && this.props.showSkeleton) {
        return <Skeleton className="h-32 w-full" />;
      }

      // Fallback de erro
      return (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-destructive">
              <AlertTriangle className="h-5 w-5" />
              {this.props.fallbackTitle || 'Erro no componente'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              {this.props.fallbackDescription || 
                'Ocorreu um erro ao carregar este componente. Tente novamente.'}
            </p>
            
            {this.state.error && (
              <details className="text-xs text-muted-foreground">
                <summary className="cursor-pointer hover:text-foreground">
                  Detalhes do erro
                </summary>
                <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto">
                  {this.state.error.message}
                </pre>
              </details>
            )}

            <div className="flex gap-2">
              <Button 
                onClick={this.handleManualRetry} 
                variant="outline" 
                size="sm"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Tentar novamente
              </Button>
            </div>

            {this.state.retryCount > 0 && (
              <p className="text-xs text-muted-foreground">
                Tentativas: {this.state.retryCount}/{this.props.maxRetries || 2}
              </p>
            )}
          </CardContent>
        </Card>
      );
    }

    return this.props.children;
  }
}

/**
 * Hook para usar o wrapper de erro em componentes funcionais
 */
export function useDashboardErrorHandler() {
  return (error: Error, errorInfo?: ErrorInfo) => {
    console.error('Dashboard error reported:', error);
    if (errorInfo) {
      console.error('Error info:', errorInfo);
    }
  };
}

/**
 * HOC para envolver componentes com o wrapper de erro
 */
export function withDashboardErrorWrapper<P extends object>(
  Component: React.ComponentType<P>,
  wrapperProps?: Omit<DashboardErrorWrapperProps, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <DashboardErrorWrapper {...wrapperProps}>
      <Component {...props} />
    </DashboardErrorWrapper>
  );

  WrappedComponent.displayName = `withDashboardErrorWrapper(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}
