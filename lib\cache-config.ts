/**
 * Sistema de Cache Simplificado e Unificado
 * Configuração dinâmica: local-first + IndexedDB
 * Integração transparente com React Query
 */

export interface SimpleCacheConfig {
  enabled: boolean;
  defaultTTL: number; // em milissegundos
  dbName: string;
  version: number;
}

/**
 * Gera nome dinâmico do banco baseado no project ID
 */
function getDynamicDBName(): string {
  const projectId = process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID;
  const customName = process.env.NEXT_PUBLIC_CACHE_DB_NAME;

  if (customName) {
    return customName;
  }

  if (projectId) {
    return `AppwriteDB_${projectId}`;
  }

  // Fallback para desenvolvimento
  return 'AppwriteLocalDB';
}

/**
 * Configuração dinâmica baseada no projeto
 */
const CACHE_CONFIG: SimpleCacheConfig = {
  enabled: process.env.NEXT_PUBLIC_CACHE_ENABLED !== 'false',
  defaultTTL: 5 * 60 * 1000, // 5 minutos
  dbName: getDynamicDBName(),
  version: 1, // Resetar versão para novo sistema dinâmico
};

/**
 * Verifica se o cache está habilitado
 */
export function isCacheEnabled(): boolean {
  return typeof window !== 'undefined' && CACHE_CONFIG.enabled;
}

/**
 * Obtém o TTL padrão configurado
 */
export function getDefaultTTL(): number {
  return CACHE_CONFIG.defaultTTL;
}

/**
 * Obtém a configuração do cache
 */
export function getCacheConfig(): SimpleCacheConfig {
  return CACHE_CONFIG;
}

/**
 * Auto-descoberta de coleções baseada nas variáveis de ambiente
 */
export function getCollectionStores(): string[] {
  const stores: string[] = [];

  // Coleções principais
  if (process.env.NEXT_PUBLIC_APPWRITE_CLIENTS_ID) stores.push('clients');
  if (process.env.NEXT_PUBLIC_APPWRITE_NOTIFICATIONS_ID) stores.push('notifications');
  if (process.env.NEXT_PUBLIC_APPWRITE_PUBLIC_PROFILES_ID) stores.push('public_profiles');
  if (process.env.NEXT_PUBLIC_APPWRITE_ACTIVITY_LOGS_ID) stores.push('activity_logs');

  // Chat
  if (process.env.NEXT_PUBLIC_APPWRITE_TEAM_CHATS_ID) stores.push('team_chats');
  if (process.env.NEXT_PUBLIC_APPWRITE_CHAT_MESSAGES_ID) stores.push('chat_messages');

  // Eventos
  if (process.env.NEXT_PUBLIC_APPWRITE_EVENTS_ID) stores.push('events');
  if (process.env.NEXT_PUBLIC_APPWRITE_EVENT_CATEGORIES_ID) stores.push('event_categories');

  // Kanban
  if (process.env.NEXT_PUBLIC_APPWRITE_KANBAN_BOARDS_ID) stores.push('kanban_boards');
  if (process.env.NEXT_PUBLIC_APPWRITE_KANBAN_COLUMNS_ID) stores.push('kanban_columns');
  if (process.env.NEXT_PUBLIC_APPWRITE_KANBAN_TASKS_ID) stores.push('kanban_tasks');

  // Stores obrigatórios
  stores.push('cache_metadata');

  return stores;
}

/**
 * Mapeia collection ID para nome do store
 */
export function getStoreNameFromCollectionId(collectionId: string): string | null {
  // Mapear IDs das collections para nomes dos stores
  if (collectionId === process.env.NEXT_PUBLIC_APPWRITE_CLIENTS_ID) return 'clients';
  if (collectionId === process.env.NEXT_PUBLIC_APPWRITE_NOTIFICATIONS_ID) return 'notifications';
  if (collectionId === process.env.NEXT_PUBLIC_APPWRITE_PUBLIC_PROFILES_ID) return 'public_profiles';
  if (collectionId === process.env.NEXT_PUBLIC_APPWRITE_ACTIVITY_LOGS_ID) return 'activity_logs';
  if (collectionId === process.env.NEXT_PUBLIC_APPWRITE_TEAM_CHATS_ID) return 'team_chats';
  if (collectionId === process.env.NEXT_PUBLIC_APPWRITE_CHAT_MESSAGES_ID) return 'chat_messages';
  if (collectionId === process.env.NEXT_PUBLIC_APPWRITE_EVENTS_ID) return 'events';
  if (collectionId === process.env.NEXT_PUBLIC_APPWRITE_EVENT_CATEGORIES_ID) return 'event_categories';
  if (collectionId === process.env.NEXT_PUBLIC_APPWRITE_KANBAN_BOARDS_ID) return 'kanban_boards';
  if (collectionId === process.env.NEXT_PUBLIC_APPWRITE_KANBAN_COLUMNS_ID) return 'kanban_columns';
  if (collectionId === process.env.NEXT_PUBLIC_APPWRITE_KANBAN_TASKS_ID) return 'kanban_tasks';

  return null;
}

/**
 * Configuração de índices para cada store
 */
export function getStoreIndexes(storeName: string): { name: string; keyPath: string; unique?: boolean }[] {
  const commonIndexes = [
    { name: 'createdAt', keyPath: '$createdAt' },
    { name: 'updatedAt', keyPath: '$updatedAt' },
  ];

  switch (storeName) {
    case 'clients':
      return [
        ...commonIndexes,
        { name: 'userId', keyPath: 'userId' },
        { name: 'teamId', keyPath: 'teamId' },
        { name: 'status', keyPath: 'status' },
        { name: 'email', keyPath: 'email', unique: false },
        { name: 'type', keyPath: 'type' },
        { name: 'isDeleted', keyPath: 'isDeleted' },
      ];

    case 'notifications':
      return [
        ...commonIndexes,
        { name: 'userId', keyPath: 'userId' },
        { name: 'read', keyPath: 'read' },
        { name: 'type', keyPath: 'type' },
        { name: 'isDeleted', keyPath: 'isDeleted' },
      ];

    case 'team_chats':
      return [
        ...commonIndexes,
        { name: 'teamId', keyPath: 'teamId' },
        { name: 'isActive', keyPath: 'isActive' },
        { name: 'isPrivate', keyPath: 'isPrivate' },
        { name: 'isDeleted', keyPath: 'isDeleted' },
      ];

    case 'chat_messages':
      return [
        ...commonIndexes,
        { name: 'chatId', keyPath: 'chatId' },
        { name: 'teamId', keyPath: 'teamId' },
        { name: 'userId', keyPath: 'userId' },
        { name: 'type', keyPath: 'type' },
        { name: 'isDeleted', keyPath: 'isDeleted' },
      ];

    case 'activity_logs':
      return [
        ...commonIndexes,
        { name: 'userId', keyPath: 'userId' },
        { name: 'teamId', keyPath: 'teamId' },
        { name: 'type', keyPath: 'type' },
        { name: 'action', keyPath: 'action' },
        { name: 'resource', keyPath: 'resource' },
        { name: 'priority', keyPath: 'priority' },
        { name: 'isDeleted', keyPath: 'isDeleted' },
      ];

    case 'events':
      return [
        ...commonIndexes,
        { name: 'userId', keyPath: 'userId' },
        { name: 'teamId', keyPath: 'teamId' },
        { name: 'startDate', keyPath: 'startDate' },
        { name: 'endDate', keyPath: 'endDate' },
        { name: 'categoryId', keyPath: 'categoryId' },
        { name: 'isDeleted', keyPath: 'isDeleted' },
      ];

    case 'kanban_boards':
    case 'kanban_columns':
    case 'kanban_tasks':
      return [
        ...commonIndexes,
        { name: 'userId', keyPath: 'userId' },
        { name: 'teamId', keyPath: 'teamId' },
        { name: 'isDeleted', keyPath: 'isDeleted' },
      ];

    default:
      return commonIndexes;
  }
}

/**
 * Log de configuração do cache (apenas em desenvolvimento)
 */
export function logCacheConfig(): void {
  if (process.env.NODE_ENV === "development") {
    import('@/lib/logger').then(({ log }) => {
      log.cache('Dynamic Cache Configuration', {
        enabled: CACHE_CONFIG.enabled,
        strategy: 'local-first',
        storage: 'IndexedDB',
        defaultTTL: `${CACHE_CONFIG.defaultTTL}ms`,
        dbName: CACHE_CONFIG.dbName,
        projectId: process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID,
        discoveredStores: getCollectionStores(),
      });
    });
  }
}
