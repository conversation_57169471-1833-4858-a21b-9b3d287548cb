import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { Crown, Shield, User, Mail } from 'lucide-react';
import { useUpdateMemberRoles } from '../../hooks/use-api';
import type { TeamMembership, UpdateMembershipData } from '@/schemas/teams';

const editMemberSchema = z.object({
  role: z.enum(['owner', 'admin', 'member']),
});

type EditMemberFormData = z.infer<typeof editMemberSchema>;

interface TeamMemberEditModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  teamId: string;
  member: TeamMembership | null;
}

const getRoleIcon = (role: string) => {
  switch (role) {
    case 'owner':
      return <Crown className="h-4 w-4 text-yellow-500" />;
    case 'admin':
      return <Shield className="h-4 w-4 text-blue-500" />;
    default:
      return <User className="h-4 w-4 text-gray-500" />;
  }
};

const getRoleDescription = (role: string) => {
  switch (role) {
    case 'owner':
      return 'Controle total do time, incluindo exclusão';
    case 'admin':
      return 'Pode gerenciar membros e configurações';
    case 'member':
      return 'Pode visualizar e participar do time';
    default:
      return '';
  }
};

export function TeamMemberEditModal({
  open,
  onOpenChange,
  teamId,
  member
}: TeamMemberEditModalProps) {
  const updateMemberMutation = useUpdateMemberRoles();

  const currentRole = member?.roles?.[0] || 'member';

  const form = useForm<EditMemberFormData>({
    resolver: zodResolver(editMemberSchema),
    defaultValues: {
      role: currentRole as 'owner' | 'admin' | 'member',
    },
  });

  // Reset form when member changes
  React.useEffect(() => {
    if (member) {
      const role = member.roles?.[0] || 'member';
      form.reset({
        role: role as 'owner' | 'admin' | 'member',
      });
    }
  }, [member, form]);

  const onSubmit = async (data: EditMemberFormData) => {
    if (!member || !teamId) return;

    try {
      const updateData: UpdateMembershipData = {
        roles: [data.role],
      };

      await updateMemberMutation.mutateAsync({
        teamId,
        membershipId: member.$id,
        roles: [data.role],
      });

      onOpenChange(false);
    } catch (error) {
      console.error('Error updating member:', error);
    }
  };

  const isLoading = form.formState.isSubmitting || updateMemberMutation.isPending;

  if (!member) return null;

  const initials = member.userName
    ? member.userName.split(' ').map(n => n[0]).join('').toUpperCase()
    : member.userEmail.substring(0, 2).toUpperCase();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Alterar Função do Membro</DialogTitle>
          <DialogDescription>
            Altere a função e permissões do membro no time.
          </DialogDescription>
        </DialogHeader>

        {/* Member Info */}
        <div className="flex items-center space-x-3 p-4 bg-muted/50 rounded-lg">
          <Avatar className="h-10 w-10">
            <AvatarImage src={undefined} />
            <AvatarFallback className="text-sm">
              {initials}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <div className="font-medium">{member.userName || 'Usuário'}</div>
            <div className="text-sm text-muted-foreground flex items-center gap-1">
              <Mail className="h-3 w-3" />
              {member.userEmail}
            </div>
          </div>
          <Badge variant="outline">
            {currentRole === 'owner' ? 'Proprietário' :
             currentRole === 'admin' ? 'Administrador' : 'Membro'}
          </Badge>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Role Selection */}
            <FormField
              control={form.control}
              name="role"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nova Função</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione uma função" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="member">
                        <div className="flex items-center gap-3 py-2">
                          {getRoleIcon('member')}
                          <div>
                            <div className="font-medium">Membro</div>
                            <div className="text-xs text-muted-foreground">
                              {getRoleDescription('member')}
                            </div>
                          </div>
                        </div>
                      </SelectItem>
                      <SelectItem value="admin">
                        <div className="flex items-center gap-3 py-2">
                          {getRoleIcon('admin')}
                          <div>
                            <div className="font-medium">Administrador</div>
                            <div className="text-xs text-muted-foreground">
                              {getRoleDescription('admin')}
                            </div>
                          </div>
                        </div>
                      </SelectItem>
                      <SelectItem value="owner">
                        <div className="flex items-center gap-3 py-2">
                          {getRoleIcon('owner')}
                          <div>
                            <div className="font-medium">Proprietário</div>
                            <div className="text-xs text-muted-foreground">
                              {getRoleDescription('owner')}
                            </div>
                          </div>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Warning for owner transfer */}
            {form.watch('role') === 'owner' && currentRole !== 'owner' && (
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center gap-2 text-yellow-800">
                  <Crown className="h-4 w-4" />
                  <span className="font-medium text-sm">Atenção!</span>
                </div>
                <p className="text-xs text-yellow-700 mt-1">
                  Ao tornar este membro proprietário, você perderá o controle total do time.
                  Esta ação não pode ser desfeita por você.
                </p>
              </div>
            )}

            {/* Actions */}
            <div className="flex justify-end gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancelar
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Salvando...' : 'Salvar Alterações'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
