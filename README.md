# 📚 Template Next.js + Appwrite

Um template moderno e completo para desenvolvimento de aplicações web usando Next.js 15 e Appwrite.

## 🎯 O que é este Template?

Um template moderno e completo para desenvolvimento de aplicações web usando:

- **Frontend**: Next.js 15 + TypeScript + shadcn/ui
- **Backend**: Appwrite (SPA mode)
- **Cache**: IndexedDB com TanStack Query
- **Arquitetura**: Local-first com sincronização automática

## 🚀 Como Começar

### 1. Configuração Inicial
```bash
# Clonar e instalar
git clone <seu-repo>
cd template-nextjs-appwrite
yarn install
```

### 2. Configurar Ambiente
```bash
# Copiar variáveis de ambiente
cp .env.example .env
# Editar .env com suas configurações do Appwrite
```

### 3. Setup Automático
```bash
# Configurar banco de dados automaticamente
yarn setup
```

### 4. Executar
```bash
# Iniciar desenvolvimento
yarn dev
```

Abra [http://localhost:3000](http://localhost:3000) no seu navegador para ver o resultado.

## 🔧 Scripts Disponíveis

```bash
# Desenvolvimento
yarn dev              # Servidor de desenvolvimento
yarn build            # Build para produção
yarn start            # Servidor de produção
yarn lint             # Linting do código

# Configuração
yarn setup            # Setup completo do banco
yarn setup:collections # Apenas coleções
yarn setup:attributes  # Apenas atributos

# Qualidade
yarn check            # TypeScript check
yarn typecheck        # Verificação completa
```

## 📖 Documentação Completa

Para documentação detalhada, consulte:

- **[docs/guide.md](./docs/guide.md)** - Guia completo do template
- **[docs/authentication.md](./docs/authentication.md)** - Sistema de autenticação
- **[docs/database.md](./docs/database.md)** - Banco de dados e API
- **[docs/components.md](./docs/components.md)** - Componentes UI

## 🏗️ Estrutura do Projeto

```
template-nextjs-appwrite/
├── app/                    # Código principal (App Router)
│   ├── components/         # Componentes reutilizáveis
│   ├── hooks/             # React hooks customizados
│   ├── lib/               # Bibliotecas e utilitários
│   │   └── appwrite/      # Configurações Appwrite
│   ├── (auth)/            # Rotas de autenticação
│   ├── (dashboard)/       # Rotas protegidas
│   └── types/             # Definições TypeScript
├── docs/                  # Documentação completa
├── scripts/               # Scripts de automação
└── public/                # Arquivos estáticos
```

## 🔧 Funcionalidades Principais

- ✅ **Autenticação completa** com Appwrite
- ✅ **Sistema de equipes** e permissões
- ✅ **Chat em tempo real** com IA
- ✅ **Kanban boards** otimizados
- ✅ **Calendário** e eventos
- ✅ **Relatórios** e analytics
- ✅ **PWA** com suporte offline
- ✅ **Temas** dark/light automáticos

## 🚀 Deploy

O deploy mais fácil é usar a [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme).

Consulte a [documentação de deploy do Next.js](https://nextjs.org/docs/app/building-your-application/deploying) para mais detalhes.
