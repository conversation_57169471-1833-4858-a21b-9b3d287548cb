'use client';

/**
 * Calendar Page
 * Página principal do sistema de calendário
 */

import { useState, useEffect } from 'react';
import { EventCalendar } from '@/components/calendar/EventCalendar';
import { useAuth } from '@/hooks/use-auth';
import { useTeams } from '@/hooks/use-api';
import {
  useEvents,
  useCreateEvent,
  useUpdateEvent,
  useDeleteEvent
} from '@/hooks/api/use-events';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CalendarIcon, Users, Plus } from 'lucide-react';

export default function CalendarPage() {
  const { user } = useAuth();
  const { data: teams = [], isLoading: teamsLoading } = useTeams();
  const [selectedTeamId, setSelectedTeamId] = useState<string>('');

  // Selecionar primeiro time por padrão
  useEffect(() => {
    if (teams.length > 0 && !selectedTeamId) {
      setSelectedTeamId(teams[0].$id);
    }
  }, [teams, selectedTeamId]);

  const {
    data: events = [],
    isLoading: eventsLoading,
    error: eventsError
  } = useEvents(selectedTeamId);

  const createEventMutation = useCreateEvent();
  const updateEventMutation = useUpdateEvent();
  const deleteEventMutation = useDeleteEvent();

  const handleCreateEvent = async (eventData: any) => {
    try {
      await createEventMutation.mutateAsync({
        ...eventData,
        teamId: selectedTeamId,
        createdBy: user?.$id
      });
    } catch (error) {
      console.error('Erro ao criar evento:', error);
      throw error;
    }
  };

  const handleUpdateEvent = async (eventId: string, eventData: any) => {
    try {
      await updateEventMutation.mutateAsync({
        eventId,
        data: eventData
      });
    } catch (error) {
      console.error('Erro ao atualizar evento:', error);
      throw error;
    }
  };

  // Wrapper para adaptar a assinatura do EventCalendar
  const handleEventUpdate = async (event: any) => {
    await handleUpdateEvent(event.$id, event);
  };

  const handleDeleteEvent = async (eventId: string) => {
    try {
      await deleteEventMutation.mutateAsync(eventId);
    } catch (error) {
      console.error('Erro ao deletar evento:', error);
      throw error;
    }
  };

  if (teamsLoading) {
    return (
      <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
        <div className="px-4 lg:px-6">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-muted rounded w-1/3"></div>
            <div className="h-4 bg-muted rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  if (teams.length === 0) {
    return (
      <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
        <div className="px-4 lg:px-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Nenhum time encontrado
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                Você precisa fazer parte de um time para acessar o calendário.
              </p>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Criar Time
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
      {/* Header */}
      <div className="px-4 lg:px-6">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div className="space-y-1">
            <h1 className="text-2xl font-bold tracking-tight flex items-center gap-2">
              <CalendarIcon className="h-6 w-6" />
              Calendário
            </h1>
            <p className="text-muted-foreground">
              Gerencie eventos, reuniões e compromissos da sua equipe.
            </p>
          </div>

          {/* Team Selector */}
          {teams.length > 1 && (
            <div className="flex items-center gap-2">
              <label htmlFor="team-select" className="text-sm font-medium">
                Time:
              </label>
              <select
                id="team-select"
                value={selectedTeamId}
                onChange={(e) => setSelectedTeamId(e.target.value)}
                className="px-3 py-1 border rounded-md text-sm"
              >
                {teams.map((team) => (
                  <option key={team.$id} value={team.$id}>
                    {team.name}
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>
      </div>

      {/* Calendar */}
      <div className="px-4 lg:px-6">
        <Card>
          <CardContent className="p-0">
            <EventCalendar
              events={events}
              onEventAdd={handleCreateEvent}
              onEventUpdate={handleEventUpdate}
              onEventDelete={handleDeleteEvent}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
