/**
 * Activity Detail Modal Component
 * Shows detailed information about a specific activity
 */

import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import {
  IconUser,
  IconUsers,
  IconMessage,
  IconFile,
  IconSettings,
  IconBell,
  IconCalendar,
  IconFolder,
  IconShield,
  IconSettings2,
  IconCopy,
  IconExternalLink,
} from '@tabler/icons-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { Separator } from '../ui/separator';
import { toast } from 'sonner';
import type { Activity, ActivityType } from '@/schemas/activities';

// ============================================================================
// CONFIGURATION
// ============================================================================

const ACTIVITY_TYPE_CONFIG: Record<ActivityType, { icon: React.ComponentType<any>; color: string; bgColor: string; label: string }> = {
  auth: { icon: IconUser, color: 'text-blue-600', bgColor: 'bg-blue-100', label: 'Autenticação' },
  client: { icon: IconUser, color: 'text-green-600', bgColor: 'bg-green-100', label: 'Cliente' },
  team: { icon: IconUsers, color: 'text-purple-600', bgColor: 'bg-purple-100', label: 'Equipe' },
  chat: { icon: IconMessage, color: 'text-orange-600', bgColor: 'bg-orange-100', label: 'Chat' },
  file: { icon: IconFile, color: 'text-indigo-600', bgColor: 'bg-indigo-100', label: 'Arquivo' },
  system: { icon: IconSettings2, color: 'text-gray-600', bgColor: 'bg-gray-100', label: 'Sistema' },
  admin: { icon: IconShield, color: 'text-red-600', bgColor: 'bg-red-100', label: 'Admin' },
  notification: { icon: IconBell, color: 'text-yellow-600', bgColor: 'bg-yellow-100', label: 'Notificação' },
  preference: { icon: IconSettings, color: 'text-slate-600', bgColor: 'bg-slate-100', label: 'Preferência' },
  calendar: { icon: IconCalendar, color: 'text-cyan-600', bgColor: 'bg-cyan-100', label: 'Calendário' },
  document: { icon: IconFolder, color: 'text-emerald-600', bgColor: 'bg-emerald-100', label: 'Documento' },
};

const PRIORITY_CONFIG = {
  low: { color: 'text-gray-500', bgColor: 'bg-gray-100', label: 'Baixa' },
  normal: { color: 'text-blue-500', bgColor: 'bg-blue-100', label: 'Normal' },
  high: { color: 'text-orange-500', bgColor: 'bg-orange-100', label: 'Alta' },
  critical: { color: 'text-red-500', bgColor: 'bg-red-100', label: 'Crítica' },
};

// ============================================================================
// COMPONENTS
// ============================================================================

interface ActivityDetailModalProps {
  activity: Activity | null;
  isOpen: boolean;
  onClose: () => void;
}

export function ActivityDetailModal({ activity, isOpen, onClose }: ActivityDetailModalProps) {
  if (!activity) return null;

  const typeConfig = ACTIVITY_TYPE_CONFIG[activity.type];
  const priorityConfig = PRIORITY_CONFIG[activity.priority];
  const TypeIcon = typeConfig.icon;

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`${label} copiado para a área de transferência`);
  };

  const formatJsonValue = (value: any): string => {
    if (typeof value === 'object' && value !== null) {
      return JSON.stringify(value, null, 2);
    }
    return String(value);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-3">
            <div className={`p-2 rounded-full ${typeConfig.bgColor}`}>
              <TypeIcon className={`h-5 w-5 ${typeConfig.color}`} />
            </div>
            <div>
              <div className="flex items-center space-x-2">
                <span>{activity.title}</span>
                <Badge variant="secondary">{typeConfig.label}</Badge>
                {activity.priority !== 'normal' && (
                  <Badge
                    variant="outline"
                    className={`${priorityConfig.color} border-current`}
                  >
                    {priorityConfig.label}
                  </Badge>
                )}
              </div>
              <p className="text-sm text-gray-500 font-normal mt-1">
                {format(new Date(activity.$createdAt), 'PPpp', { locale: ptBR })}
              </p>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Informações Básicas</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-xs font-medium text-gray-500">Tipo</label>
                  <p className="text-sm">{typeConfig.label}</p>
                </div>
                <div>
                  <label className="text-xs font-medium text-gray-500">Ação</label>
                  <p className="text-sm capitalize">{activity.action}</p>
                </div>
                <div>
                  <label className="text-xs font-medium text-gray-500">Recurso</label>
                  <p className="text-sm">{activity.resource}</p>
                </div>
                <div>
                  <label className="text-xs font-medium text-gray-500">Prioridade</label>
                  <p className="text-sm">{priorityConfig.label}</p>
                </div>
              </div>

              {activity.description && (
                <div>
                  <label className="text-xs font-medium text-gray-500">Descrição</label>
                  <p className="text-sm mt-1">{activity.description}</p>
                </div>
              )}

              {activity.resourceId && (
                <div>
                  <label className="text-xs font-medium text-gray-500">ID do Recurso</label>
                  <div className="flex items-center space-x-2 mt-1">
                    <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                      {activity.resourceId}
                    </code>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(activity.resourceId!, 'ID do recurso')}
                    >
                      <IconCopy className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* User Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Informações do Usuário</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center space-x-3">
                <Avatar className="h-8 w-8">
                  <AvatarFallback className="text-xs">
                    {activity.userId.slice(0, 2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <div className="flex items-center space-x-2">
                    <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                      {activity.userId}
                    </code>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(activity.userId, 'ID do usuário')}
                    >
                      <IconCopy className="h-3 w-3" />
                    </Button>
                  </div>
                  <p className="text-xs text-gray-500">ID do Usuário</p>
                </div>
              </div>

              {activity.teamId && (
                <div>
                  <label className="text-xs font-medium text-gray-500">Team ID</label>
                  <div className="flex items-center space-x-2 mt-1">
                    <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                      {activity.teamId}
                    </code>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(activity.teamId!, 'ID do team')}
                    >
                      <IconCopy className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Technical Details */}
          {(activity.ipAddress || activity.userAgent) && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Detalhes Técnicos</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {activity.ipAddress && (
                  <div>
                    <label className="text-xs font-medium text-gray-500">Endereço IP</label>
                    <div className="flex items-center space-x-2 mt-1">
                      <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                        {activity.ipAddress}
                      </code>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(activity.ipAddress!, 'Endereço IP')}
                      >
                        <IconCopy className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                )}

                {activity.userAgent && (
                  <div>
                    <label className="text-xs font-medium text-gray-500">User Agent</label>
                    <div className="flex items-start space-x-2 mt-1">
                      <code className="text-xs bg-gray-100 px-2 py-1 rounded flex-1 break-all">
                        {activity.userAgent}
                      </code>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(activity.userAgent!, 'User Agent')}
                      >
                        <IconCopy className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Tags */}
          {activity.tags && activity.tags.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Tags</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {activity.tags.map((tag, index) => (
                    <Badge key={index} variant="outline">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Details */}
          {activity.details && Object.keys(activity.details).length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Detalhes Adicionais</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {Object.entries(activity.details).map(([key, value]) => (
                    <div key={key}>
                      <label className="text-xs font-medium text-gray-500 capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </label>
                      <pre className="text-xs bg-gray-50 p-2 rounded mt-1 overflow-x-auto">
                        {formatJsonValue(value)}
                      </pre>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Metadata */}
          {activity.metadata && Object.keys(activity.metadata).length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Metadados</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {Object.entries(activity.metadata).map(([key, value]) => (
                    <div key={key}>
                      <label className="text-xs font-medium text-gray-500 capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </label>
                      <pre className="text-xs bg-gray-50 p-2 rounded mt-1 overflow-x-auto">
                        {formatJsonValue(value)}
                      </pre>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* System Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Informações do Sistema</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-xs font-medium text-gray-500">ID da Atividade</label>
                  <div className="flex items-center space-x-2 mt-1">
                    <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                      {activity.$id}
                    </code>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(activity.$id, 'ID da atividade')}
                    >
                      <IconCopy className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
                <div>
                  <label className="text-xs font-medium text-gray-500">Criado em</label>
                  <p className="text-xs mt-1">
                    {format(new Date(activity.$createdAt), 'PPpp', { locale: ptBR })}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
}
