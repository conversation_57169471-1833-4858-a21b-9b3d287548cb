# 🎨 Componentes UI

Este documento explica a arquitetura de componentes do template, baseada em shadcn/ui e organizados para máxima reutilização.

## 📋 Visão Geral

O sistema de componentes é construído sobre:

- **shadcn/ui** - Componentes base acessíveis
- **Tailwind CSS** - Estilização utilitária
- **TypeScript** - Type safety completo
- **Radix UI** - Primitivos acessíveis
- **Class Variance Authority** - Variantes tipadas

## 🏗️ Arquitetura

### Estrutura de Componentes (Next.js App Router)
```
app/
├── components/             # Componentes reutilizáveis
│   ├── ui/                 # Componentes shadcn/ui base
│   │   ├── button.tsx      # Botões com variantes
│   │   ├── input.tsx       # Inputs de formulário
│   │   ├── card.tsx        # Cards e containers
│   │   ├── dialog.tsx      # Modais e dialogs
│   │   └── ...             # Outros componentes base
│   ├── chat/               # Componentes de chat
│   │   ├── team-chat.tsx   # Chat de equipe
│   │   ├── chat-bubble.tsx # Bolhas de mensagem
│   │   ├── chat-input.tsx  # Input de mensagem
│   │   └── gemini-chatbox.tsx # Chat com IA
│   ├── forms/              # Componentes de formulário
│   ├── dashboard/          # Componentes do dashboard
│   ├── examples/           # Componentes de exemplo
│   └── route-guard.tsx     # Proteção de rotas
├── (auth)/                 # Rotas de autenticação
├── (dashboard)/            # Rotas protegidas
└── globals.css             # Estilos globais
└── ...                     # Outras categorias
```

### Hierarquia de Componentes
```
┌─────────────────┐
│   Page Level    │ ← Páginas completas
├─────────────────┤
│   Layout        │ ← Layouts e templates
├─────────────────┤
│   Feature       │ ← Componentes de funcionalidade
├─────────────────┤
│   UI Components │ ← Componentes reutilizáveis
├─────────────────┤
│   shadcn/ui     │ ← Componentes base
└─────────────────┘
```

## 🧩 Componentes Base (shadcn/ui)

### Button
Componente de botão com múltiplas variantes:

```typescript
import { Button } from '@/components/ui/button';

function ButtonExamples() {
  return (
    <div className="space-x-2">
      <Button variant="default">Default</Button>
      <Button variant="destructive">Destructive</Button>
      <Button variant="outline">Outline</Button>
      <Button variant="secondary">Secondary</Button>
      <Button variant="ghost">Ghost</Button>
      <Button variant="link">Link</Button>

      {/* Tamanhos */}
      <Button size="sm">Small</Button>
      <Button size="default">Default</Button>
      <Button size="lg">Large</Button>
      <Button size="icon">
        <Icon className="h-4 w-4" />
      </Button>
    </div>
  );
}
```

### Input
Componente de input com estilos consistentes:

```typescript
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

function InputExample() {
  return (
    <div className="space-y-2">
      <Label htmlFor="email">Email</Label>
      <Input
        id="email"
        type="email"
        placeholder="<EMAIL>"
        className="w-full"
      />
    </div>
  );
}
```

### Card
Componente de card para containers:

```typescript
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

function CardExample() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Título do Card</CardTitle>
        <CardDescription>
          Descrição do conteúdo do card
        </CardDescription>
      </CardHeader>
      <CardContent>
        <p>Conteúdo principal do card</p>
      </CardContent>
      <CardFooter>
        <Button>Ação</Button>
      </CardFooter>
    </Card>
  );
}
```

### Dialog
Componente de modal/dialog:

```typescript
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

function DialogExample() {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button>Abrir Modal</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Título do Modal</DialogTitle>
          <DialogDescription>
            Descrição do modal
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          {/* Conteúdo do modal */}
        </div>
        <DialogFooter>
          <Button variant="outline">Cancelar</Button>
          <Button>Confirmar</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
```

## 💬 Componentes de Chat

### TeamChat
Componente principal de chat de equipe:

```typescript
import { TeamChat } from '@/components/chat/team-chat';

function ChatPage() {
  return (
    <div className="h-screen">
      <TeamChat
        teamId="team-123"
        chatId="chat-456"
        className="h-full"
      />
    </div>
  );
}
```

### ChatBubble
Componente para bolhas de mensagem:

```typescript
import {
  ChatBubble,
  ChatBubbleAvatar,
  ChatBubbleMessage,
  ChatBubbleTimestamp,
} from '@/components/chat/chat-bubble';

function MessageList({ messages }: { messages: ChatMessage[] }) {
  return (
    <div className="space-y-4">
      {messages.map(message => (
        <ChatBubble key={message.$id} variant="received">
          <ChatBubbleAvatar
            src={message.userAvatar}
            fallback={message.userName[0]}
          />
          <ChatBubbleMessage>
            {message.content}
          </ChatBubbleMessage>
          <ChatBubbleTimestamp>
            {formatDistanceToNow(new Date(message.$createdAt))}
          </ChatBubbleTimestamp>
        </ChatBubble>
      ))}
    </div>
  );
}
```

### GeminiChatbox
Componente de chat com IA:

```typescript
import { GeminiChatbox } from '@/components/chat/gemini-chatbox';

function AIAssistant() {
  return (
    <GeminiChatbox
      position="bottom-right"
      size="large"
      icon={<Bot className="h-5 w-5" />}
      title="Assistente IA"
      placeholder="Pergunte algo sobre o projeto..."
    />
  );
}
```

## 📝 Componentes de Formulário

### Form (React Hook Form + Zod)
Componentes de formulário integrados:

```typescript
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

const formSchema = z.object({
  name: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres'),
  email: z.string().email('Email inválido'),
});

function MyForm() {
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      email: '',
    },
  });

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    console.log(values);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Nome</FormLabel>
              <FormControl>
                <Input placeholder="Seu nome" {...field} />
              </FormControl>
              <FormDescription>
                Este será seu nome de exibição
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input type="email" placeholder="<EMAIL>" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit">Enviar</Button>
      </form>
    </Form>
  );
}
```

## 📊 Componentes de Dashboard

### SectionCards
Cards de métricas do dashboard:

```typescript
import { SectionCards } from '@/components/section-cards';

function Dashboard() {
  return (
    <div className="space-y-6">
      <SectionCards />
      {/* Outros componentes do dashboard */}
    </div>
  );
}
```

### DataTable
Tabela de dados com paginação e filtros:

```typescript
import { DataTable } from '@/components/data-table';
import { columns } from './columns';

function ClientsTable() {
  const { data: clients, isLoading } = useClients();

  if (isLoading) return <Skeleton />;

  return (
    <DataTable
      columns={columns}
      data={clients || []}
      searchKey="name"
      searchPlaceholder="Buscar clientes..."
    />
  );
}
```

## 🎨 Sistema de Temas

### ThemeProvider
Provedor de tema dark/light:

```typescript
import { ThemeProvider } from '@/components/theme-provider';

function App() {
  return (
    <ThemeProvider defaultTheme="system" storageKey="app-theme">
      <Router />
    </ThemeProvider>
  );
}
```

### useTheme Hook
Hook para controle de tema:

```typescript
import { useTheme } from '@/hooks/use-theme';

function ThemeToggle() {
  const { theme, setTheme } = useTheme();

  return (
    <Button
      variant="outline"
      size="icon"
      onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
    >
      {theme === 'light' ? (
        <Moon className="h-4 w-4" />
      ) : (
        <Sun className="h-4 w-4" />
      )}
    </Button>
  );
}
```

## 🔧 Componentes Utilitários

### Sidebar
Sistema de sidebar responsivo:

```typescript
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarTrigger,
} from '@/components/ui/sidebar';

function AppSidebar() {
  return (
    <Sidebar>
      <SidebarHeader>
        <h2>Menu</h2>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton asChild>
                <Link to="/dashboard">
                  <Home className="h-4 w-4" />
                  Dashboard
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        <UserMenu />
      </SidebarFooter>
    </Sidebar>
  );
}
```

### Command
Componente de comando/busca:

```typescript
import {
  Command,
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';

function CommandMenu() {
  const [open, setOpen] = useState(false);

  return (
    <CommandDialog open={open} onOpenChange={setOpen}>
      <CommandInput placeholder="Digite um comando ou busque..." />
      <CommandList>
        <CommandEmpty>Nenhum resultado encontrado.</CommandEmpty>
        <CommandGroup heading="Sugestões">
          <CommandItem>
            <Calendar className="mr-2 h-4 w-4" />
            <span>Calendário</span>
          </CommandItem>
          <CommandItem>
            <Users className="mr-2 h-4 w-4" />
            <span>Equipes</span>
          </CommandItem>
        </CommandGroup>
      </CommandList>
    </CommandDialog>
  );
}
```

## 📱 Responsividade

### Breakpoints
Sistema de breakpoints do Tailwind:

```typescript
// Componente responsivo
function ResponsiveCard() {
  return (
    <Card className="w-full sm:w-1/2 lg:w-1/3 xl:w-1/4">
      <CardContent className="p-4 sm:p-6">
        <h3 className="text-lg sm:text-xl font-semibold">
          Título Responsivo
        </h3>
        <p className="text-sm sm:text-base text-muted-foreground">
          Conteúdo que se adapta ao tamanho da tela
        </p>
      </CardContent>
    </Card>
  );
}
```

### Mobile-First
Abordagem mobile-first:

```typescript
function MobileFirstComponent() {
  return (
    <div className="
      flex flex-col gap-2
      sm:flex-row sm:gap-4
      lg:gap-6
      xl:gap-8
    ">
      <div className="
        w-full
        sm:w-1/2
        lg:w-1/3
      ">
        Conteúdo principal
      </div>
      <div className="
        w-full
        sm:w-1/2
        lg:w-2/3
      ">
        Conteúdo secundário
      </div>
    </div>
  );
}
```

## ♿ Acessibilidade

### ARIA Labels
Componentes com acessibilidade:

```typescript
function AccessibleButton() {
  return (
    <Button
      aria-label="Fechar modal"
      aria-describedby="close-description"
    >
      <X className="h-4 w-4" />
      <span id="close-description" className="sr-only">
        Clique para fechar o modal
      </span>
    </Button>
  );
}
```

### Navegação por Teclado
Suporte a navegação por teclado:

```typescript
function KeyboardNavigation() {
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleClick();
    }
  };

  return (
    <div
      role="button"
      tabIndex={0}
      onKeyDown={handleKeyDown}
      onClick={handleClick}
      className="cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary"
    >
      Elemento clicável
    </div>
  );
}
```

## ✅ Boas Práticas

### 1. **Composição sobre Herança**
```typescript
// ✅ Bom - composição
function ActionCard({ children, actions }: {
  children: React.ReactNode;
  actions: React.ReactNode;
}) {
  return (
    <Card>
      <CardContent>{children}</CardContent>
      <CardFooter>{actions}</CardFooter>
    </Card>
  );
}

// ❌ Ruim - herança complexa
class ExtendedCard extends Card {
  // Lógica complexa
}
```

### 2. **Props Tipadas**
```typescript
// ✅ Bom - props bem tipadas
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline';
  size?: 'sm' | 'default' | 'lg';
  isLoading?: boolean;
}

// ❌ Ruim - props genéricas
interface ButtonProps {
  [key: string]: any;
}
```

### 3. **Forwarding Refs**
```typescript
// ✅ Bom - ref forwarding
const Input = React.forwardRef<
  HTMLInputElement,
  React.InputHTMLAttributes<HTMLInputElement>
>(({ className, ...props }, ref) => {
  return (
    <input
      ref={ref}
      className={cn("base-input-styles", className)}
      {...props}
    />
  );
});
```

---

**📖 Próximos Passos:**
- [types.md](./types.md) - Tipos para componentes
- [hooks.md](./hooks.md) - Hooks usados em componentes
- [authentication.md](./authentication.md) - Componentes de auth
