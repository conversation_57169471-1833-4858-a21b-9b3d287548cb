"use client"

import { TrendingUp } from "lucide-react"
import { <PERSON>, <PERSON><PERSON>, CartesianGrid, LabelList, XAxis, YA<PERSON>s } from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"
import { useActivities } from "@/hooks/api/use-activities"

const chartConfig = {
  activities: {
    label: "Atividades",
    color: "hsl(var(--chart-2))",
  },
  label: {
    color: "hsl(var(--background))",
  },
} satisfies ChartConfig

export function ActivityLogsChart() {
  const { data: activities } = useActivities({
    filters: {},
    orderBy: 'createdAt',
    orderDirection: 'desc',
  })

  // Processar dados das atividades por mês
  const hasRealData = activities && activities.length > 0
  let chartData: Array<{ month: string; activities: number }> = []
  let totalActivities = 0
  let growthPercentage = "0"

  if (hasRealData) {
    const currentDate = new Date()
    const monthsData = []

    for (let i = 5; i >= 0; i--) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1)
      const monthName = date.toLocaleDateString('pt-BR', { month: 'short' })

      const monthActivities = activities.filter(activity => {
        const activityDate = new Date(activity.$createdAt)
        return activityDate.getFullYear() === date.getFullYear() &&
               activityDate.getMonth() === date.getMonth()
      }).length

      monthsData.push({
        month: monthName,
        activities: monthActivities
      })
    }

    chartData = monthsData
    totalActivities = activities.length

    const currentMonthActivities = monthsData[monthsData.length - 1]?.activities || 0
    const previousMonthActivities = monthsData[monthsData.length - 2]?.activities || 0

    growthPercentage = previousMonthActivities > 0
      ? ((currentMonthActivities - previousMonthActivities) / previousMonthActivities * 100).toFixed(1)
      : '0'
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Logs de Atividade</CardTitle>
        <CardDescription>Atividades registradas nos últimos 6 meses</CardDescription>
      </CardHeader>
      <CardContent>
        {hasRealData ? (
          <ChartContainer config={chartConfig}>
            <BarChart
              accessibilityLayer
              data={chartData}
              layout="vertical"
              margin={{
                right: 16,
              }}
            >
              <CartesianGrid horizontal={false} />
              <YAxis
                dataKey="month"
                type="category"
                tickLine={false}
                tickMargin={10}
                axisLine={false}
                tickFormatter={(value) => String(value).slice(0, 3)}
                hide
              />
              <XAxis dataKey="activities" type="number" hide />
              <ChartTooltip
                cursor={false}
                content={<ChartTooltipContent indicator="line" />}
              />
              <Bar
                dataKey="activities"
                fill="var(--color-activities)"
                radius={4}
              >
                <LabelList
                  dataKey="month"
                  position="insideLeft"
                  offset={8}
                  className="fill-[--color-label]"
                  fontSize={12}
                />
                <LabelList
                  dataKey="activities"
                  position="right"
                  offset={8}
                  className="fill-foreground"
                  fontSize={12}
                />
              </Bar>
            </BarChart>
          </ChartContainer>
        ) : (
          <div className="flex items-center justify-center h-60 text-muted-foreground">
            <div className="text-center">
              <p className="text-lg font-medium">Não há dados disponíveis</p>
              <p className="text-sm">Nenhuma atividade foi registrada ainda</p>
            </div>
          </div>
        )}
      </CardContent>
      {hasRealData && (
        <CardFooter className="flex-col items-start gap-2 text-sm">
          <div className="flex gap-2 leading-none font-medium">
            {parseFloat(growthPercentage) >= 0 ? (
              <>
                Crescimento de {growthPercentage}% este mês <TrendingUp className="h-4 w-4" />
              </>
            ) : (
              <>
                Redução de {Math.abs(parseFloat(growthPercentage))}% este mês
              </>
            )}
          </div>
          <div className="text-muted-foreground leading-none">
            Total de {totalActivities} atividades registradas
          </div>
        </CardFooter>
      )}
    </Card>
  )
}
