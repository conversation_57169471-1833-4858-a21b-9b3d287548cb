/**
 * Dashboard Clients List Component
 * Displays a compact list of recent clients for the dashboard
 */

import React from 'react';
import Link from 'next/link';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from './ui/card';
import { Badge } from './ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { Button } from './ui/button';
import { Skeleton } from './ui/skeleton';
import {
  User,
  Building,
  Mail,
  Phone,
  ArrowRight,
  Users,
  Plus
} from 'lucide-react';
import { useClients } from '../hooks/use-api';
import { useIsMobile } from '../hooks/use-mobile';
import type { Client } from '@/schemas/clients';

// ============================================================================
// TYPES
// ============================================================================

interface DashboardClientsListProps {
  limit?: number;
  showHeader?: boolean;
  className?: string;
}

// ============================================================================
// COMPONENTS
// ============================================================================

function ClientItem({ client }: { client: Client }) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ativo':
        return 'bg-green-100 text-green-800';
      case 'inativo':
        return 'bg-gray-100 text-gray-800';
      case 'prospecto':
        return 'bg-blue-100 text-blue-800';
      case 'arquivado':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'alta':
        return 'bg-red-100 text-red-800';
      case 'media':
        return 'bg-yellow-100 text-yellow-800';
      case 'baixa':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="flex items-center justify-between p-3 hover:bg-muted/50 rounded-lg transition-colors">
      <div className="flex items-center space-x-3 flex-1 min-w-0">
        <Avatar className="h-10 w-10 flex-shrink-0">
          <AvatarImage src={client.avatar} />
          <AvatarFallback>
            {client.name.split(' ').map(n => n[0]).join('').toUpperCase()}
          </AvatarFallback>
        </Avatar>

        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <p className="text-sm font-medium truncate">{client.name}</p>
            {client.type === 'pessoa_juridica' ? (
              <Building className="h-3 w-3 text-muted-foreground flex-shrink-0" />
            ) : (
              <User className="h-3 w-3 text-muted-foreground flex-shrink-0" />
            )}
          </div>

          <div className="flex items-center gap-2 mt-1">
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <Mail className="h-3 w-3" />
              <span className="truncate max-w-[120px]">{client.email}</span>
            </div>
            {client.phone && (
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <Phone className="h-3 w-3" />
                <span className="truncate max-w-[100px]">{client.phone}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="flex items-center gap-2 flex-shrink-0">
        <div className="flex flex-col gap-1">
          <Badge variant="outline" className={`text-xs ${getStatusColor(client.status)}`}>
            {client.status}
          </Badge>
          <Badge variant="outline" className={`text-xs ${getPriorityColor(client.priority)}`}>
            {client.priority}
          </Badge>
        </div>

        <div className="text-xs text-muted-foreground text-right">
          <div>
            {formatDistanceToNow(new Date(client.$createdAt), {
              addSuffix: true,
              locale: ptBR,
            })}
          </div>
          {client.company && (
            <div className="truncate max-w-[80px]" title={client.company}>
              {client.company}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

function ClientsListSkeleton({ count = 5 }: { count?: number }) {
  return (
    <div className="space-y-3">
      {Array.from({ length: count }).map((_, i) => (
        <div key={i} className="flex items-center space-x-3 p-3">
          <Skeleton className="h-10 w-10 rounded-full" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-3 w-48" />
          </div>
          <div className="space-y-1">
            <Skeleton className="h-5 w-16" />
            <Skeleton className="h-5 w-12" />
          </div>
        </div>
      ))}
    </div>
  );
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function DashboardClientsList({
  limit = 8,
  showHeader = true,
  className = ""
}: DashboardClientsListProps) {
  const { data: clients = [], isLoading, error } = useClients();
  const isMobile = useIsMobile();

  // Get recent clients (sorted by creation date)
  const recentClients = React.useMemo(() => {
    return [...clients]
      .sort((a, b) => new Date(b.$createdAt).getTime() - new Date(a.$createdAt).getTime())
      .slice(0, limit);
  }, [clients, limit]);

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <Users className="h-8 w-8 mx-auto mb-2" />
            <p>Erro ao carregar clientes</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      {showHeader && (
        <CardHeader className="pb-3">
          <div className={`flex ${isMobile ? 'flex-col gap-3' : 'items-center justify-between'}`}>
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-primary" />
              <div className="flex-1 min-w-0">
                <CardTitle className={`${isMobile ? 'text-base' : 'text-lg'}`}>Clientes Recentes</CardTitle>
                <CardDescription className={isMobile ? 'text-xs' : ''}>
                  {isLoading ? 'Carregando...' : `${clients.length} cliente(s) cadastrado(s)`}
                </CardDescription>
              </div>
            </div>

            <div className="flex gap-2">
              <Button variant="outline" size="sm" asChild>
                <Link href="/dashboard/clients">
                  Ver todos
                  <ArrowRight className="h-4 w-4 ml-1" />
                </Link>
              </Button>
              <Button size="sm" asChild>
                <Link href="/dashboard/clients">
                  <Plus className="h-4 w-4 mr-1" />
                  Novo
                </Link>
              </Button>
            </div>
          </div>
        </CardHeader>
      )}

      <CardContent className={showHeader ? "pt-0" : "p-6"}>
        {isLoading ? (
          <ClientsListSkeleton count={limit} />
        ) : recentClients.length === 0 ? (
          <div className="text-center py-8">
            <Users className="h-12 w-12 mx-auto text-muted-foreground mb-3" />
            <h3 className="text-sm font-medium text-muted-foreground mb-2">
              Nenhum cliente cadastrado
            </h3>
            <p className="text-xs text-muted-foreground mb-4">
              Comece adicionando seu primeiro cliente
            </p>
            <Button size="sm" asChild>
              <Link href="/dashboard/clients">
                <Plus className="h-4 w-4 mr-1" />
                Adicionar Cliente
              </Link>
            </Button>
          </div>
        ) : (
          <div className="space-y-1">
            {recentClients.map((client) => (
              <ClientItem key={client.$id} client={client} />
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
