import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../ui/form';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { useCreateTeam, useUpdateTeam } from '../../hooks/use-api';
import { useAuth } from '../../hooks/use-auth';
import type { Team, CreateTeamData, UpdateTeamData } from '@/schemas/teams';

const teamSchema = z.object({
  name: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres').max(50, 'Nome muito longo'),
  description: z.string().max(200, 'Descrição muito longa').optional(),
});

type TeamFormData = z.infer<typeof teamSchema>;

interface TeamFormModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  team?: Team | null;
  mode: 'create' | 'edit';
}

export function TeamFormModal({ open, onOpenChange, team, mode }: TeamFormModalProps) {
  const { user } = useAuth();
  const createTeamMutation = useCreateTeam();
  const updateTeamMutation = useUpdateTeam();

  const form = useForm<TeamFormData>({
    resolver: zodResolver(teamSchema),
    defaultValues: {
      name: '',
      description: '',
    },
  });

  // Reset form when team changes or modal opens
  React.useEffect(() => {
    if (mode === 'edit' && team) {
      form.reset({
        name: team.name || '',
        description: '', // Appwrite Teams não tem description nativo
      });
    } else if (mode === 'create') {
      form.reset({
        name: '',
        description: '',
      });
    }
  }, [team, mode, form, open]);

  const onSubmit = async (data: TeamFormData) => {
    if (!user?.$id) return;

    try {
      if (mode === 'create') {
        const createData: CreateTeamData = {
          name: data.name,
          roles: ['owner'], // Criador vira owner automaticamente
        };

        await createTeamMutation.mutateAsync(createData);
      } else if (mode === 'edit' && team) {
        const updateData: UpdateTeamData = {
          name: data.name,
        };

        await updateTeamMutation.mutateAsync({
          teamId: team.$id,
          name: data.name,
        });
      }

      form.reset();
      onOpenChange(false);
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  const isLoading = form.formState.isSubmitting || createTeamMutation.isPending || updateTeamMutation.isPending;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>
            {mode === 'create' ? 'Criar Novo Time' : 'Editar Time'}
          </DialogTitle>
          <DialogDescription>
            {mode === 'create'
              ? 'Crie um novo time para colaborar com outros membros.'
              : 'Atualize as informações do time.'
            }
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Team Name */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome do Time *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Ex: Desenvolvimento Frontend"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Description (for future use) */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Descrição (opcional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Descreva o propósito e objetivos do time..."
                      className="resize-none"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                  <p className="text-xs text-muted-foreground">
                    Nota: A descrição será salva localmente para referência futura.
                  </p>
                </FormItem>
              )}
            />

            {/* Plan limits info for create mode */}
            {mode === 'create' && (
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <p className="text-xs text-blue-700">
                  <strong>Dica:</strong> Como criador do time, você será automaticamente o proprietário
                  e poderá convidar outros membros e gerenciar permissões.
                </p>
              </div>
            )}

            {/* Actions */}
            <div className="flex justify-end gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancelar
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading
                  ? (mode === 'create' ? 'Criando...' : 'Salvando...')
                  : (mode === 'create' ? 'Criar Time' : 'Salvar Alterações')
                }
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
