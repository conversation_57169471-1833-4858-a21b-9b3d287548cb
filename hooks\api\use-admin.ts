import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  isCurrentUserAdmin,
  getAllUsers,
  getUserById,
  updateUser,
  suspendUser,
  activateUser,
  getAdminStats,
  getRecentActivity,
  type AdminUser,
  type UpdateUserData,
  type AdminStats,
  type ActivityLog
} from '../../lib/appwrite/functions/admin';

/**
 * Query keys for admin functions
 */
export const adminKeys = {
  all: ['admin'] as const,
  users: () => [...adminKeys.all, 'users'] as const,
  usersList: (filters: Record<string, unknown>) => [...adminKeys.users(), 'list', { filters }] as const,
  user: (id: string) => [...adminKeys.users(), 'detail', id] as const,
  stats: () => [...adminKeys.all, 'stats'] as const,
  activity: () => [...adminKeys.all, 'activity'] as const,
  isAdmin: () => [...adminKeys.all, 'isAdmin'] as const,
};

/**
 * Hook to check if current user is admin
 */
export function useIsAdmin() {
  return useQuery({
    queryKey: adminKeys.isAdmin(),
    queryFn: isCurrentUserAdmin,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to get all users (admin only)
 */
export function useAllUsers(
  queries?: string[],
  limit?: number,
  enabled: boolean = true
) {
  const { data: isAdmin } = useIsAdmin();

  return useQuery({
    queryKey: adminKeys.usersList({ queries, limit }),
    queryFn: () => getAllUsers(queries, limit),
    enabled: enabled && isAdmin === true,
  });
}

/**
 * Hook to get user by ID (admin only)
 */
export function useUserById(userId: string, enabled: boolean = true) {
  const { data: isAdmin } = useIsAdmin();

  return useQuery({
    queryKey: adminKeys.user(userId),
    queryFn: () => getUserById(userId),
    enabled: enabled && isAdmin === true && !!userId,
  });
}

/**
 * Hook to get admin statistics
 */
export function useAdminStats(enabled: boolean = true) {
  const { data: isAdmin } = useIsAdmin();

  return useQuery({
    queryKey: adminKeys.stats(),
    queryFn: getAdminStats,
    enabled: enabled && isAdmin === true,
    refetchInterval: 30000, // Refresh every 30 seconds
  });
}

/**
 * Hook to get recent activity
 */
export function useRecentActivity(limit?: number, enabled: boolean = true) {
  const { data: isAdmin } = useIsAdmin();

  return useQuery({
    queryKey: [...adminKeys.activity(), { limit }],
    queryFn: () => getRecentActivity(limit),
    enabled: enabled && isAdmin === true,
    refetchInterval: 10000, // Refresh every 10 seconds
  });
}

/**
 * Hook to update user (admin only)
 */
export function useUpdateUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ userId, data }: { userId: string; data: UpdateUserData }) =>
      updateUser(userId, data),
    onSuccess: (_, { userId }) => {
      queryClient.invalidateQueries({ queryKey: adminKeys.user(userId) });
      queryClient.invalidateQueries({ queryKey: adminKeys.users() });
      queryClient.invalidateQueries({ queryKey: adminKeys.stats() });
      queryClient.invalidateQueries({ queryKey: adminKeys.activity() });
    },
  });
}

/**
 * Hook to suspend user (admin only)
 */
export function useSuspendUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ userId, reason }: { userId: string; reason?: string }) =>
      suspendUser(userId, reason),
    onSuccess: (_, { userId }) => {
      queryClient.invalidateQueries({ queryKey: adminKeys.user(userId) });
      queryClient.invalidateQueries({ queryKey: adminKeys.users() });
      queryClient.invalidateQueries({ queryKey: adminKeys.stats() });
      queryClient.invalidateQueries({ queryKey: adminKeys.activity() });
    },
  });
}

/**
 * Hook to activate user (admin only)
 */
export function useActivateUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (userId: string) => activateUser(userId),
    onSuccess: (_, userId) => {
      queryClient.invalidateQueries({ queryKey: adminKeys.user(userId) });
      queryClient.invalidateQueries({ queryKey: adminKeys.users() });
      queryClient.invalidateQueries({ queryKey: adminKeys.stats() });
      queryClient.invalidateQueries({ queryKey: adminKeys.activity() });
    },
  });
}

/**
 * Hook to check if user has specific admin permissions
 */
export function useAdminPermissions() {
  const { data: isAdmin, isLoading } = useIsAdmin();

  return {
    canViewUsers: isAdmin === true,
    canEditUsers: isAdmin === true,
    canSuspendUsers: isAdmin === true,
    canViewStats: isAdmin === true,
    canViewActivity: isAdmin === true,
    isLoading,
  };
}

/**
 * Hook for admin dashboard data
 */
export function useAdminDashboard() {
  const { data: isAdmin } = useIsAdmin();
  const enabled = isAdmin === true;

  const stats = useAdminStats(enabled);
  const recentActivity = useRecentActivity(5, enabled);
  const recentUsers = useAllUsers(undefined, 10, enabled);

  return {
    stats: stats.data,
    recentActivity: recentActivity.data,
    recentUsers: recentUsers.data,
    isLoading: stats.isLoading || recentActivity.isLoading || recentUsers.isLoading,
    error: stats.error || recentActivity.error || recentUsers.error,
  };
}
