/**
 * Local-First Client API Hooks
 * Integrates Appwrite with IndexedDB cache for optimal performance
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useAuth } from '../use-auth';
import { useTeamContext } from '../../contexts/team-context';
import { useDocumentPermissions } from '../use-document-permissions';
import { isCacheEnabled } from '../../lib/cache-config';
import {
  hasDataInIndexedDB,
  getFromIndexedDB,
  saveToIndexedDB,
  syncUpdatedDataFromServer,
  syncAfterMutation
} from '../../lib/cache-sync';
import type { Client, CreateClientData, UpdateClientData } from '@/schemas/clients';
import { clients } from '../../lib/appwrite/functions/database';
import { useActivityLogger } from './use-activities';

/**
 * Get all clients with local-first strategy
 */
export function useClients() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const query = useQuery({
    queryKey: ['clients', user?.$id],
    queryFn: async () => {
      if (!user?.$id) return [];

      // Verificar se o cache está habilitado
      if (isCacheEnabled()) {
        // Verificar se IndexedDB tem dados
        const hasData = await hasDataInIndexedDB('clients', user.$id);

        if (hasData) {
          // Servir dados do IndexedDB e ignorar fetch
          const cachedData = await getFromIndexedDB<Client>('clients', user.$id);
          console.log('🗄️ Dados carregados do cache local');

          // Sincronizar dados atualizados em background (não bloqueia o retorno)
          setTimeout(async () => {
            await syncUpdatedDataFromServer<Client>(
              'clients',
              user.$id,
              (activeData, deletedData) => {
                // Atualizar React Query cache com dados atualizados
                queryClient.setQueryData<Client[]>(['clients', user.$id], (oldData) => {
                  if (!oldData) return activeData;

                  // Começar com dados existentes
                  let mergedData = [...oldData];

                  // Remover documentos que foram soft deleted
                  if (deletedData.length > 0) {
                    const deletedIds = deletedData.map(doc => doc.$id);
                    mergedData = mergedData.filter(item => !deletedIds.includes(item.$id));
                    console.log(`🗑️ Removidos ${deletedData.length} clientes deletados do cache React Query`);
                  }

                  // Merge dos dados ativos: substituir existentes e adicionar novos
                  activeData.forEach(updatedItem => {
                    const existingIndex = mergedData.findIndex(item => item.$id === updatedItem.$id);
                    if (existingIndex >= 0) {
                      mergedData[existingIndex] = updatedItem;
                    } else {
                      mergedData.push(updatedItem);
                    }
                  });

                  return mergedData;
                });

                // Atualizar cache de deletados se houver novos documentos deletados
                if (deletedData.length > 0) {
                  queryClient.setQueryData<Client[]>(['clients', 'deleted', user.$id], (oldDeleted) => {
                    if (!oldDeleted) return deletedData;

                    // Merge dos dados deletados
                    const mergedDeleted = [...oldDeleted];
                    deletedData.forEach(deletedItem => {
                      const existingIndex = mergedDeleted.findIndex(item => item.$id === deletedItem.$id);
                      if (existingIndex >= 0) {
                        mergedDeleted[existingIndex] = deletedItem;
                      } else {
                        mergedDeleted.push(deletedItem);
                      }
                    });

                    return mergedDeleted;
                  });
                }
              }
            );
          }, 0);

          return cachedData;
        }
      }

      // Buscar do servidor (só se IndexedDB estiver vazio)
      console.log('🌐 Buscando dados do servidor');
      const result = await clients.list();
      const clientsData = result.documents as unknown as Client[];

      // Salvar no cache se habilitado
      if (isCacheEnabled() && clientsData.length > 0) {
        await saveToIndexedDB('clients', clientsData, {
          collection: 'clients',
          userId: user.$id
        });
      }

      return clientsData;
    },
    enabled: !!user?.$id,
    staleTime: isCacheEnabled() ? 0 : 5 * 60 * 1000,
    refetchOnWindowFocus: false,
  });

  return query;
}

/**
 * Get single client with local-first strategy
 */
export function useClient(id: string) {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['clients', id],
    queryFn: async () => {
      // Verificar se o cache está habilitado
      if (isCacheEnabled()) {
        // Buscar dados do IndexedDB primeiro
        const cachedData = await getFromIndexedDB<Client>('clients', user?.$id);
        const cachedClient = cachedData.find(client => client.$id === id);

        if (cachedClient) {
          console.log(`🗄️ Cliente ${id} carregado do cache local`);
          return cachedClient;
        }
      }

      // Buscar do servidor
      console.log(`🌐 Buscando cliente ${id} do servidor`);
      const result = await clients.get(id);
      const clientData = result as unknown as Client;

      // Salvar no cache se habilitado
      if (isCacheEnabled() && clientData) {
        await saveToIndexedDB('clients', clientData, {
          collection: 'clients',
          userId: user?.$id
        });
      }

      return clientData;
    },
    enabled: !!id,
    staleTime: isCacheEnabled() ? 0 : 5 * 60 * 1000,
    refetchOnWindowFocus: false,
  });
}

/**
 * Create client with optimistic updates and permissions
 */
export function useCreateClient() {
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const { permissionContext } = useTeamContext();
  const { teamId } = useDocumentPermissions();
  const { logClient } = useActivityLogger();

  return useMutation({
    mutationFn: async (data: CreateClientData) => {
      if (!user?.$id) {
        throw new Error('Usuário não autenticado');
      }

      const clientData = {
        ...data,
        userId: user.$id,
        createdBy: user.$id,
        teamId: teamId || undefined,
      };

      // Criar cliente com permissões baseadas no team
      const result = await clients.create(clientData, {
        userId: user.$id,
        teamId,
        permissionContext,
        isPublic: false,
      });

      const newClient = result as unknown as Client;

      // Sincronização completa: IndexedDB + preferências do usuário
      if (isCacheEnabled()) {
        await syncAfterMutation('clients', 'create', newClient, user.$id);
      }

      return newClient;
    },
    onMutate: async (data: CreateClientData) => {
      // Optimistic update: atualizar React Query cache imediatamente
      const queryKey = ['clients', user?.$id];

      // Cancelar queries em andamento
      await queryClient.cancelQueries({ queryKey });

      // Snapshot do estado anterior
      const previousClients = queryClient.getQueryData<Client[]>(queryKey);

      // Criar cliente temporário para UI (sem avatar File)
      const tempClient: Client = {
        $id: `temp-${Date.now()}`,
        $createdAt: new Date().toISOString(),
        $updatedAt: new Date().toISOString(),
        ...data,
        avatar: typeof data.avatar === 'string' ? data.avatar : '', // Converter File para string vazia temporariamente
        userId: user?.$id || '',
        createdBy: user?.$id || '',
      };

      // Atualizar cache do React Query otimisticamente
      queryClient.setQueryData<Client[]>(queryKey, (old) =>
        old ? [tempClient, ...old] : [tempClient]
      );

      return { previousClients, tempClient };
    },
    onSuccess: (newClient, _variables, context) => {
      // Substituir cliente temporário pelo real
      const queryKey = ['clients', user?.$id];
      queryClient.setQueryData<Client[]>(queryKey, (old) =>
        old ? old.map(client =>
          client.$id === context?.tempClient.$id ? newClient : client
        ) : [newClient]
      );

      // Log activity
      logClient('create', newClient.$id, {
        clientName: newClient.name,
        clientEmail: newClient.email,
        clientType: newClient.type,
      });

      toast.success('Cliente criado com sucesso');
    },
    onError: (error: any, _variables, context) => {
      // Reverter em caso de erro
      if (context?.previousClients) {
        queryClient.setQueryData(['clients', user?.$id], context.previousClients);
      }
      toast.error(error.message || 'Erro ao criar cliente');
    },
  });
}

/**
 * Update client with optimistic updates
 */
export function useUpdateClient() {
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const { logClient } = useActivityLogger();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: UpdateClientData }) => {
      const updateData = {
        ...data,
        updatedBy: user?.$id,
      };

      const result = await clients.update(id, updateData);
      const updatedClient = result as unknown as Client;

      // Sincronização completa: IndexedDB + preferências do usuário
      if (isCacheEnabled()) {
        await syncAfterMutation('clients', 'update', updatedClient, user?.$id);
      }

      return updatedClient;
    },
    onMutate: async ({ id, data }) => {
      // Optimistic update: atualizar React Query cache imediatamente
      const queryKey = ['clients', user?.$id];
      const singleQueryKey = ['clients', id];

      // Cancelar queries em andamento
      await queryClient.cancelQueries({ queryKey });
      await queryClient.cancelQueries({ queryKey: singleQueryKey });

      // Snapshot do estado anterior
      const previousClients = queryClient.getQueryData<Client[]>(queryKey);
      const previousClient = queryClient.getQueryData<Client>(singleQueryKey);

      // Atualizar cache do React Query otimisticamente
      queryClient.setQueryData<Client[]>(queryKey, (old) =>
        old ? old.map(client =>
          client.$id === id
            ? { ...client, ...data, $updatedAt: new Date().toISOString() }
            : client
        ) : []
      );

      // Atualizar cache do cliente individual
      queryClient.setQueryData<Client>(singleQueryKey, (old) =>
        old ? { ...old, ...data, $updatedAt: new Date().toISOString() } : old
      );

      return { previousClients, previousClient };
    },
    onSuccess: (updatedClient, { id, data }) => {
      // Atualizar com dados reais do servidor
      const queryKey = ['clients', user?.$id];
      const singleQueryKey = ['clients', id];

      queryClient.setQueryData<Client[]>(queryKey, (old) =>
        old ? old.map(client =>
          client.$id === id ? updatedClient : client
        ) : [updatedClient]
      );

      queryClient.setQueryData<Client>(singleQueryKey, updatedClient);

      // Log activity
      logClient('update', id, {
        clientName: updatedClient.name,
        updatedFields: Object.keys(data),
      });

      toast.success('Cliente atualizado com sucesso');
    },
    onError: (error: any, { id }, context) => {
      // Reverter em caso de erro
      if (context?.previousClients) {
        queryClient.setQueryData(['clients', user?.$id], context.previousClients);
      }
      if (context?.previousClient) {
        queryClient.setQueryData(['clients', id], context.previousClient);
      }
      toast.error(error.message || 'Erro ao atualizar cliente');
    },
  });
}

/**
 * Delete client with optimistic updates
 */
export function useDeleteClient() {
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const { logClient } = useActivityLogger();

  return useMutation({
    mutationFn: async (id: string) => {
      await clients.delete(id);

      // Sincronização completa: IndexedDB + preferências do usuário
      if (isCacheEnabled()) {
        await syncAfterMutation('clients', 'delete', id, user?.$id);
      }
    },
    onMutate: async (id: string) => {
      // Optimistic update: remover do React Query cache imediatamente
      const queryKey = ['clients', user?.$id];
      const singleQueryKey = ['clients', id];

      // Cancelar queries em andamento
      await queryClient.cancelQueries({ queryKey });
      await queryClient.cancelQueries({ queryKey: singleQueryKey });

      // Snapshot do estado anterior
      const previousClients = queryClient.getQueryData<Client[]>(queryKey);
      const previousClient = queryClient.getQueryData<Client>(singleQueryKey);

      // Remover do cache do React Query otimisticamente
      queryClient.setQueryData<Client[]>(queryKey, (old) =>
        old ? old.filter(client => client.$id !== id) : []
      );

      // Remover cache do cliente individual
      queryClient.removeQueries({ queryKey: singleQueryKey });

      return { previousClients, previousClient, deletedId: id };
    },
    onSuccess: (_, id, context) => {
      // Log activity
      logClient('delete', id, {
        clientName: context?.previousClient?.name || 'Cliente removido',
      });

      toast.success('Cliente excluído com sucesso');
    },
    onError: (error: any, id, context) => {
      // Reverter em caso de erro
      if (context?.previousClients) {
        queryClient.setQueryData(['clients', user?.$id], context.previousClients);
      }
      if (context?.previousClient) {
        queryClient.setQueryData(['clients', id], context.previousClient);
      }
      toast.error(error.message || 'Erro ao excluir cliente');
    },
  });
}

/**
 * Get deleted clients (soft deleted)
 */
export function useDeletedClients() {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['clients', 'deleted', user?.$id],
    queryFn: async () => {
      if (!user?.$id) return [];

      const result = await clients.listDeleted();
      return result.documents as unknown as Client[];
    },
    enabled: !!user?.$id,
    staleTime: 5 * 60 * 1000, // 5 minutos
    refetchOnWindowFocus: false,
  });
}

/**
 * Restore a soft deleted client
 */
export function useRestoreClient() {
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const { logClient } = useActivityLogger();

  return useMutation({
    mutationFn: async (id: string) => {
      const result = await clients.restore(id);
      const restoredClient = result as unknown as Client;

      // Sincronização completa: IndexedDB + preferências do usuário
      if (isCacheEnabled()) {
        await syncAfterMutation('clients', 'update', restoredClient, user?.$id);
      }

      return restoredClient;
    },
    onSuccess: (restoredClient, id) => {
      // Atualizar cache de clientes ativos
      const activeQueryKey = ['clients', user?.$id];
      queryClient.setQueryData<Client[]>(activeQueryKey, (old) =>
        old ? [restoredClient, ...old] : [restoredClient]
      );

      // Remover do cache de deletados
      const deletedQueryKey = ['clients', 'deleted', user?.$id];
      queryClient.setQueryData<Client[]>(deletedQueryKey, (old) =>
        old ? old.filter(client => client.$id !== id) : []
      );

      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: ['clients'] });

      // Log activity
      logClient('restore', id, {
        clientName: restoredClient.name,
      });

      toast.success('Cliente restaurado com sucesso');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erro ao restaurar cliente');
    },
  });
}

/**
 * Hard delete a client (permanent deletion)
 */
export function useHardDeleteClient() {
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const { logClient } = useActivityLogger();

  return useMutation({
    mutationFn: async (id: string) => {
      await clients.hardDelete(id);

      // Remover do cache local também
      if (isCacheEnabled()) {
        await syncAfterMutation('clients', 'delete', id, user?.$id);
      }
    },
    onSuccess: (_, id) => {
      // Remover de todos os caches
      const activeQueryKey = ['clients', user?.$id];
      const deletedQueryKey = ['clients', 'deleted', user?.$id];
      const singleQueryKey = ['clients', id];

      queryClient.setQueryData<Client[]>(activeQueryKey, (old) =>
        old ? old.filter(client => client.$id !== id) : []
      );

      queryClient.setQueryData<Client[]>(deletedQueryKey, (old) =>
        old ? old.filter(client => client.$id !== id) : []
      );

      queryClient.removeQueries({ queryKey: singleQueryKey });

      // Log activity
      logClient('hardDelete', id, {
        clientName: 'Cliente removido permanentemente',
      });

      toast.success('Cliente removido permanentemente');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erro ao remover cliente permanentemente');
    },
  });
}
