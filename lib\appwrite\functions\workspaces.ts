/**
 * Workspace Appwrite Functions
 * CRUD operations for workspaces
 */

import { databases, DATABASE_ID, COLLECTIONS } from '../config';
import { ID, Query } from 'appwrite';
import type {
  OptimizedWorkspace,
  CreateWorkspaceData,
  UpdateWorkspaceData
} from '@/schemas/kanban';
import type { CreateDocumentOptions } from './database';

// ============================================================================
// WORKSPACE FUNCTIONS
// ============================================================================

export async function createWorkspace(data: CreateWorkspaceData, options?: CreateDocumentOptions) {
  const workspaceData = {
    ...data,
    createdBy: data.userId,
    updatedBy: data.userId,
  };

  // Usar a função createDocument com permissões se options foram fornecidas
  if (options) {
    const { createDocument } = await import('./database');
    return await createDocument(COLLECTIONS.WORKSPACES, workspaceData, options, ID.unique());
  }

  // Fallback para método antigo
  return await databases.createDocument(
    DATABASE_ID,
    COLLECTIONS.WORKSPACES,
    ID.unique(),
    workspaceData
  );
}

export async function getWorkspace(workspaceId: string) {
  return await databases.getDocument(
    DATABASE_ID,
    COLLECTIONS.WORKSPACES,
    workspaceId
  );
}

export async function updateWorkspace(workspaceId: string, data: UpdateWorkspaceData) {
  return await databases.updateDocument(
    DATABASE_ID,
    COLLECTIONS.WORKSPACES,
    workspaceId,
    data
  );
}

export async function deleteWorkspace(workspaceId: string) {
  return await databases.deleteDocument(
    DATABASE_ID,
    COLLECTIONS.WORKSPACES,
    workspaceId
  );
}

export async function listWorkspaces(userId?: string) {
  const queries = [];

  if (userId) {
    queries.push(Query.equal('userId', userId));
  }

  queries.push(Query.equal('isArchived', false));
  queries.push(Query.orderDesc('$createdAt'));

  return await databases.listDocuments(
    DATABASE_ID,
    COLLECTIONS.WORKSPACES,
    queries
  );
}

export async function listWorkspacesByMember(userId: string) {
  return await databases.listDocuments(
    DATABASE_ID,
    COLLECTIONS.WORKSPACES,
    [
      Query.search('members', userId),
      Query.equal('isArchived', false),
      Query.orderDesc('$createdAt')
    ]
  );
}

export async function addWorkspaceMember(workspaceId: string, userId: string) {
  const workspace = await getWorkspace(workspaceId);
  const currentMembers = workspace.members || [];

  if (!currentMembers.includes(userId)) {
    const updatedMembers = [...currentMembers, userId];
    return await updateWorkspace(workspaceId, {
      members: updatedMembers,
      updatedBy: userId
    });
  }

  return workspace;
}

export async function removeWorkspaceMember(workspaceId: string, userId: string, removedBy: string) {
  const workspace = await getWorkspace(workspaceId);
  const currentMembers = workspace.members || [];

  const updatedMembers = currentMembers.filter((memberId: string) => memberId !== userId);
  return await updateWorkspace(workspaceId, {
    members: updatedMembers,
    updatedBy: removedBy
  });
}

export async function archiveWorkspace(workspaceId: string, userId: string) {
  return await updateWorkspace(workspaceId, {
    isArchived: true,
    updatedBy: userId
  });
}

export async function restoreWorkspace(workspaceId: string, userId: string) {
  return await updateWorkspace(workspaceId, {
    isArchived: false,
    updatedBy: userId
  });
}

// ============================================================================
// WORKSPACE WITH DATA
// ============================================================================

export async function getWorkspaceWithBoards(workspaceId: string) {
  const [workspace, boards] = await Promise.all([
    getWorkspace(workspaceId),
    databases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.KANBAN_BOARDS_OPTIMIZED,
      [
        Query.equal('workspaceId', workspaceId),
        Query.equal('isArchived', false),
        Query.orderDesc('$createdAt')
      ]
    )
  ]);

  return {
    workspace,
    boards: boards.documents,
    boardsCount: boards.total
  };
}

export async function getUserWorkspaces(userId: string) {
  // Get workspaces where user is owner or member
  const [ownedWorkspaces, memberWorkspaces] = await Promise.all([
    listWorkspaces(userId),
    listWorkspacesByMember(userId)
  ]);

  // Combine and deduplicate
  const allWorkspaces = [...ownedWorkspaces.documents];

  memberWorkspaces.documents.forEach(workspace => {
    if (!allWorkspaces.find(w => w.$id === workspace.$id)) {
      allWorkspaces.push(workspace);
    }
  });

  // Sort by creation date
  allWorkspaces.sort((a, b) =>
    new Date(b.$createdAt).getTime() - new Date(a.$createdAt).getTime()
  );

  return {
    documents: allWorkspaces,
    total: allWorkspaces.length
  };
}

// ============================================================================
// WORKSPACE PERMISSIONS
// ============================================================================

export async function canUserAccessWorkspace(workspaceId: string, userId: string): Promise<boolean> {
  try {
    const workspace = await getWorkspace(workspaceId);

    // Owner can always access
    if (workspace.userId === userId) {
      return true;
    }

    // Check if user is a member
    if (workspace.members?.includes(userId)) {
      return true;
    }

    // Check visibility
    if (workspace.visibility === 'public') {
      return true;
    }

    return false;
  } catch (error) {
    return false;
  }
}

export async function canUserEditWorkspace(workspaceId: string, userId: string): Promise<boolean> {
  try {
    const workspace = await getWorkspace(workspaceId);

    // Only owner can edit
    return workspace.userId === userId;
  } catch (error) {
    return false;
  }
}

export async function canUserInviteToWorkspace(workspaceId: string, userId: string): Promise<boolean> {
  try {
    const workspace = await getWorkspace(workspaceId);

    // Owner can always invite
    if (workspace.userId === userId) {
      return true;
    }

    // Check if invites are allowed and user is member
    if (workspace.allowInvites && workspace.members?.includes(userId)) {
      return true;
    }

    return false;
  } catch (error) {
    return false;
  }
}

// ============================================================================
// WORKSPACE STATISTICS
// ============================================================================

export async function getWorkspaceStats(workspaceId: string) {
  const [boards, workspace] = await Promise.all([
    databases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.KANBAN_BOARDS_OPTIMIZED,
      [
        Query.equal('workspaceId', workspaceId),
        Query.equal('isArchived', false)
      ]
    ),
    getWorkspace(workspaceId)
  ]);

  // Get total tasks across all boards
  let totalTasks = 0;
  let completedTasks = 0;

  for (const board of boards.documents) {
    const tasks = await databases.listDocuments(
      DATABASE_ID,
      COLLECTIONS.KANBAN_BOARDS_OPTIMIZED,
      [
        Query.equal('boardId', board.$id),
        Query.equal('isArchived', false)
      ]
    );

    totalTasks += tasks.total;
    completedTasks += tasks.documents.filter((task: any) => task.status === 'done').length;
  }

  return {
    boardsCount: boards.total,
    membersCount: workspace.members?.length || 0,
    totalTasks,
    completedTasks,
    completionRate: totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0
  };
}
