/**
 * PWA (Progressive Web App) type definitions
 * Provides type safety for PWA functionality
 */

export interface PWAInstallPrompt {
  prompt(): Promise<void>;
  userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
}

export interface PWADisplayMode {
  standalone: boolean;
  fullscreen: boolean;
  minimal: boolean;
  browser: boolean;
}

export interface PWACapabilities {
  canInstall: boolean;
  isInstalled: boolean;
  isStandalone: boolean;
  supportsNotifications: boolean;
  supportsBackgroundSync: boolean;
  supportsOffline: boolean;
}

export interface PWAUpdateInfo {
  available: boolean;
  waiting: boolean;
  installing: boolean;
  error?: string;
}

export interface PWANotificationOptions {
  title: string;
  body?: string;
  icon?: string;
  badge?: string;
  image?: string;
  tag?: string;
  requireInteraction?: boolean;
  silent?: boolean;
  vibrate?: number[];
  actions?: PWANotificationAction[];
  data?: Record<string, unknown>;
}

export interface PWANotificationAction {
  action: string;
  title: string;
  icon?: string;
}

export interface PWAPermissions {
  notifications: PermissionState;
  camera: PermissionState;
  microphone: PermissionState;
  geolocation: PermissionState;
  persistentStorage: PermissionState;
}

export interface PWANetworkStatus {
  online: boolean;
  effectiveType: string;
  downlink: number;
  rtt: number;
  saveData: boolean;
}

export interface PWAShareData {
  title?: string;
  text?: string;
  url?: string;
  files?: File[];
}

export interface PWAManifest {
  name: string;
  short_name: string;
  description: string;
  start_url: string;
  display: 'standalone' | 'fullscreen' | 'minimal-ui' | 'browser';
  background_color: string;
  theme_color: string;
  icons: PWAIcon[];
  categories?: string[];
  lang?: string;
  orientation?: 'portrait' | 'landscape' | 'any';
  scope?: string;
}

export interface PWAIcon {
  src: string;
  sizes: string;
  type: string;
  purpose?: 'any' | 'maskable' | 'monochrome';
}

export interface PWABeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

// Global window extensions for PWA
declare global {
  interface Window {
    __PWA_ENABLED__?: boolean;
  }

  interface WindowEventMap {
    beforeinstallprompt: PWABeforeInstallPromptEvent;
    appinstalled: Event;
  }
}

export interface PWAConfig {
  enabled: boolean;
  autoUpdate: boolean;
  showInstallPrompt: boolean;
  notificationPermission: boolean;
  offlineSupport: boolean;
  backgroundSync: boolean;
}

export interface PWAHooks {
  onInstallPrompt?: (event: PWABeforeInstallPromptEvent) => void;
  onInstalled?: () => void;
  onUpdateAvailable?: () => void;
  onOffline?: () => void;
  onOnline?: () => void;
}

// Context type for PWA hook
export interface PWAContextType {
  isInstalled: boolean;
  isInstallable: boolean;
  canInstall: boolean;
  installPrompt: PWABeforeInstallPromptEvent | null;
  isStandalone: boolean;
  updateAvailable: boolean;
  isOnline: boolean;
  install: () => Promise<void>;
  skipWaiting: () => void;
  updateApp: () => Promise<void>;
  requestNotificationPermission: () => Promise<boolean>;
  sendNotification: (options: {
    title: string;
    body?: string;
    icon?: string;
    badge?: string;
    tag?: string;
    data?: any;
  }) => void;
  checkForUpdates: () => Promise<void>;
  showInstallPrompt: () => void;
  dismissInstallPrompt: () => void;
}

// Alias for easier importing
export type BeforeInstallPromptEvent = PWABeforeInstallPromptEvent;

// Service Worker update event
export interface ServiceWorkerUpdateEvent extends Event {
  type: 'updatefound' | 'statechange' | 'controllerchange' | 'update-available' | 'update-installed' | 'offline-ready';
  target: ServiceWorker;
  registration?: ServiceWorkerRegistration;
}
