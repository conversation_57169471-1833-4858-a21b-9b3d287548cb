import * as React from "react";
import { Send, Paperclip, Loader2 } from "lucide-react";
import { But<PERSON> } from "../ui/button";
import { Textarea } from "../ui/textarea";
import { cn } from "../../lib/utils";
import { EmojiPicker } from './emoji-picker';

interface ChatInputProps {
  value: string;
  onChange: (value: string) => void;
  onSend: () => void;
  onKeyDown?: (e: React.KeyboardEvent) => void;
  placeholder?: string;
  disabled?: boolean;
  isLoading?: boolean;
  showFileUpload?: boolean;
  showEmojiPicker?: boolean;
  onFileUpload?: (fileData: { fileId: string; url: string; fileName: string; fileType: string; fileSize: number; }) => void;
  onEmojiSelect?: (emoji: string) => void;
  className?: string;
}

export const ChatInput = React.forwardRef<
  HTMLTextAreaElement,
  ChatInputProps
>(({
  value,
  onChange,
  onSend,
  onKeyDown,
  placeholder = "Pergunte alguma coisa",
  disabled = false,
  isLoading = false,
  showFileUpload = true,
  showEmojiPicker = true,
  onFileUpload,
  onEmojiSelect,
  className,
  ...props
}, ref) => {
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  // Auto-resize textarea
  const textareaRef = React.useRef<HTMLTextAreaElement>(null);
  React.useImperativeHandle(ref, () => textareaRef.current!);

  React.useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
    }
  }, [value]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (value.trim() && !disabled && !isLoading) {
        onSend();
      }
    }
    onKeyDown?.(e);
  };

  const handleEmojiSelect = (emoji: string) => {
    if (onEmojiSelect) {
      onEmojiSelect(emoji);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file || !onFileUpload) return;

    // Simular upload de arquivo
    const fileData = {
      fileId: `file-${Date.now()}`,
      url: URL.createObjectURL(file),
      fileName: file.name,
      fileType: file.type,
      fileSize: file.size,
    };

    onFileUpload(fileData);

    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const canSend = value.trim().length > 0 && !disabled && !isLoading;

  return (
    <div className={cn("flex items-center gap-3 p-4 border-t bg-background", className)}>
      {/* Input Container - seguindo exatamente o layout da imagem */}
      <div className="flex-1 relative">
        <div className="flex items-center gap-3 bg-muted/50 rounded-full px-4 py-3 border border-border/30">
          {/* Emoji Picker Button - à esquerda dentro do input */}
          {showEmojiPicker && (
            <EmojiPicker
              onEmojiSelect={handleEmojiSelect}
              disabled={disabled}
            />
          )}

          {/* Text Input */}
          <div className="flex-1 min-w-0">
            <Textarea
              ref={textareaRef}
              value={value}
              onChange={(e) => onChange(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              disabled={disabled}
              className={cn(
                "min-h-[20px] max-h-[120px] resize-none border-0 bg-transparent p-0",
                "focus-visible:ring-0 focus-visible:ring-offset-0",
                "placeholder:text-muted-foreground/60",
                "text-sm leading-relaxed overflow-hidden"
              )}
              style={{ height: 'auto' }}
              rows={1}
            />
          </div>

          {/* File Upload Button - dentro do input se habilitado */}
          {showFileUpload && (
            <>
              <input
                ref={fileInputRef}
                type="file"
                className="hidden"
                onChange={handleFileSelect}
                accept="image/*,video/*,.pdf,.doc,.docx,.txt"
              />
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-6 w-6 rounded-full shrink-0 hover:bg-muted"
                disabled={disabled}
                onClick={() => fileInputRef.current?.click()}
              >
                <Paperclip className="h-4 w-4 text-muted-foreground" />
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Send Button - circular azul à direita, fora do input */}
      <Button
        onClick={onSend}
        disabled={!canSend}
        size="icon"
        className="h-10 w-10 rounded-full shrink-0 bg-primary hover:bg-primary/90"
      >
        {isLoading ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <Send className="h-4 w-4" />
        )}
      </Button>
    </div>
  );
});

ChatInput.displayName = "ChatInput";
