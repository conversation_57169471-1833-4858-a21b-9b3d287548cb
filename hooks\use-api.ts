/**
 * Simplified API Hooks - Main Entry Point
 * Re-exports only the hooks that actually exist
 */

// ============================================================================
// RE-EXPORTS FROM EXISTING MODULES
// ============================================================================

// Query Keys
export { queryKeys } from './api/query-keys';

// Public Profile Hooks - Removido (não usado)

// Notification Hooks
export {
  useNotifications as useNotificationsApi,
  useNotification,
  useDeleteNotification,
  useMarkAsRead,
  useMarkAllAsRead,
} from './api/use-notifications';

// Compatibility wrapper for old notification hook interface
export { useNotifications, useUnreadNotificationCount } from './use-notifications-compat';

// Client Hooks
export {
  useClients,
  useClient,
  useCreateClient,
  useUpdateClient,
  useDeleteClient,
} from './api/use-clients';

// Team Hooks
export {
  useTeam,
  useTeams,
  useTeamMembers,
  useCreateTeam,
  useUpdateTeam,
  useDeleteTeam,
  useInviteToTeam,
  useRemoveTeamMember,
  useUpdateMemberRoles,
} from './api/use-teams';

// Team Chat Hooks (Novo padrão simples)
export {
  useTeamChat,
  useChat,
  useCreateOrGetTeamChat,
  useChatRealtime,
} from './api/use-team-chat';

// Messages Hooks (Nova estratégia - busca direta)
export {
  useMessages,
  useSendMessage,
  useEditMessage,
  useDeleteMessage,
} from './api/use-messages';

// Reactions Hooks
export {
  useAddReaction,
  useRemoveReaction,
  useToggleReaction,
} from './api/use-reactions';

// Admin Hooks
export {
  useIsAdmin,
  useAllUsers,
  useUserById,
  useAdminStats,
  useRecentActivity,
  useUpdateUser,
  useSuspendUser,
  useActivateUser,
  useAdminPermissions,
  useAdminDashboard,
  adminKeys,
} from './api/use-admin';

// Storage Hooks
export {
  useFile,
  useFiles,
  useUploadFile,
  useDeleteFile,
  useUpdateFile,
} from './api/use-storage';

// Event Hooks
export {
  useEvents,
  useEvent,
  useSearchEvents,
  useEventsByDateRange,
  useUpcomingEvents,
  useRecurringEvents,
  useEventStats,
  useCreateEvent,
  useUpdateEvent,
  useDeleteEvent,
  useMarkReminderSent,
  useEventCategories,
  useEventCategory,
  useDefaultEventCategories,
  useCreateEventCategory,
  useUpdateEventCategory,
  useDeleteEventCategory,
  eventKeys,
} from './api/use-events';

// Kanban Hooks
export {
  useBoards,
  useBoardWithData,
  useBoardsSummary,
  useCreateBoard,
  useUpdateBoard,
  useDeleteBoard,
  useCreateColumn,
  useUpdateColumn,
  useDeleteColumn,
  useCreateTask,
  useUpdateTask,
  useDeleteTask,
  useMoveTask,
} from './api/use-kanban';

// Activity Hooks
export {
  useActivities,
  useInfiniteActivities,
  useActivity,
  useActivityStats,
  useCreateActivity,
  useLogActivity,
  useRecentActivities,
  useActivitiesByType,
  useUserActivities,
  useTeamActivities,
  useSearchActivities,
  useActivityLogger,
  activityKeys,
} from './api/use-activities';

// Mutation Helpers (for advanced usage)
export {
  useBasicMutation,
  useOptimisticCreateMutation,
  useOptimisticUpdateMutation,
  useOptimisticDeleteMutation,
  wrapApiResponse,
} from './api/mutation-helpers';

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Helper to invalidate all caches for a specific entity
 */
export function invalidateEntityCache(entityName: string) {
  // This function should be used within a React component or hook
  // where useQueryClient is available
  return (queryClient: any) => {
    queryClient.invalidateQueries({ queryKey: [entityName] });
  };
}

/**
 * Helper to get all available query keys
 */
export function getAllQueryKeys() {
  // Import query keys dynamically to avoid circular dependencies
  return {
    clients: ['clients'],
    teams: ['teams'],
    notifications: ['notifications'],
    activities: ['activities'],
    // Add more as needed
  };
}

/**
 * Helper to check if any queries are loading
 */
export function useIsAnyQueryLoading() {
  // This should be imported at the top level instead
  // For now, return false to avoid the require error
  return false;
}

// ============================================================================
// LEGACY COMPATIBILITY (for gradual migration)
// ============================================================================

// These are aliases for backward compatibility
// TODO: Remove these after migration is complete

// Note: These are already exported above, just keeping for reference

// ============================================================================
// TYPES RE-EXPORTS
// ============================================================================

export type {
  MutationConfig,
  OptimisticCreateConfig,
  OptimisticUpdateConfig,
  OptimisticDeleteConfig,
} from './api/mutation-helpers';

// ============================================================================
// DOCUMENTATION
// ============================================================================

/**
 * USAGE EXAMPLES:
 *
 * // ===== CLIENTS =====
 * const { data: clients, isLoading } = useClients();
 * const { data: filteredClients } = useFilteredClients('search term', { status: 'ativo' });
 * const createClient = useCreateClient({
 *   onSuccess: (client) => console.log('Created:', client.name)
 * });
 * const { data: stats } = useClientStats();
 *
 * // ===== TEAMS =====
 * const { data: teams } = useTeams();
 * const { data: team } = useTeam(teamId);
 * const { data: members } = useTeamMembers(teamId);
 * const createTeam = useCreateTeam();
 * const { canCreateTeam, hasTeamAccess } = useTeamLimits();
 *
 * // ===== CHAT =====
 * const { data: chat } = useChat(chatId); // Novo hook principal
 * const { data: messages } = useChatMessages(chatId);
 * const sendMessage = useSendMessage();
 * const { isConnected, sendTypingIndicator } = useChatRealtime(teamId);
 *
 * // ===== NOTIFICATIONS =====
 * const { data: notifications } = useNotifications();
 * const unreadCount = useUnreadNotificationCount();
 * const markAsRead = useMarkNotificationAsRead();
 *
 * // ===== PUBLIC PROFILES =====
 * const { data: profile } = usePublicProfile(userId);
 * const { data: profiles } = usePublicProfiles();
 *
 * // ===== ADMIN =====
 * const { data: isAdmin } = useIsAdmin();
 * const { data: users } = useAllUsers();
 * const { data: adminStats } = useAdminStats();
 *
 * // ===== STORAGE =====
 * const uploadFile = useUploadFile();
 * const { data: files } = useFiles();
 * const { getViewUrl, getDownloadUrl } = useFileUrls();
 */
