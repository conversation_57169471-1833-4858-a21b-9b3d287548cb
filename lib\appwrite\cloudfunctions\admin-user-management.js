/**
 * Appwrite Cloud Function: Admin User Management
 *
 * Handles advanced user management operations that require admin privileges:
 * - Promote users to admin/moderator
 * - Demote admin/moderator users
 * - Manage user roles and permissions
 * - Bulk user operations
 *
 * CONFIGURAÇÃO DE PERMISSÕES NO APPWRITE CONSOLE:
 * 1. Acesse Functions > admin-user-management > Settings > Execute Access
 * 2. Configure: ["role:admin"]
 * 3. Scopes necessários: ["users.read", "users.write"]
 *
 * VALIDAÇÃO DE ACESSO:
 * - Apenas usuários com role admin podem executar
 * - Verifica JWT automaticamente via Appwrite
 *
 * Method: POST
 * Headers: x-appwrite-user-jwt (automático quando autenticado)
 *
 * Body:
 * {
 *   "action": "promote_to_admin" | "promote_to_moderator" | "demote_to_user" | "update_permissions" | "bulk_update_roles",
 *   "userId": "user-id",
 *   "role": "admin" | "moderator" | "user" (para bulk),
 *   "permissions": ["read", "write", "admin"] (para update_permissions),
 *   "users": [{"userId": "...", "role": "..."}] (para bulk_update_roles)
 * }
 *
 * Response:
 * {
 *   "success": true,
 *   "message": "Operation completed successfully",
 *   "data": { ... }
 * }
 */

import { Client, Databases, Users } from 'node-appwrite';

// Initialize Appwrite client
const client = new Client();
const databases = new Databases(client);
const users = new Users(client);

// Configure client
client
  .setEndpoint(process.env.APPWRITE_FUNCTION_ENDPOINT || 'https://cloud.appwrite.io/v1')
  .setProject(process.env.APPWRITE_FUNCTION_PROJECT_ID)
  .setKey(process.env.APPWRITE_FUNCTION_API_KEY);

// Configuration
const DATABASE_ID = process.env.APPWRITE_FUNCTION_DATABASE_ID || 'main';
const USERS_COLLECTION_ID = process.env.APPWRITE_FUNCTION_USERS_COLLECTION_ID || 'users';

export default async ({ req, res, log, error }) => {

  try {
    // Verify request method
    if (req.method !== 'POST') {
      return res.json({
        success: false,
        error: 'Method not allowed. Use POST.',
      }, 405);
    }

    // Verificar autenticação - usar header do Appwrite
    const jwt = req.headers['x-appwrite-user-jwt'];
    if (!jwt) {
      return res.json({
        success: false,
        error: 'Token de autenticação necessário'
      }, 401);
    }

    // Parse request body
    const data = req.bodyJson || {};
    const { action, userId, role, permissions } = data;

    // Validate required parameters
    if (!action || !userId) {
      return res.json({
        success: false,
        error: 'Missing required parameters: action, userId',
      }, 400);
    }

    log(`Admin user management action: ${action} for user: ${userId}`);

    let result;

    switch (action) {
      case 'promote_to_admin':
        result = await promoteToAdmin(userId, log, error);
        break;

      case 'promote_to_moderator':
        result = await promoteToModerator(userId, log, error);
        break;

      case 'demote_to_user':
        result = await demoteToUser(userId, log, error);
        break;

      case 'update_permissions':
        result = await updateUserPermissions(userId, permissions, log, error);
        break;

      case 'bulk_update_roles':
        result = await bulkUpdateRoles(data.users, log, error);
        break;

      default:
        return res.json({
          success: false,
          error: `Unknown action: ${action}`,
        }, 400);
    }

    return res.json(result);

  } catch (err) {
    error('Function execution error:', err);
    return res.json({
      success: false,
      error: err.message || 'Internal server error',
    }, 500);
  }
};

/**
 * Promote user to admin role
 */
async function promoteToAdmin(userId, log, error) {
  try {
    // Update user labels to include 'admin'
    const user = await users.get(userId);
    const currentLabels = user.labels || [];

    if (!currentLabels.includes('admin')) {
      const newLabels = [...currentLabels.filter(label => label !== 'moderator'), 'admin'];
      await users.updateLabels(userId, newLabels);
    }

    // Update user profile in database
    try {
      await databases.updateDocument(
        DATABASE_ID,
        USERS_COLLECTION_ID,
        userId,
        {
          role: 'admin',
          permissions: ['read', 'write', 'admin', 'moderate'],
          updatedAt: new Date().toISOString(),
        }
      );
    } catch (dbError) {
      // If user document doesn't exist in our collection, create it
      if (dbError.code === 404) {
        await databases.createDocument(
          DATABASE_ID,
          USERS_COLLECTION_ID,
          userId,
          {
            userId: userId,
            role: 'admin',
            permissions: ['read', 'write', 'admin', 'moderate'],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }
        );
      } else {
        throw dbError;
      }
    }

    log(`User ${userId} promoted to admin successfully`);

    return {
      success: true,
      message: 'User promoted to admin successfully',
      data: {
        userId,
        role: 'admin',
        permissions: ['read', 'write', 'admin', 'moderate'],
      },
    };

  } catch (err) {
    error('Error promoting user to admin:', err);
    return {
      success: false,
      error: err.message || 'Failed to promote user to admin',
    };
  }
}

/**
 * Promote user to moderator role
 */
async function promoteToModerator(userId, log, error) {
  try {
    // Update user labels to include 'moderator'
    const user = await users.get(userId);
    const currentLabels = user.labels || [];

    if (!currentLabels.includes('moderator') && !currentLabels.includes('admin')) {
      const newLabels = [...currentLabels, 'moderator'];
      await users.updateLabels(userId, newLabels);
    }

    // Update user profile in database
    try {
      await databases.updateDocument(
        DATABASE_ID,
        USERS_COLLECTION_ID,
        userId,
        {
          role: 'moderator',
          permissions: ['read', 'write', 'moderate'],
          updatedAt: new Date().toISOString(),
        }
      );
    } catch (dbError) {
      // If user document doesn't exist in our collection, create it
      if (dbError.code === 404) {
        await databases.createDocument(
          DATABASE_ID,
          USERS_COLLECTION_ID,
          userId,
          {
            userId: userId,
            role: 'moderator',
            permissions: ['read', 'write', 'moderate'],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }
        );
      } else {
        throw dbError;
      }
    }

    log(`User ${userId} promoted to moderator successfully`);

    return {
      success: true,
      message: 'User promoted to moderator successfully',
      data: {
        userId,
        role: 'moderator',
        permissions: ['read', 'write', 'moderate'],
      },
    };

  } catch (err) {
    error('Error promoting user to moderator:', err);
    return {
      success: false,
      error: err.message || 'Failed to promote user to moderator',
    };
  }
}

/**
 * Demote user to regular user role
 */
async function demoteToUser(userId, log, error) {
  try {
    // Remove admin/moderator labels
    const user = await users.get(userId);
    const currentLabels = user.labels || [];
    const newLabels = currentLabels.filter(label => !['admin', 'moderator'].includes(label));

    await users.updateLabels(userId, newLabels);

    // Update user profile in database
    try {
      await databases.updateDocument(
        DATABASE_ID,
        USERS_COLLECTION_ID,
        userId,
        {
          role: 'user',
          permissions: ['read', 'write'],
          updatedAt: new Date().toISOString(),
        }
      );
    } catch (dbError) {
      // If user document doesn't exist in our collection, create it
      if (dbError.code === 404) {
        await databases.createDocument(
          DATABASE_ID,
          USERS_COLLECTION_ID,
          userId,
          {
            userId: userId,
            role: 'user',
            permissions: ['read', 'write'],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }
        );
      } else {
        throw dbError;
      }
    }

    log(`User ${userId} demoted to user successfully`);

    return {
      success: true,
      message: 'User demoted to regular user successfully',
      data: {
        userId,
        role: 'user',
        permissions: ['read', 'write'],
      },
    };

  } catch (err) {
    error('Error demoting user:', err);
    return {
      success: false,
      error: err.message || 'Failed to demote user',
    };
  }
}

/**
 * Update user permissions
 */
async function updateUserPermissions(userId, permissions, log, error) {
  try {
    if (!Array.isArray(permissions)) {
      throw new Error('Permissions must be an array');
    }

    // Update user profile in database
    await databases.updateDocument(
      DATABASE_ID,
      USERS_COLLECTION_ID,
      userId,
      {
        permissions,
        updatedAt: new Date().toISOString(),
      }
    );

    log(`User ${userId} permissions updated successfully`);

    return {
      success: true,
      message: 'User permissions updated successfully',
      data: {
        userId,
        permissions,
      },
    };

  } catch (err) {
    error('Error updating user permissions:', err);
    return {
      success: false,
      error: err.message || 'Failed to update user permissions',
    };
  }
}

/**
 * Bulk update user roles
 */
async function bulkUpdateRoles(usersData, log, error) {
  try {
    if (!Array.isArray(usersData)) {
      throw new Error('Users data must be an array');
    }

    const results = [];

    for (const userData of usersData) {
      const { userId, role } = userData;

      try {
        let result;

        switch (role) {
          case 'admin':
            result = await promoteToAdmin(userId, log, error);
            break;
          case 'moderator':
            result = await promoteToModerator(userId, log, error);
            break;
          case 'user':
            result = await demoteToUser(userId, log, error);
            break;
          default:
            result = {
              success: false,
              error: `Invalid role: ${role}`,
            };
        }

        results.push({
          userId,
          ...result,
        });

      } catch (err) {
        results.push({
          userId,
          success: false,
          error: err.message,
        });
      }
    }

    log(`Bulk role update completed for ${usersData.length} users`);

    return {
      success: true,
      message: 'Bulk role update completed',
      data: results,
    };

  } catch (err) {
    error('Error in bulk role update:', err);
    return {
      success: false,
      error: err.message || 'Failed to perform bulk role update',
    };
  }
}
