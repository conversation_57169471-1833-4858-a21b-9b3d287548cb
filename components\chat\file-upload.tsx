import { useRef, useState } from 'react';
import { Button } from '../ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '../ui/popover';
import { Paperclip, Image, FileText, Upload, X } from 'lucide-react';
import { useUploadFile } from '../../hooks/use-api';
import { toast } from 'sonner';

interface FileUploadProps {
  onFileUpload: (fileData: { fileId: string; url: string; fileName: string; fileType: string; fileSize: number }) => void;
  disabled?: boolean;
}

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
const ALLOWED_FILE_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'text/plain',
  ...ALLOWED_IMAGE_TYPES
];

export function FileUpload({ onFileUpload, disabled }: FileUploadProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const imageInputRef = useRef<HTMLInputElement>(null);

  const uploadMutation = useUploadFile();

  const handleFileSelect = async (file: File, type: 'image' | 'file') => {
    // Validar tamanho
    if (file.size > MAX_FILE_SIZE) {
      toast.error('Arquivo muito grande. Máximo 10MB permitido.');
      return;
    }

    // Validar tipo
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      toast.error('Tipo de arquivo não permitido.');
      return;
    }

    // Se for imagem, validar tipos específicos
    if (type === 'image' && !ALLOWED_IMAGE_TYPES.includes(file.type)) {
      toast.error('Tipo de imagem não permitido. Use JPEG, PNG, GIF ou WebP.');
      return;
    }

    setIsUploading(true);
    setIsOpen(false);

    try {
      const result = await uploadMutation.mutateAsync({ file });

      onFileUpload({
        fileId: result.$id,
        url: `${window.location.origin}/api/files/${result.$id}/view`,
        fileName: file.name,
        fileType: file.type,
        fileSize: file.size,
      });

      toast.success(`${type === 'image' ? 'Imagem' : 'Arquivo'} enviado com sucesso!`);
    } catch (error) {
      console.error('Erro no upload:', error);
      toast.error(`Erro ao enviar ${type === 'image' ? 'imagem' : 'arquivo'}`);
    } finally {
      setIsUploading(false);
    }
  };

  const handleImageUpload = () => {
    imageInputRef.current?.click();
  };

  const handleFileUpload = () => {
    fileInputRef.current?.click();
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="shrink-0"
            disabled={disabled || isUploading}
          >
            {isUploading ? (
              <Upload className="h-4 w-4 animate-spin" />
            ) : (
              <Paperclip className="h-4 w-4" />
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-64 p-0" align="start" side="top">
          <div className="p-4 space-y-2">
            <div className="text-sm font-medium mb-3">Enviar arquivo</div>

            <Button
              variant="ghost"
              className="w-full justify-start gap-3 h-auto p-3"
              onClick={handleImageUpload}
            >
              <div className="p-2 bg-blue-100 rounded-lg dark:bg-blue-900/20">
                <Image className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="text-left">
                <div className="font-medium">Imagem</div>
                <div className="text-xs text-muted-foreground">
                  JPEG, PNG, GIF, WebP
                </div>
              </div>
            </Button>

            <Button
              variant="ghost"
              className="w-full justify-start gap-3 h-auto p-3"
              onClick={handleFileUpload}
            >
              <div className="p-2 bg-green-100 rounded-lg dark:bg-green-900/20">
                <FileText className="h-4 w-4 text-green-600 dark:text-green-400" />
              </div>
              <div className="text-left">
                <div className="font-medium">Documento</div>
                <div className="text-xs text-muted-foreground">
                  PDF, DOC, TXT
                </div>
              </div>
            </Button>

            <div className="pt-2 border-t">
              <div className="text-xs text-muted-foreground text-center">
                Máximo {formatFileSize(MAX_FILE_SIZE)}
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {/* Hidden file inputs */}
      <input
        ref={imageInputRef}
        type="file"
        accept={ALLOWED_IMAGE_TYPES.join(',')}
        className="hidden"
        onChange={(e) => {
          const file = e.target.files?.[0];
          if (file) {
            handleFileSelect(file, 'image');
          }
          e.target.value = '';
        }}
      />

      <input
        ref={fileInputRef}
        type="file"
        accept={ALLOWED_FILE_TYPES.join(',')}
        className="hidden"
        onChange={(e) => {
          const file = e.target.files?.[0];
          if (file) {
            handleFileSelect(file, 'file');
          }
          e.target.value = '';
        }}
      />
    </>
  );
}
