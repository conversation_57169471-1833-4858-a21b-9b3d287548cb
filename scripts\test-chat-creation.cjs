// Script para testar criação de chat e mensagens
require('dotenv').config();

const { Client, Databases, ID, Query } = require('node-appwrite');

// Configuração do cliente
const client = new Client();
client
  .setEndpoint(process.env.APPWRITE_ENDPOINT)
  .setProject(process.env.APPWRITE_PROJECT_ID)
  .setKey(process.env.APPWRITE_API_KEY);

const databases = new Databases(client);
const DATABASE_ID = process.env.APPWRITE_DATABASE_ID;

async function testChatCreation() {
  try {
    console.log('🧪 Testando criação de chat e mensagens...\n');

    // 1. Criar um chat de teste
    console.log('1. Criando chat de teste...');
    const chatData = {
      teamId: 'test-team-123',
      name: 'Chat de Teste',
      description: 'Chat criado para teste',
      isPrivate: false,
      isActive: true,
      members: ['test-user-123'],
      lastActivity: new Date().toISOString(),
      unreadCount: 0,
      allowFileSharing: true,
      allowReactions: true,
      retentionDays: 30,
      userId: 'test-user-123',
      createdBy: 'test-user-123',
      isDeleted: false,
    };

    const chat = await databases.createDocument(
      DATABASE_ID,
      process.env.NEXT_PUBLIC_APPWRITE_TEAM_CHATS_ID,
      ID.unique(),
      chatData
    );

    console.log('✅ Chat criado:', chat.$id);

    // 2. Tentar criar uma mensagem usando o relacionamento
    console.log('\n2. Criando mensagem com relacionamento...');
    const messageData = {
      content: 'Mensagem de teste',
      senderId: 'test-user-123',
      senderName: 'Usuário Teste',
      teamId: 'test-team-123',
      type: 'text',
      status: 'sent',
      userId: 'test-user-123',
      createdBy: 'test-user-123',
      isDeleted: false,
      // Relacionamento: especificar o chat pai
      chat: chat.$id, // ID do chat pai
    };

    const message = await databases.createDocument(
      DATABASE_ID,
      process.env.NEXT_PUBLIC_APPWRITE_CHAT_MESSAGES_ID,
      ID.unique(),
      messageData
    );

    console.log('✅ Mensagem criada:', message.$id);

    // 3. Verificar se o relacionamento funcionou
    console.log('\n3. Verificando relacionamento...');
    const chatWithMessages = await databases.getDocument(
      DATABASE_ID,
      process.env.NEXT_PUBLIC_APPWRITE_TEAM_CHATS_ID,
      chat.$id
    );

    console.log('📋 Chat com mensagens:', {
      chatId: chatWithMessages.$id,
      messagesCount: chatWithMessages.messages?.length || 0,
      messages: chatWithMessages.messages?.map(m => ({
        id: m.$id,
        content: m.content,
        senderId: m.senderId
      })) || []
    });

    // 4. Testar query de mensagens por chat
    console.log('\n4. Testando query de mensagens por chat...');
    const messagesResponse = await databases.listDocuments(
      DATABASE_ID,
      process.env.NEXT_PUBLIC_APPWRITE_CHAT_MESSAGES_ID,
      [Query.equal('chat', chat.$id)]
    );

    console.log('📋 Mensagens encontradas:', messagesResponse.documents.length);

    // 5. Limpeza - deletar documentos de teste
    console.log('\n5. Limpando dados de teste...');
    await databases.deleteDocument(
      DATABASE_ID,
      process.env.NEXT_PUBLIC_APPWRITE_CHAT_MESSAGES_ID,
      message.$id
    );
    console.log('🗑️ Mensagem deletada');

    await databases.deleteDocument(
      DATABASE_ID,
      process.env.NEXT_PUBLIC_APPWRITE_TEAM_CHATS_ID,
      chat.$id
    );
    console.log('🗑️ Chat deletado');

    console.log('\n🎉 Teste concluído com sucesso!');

  } catch (error) {
    console.error('❌ Erro no teste:', error);

    // Mostrar detalhes do erro
    if (error.code) {
      console.error('Código do erro:', error.code);
    }
    if (error.type) {
      console.error('Tipo do erro:', error.type);
    }
    if (error.response) {
      console.error('Resposta do erro:', error.response);
    }
  }
}

// Executar teste
testChatCreation();
