import { useEffect } from 'react';
import { useSnapshot } from 'valtio';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '../ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../ui/form';
import { Input } from '../ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';

import { useAuth } from '../../hooks/use-auth';
import { useUpdateClient, useUploadFile } from '../../hooks/use-api';
import { clientModalsStore } from '../../lib/stores/client-modals';
import { clientFormSchema, type UpdateClientData, type ClientFormData } from '../../schemas/clients';
import { getFileView } from '../../lib/appwrite/functions/storage';
import { AvatarUpload } from './avatar-upload';
import { TagsInput } from './tags-input';

export function ClientEditModal() {
  const { user } = useAuth();
  const snap = useSnapshot(clientModalsStore);
  const updateClientMutation = useUpdateClient();
  const uploadFileMutation = useUploadFile();

  // Form setup
  const form = useForm({
    resolver: zodResolver(clientFormSchema),
    defaultValues: {
      userId: user?.$id || '',
      teamId: '',
      name: '',
      email: '',
      phone: '',
      document: '',
      type: 'pessoa_fisica',
      company: '',
      companyDocument: '',
      status: 'ativo',
      priority: 'media',
      tags: [],
      avatar: '',
    },
  });

  // Reset form when modal opens with client data
  useEffect(() => {
    if (snap.edit.isOpen && snap.edit.client) {
      const client = snap.edit.client;
      form.reset({
        userId: client.userId || user?.$id || '',
        teamId: client.teamId || '',
        name: client.name || '',
        email: client.email || '',
        phone: client.phone || '',
        document: client.document || '',
        type: client.type || 'pessoa_fisica',
        company: client.company || '',
        companyDocument: client.companyDocument || '',
        status: client.status || 'ativo',
        priority: client.priority || 'media',
        tags: client.tags ? [...client.tags] : [],
        avatar: client.avatar || '',
      });
    }
  }, [snap.edit.isOpen, snap.edit.client, form, user?.$id]);

  const onSubmit = async (data: ClientFormData) => {
    if (!user?.$id || !snap.edit.client) return;

    try {
      let avatarUrl = '';

      // Se há um arquivo de avatar, fazer upload primeiro
      if (data.avatar instanceof File) {
        const uploadResult = await uploadFileMutation.mutateAsync({
          file: data.avatar
        });
        avatarUrl = getFileView(uploadResult.$id).toString();
      } else if (typeof data.avatar === 'string') {
        avatarUrl = data.avatar;
      }

      const updateData: UpdateClientData = {
        ...data,
        avatar: avatarUrl,
        updatedBy: user.$id,
      };

      await updateClientMutation.mutateAsync({
        id: snap.edit.client.$id,
        data: updateData,
      });

      // Fechar modal após sucesso
      clientModalsStore.edit.isOpen = false;
      clientModalsStore.edit.client = null;
    } catch (error) {
      console.error('Error updating client:', error);
    }
  };

  const handleClose = () => {
    clientModalsStore.edit.isOpen = false;
    clientModalsStore.edit.client = null;
  };

  const isLoading = form.formState.isSubmitting || updateClientMutation.isPending;

  return (
    <Dialog open={snap.edit.isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Editar Cliente</DialogTitle>
          <DialogDescription>
            Edite as informações do cliente {snap.edit.client?.name}.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Avatar */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Avatar</h3>
              <div className="flex justify-center">
                <FormField
                  control={form.control}
                  name="avatar"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <AvatarUpload
                          value={field.value}
                          onChange={field.onChange}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Informações Básicas */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Informações Básicas</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Nome <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="Nome do cliente" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Telefone</FormLabel>
                      <FormControl>
                        <Input placeholder="(11) 99999-9999" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Tipo <span className="text-red-500">*</span>
                      </FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o tipo" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="pessoa_fisica">Pessoa Física</SelectItem>
                          <SelectItem value="pessoa_juridica">Pessoa Jurídica</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Campos específicos por tipo */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="document"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {form.watch('type') === 'pessoa_juridica' ? 'CNPJ' : 'CPF'}
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder={form.watch('type') === 'pessoa_juridica' ? '00.000.000/0000-00' : '000.000.000-00'}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {form.watch('type') === 'pessoa_juridica' && (
                  <>
                    <FormField
                      control={form.control}
                      name="company"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Nome da Empresa <span className="text-red-500">*</span>
                          </FormLabel>
                          <FormControl>
                            <Input placeholder="Nome da empresa" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="companyDocument"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>CNPJ da Empresa</FormLabel>
                          <FormControl>
                            <Input placeholder="00.000.000/0000-00" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}
              </div>
            </div>

            {/* Status e Prioridade */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Status e Prioridade</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="ativo">Ativo</SelectItem>
                          <SelectItem value="inativo">Inativo</SelectItem>
                          <SelectItem value="prospecto">Prospecto</SelectItem>
                          <SelectItem value="arquivado">Arquivado</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="priority"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Prioridade</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione a prioridade" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="baixa">Baixa</SelectItem>
                          <SelectItem value="media">Média</SelectItem>
                          <SelectItem value="alta">Alta</SelectItem>
                          <SelectItem value="critica">Crítica</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Tags */}
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="tags"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tags</FormLabel>
                    <FormControl>
                      <TagsInput
                        value={field.value || []}
                        onChange={field.onChange}
                        disabled={isLoading}
                        placeholder="Digite uma tag e pressione Enter"
                        maxTags={8}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleClose}>
                Cancelar
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Salvando...' : 'Salvar Alterações'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
