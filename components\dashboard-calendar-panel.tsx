/**
 * Dashboard Calendar Panel Component
 * Displays upcoming events and calendar summary
 */

import React from 'react';
import Link from 'next/link';
import { format, formatDistanceToNow, isToday, isTomorrow, addDays, startOfDay, endOfDay } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from './ui/card';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { Skeleton } from './ui/skeleton';
import {
  Calendar,
  ArrowRight,
  Plus,
  Clock,
  MapPin,
  Users,
  AlertCircle,
  CheckCircle,
  CalendarDays,
} from 'lucide-react';
import { useEvents } from '../hooks/use-api';
import { useIsMobile } from '../hooks/use-mobile';
import type { Event, EventPriority, EventStatus } from '@/schemas/events';

// ============================================================================
// TYPES
// ============================================================================

interface DashboardCalendarPanelProps {
  limit?: number;
  showHeader?: boolean;
  className?: string;
  teamId?: string;
}

// ============================================================================
// COMPONENTS
// ============================================================================

function EventItem({ event }: { event: Event }) {
  const eventDate = new Date(event.startDate);
  const eventEndDate = new Date(event.endDate);

  const getPriorityColor = (priority: EventPriority) => {
    switch (priority) {
      case 'critica':
        return 'bg-red-100 text-red-800';
      case 'alta':
        return 'bg-orange-100 text-orange-800';
      case 'media':
        return 'bg-yellow-100 text-yellow-800';
      case 'baixa':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: EventStatus) => {
    switch (status) {
      case 'concluido':
        return <CheckCircle className="h-3 w-3 text-green-600" />;
      case 'cancelado':
        return <AlertCircle className="h-3 w-3 text-red-600" />;
      case 'em_andamento':
        return <Clock className="h-3 w-3 text-orange-600" />;
      default:
        return <Clock className="h-3 w-3 text-blue-600" />;
    }
  };

  const getDateLabel = () => {
    if (isToday(eventDate)) {
      return 'Hoje';
    } else if (isTomorrow(eventDate)) {
      return 'Amanhã';
    } else {
      return format(eventDate, 'EEE, dd/MM', { locale: ptBR });
    }
  };

  return (
    <div className="flex items-start gap-3 p-3 hover:bg-muted/50 rounded-lg transition-colors">
      <div
        className="w-3 h-3 rounded-full flex-shrink-0 mt-2"
        style={{ backgroundColor: event.color || '#3B82F6' }}
      />

      <div className="flex-1 min-w-0">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-sm truncate">{event.title}</h4>
            {event.description && (
              <p className="text-xs text-muted-foreground truncate mt-1">
                {event.description}
              </p>
            )}
          </div>

          <div className="flex items-center gap-1 ml-2">
            {getStatusIcon(event.status)}
          </div>
        </div>

        <div className="flex items-center gap-3 mt-2 text-xs text-muted-foreground">
          <div className="flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            <span>{getDateLabel()}</span>
          </div>

          {!event.allDay && (
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              <span>
                {format(eventDate, 'HH:mm')}
                {eventEndDate && format(eventEndDate, 'HH:mm') !== format(eventDate, 'HH:mm') &&
                  ` - ${format(eventEndDate, 'HH:mm')}`
                }
              </span>
            </div>
          )}

          {event.location && (
            <div className="flex items-center gap-1">
              <MapPin className="h-3 w-3" />
              <span className="truncate max-w-[100px]">{event.location}</span>
            </div>
          )}
        </div>

        <div className="flex items-center gap-2 mt-2">
          <Badge variant="outline" className={`text-xs ${getPriorityColor(event.priority)}`}>
            {event.priority}
          </Badge>

          <Badge variant="outline" className="text-xs">
            {event.type}
          </Badge>

          {event.attendees && event.attendees.length > 0 && (
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <Users className="h-3 w-3" />
              <span>{event.attendees.length}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

function CalendarSkeleton({ count = 5 }: { count?: number }) {
  return (
    <div className="space-y-3">
      {Array.from({ length: count }).map((_, i) => (
        <div key={i} className="flex items-start gap-3 p-3">
          <Skeleton className="w-3 h-3 rounded-full" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-48" />
            <Skeleton className="h-3 w-32" />
            <div className="flex gap-2">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-4 w-12" />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function DashboardCalendarPanel({
  limit = 6,
  showHeader = true,
  className = "",
  teamId
}: DashboardCalendarPanelProps) {
  const { data: events = [], isLoading, error } = useEvents(teamId);
  const isMobile = useIsMobile();

  // Filter and sort upcoming events
  const upcomingEvents = React.useMemo(() => {
    const now = new Date();
    const nextWeek = addDays(now, 7);

    return events
      .filter(event => {
        const eventDate = new Date(event.startDate);
        return eventDate >= startOfDay(now) && eventDate <= endOfDay(nextWeek);
      })
      .sort((a, b) => new Date(a.startDate).getTime() - new Date(b.startDate).getTime())
      .slice(0, limit);
  }, [events, limit]);

  // Calculate stats
  const stats = React.useMemo(() => {
    const now = new Date();
    const today = startOfDay(now);
    const tomorrow = startOfDay(addDays(now, 1));
    const nextWeek = endOfDay(addDays(now, 7));

    const todayEvents = events.filter(event => {
      const eventDate = new Date(event.startDate);
      return eventDate >= today && eventDate < tomorrow;
    });

    const thisWeekEvents = events.filter(event => {
      const eventDate = new Date(event.startDate);
      return eventDate >= today && eventDate <= nextWeek;
    });

    const confirmedEvents = thisWeekEvents.filter(event => event.status === 'concluido');
    const pendingEvents = thisWeekEvents.filter(event => event.status === 'agendado');

    return {
      totalEvents: events.length,
      todayEvents: todayEvents.length,
      thisWeekEvents: thisWeekEvents.length,
      confirmedEvents: confirmedEvents.length,
      pendingEvents: pendingEvents.length,
    };
  }, [events]);

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <Calendar className="h-8 w-8 mx-auto mb-2" />
            <p>Erro ao carregar eventos</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      {showHeader && (
        <CardHeader className="pb-3">
          <div className={`flex ${isMobile ? 'flex-col gap-3' : 'items-center justify-between'}`}>
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-primary" />
              <div className="flex-1 min-w-0">
                <CardTitle className={`${isMobile ? 'text-base' : 'text-lg'}`}>Próximos Eventos</CardTitle>
                <CardDescription className={isMobile ? 'text-xs' : ''}>
                  {isLoading ? 'Carregando...' : `${stats.thisWeekEvents} evento(s) esta semana`}
                </CardDescription>
              </div>
            </div>

            <div className="flex gap-2">
              <Button variant="outline" size="sm" asChild>
                <Link href="/dashboard/calendar">
                  Ver calendário
                  <ArrowRight className="h-4 w-4 ml-1" />
                </Link>
              </Button>
              <Button size="sm" asChild>
                <Link href="/dashboard/calendar">
                  <Plus className="h-4 w-4 mr-1" />
                  Novo
                </Link>
              </Button>
            </div>
          </div>

          {/* Stats */}
          {!isLoading && stats.totalEvents > 0 && (
            <div className={`grid gap-3 mt-4 ${isMobile ? 'grid-cols-2' : 'grid-cols-2 md:grid-cols-4'}`}>
              <div className="text-center">
                <div className={`font-bold text-blue-600 ${isMobile ? 'text-lg' : 'text-2xl'}`}>{stats.todayEvents}</div>
                <div className="text-xs text-muted-foreground">Hoje</div>
              </div>
              <div className="text-center">
                <div className={`font-bold text-green-600 ${isMobile ? 'text-lg' : 'text-2xl'}`}>{stats.confirmedEvents}</div>
                <div className="text-xs text-muted-foreground">Confirmados</div>
              </div>
              <div className="text-center">
                <div className={`font-bold text-orange-600 ${isMobile ? 'text-lg' : 'text-2xl'}`}>{stats.pendingEvents}</div>
                <div className="text-xs text-muted-foreground">Pendentes</div>
              </div>
              <div className="text-center">
                <div className={`font-bold text-purple-600 ${isMobile ? 'text-lg' : 'text-2xl'}`}>{stats.thisWeekEvents}</div>
                <div className="text-xs text-muted-foreground">Esta semana</div>
              </div>
            </div>
          )}
        </CardHeader>
      )}

      <CardContent className={showHeader ? "pt-0" : "p-6"}>
        {isLoading ? (
          <CalendarSkeleton count={limit} />
        ) : upcomingEvents.length === 0 ? (
          <div className="text-center py-8">
            <CalendarDays className="h-12 w-12 mx-auto text-muted-foreground mb-3" />
            <h3 className="text-sm font-medium text-muted-foreground mb-2">
              Nenhum evento próximo
            </h3>
            <p className="text-xs text-muted-foreground mb-4">
              Crie eventos para organizar sua agenda
            </p>
            <Button size="sm" asChild>
              <Link href="/dashboard/calendar">
                <Plus className="h-4 w-4 mr-1" />
                Criar Evento
              </Link>
            </Button>
          </div>
        ) : (
          <div className="space-y-1">
            {upcomingEvents.map((event) => (
              <EventItem key={event.$id} event={event} />
            ))}

            {events.length > limit && (
              <div className="pt-3 border-t">
                <Button variant="ghost" size="sm" className="w-full" asChild>
                  <Link href="/dashboard/calendar">
                    Ver mais {events.length - limit} evento(s)
                    <ArrowRight className="h-4 w-4 ml-1" />
                  </Link>
                </Button>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
