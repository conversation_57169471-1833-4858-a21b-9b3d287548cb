/**
 * Client-related type definitions and validation schemas
 * Uses Zod as single source of truth for type safety and validation
 * Sempre que alterar algo aqui, atualize o schema no const.cjs
 */

import { z } from 'zod';

// ============================================================================
// BASE ENUM SCHEMAS
// ============================================================================

export const clientStatusSchema = z.enum(['ativo', 'inativo', 'prospecto', 'arquivado']);
export const clientTypeSchema = z.enum(['pessoa_fisica', 'pessoa_juridica']);
export const clientPrioritySchema = z.enum(['baixa', 'media', 'alta', 'critica']);

// Address schema (campos individuais para compatibilidade com Appwrite)
export const clientAddressSchema = z.object({
  street: z.string().min(1, 'Rua é obrigatória').max(200, 'Rua muito longa'),
  number: z.string().min(1, 'Número é obrigatório').max(20, 'Número muito longo'),
  complement: z.string().max(100, 'Complemento muito longo').optional(),
  neighborhood: z.string().min(1, 'Bairro é obrigatório').max(100, 'Bairro muito longo'),
  city: z.string().min(1, 'Cidade é obrigatória').max(100, 'Cidade muito longa'),
  state: z.string().min(2, 'Estado deve ter pelo menos 2 caracteres').max(50, 'Estado muito longo'),
  zipCode: z.string().min(8, 'CEP deve ter 8 dígitos').max(10, 'CEP muito longo'),
  country: z.string().min(1, 'País é obrigatório').max(50, 'País muito longo').default('Brasil'),
});

// ============================================================================
// SCHEMA PRINCIPAL - Single source of truth (compatível com const.cjs)
// ============================================================================

export const clientSchema = z.object({
  $id: z.string().min(1, 'ID é obrigatório'),
  userId: z.string().min(1, 'ID do usuário é obrigatório'),
  teamId: z.string().optional(),
  createdBy: z.string().min(1, 'ID do criador é obrigatório'),
  updatedBy: z.string().optional(),
  $createdAt: z.string().min(1, 'Data de criação é obrigatória'),
  $updatedAt: z.string().min(1, 'Data de atualização é obrigatória'),

  // Informações básicas
  name: z.string()
    .min(2, 'Nome deve ter pelo menos 2 caracteres')
    .max(200, 'Nome muito longo')
    .trim(),
  email: z.string()
    .email('Email inválido')
    .max(200, 'Email muito longo')
    .toLowerCase()
    .optional()
    .or(z.literal('')),
  phone: z.string()
    .max(50, 'Telefone muito longo')
    .optional()
    .or(z.literal('')),
  document: z.string()
    .max(50, 'Documento muito longo')
    .optional()
    .or(z.literal('')),
  type: clientTypeSchema,

  // Informações da empresa
  company: z.string()
    .max(200, 'Nome da empresa muito longo')
    .optional()
    .or(z.literal('')),
  companyDocument: z.string()
    .max(50, 'CNPJ muito longo')
    .optional()
    .or(z.literal('')),

  // Status e classificação
  status: clientStatusSchema.default('ativo'),
  priority: clientPrioritySchema.default('media'),

  // Informações adicionais
  tags: z.array(z.string().trim().min(1)).default([]),
  avatar: z.string()
    .url('URL do avatar inválida')
    .max(200, 'URL do avatar muito longa')
    .optional()
    .or(z.literal('')),

});

// ============================================================================
// SCHEMAS DERIVADOS - Apenas 3 schemas essenciais
// ============================================================================

// Schema para formulários (sem campos de auditoria)
export const clientFormSchema = clientSchema.omit({
  $id: true,
  $createdAt: true,
  $updatedAt: true,
  createdBy: true,
  updatedBy: true,
}).extend({
  // Avatar pode ser File (novo upload) ou string (URL existente)
  avatar: z.union([
    z.instanceof(File),
    z.string().url('URL do avatar inválida').optional(),
    z.literal('')
  ]).optional(),
}).refine((data) => {
  // Validação condicional: se for pessoa jurídica, empresa é obrigatória
  if (data.type === 'pessoa_juridica' && (!data.company || data.company.trim() === '')) {
    return false;
  }
  return true;
}, {
  message: 'Nome da empresa é obrigatório para pessoa jurídica',
  path: ['company'],
});

// Schema para atualização (todos os campos opcionais)
export const updateClientSchema = clientSchema.partial().extend({
  updatedBy: z.string().min(1, 'ID do atualizador é obrigatório'),
});


// ============================================================================
// DERIVED TYPESCRIPT TYPES
// ============================================================================

export type ClientStatus = z.infer<typeof clientStatusSchema>;
export type ClientType = z.infer<typeof clientTypeSchema>;
export type ClientPriority = z.infer<typeof clientPrioritySchema>;
export type ClientAddress = z.infer<typeof clientAddressSchema>;
export type ClientFormData = z.infer<typeof clientFormSchema>;
export type CreateClientData = z.infer<typeof clientFormSchema>;
export type UpdateClientData = z.infer<typeof updateClientSchema>;
export type Client = z.infer<typeof clientSchema>;

// Filters type for client filtering
export interface ClientFilters {
  status?: ClientStatus[];
  type?: ClientType[];
  priority?: ClientPriority[];
  search?: string;
  teamId?: string;
}

