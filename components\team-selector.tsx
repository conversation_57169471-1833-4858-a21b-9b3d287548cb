/**
 * <PERSON><PERSON><PERSON> de Times
 * Permite ao usuário trocar entre os times que participa
 */

import { useState } from 'react';
import { Check, ChevronsUpDown, Users, Crown, Shield } from 'lucide-react';
import { cn } from '../lib/utils';
import { Button } from './ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from './ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from './ui/popover';
import { Badge } from './ui/badge';
import { Avatar, AvatarFallback } from './ui/avatar';
import { useTeamContext } from '../contexts/team-context';
import type { Team } from '@/schemas/teams';

interface TeamSelectorProps {
  className?: string;
  variant?: 'default' | 'compact';
}

export function TeamSelector({ className, variant = 'default' }: TeamSelectorProps) {
  const [open, setOpen] = useState(false);
  const { 
    currentTeam, 
    currentTeamId, 
    teams, 
    switchTeam, 
    isLoadingTeams,
    currentUserMembership 
  } = useTeamContext();

  // Se não há times ou está carregando, não mostrar
  if (isLoadingTeams || teams.length === 0) {
    return null;
  }

  // Se só há um time, não precisa mostrar seletor
  if (teams.length === 1 && variant === 'compact') {
    return null;
  }

  const handleSelectTeam = (teamId: string) => {
    switchTeam(teamId);
    setOpen(false);
  };

  const getUserRoleInTeam = (team: Team) => {
    // Aqui você pode implementar lógica para buscar o role do usuário no time
    // Por enquanto, vamos usar uma lógica simples baseada no membership atual
    if (currentTeamId === team.$id && currentUserMembership) {
      if (currentUserMembership.roles.includes('owner')) {
        return { type: 'owner', label: 'Proprietário', icon: Crown };
      }
      if (currentUserMembership.roles.includes('admin')) {
        return { type: 'admin', label: 'Administrador', icon: Shield };
      }
      return { type: 'member', label: 'Membro', icon: Users };
    }
    return { type: 'member', label: 'Membro', icon: Users };
  };

  const getTeamInitials = (teamName: string) => {
    return teamName
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  if (variant === 'compact') {
    return (
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            role="combobox"
            aria-expanded={open}
            className={cn("w-full justify-between", className)}
            size="sm"
          >
            <div className="flex items-center gap-2 min-w-0">
              <Avatar className="h-6 w-6">
                <AvatarFallback className="text-xs">
                  {currentTeam ? getTeamInitials(currentTeam.name) : 'T'}
                </AvatarFallback>
              </Avatar>
              <span className="truncate text-sm">
                {currentTeam?.name || 'Selecionar time'}
              </span>
            </div>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[300px] p-0">
          <Command>
            <CommandInput placeholder="Buscar time..." />
            <CommandList>
              <CommandEmpty>Nenhum time encontrado.</CommandEmpty>
              <CommandGroup>
                {teams.map((team) => {
                  const role = getUserRoleInTeam(team);
                  const RoleIcon = role.icon;
                  
                  return (
                    <CommandItem
                      key={team.$id}
                      value={team.name}
                      onSelect={() => handleSelectTeam(team.$id)}
                    >
                      <div className="flex items-center gap-3 w-full">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback>
                            {getTeamInitials(team.name)}
                          </AvatarFallback>
                        </Avatar>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <span className="font-medium truncate">
                              {team.name}
                            </span>
                            <Badge 
                              variant={role.type === 'owner' ? 'destructive' : 'secondary'}
                              className="text-xs"
                            >
                              <RoleIcon className="w-3 h-3 mr-1" />
                              {role.label}
                            </Badge>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {team.total} {team.total === 1 ? 'membro' : 'membros'}
                          </div>
                        </div>
                        
                        <Check
                          className={cn(
                            "ml-auto h-4 w-4",
                            currentTeamId === team.$id ? "opacity-100" : "opacity-0"
                          )}
                        />
                      </div>
                    </CommandItem>
                  );
                })}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    );
  }

  // Variant default - mais detalhado
  return (
    <div className={cn("space-y-2", className)}>
      <label className="text-sm font-medium">Time Ativo</label>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between h-auto p-3"
          >
            {currentTeam ? (
              <div className="flex items-center gap-3">
                <Avatar className="h-10 w-10">
                  <AvatarFallback>
                    {getTeamInitials(currentTeam.name)}
                  </AvatarFallback>
                </Avatar>
                <div className="text-left">
                  <div className="font-medium">{currentTeam.name}</div>
                  <div className="text-sm text-muted-foreground">
                    {currentTeam.total} {currentTeam.total === 1 ? 'membro' : 'membros'}
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex items-center gap-3">
                <Avatar className="h-10 w-10">
                  <AvatarFallback>T</AvatarFallback>
                </Avatar>
                <div className="text-left">
                  <div className="font-medium">Selecionar time</div>
                  <div className="text-sm text-muted-foreground">
                    Escolha um time para continuar
                  </div>
                </div>
              </div>
            )}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[400px] p-0">
          <Command>
            <CommandInput placeholder="Buscar time..." />
            <CommandList>
              <CommandEmpty>Nenhum time encontrado.</CommandEmpty>
              <CommandGroup>
                {teams.map((team) => {
                  const role = getUserRoleInTeam(team);
                  const RoleIcon = role.icon;
                  
                  return (
                    <CommandItem
                      key={team.$id}
                      value={team.name}
                      onSelect={() => handleSelectTeam(team.$id)}
                      className="p-3"
                    >
                      <div className="flex items-center gap-3 w-full">
                        <Avatar className="h-10 w-10">
                          <AvatarFallback>
                            {getTeamInitials(team.name)}
                          </AvatarFallback>
                        </Avatar>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-medium truncate">
                              {team.name}
                            </span>
                            <Badge 
                              variant={role.type === 'owner' ? 'destructive' : 'secondary'}
                              className="text-xs"
                            >
                              <RoleIcon className="w-3 h-3 mr-1" />
                              {role.label}
                            </Badge>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {team.total} {team.total === 1 ? 'membro' : 'membros'}
                          </div>
                        </div>
                        
                        <Check
                          className={cn(
                            "ml-auto h-4 w-4",
                            currentTeamId === team.$id ? "opacity-100" : "opacity-0"
                          )}
                        />
                      </div>
                    </CommandItem>
                  );
                })}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
