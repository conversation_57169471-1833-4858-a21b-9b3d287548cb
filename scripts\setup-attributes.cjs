// Script para configurar atributos e índices das coleções
require('dotenv').config();
const { Client, Databases } = require('node-appwrite');
const { collections } = require('./const.cjs');

// Inicializar cliente Appwrite
const client = new Client();
client
  .setEndpoint(process.env.APPWRITE_ENDPOINT)
  .setProject(process.env.APPWRITE_PROJECT_ID)
  .setKey(process.env.APPWRITE_API_KEY);

const databases = new Databases(client);
const DATABASE_ID = process.env.APPWRITE_DATABASE_ID;

// Funções para criar diferentes tipos de atributos
async function createStringAttribute(collectionId, name, size, required = false, default_value = null, array = false) {
  try {
    await databases.createStringAttribute(
      DATABASE_ID,
      collectionId,
      name,
      size,
      required,
      default_value,
      array
    );
    console.log(`✅ String attribute '${name}' created successfully`);
    return true;
  } catch (error) {
    if (error.code === 409) {
      console.log(`ℹ️ String attribute '${name}' already exists`);
      return true;
    } else {
      console.error(`❌ Error creating string attribute '${name}':`, error);
      return false;
    }
  }
}

async function createEmailAttribute(collectionId, name, required = false, default_value = null, array = false) {
  try {
    await databases.createEmailAttribute(
      DATABASE_ID,
      collectionId,
      name,
      required,
      default_value,
      array
    );
    console.log(`✅ Email attribute '${name}' created successfully`);
    return true;
  } catch (error) {
    if (error.code === 409) {
      console.log(`ℹ️ Email attribute '${name}' already exists`);
      return true;
    } else {
      console.error(`❌ Error creating email attribute '${name}':`, error);
      return false;
    }
  }
}

async function createEnumAttribute(collectionId, name, elements, required = false, default_value = null, array = false) {
  try {
    await databases.createEnumAttribute(
      DATABASE_ID,
      collectionId,
      name,
      elements,
      required,
      default_value,
      array
    );
    console.log(`✅ Enum attribute '${name}' created successfully`);
    return true;
  } catch (error) {
    if (error.code === 409) {
      console.log(`ℹ️ Enum attribute '${name}' already exists`);
      return true;
    } else {
      console.error(`❌ Error creating enum attribute '${name}':`, error);
      return false;
    }
  }
}

async function createBooleanAttribute(collectionId, name, required = false, default_value = null, array = false) {
  try {
    await databases.createBooleanAttribute(
      DATABASE_ID,
      collectionId,
      name,
      required,
      default_value,
      array
    );
    console.log(`✅ Boolean attribute '${name}' created successfully`);
    return true;
  } catch (error) {
    if (error.code === 409) {
      console.log(`ℹ️ Boolean attribute '${name}' already exists`);
      return true;
    } else {
      console.error(`❌ Error creating boolean attribute '${name}':`, error);
      return false;
    }
  }
}

async function createUrlAttribute(collectionId, name, required = false, default_value = null, array = false) {
  try {
    await databases.createUrlAttribute(
      DATABASE_ID,
      collectionId,
      name,
      required,
      default_value,
      array
    );
    console.log(`✅ URL attribute '${name}' created successfully`);
    return true;
  } catch (error) {
    if (error.code === 409) {
      console.log(`ℹ️ URL attribute '${name}' already exists`);
      return true;
    } else {
      console.error(`❌ Error creating URL attribute '${name}':`, error);
      return false;
    }
  }
}

async function createIntegerAttribute(collectionId, name, required = false, min = null, max = null, default_value = null, array = false) {
  try {
    await databases.createIntegerAttribute(
      DATABASE_ID,
      collectionId,
      name,
      required,
      min,
      max,
      default_value,
      array
    );
    console.log(`✅ Integer attribute '${name}' created successfully`);
    return true;
  } catch (error) {
    if (error.code === 409) {
      console.log(`ℹ️ Integer attribute '${name}' already exists`);
      return true;
    } else {
      console.error(`❌ Error creating integer attribute '${name}':`, error);
      return false;
    }
  }
}

async function createFloatAttribute(collectionId, name, required = false, min = null, max = null, default_value = null, array = false) {
  try {
    await databases.createFloatAttribute(
      DATABASE_ID,
      collectionId,
      name,
      required,
      min,
      max,
      default_value,
      array
    );
    console.log(`✅ Float attribute '${name}' created successfully`);
    return true;
  } catch (error) {
    if (error.code === 409) {
      console.log(`ℹ️ Float attribute '${name}' already exists`);
      return true;
    } else {
      console.error(`❌ Error creating float attribute '${name}':`, error);
      return false;
    }
  }
}

async function createDatetimeAttribute(collectionId, name, required = false, default_value = null, array = false) {
  try {
    await databases.createDatetimeAttribute(
      DATABASE_ID,
      collectionId,
      name,
      required,
      default_value,
      array
    );
    console.log(`✅ Datetime attribute '${name}' created successfully`);
    return true;
  } catch (error) {
    if (error.code === 409) {
      console.log(`ℹ️ Datetime attribute '${name}' already exists`);
      return true;
    } else {
      console.error(`❌ Error creating datetime attribute '${name}':`, error);
      return false;
    }
  }
}

async function createIndex(collectionId, attributeName, indexType) {
  try {
    await databases.createIndex(
      DATABASE_ID,
      collectionId,
      `${attributeName}_${indexType}`,
      indexType,
      [attributeName]
    );
    console.log(`✅ Index '${attributeName}_${indexType}' created successfully`);
    return true;
  } catch (error) {
    if (error.code === 409) {
      console.log(`ℹ️ Index '${attributeName}_${indexType}' already exists`);
      return true;
    } else {
      console.error(`❌ Error creating index '${attributeName}_${indexType}':`, error);
      return false;
    }
  }
}

async function setupCollectionAttributes(collection) {
  console.log(`\n🔄 Setting up attributes for collection: ${collection.name}`);

  // Get collection ID from environment variable
  const collectionId = process.env[collection.envKey];
  if (!collectionId) {
    console.error(`❌ Collection ID not found in environment variables for ${collection.name}`);
    return;
  }

  // Primeiro, criar todos os atributos
  const attributesWithIndexes = [];

  for (const attr of collection.attributes) {
    let attributeCreated = false;

    // Pular se for definição de índice
    if (attr.index && !attr.type) {
      continue;
    }

    switch (attr.type) {
      case 'string':
        attributeCreated = await createStringAttribute(
          collectionId,
          attr.name,
          attr.size,
          attr.required,
          attr.default,
          attr.array
        );
        break;
      case 'email':
        attributeCreated = await createEmailAttribute(
          collectionId,
          attr.name,
          attr.required,
          attr.default,
          attr.array
        );
        break;
      case 'enum':
        attributeCreated = await createEnumAttribute(
          collectionId,
          attr.name,
          attr.elements,
          attr.required,
          attr.default,
          attr.array
        );
        break;
      case 'boolean':
        attributeCreated = await createBooleanAttribute(
          collectionId,
          attr.name,
          attr.required,
          attr.default,
          attr.array
        );
        break;
      case 'url':
        attributeCreated = await createUrlAttribute(
          collectionId,
          attr.name,
          attr.required,
          attr.default,
          attr.array
        );
        break;
      case 'integer':
        attributeCreated = await createIntegerAttribute(
          collectionId,
          attr.name,
          attr.required,
          attr.min,
          attr.max,
          attr.default,
          attr.array
        );
        break;
      case 'float':
        attributeCreated = await createFloatAttribute(
          collectionId,
          attr.name,
          attr.required,
          attr.min,
          attr.max,
          attr.default,
          attr.array
        );
        break;
      case 'datetime':
        attributeCreated = await createDatetimeAttribute(
          collectionId,
          attr.name,
          attr.required,
          attr.default,
          attr.array
        );
        break;
      default:
        console.log(`⚠️ Unsupported attribute type: ${attr.type} for ${attr.name}`);
    }

    // Se o atributo foi criado com sucesso e tem um índice, adicionar à lista para criar depois
    if (attributeCreated && attr.index) {
      attributesWithIndexes.push({
        name: attr.name,
        indexType: attr.index.type
      });
    }
  }

  // Aguardar um momento para garantir que os atributos foram processados
  console.log(`⏳ Aguardando 2 segundos para garantir que os atributos foram processados...`);
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Depois, criar os índices para os atributos que foram criados com sucesso
  console.log(`🔍 Criando índices para a coleção ${collection.name}...`);
  for (const attr of attributesWithIndexes) {
    await createIndex(collectionId, attr.name, attr.indexType);
  }
}

async function setupAttributes() {
  try {
    console.log('🚀 Iniciando configuração dos atributos...');

    // Verificar configuração
    if (!process.env.APPWRITE_ENDPOINT || !process.env.APPWRITE_PROJECT_ID || !process.env.APPWRITE_API_KEY) {
      throw new Error('❌ Variáveis de ambiente do Appwrite não configuradas');
    }

    for (const collection of collections) {
      await setupCollectionAttributes(collection);
    }

    console.log('🎉 Configuração dos atributos concluída com sucesso!');
  } catch (error) {
    console.error('❌ Erro ao configurar atributos:', error);
    process.exit(1);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  setupAttributes();
}

module.exports = { setupAttributes };
