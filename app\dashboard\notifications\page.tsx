'use client';

/**
 * Notifications Page
 * Comprehensive notifications dashboard with filtering and management
 */

import React, { useState, useMemo } from 'react';
import {
  BellIcon,
  CheckCircle,
  AlertTriangle,
  AlertCircle,
  Info,
  X,
  Trash2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useNotifications } from '@/hooks/use-api';
import { cn } from '@/lib/utils';

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

function getNotificationIcon(type: string) {
  switch (type) {
    case 'success':
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    case 'warning':
      return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
    case 'error':
      return <AlertCircle className="h-5 w-5 text-red-500" />;
    default:
      return <Info className="h-5 w-5 text-blue-500" />;
  }
}

function formatTimestamp(timestamp: string) {
  const date = new Date(timestamp);
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) return 'Agora';
  if (diffInMinutes < 60) return `${diffInMinutes}m atrás`;
  if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h atrás`;
  if (diffInMinutes < 10080) return `${Math.floor(diffInMinutes / 1440)}d atrás`;

  return date.toLocaleDateString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export default function NotificationsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  const {
    notifications,
    unreadCount,
    isLoading,
    markAsRead,
    markAllAsRead,
    removeNotification
  } = useNotifications();

  // Filter notifications based on search and filters
  const filteredNotifications = useMemo(() => {
    if (!notifications) return [];

    return notifications.filter((notification: any) => {
      const matchesSearch = searchTerm === '' ||
        notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        notification.message.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesType = typeFilter === 'all' || notification.type === typeFilter;

      const matchesStatus = statusFilter === 'all' ||
        (statusFilter === 'read' && notification.read) ||
        (statusFilter === 'unread' && !notification.read);

      return matchesSearch && matchesType && matchesStatus;
    });
  }, [notifications, searchTerm, typeFilter, statusFilter]);

  // Statistics
  const stats = useMemo(() => {
    if (!notifications) return { total: 0, unread: 0, read: 0 };

    return {
      total: notifications.length,
      unread: notifications.filter((n: any) => !n.read).length,
      read: notifications.filter((n: any) => n.read).length,
    };
  }, [notifications]);

  const handleClearFilters = () => {
    setSearchTerm('');
    setTypeFilter('all');
    setStatusFilter('all');
  };

  if (isLoading) {
    return (
      <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
        <div className="px-4 lg:px-6">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div className="space-y-2">
              <h1 className="text-2xl font-bold tracking-tight">Notificações</h1>
              <p className="text-muted-foreground">
                Gerencie todas as suas notificações
              </p>
            </div>
          </div>
        </div>
        <div className="px-4 lg:px-6">
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <BellIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">Carregando notificações...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex flex-col gap-4 p-4 md:p-6 border-b md:flex-row md:items-center md:justify-between">
        <div className="min-w-0">
          <h1 className="text-xl md:text-2xl font-bold">Notificações</h1>
          <p className="text-muted-foreground text-sm md:text-base">
            Gerencie todas as suas notificações
          </p>
        </div>

        <div className="flex items-center gap-2 shrink-0">
          {unreadCount > 0 && (
            <Button onClick={markAllAsRead} variant="outline" size="sm">
              <CheckCircle className="h-4 w-4 mr-2" />
              Marcar todas como lidas
            </Button>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 p-4 md:p-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total</p>
                  <p className="text-2xl font-bold">{stats.total}</p>
                </div>
                <BellIcon className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Não lidas</p>
                  <p className="text-2xl font-bold">{stats.unread}</p>
                </div>
                <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <div className="h-3 w-3 bg-blue-500 rounded-full" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Lidas</p>
                  <p className="text-2xl font-bold">{stats.read}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Buscar notificações..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-full sm:w-[180px]">
                  <SelectValue>
                    {typeFilter === 'all' ? 'Tipo: Todos' :
                     typeFilter === 'info' ? 'Tipo: Informação' :
                     typeFilter === 'success' ? 'Tipo: Sucesso' :
                     typeFilter === 'warning' ? 'Tipo: Aviso' :
                     typeFilter === 'error' ? 'Tipo: Erro' :
                     'Tipo'}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tipo: Todos</SelectItem>
                  <SelectItem value="info">Tipo: Informação</SelectItem>
                  <SelectItem value="success">Tipo: Sucesso</SelectItem>
                  <SelectItem value="warning">Tipo: Aviso</SelectItem>
                  <SelectItem value="error">Tipo: Erro</SelectItem>
                </SelectContent>
              </Select>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full sm:w-[180px]">
                  <SelectValue>
                    {statusFilter === 'all' ? 'Status: Todos' :
                     statusFilter === 'unread' ? 'Status: Não lidas' :
                     statusFilter === 'read' ? 'Status: Lidas' :
                     'Status'}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Status: Todos</SelectItem>
                  <SelectItem value="unread">Status: Não lidas</SelectItem>
                  <SelectItem value="read">Status: Lidas</SelectItem>
                </SelectContent>
              </Select>
              {(searchTerm || typeFilter !== 'all' || statusFilter !== 'all') && (
                <Button onClick={handleClearFilters} variant="outline" size="sm">
                  Limpar filtros
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Notifications List */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Notificações</span>
              {filteredNotifications.length > 0 && (
                <Badge variant="secondary">
                  {filteredNotifications.length} notificação{filteredNotifications.length !== 1 ? 'ões' : ''}
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {filteredNotifications.length === 0 ? (
              <div className="text-center py-12">
                <BellIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Nenhuma notificação encontrada</h3>
                <p className="text-muted-foreground">
                  {searchTerm || typeFilter !== 'all' || statusFilter !== 'all'
                    ? 'Tente ajustar os filtros para ver mais resultados.'
                    : 'Você não possui notificações no momento.'}
                </p>
              </div>
            ) : (
              <div className="space-y-2">
                {filteredNotifications.map((notification: any) => (
                  <div
                    key={notification.id}
                    className={cn(
                      "flex items-start gap-4 p-4 rounded-lg border transition-colors hover:bg-accent/50",
                      !notification.read && "bg-muted/30 border-blue-200"
                    )}
                  >
                    <div className="mt-1">
                      {getNotificationIcon(notification.type)}
                    </div>

                    <div className="flex-1 space-y-1">
                      <div className="flex items-start justify-between gap-2">
                        <h4 className="font-medium leading-tight">
                          {notification.title}
                        </h4>
                        <div className="flex items-center gap-1 shrink-0">
                          {!notification.read && (
                            <div className="h-2 w-2 bg-blue-500 rounded-full" />
                          )}
                          <span className="text-xs text-muted-foreground">
                            {formatTimestamp(notification.timestamp)}
                          </span>
                        </div>
                      </div>

                      <p className="text-sm text-muted-foreground leading-relaxed">
                        {notification.message}
                      </p>

                      <div className="flex items-center gap-2 pt-2">
                        {!notification.read && (
                          <Button
                            onClick={() => markAsRead(notification.id)}
                            variant="ghost"
                            size="sm"
                            className="h-8 px-2 text-xs"
                          >
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Marcar como lida
                          </Button>
                        )}
                        <Button
                          onClick={() => removeNotification(notification.id)}
                          variant="ghost"
                          size="sm"
                          className="h-8 px-2 text-xs text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-3 w-3 mr-1" />
                          Remover
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
