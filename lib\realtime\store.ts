/**
 * Store Valtio Simples para Realtime
 * Estado global apenas para dados novos em tempo real
 */

import { proxy } from 'valtio';
import type { Client } from '@/schemas/clients';
import type { Notification, ActivityLog } from '@/schemas/database';
import type { TeamChat, ChatMessage } from '@/schemas/chat';
import type { Team } from '@/schemas/teams';
import type { Event } from '@/schemas/events';
import type { Board, Column, Task } from '@/schemas/kanban';
import type { Activity } from '@/schemas/activities';
import type { Models } from 'appwrite';

export const realtimeStore = proxy({
  // Dados novos das collections (apenas create/update)
  clients: [] as Client[],
  notifications: [] as Notification[],
  chats: [] as TeamChat[],
  messages: [] as ChatMessage[],
  teams: [] as Team[],
  events: [] as Event[],
  boards: [] as Board[],
  columns: [] as Column[],
  tasks: [] as Task[],
  activities: [] as Activity[],
  files: [] as Models.File[],

  // Status da conexão
  isConnected: false,
  isConnecting: false,
  error: null as string | null,
});
