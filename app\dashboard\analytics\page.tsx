'use client';

import { ChartAreaInteractive } from '@/components/chart-area-interactive';
import { AnalyticsCards } from '@/components/analytics/analytics-cards';
import { AnalyticsCharts } from '@/components/analytics/analytics-charts';
import { RealTimeStats } from '@/components/analytics/realtime-stats';
import { ChartsShowcase } from '@/components/charts';
import { useAnalytics } from '@/hooks/use-analytics';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useEffect, useState } from "react";
import { trackEvent } from '@/lib/google-analytics';
import { Button } from '@/components/ui/button';
import { Eye, EyeOff } from 'lucide-react';

export default function AnalyticsPage() {
  const { data: analytics, isLoading, error } = useAnalytics();
  const [showChartsShowcase, setShowChartsShowcase] = useState(false);

  // Track page view
  useEffect(() => {
    trackEvent.analyticsView();
  }, []);

  if (error) {
    return (
      <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
        <div className="px-4 lg:px-6">
          <div className="rounded-lg border border-destructive/20 bg-destructive/10 p-4">
            <h3 className="font-semibold text-destructive">Erro ao carregar analytics</h3>
            <p className="text-sm text-muted-foreground">
              Não foi possível carregar os dados de analytics. Tente novamente.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex flex-col gap-4 p-4 md:p-6 border-b md:flex-row md:items-center md:justify-between">
        <div className="min-w-0">
          <h1 className="text-xl md:text-2xl font-bold">Analytics</h1>
          <p className="text-muted-foreground text-sm md:text-base">
            Análises detalhadas e métricas de performance da sua aplicação.
          </p>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 p-4 md:p-6 space-y-6">
        {/* Real-time Stats */}
        <RealTimeStats />

        {/* Analytics Cards */}
        {isLoading ? (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <Skeleton key={i} className="h-32" />
            ))}
          </div>
        ) : (
          <AnalyticsCards data={analytics} />
        )}

        {/* Charts Section */}
        {isLoading ? (
          <Skeleton className="h-96" />
        ) : (
          <AnalyticsCharts data={analytics} />
        )}

        {/* Fallback Interactive Chart */}
        <ChartAreaInteractive />

        {/* Charts Showcase Section */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Novos Componentes de Charts</CardTitle>
                <CardDescription>
                  Demonstração dos novos componentes baseados nos padrões do Shadcn UI
                </CardDescription>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowChartsShowcase(!showChartsShowcase)}
              >
                {showChartsShowcase ? (
                  <>
                    <EyeOff className="mr-2 h-4 w-4" />
                    Ocultar
                  </>
                ) : (
                  <>
                    <Eye className="mr-2 h-4 w-4" />
                    Mostrar
                  </>
                )}
              </Button>
            </div>
          </CardHeader>
          {showChartsShowcase && (
            <CardContent>
              <ChartsShowcase />
            </CardContent>
          )}
        </Card>
      </div>
    </div>
  );
}
