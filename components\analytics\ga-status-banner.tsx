/**
 * Banner de Status do Google Analytics
 * Mostra informações sobre a configuração do GA4
 */

import React from 'react';
import { Alert, AlertDescription, AlertTitle } from '../ui/alert';
import { Badge } from '../ui/badge';
import { <PERSON><PERSON> } from '../ui/button';
import { IconInfoCircle, IconCheck, IconAlertTriangle, IconExternalLink } from '@tabler/icons-react';
import { getGoogleAnalyticsConfig } from '../../lib/google-analytics-api';

export function GAStatusBanner() {
  const config = getGoogleAnalyticsConfig();

  if (config.isConfigured) {
    return (
      <Alert className="mb-6 border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
        <IconCheck className="h-4 w-4 text-green-600 dark:text-green-400" />
        <AlertTitle className="text-green-800 dark:text-green-200">
          Google Analytics Configurado
        </AlertTitle>
        <AlertDescription className="text-green-700 dark:text-green-300">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <p>Conectado ao Google Analytics 4 com sucesso.</p>
              <div className="flex items-center gap-2 text-sm">
                <span>Measurement ID:</span>
                <Badge variant="outline" className="font-mono text-xs">
                  {config.measurementId}
                </Badge>
                <span>Property ID:</span>
                <Badge variant="outline" className="font-mono text-xs">
                  {config.propertyId}
                </Badge>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="border-green-300 text-green-700 hover:bg-green-100 dark:border-green-700 dark:text-green-300 dark:hover:bg-green-900"
              onClick={() => window.open('https://analytics.google.com/', '_blank')}
            >
              <IconExternalLink className="h-4 w-4 mr-2" />
              Abrir GA4
            </Button>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Alert className="mb-6 border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950">
      <IconAlertTriangle className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
      <AlertTitle className="text-yellow-800 dark:text-yellow-200">
        Google Analytics Não Configurado
      </AlertTitle>
      <AlertDescription className="text-yellow-700 dark:text-yellow-300">
        <div className="space-y-3">
          <p>
            Os dados mostrados são simulados. Para ver dados reais do Google Analytics 4, 
            configure as variáveis de ambiente.
          </p>
          
          <div className="bg-yellow-100 dark:bg-yellow-900 p-3 rounded-md">
            <p className="text-sm font-medium mb-2">Para configurar o Google Analytics:</p>
            <ol className="text-sm space-y-1 list-decimal list-inside">
              <li>Acesse o <a href="https://analytics.google.com/" target="_blank" rel="noopener noreferrer" className="underline">Google Analytics</a></li>
              <li>Copie o Measurement ID (formato: G-XXXXXXXXXX)</li>
              <li>Adicione no arquivo .env: <code className="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">NEXT_PUBLIC_GA4_MEASUREMENT_ID=G-XXXXXXXXXX</code></li>
              <li>Reinicie a aplicação</li>
            </ol>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              className="border-yellow-300 text-yellow-700 hover:bg-yellow-100 dark:border-yellow-700 dark:text-yellow-300 dark:hover:bg-yellow-900"
              onClick={() => window.open('https://analytics.google.com/', '_blank')}
            >
              <IconExternalLink className="h-4 w-4 mr-2" />
              Acessar Google Analytics
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="border-yellow-300 text-yellow-700 hover:bg-yellow-100 dark:border-yellow-700 dark:text-yellow-300 dark:hover:bg-yellow-900"
              onClick={() => window.open('https://developers.google.com/analytics/devguides/collection/ga4', '_blank')}
            >
              <IconInfoCircle className="h-4 w-4 mr-2" />
              Documentação
            </Button>
          </div>
        </div>
      </AlertDescription>
    </Alert>
  );
}
