/**
 * Controller para Activities
 * Usa subscribe do Valtio - sem useEffect, useState, nada
 */

import { useEffect, useRef } from 'react';
import { subscribe } from 'valtio';
import { useQueryClient } from '@tanstack/react-query';
import { realtimeStore } from '../../lib/realtime/store';
import { saveToIndexedDB } from '../../lib/cache-sync';

export function useActivitiesController() {
  const queryClient = useQueryClient();
  const unsubscribeRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    console.log('🎛️ Inicializando controller de activities...');

    // Subscribe do Valtio - função nativa, sem useEffect
    unsubscribeRef.current = subscribe(realtimeStore.activities, () => {
      const activities = realtimeStore.activities;

      if (activities.length === 0) return;
      
      console.log(`📝 Processando ${activities.length} activities do realtime...`);

      // Adicionar dados novos aos existentes no React Query
      activities.forEach(activity => {
        console.log(`📝 Atualizando activity: ${activity.$id}`);

        // Invalidar queries relacionadas a activities
        queryClient.setQueryData(['activities'], (oldData: any) => {
          if (!oldData) return [activity];

          const exists = oldData.find((item: any) => item.$id === activity.$id);
          if (exists) {
            // Atualizar existente
            return oldData.map((item: any) =>
              item.$id === activity.$id ? activity : item
            );
          } else {
            // Adicionar novo (no início para mostrar mais recentes primeiro)
            return [activity, ...oldData];
          }
        });

        // Invalidar outras queries relacionadas
        queryClient.invalidateQueries({ queryKey: ['activities'] });
        queryClient.invalidateQueries({ queryKey: ['activityStats'] });
        queryClient.invalidateQueries({ queryKey: ['recentActivities'] });
        queryClient.invalidateQueries({ queryKey: ['userActivities', activity.userId] });
        if (activity.teamId) {
          queryClient.invalidateQueries({ queryKey: ['teamActivities', activity.teamId] });
        }

        // Salvar no IndexedDB com tratamento de erro (sem await para não bloquear)
        saveToIndexedDB('activity_logs', activity, {
          collection: 'activity_logs',
          userId: activity.userId
        }).catch(error => {
          console.error(`❌ Erro ao salvar activity ${activity.$id} no IndexedDB:`, error);
          // Não interromper o fluxo, apenas logar o erro
        });
      });

  
    });

    return () => {
      console.log('🧹 Limpando controller de activities...');
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    };
  }, [queryClient]);

  return {};
}
