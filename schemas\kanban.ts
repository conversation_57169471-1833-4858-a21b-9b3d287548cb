/**
 * Kanban Types - Estrutura consolidada
 * Apenas 2 coleções: WORKSPACES e KANBAN_BOARDS
 * Tudo embarcado como arrays de strings/objetos para otimizar performance
 */

import { z } from 'zod';

// ============================================================================
// BASE ENUM SCHEMAS
// ============================================================================

export const taskPrioritySchema = z.enum(['baixa', 'media', 'alta', 'critica']);
export const taskStatusSchema = z.enum(['todo', 'in_progress', 'review', 'done']);
export const boardVisibilitySchema = z.enum(['private', 'team', 'public']);
export const labelColorSchema = z.enum([
  'red', 'orange', 'yellow', 'green', 'blue', 'purple', 'pink', 'gray'
]);

// ============================================================================
// EMBEDDED SCHEMAS - Dados embarcados nos boards
// ============================================================================

// Checklist Item Schema
export const embeddedChecklistItemSchema = z.object({
  id: z.string().min(1, 'ID é obrigatório'),
  text: z.string().min(1, 'Texto é obrigatório').max(500, 'Texto muito longo'),
  completed: z.boolean().default(false),
  position: z.number().int().min(0).default(0),
});

// Checklist Schema
export const embeddedChecklistSchema = z.object({
  id: z.string().min(1, 'ID é obrigatório'),
  title: z.string().min(1, 'Título é obrigatório').max(200, 'Título muito longo'),
  position: z.number().int().min(0).default(0),
  items: z.array(embeddedChecklistItemSchema).default([]),

  // Auditoria
  createdAt: z.string().min(1, 'Data de criação é obrigatória'),
  createdBy: z.string().min(1, 'ID do criador é obrigatório'),
});

// Comment Schema
export const embeddedCommentSchema = z.object({
  id: z.string().min(1, 'ID é obrigatório'),
  content: z.string().min(1, 'Conteúdo é obrigatório').max(2000, 'Conteúdo muito longo'),

  // Auditoria
  createdAt: z.string().min(1, 'Data de criação é obrigatória'),
  createdBy: z.string().min(1, 'ID do criador é obrigatório'),
  updatedAt: z.string().optional(),
  updatedBy: z.string().optional(),
  isEdited: z.boolean().default(false),
  editedAt: z.string().optional(),
});

// Attachment Schema
export const embeddedAttachmentSchema = z.object({
  id: z.string().min(1, 'ID é obrigatório'),
  name: z.string().min(1, 'Nome é obrigatório').max(255, 'Nome muito longo'),
  url: z.string().url('URL inválida'),
  type: z.string().min(1, 'Tipo é obrigatório'), // MIME type
  size: z.number().int().min(0), // Size in bytes
  isCover: z.boolean().default(false), // Se é a imagem de capa da task

  // Auditoria
  createdAt: z.string().min(1, 'Data de criação é obrigatória'),
  createdBy: z.string().min(1, 'ID do criador é obrigatório'),
});

// Label Schema
export const embeddedLabelSchema = z.object({
  id: z.string().min(1, 'ID é obrigatório'),
  name: z.string().min(1, 'Nome é obrigatório').max(50, 'Nome muito longo'),
  color: labelColorSchema.default('gray'),

  // Auditoria
  createdAt: z.string().min(1, 'Data de criação é obrigatória'),
  createdBy: z.string().min(1, 'ID do criador é obrigatório'),
});

// Task Schema
export const embeddedTaskSchema = z.object({
  id: z.string().min(1, 'ID é obrigatório'),

  // Relacionamentos (para compatibilidade)
  boardId: z.string().optional(),
  columnId: z.string().optional(),

  // Ownership e auditoria
  userId: z.string().min(1, 'ID do usuário é obrigatório'),
  teamId: z.string().optional(),
  createdBy: z.string().min(1, 'ID do criador é obrigatório'),
  updatedBy: z.string().optional(),
  createdAt: z.string().min(1, 'Data de criação é obrigatória'),
  updatedAt: z.string().optional(),

  // Informações básicas da tarefa
  title: z.string().min(1, 'Título é obrigatório').max(200, 'Título muito longo'),
  description: z.string().max(2000, 'Descrição muito longa').optional(),

  // Status e prioridade
  status: taskStatusSchema.default('todo'),
  priority: taskPrioritySchema.default('media'),

  // Datas
  dueDate: z.string().optional(), // ISO string
  completedAt: z.string().optional(), // ISO string
  startDate: z.string().optional(), // Data de início

  // Organização
  position: z.number().int().min(0).default(0),
  tags: z.array(z.string().trim().min(1)).default([]),

  // Labels (relacionamento com labels)
  labelIds: z.array(z.string()).default([]),

  // Atribuição
  assignedTo: z.string().optional(), // User ID principal
  assignedMembers: z.array(z.string()).default([]), // Array de user IDs

  // Estimativas e tracking
  estimatedHours: z.number().min(0).optional(),
  actualHours: z.number().min(0).optional(),

  // Visual
  coverImageUrl: z.string().url().optional(),
  coverColor: z.string().regex(/^#[0-9A-F]{6}$/i).optional(),

  // DADOS EMBARCADOS - Tudo em um lugar!
  checklists: z.array(embeddedChecklistSchema).default([]),
  comments: z.array(embeddedCommentSchema).default([]),
  attachments: z.array(embeddedAttachmentSchema).default([]),

  // Metadados
  isArchived: z.boolean().default(false),
});

// Column Schema
export const embeddedColumnSchema = z.object({
  id: z.string().min(1, 'ID é obrigatório'),

  // Relacionamentos (para compatibilidade)
  boardId: z.string().optional(),

  // Ownership e auditoria
  userId: z.string().min(1, 'ID do usuário é obrigatório'),
  teamId: z.string().optional(),
  createdBy: z.string().min(1, 'ID do criador é obrigatório'),
  updatedBy: z.string().optional(),
  createdAt: z.string().min(1, 'Data de criação é obrigatória'),
  updatedAt: z.string().optional(),

  // Informações básicas da coluna
  title: z.string().min(1, 'Título é obrigatório').max(100, 'Título muito longo'),
  description: z.string().max(500, 'Descrição muito longa').optional(),

  // Organização
  position: z.number().int().min(0).default(0),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Cor deve ser um hex válido').optional(),

  // Configurações
  isCollapsed: z.boolean().default(false),
  taskLimit: z.number().int().min(0).optional(), // WIP limit

  // DADOS EMBARCADOS - Tasks da coluna
  tasks: z.array(embeddedTaskSchema).default([]),

  // Metadados
  isArchived: z.boolean().default(false),
});

// ============================================================================
// WORKSPACE SCHEMA
// ============================================================================

export const optimizedWorkspaceSchema = z.object({
  // Campos de auditoria Appwrite
  $id: z.string().min(1, 'ID é obrigatório'),
  $createdAt: z.string().min(1, 'Data de criação é obrigatória'),
  $updatedAt: z.string().min(1, 'Data de atualização é obrigatória'),

  // Ownership e auditoria
  userId: z.string().min(1, 'ID do usuário é obrigatório'),
  createdBy: z.string().min(1, 'ID do criador é obrigatório'),
  updatedBy: z.string().optional(),

  // Informações básicas
  name: z.string().min(1, 'Nome é obrigatório').max(100, 'Nome muito longo'),
  description: z.string().max(500, 'Descrição muito longa').optional(),

  // Configurações visuais
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Cor deve ser um hex válido').optional(),
  icon: z.string().max(50, 'Ícone muito longo').optional(),

  // Configurações
  visibility: z.enum(['private', 'team', 'public']).default('private'),
  allowInvites: z.boolean().default(true),

  // Membros (array de user IDs)
  members: z.array(z.string()).default([]),

  // Metadados
  isArchived: z.boolean().default(false),
});

// ============================================================================
// BOARD SCHEMA OTIMIZADO - Contém tudo embarcado
// ============================================================================

export const optimizedBoardSchema = z.object({
  // Campos de auditoria Appwrite
  $id: z.string().min(1, 'ID é obrigatório'),
  $createdAt: z.string().min(1, 'Data de criação é obrigatória'),
  $updatedAt: z.string().min(1, 'Data de atualização é obrigatória'),

  // Ownership e auditoria
  userId: z.string().min(1, 'ID do usuário é obrigatório'),
  teamId: z.string().optional(),
  createdBy: z.string().min(1, 'ID do criador é obrigatório'),
  updatedBy: z.string().optional(),

  // Relacionamentos
  workspaceId: z.string().optional(),

  // Informações básicas do board
  title: z.string().min(1, 'Título é obrigatório').max(200, 'Título muito longo'),
  description: z.string().max(1000, 'Descrição muito longa').optional(),

  // Configurações de visibilidade
  visibility: boardVisibilitySchema.default('private'),

  // Configurações visuais
  backgroundColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Cor deve ser um hex válido').optional(),
  backgroundImage: z.string().optional(),

  // Membros do board
  members: z.array(z.string()).default([]),

  // Configurações funcionais
  allowComments: z.boolean().default(true),
  allowAttachments: z.boolean().default(true),
  enableTimeTracking: z.boolean().default(false),
  enableLabels: z.boolean().default(true),
  enableChecklists: z.boolean().default(true),
  enableDueDates: z.boolean().default(true),

  // Configurações de automação
  enableAutomation: z.boolean().default(false),
  automationRules: z.array(z.any()).default([]),

  // DADOS EMBARCADOS - Tudo em um lugar!
  labels: z.array(embeddedLabelSchema).default([]),
  columns: z.array(embeddedColumnSchema).default([]),

  // Metadados
  isTemplate: z.boolean().default(false),
  isArchived: z.boolean().default(false),
  isFavorite: z.boolean().default(false),
  isStarred: z.boolean().default(false),

  // Estatísticas (calculadas)
  tasksCount: z.number().int().min(0).default(0),
  membersCount: z.number().int().min(0).default(0),
  lastActivity: z.string().optional(),
});

// ============================================================================
// SCHEMAS DERIVADOS - Para formulários e operações
// ============================================================================

// Formulários para criação
export const createWorkspaceSchema = optimizedWorkspaceSchema.omit({
  $id: true,
  $createdAt: true,
  $updatedAt: true,
  createdBy: true,
  updatedBy: true,
});

export const createBoardSchema = optimizedBoardSchema.omit({
  $id: true,
  $createdAt: true,
  $updatedAt: true,
  createdBy: true,
  updatedBy: true,
  tasksCount: true,
  membersCount: true,
  lastActivity: true,
  // Campos embarcados que são inicializados automaticamente
  labels: true,
  columns: true,
  // Campos que não devem ser enviados na criação
  automationRules: true,
});

export const createColumnSchema = embeddedColumnSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  createdBy: true,
  updatedBy: true,
  position: true,
  tasks: true,
}).extend({
  boardId: z.string().min(1, 'Board ID é obrigatório'),
});

export const createTaskSchema = embeddedTaskSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  createdBy: true,
  updatedBy: true,
  position: true,
  checklists: true,
  comments: true,
  attachments: true,
}).extend({
  boardId: z.string().min(1, 'Board ID é obrigatório'),
  columnId: z.string().min(1, 'Column ID é obrigatório'),
});

export const createLabelSchema = embeddedLabelSchema.omit({
  id: true,
  createdAt: true,
  createdBy: true,
});

export const createChecklistSchema = embeddedChecklistSchema.omit({
  id: true,
  createdAt: true,
  createdBy: true,
  position: true,
  items: true,
});

export const createCommentSchema = embeddedCommentSchema.omit({
  id: true,
  createdAt: true,
  createdBy: true,
  updatedAt: true,
  updatedBy: true,
  isEdited: true,
  editedAt: true,
});

export const createAttachmentSchema = embeddedAttachmentSchema.omit({
  id: true,
  createdAt: true,
  createdBy: true,
});

// Formulários para atualização
export const updateWorkspaceSchema = createWorkspaceSchema.partial().extend({
  updatedBy: z.string().optional(),
});

export const updateBoardSchema = createBoardSchema.partial().extend({
  updatedBy: z.string().optional(),
  // Permitir tanto arrays quanto strings JSON para flexibilidade
  labels: z.union([z.array(embeddedLabelSchema), z.string()]).optional(),
  columns: z.union([z.array(embeddedColumnSchema), z.string()]).optional(),
  automationRules: z.union([z.array(z.any()), z.string()]).optional(),
  // Campos calculados que podem ser atualizados
  tasksCount: z.number().int().min(0).optional(),
  membersCount: z.number().int().min(0).optional(),
  lastActivity: z.string().optional(),
});

export const updateColumnSchema = createColumnSchema.partial().extend({
  updatedBy: z.string().optional(),
});

export const updateTaskSchema = createTaskSchema.partial().extend({
  updatedBy: z.string().optional(),
});

export const updateCommentSchema = createCommentSchema.partial().extend({
  updatedBy: z.string().optional(),
  isEdited: z.boolean().default(true),
  editedAt: z.string().optional(),
});

// ============================================================================
// TYPESCRIPT TYPES
// ============================================================================

// Tipos principais
export type OptimizedWorkspace = z.infer<typeof optimizedWorkspaceSchema>;
export type OptimizedBoard = z.infer<typeof optimizedBoardSchema>;
export type EmbeddedColumn = z.infer<typeof embeddedColumnSchema>;
export type EmbeddedTask = z.infer<typeof embeddedTaskSchema>;
export type EmbeddedLabel = z.infer<typeof embeddedLabelSchema>;
export type EmbeddedChecklist = z.infer<typeof embeddedChecklistSchema>;
export type EmbeddedChecklistItem = z.infer<typeof embeddedChecklistItemSchema>;
export type EmbeddedComment = z.infer<typeof embeddedCommentSchema>;
export type EmbeddedAttachment = z.infer<typeof embeddedAttachmentSchema>;

// Tipos para formulários
export type CreateWorkspaceData = z.infer<typeof createWorkspaceSchema> & {
  permissionOptions?: import('@/lib/appwrite/functions/database').CreateDocumentOptions;
};
export type CreateBoardData = z.infer<typeof createBoardSchema> & {
  permissionOptions?: import('@/lib/appwrite/functions/database').CreateDocumentOptions;
};
export type CreateColumnData = z.infer<typeof createColumnSchema>;
export type CreateTaskData = z.infer<typeof createTaskSchema>;
export type CreateLabelData = z.infer<typeof createLabelSchema>;
export type CreateChecklistData = z.infer<typeof createChecklistSchema>;
export type CreateCommentData = z.infer<typeof createCommentSchema>;
export type CreateAttachmentData = z.infer<typeof createAttachmentSchema>;

// Tipos para atualização
export type UpdateWorkspaceData = z.infer<typeof updateWorkspaceSchema>;
export type UpdateBoardData = z.infer<typeof updateBoardSchema>;
export type UpdateColumnData = z.infer<typeof updateColumnSchema>;
export type UpdateTaskData = z.infer<typeof updateTaskSchema>;
export type UpdateCommentData = z.infer<typeof updateCommentSchema>;

// Tipos auxiliares
export type TaskPriority = z.infer<typeof taskPrioritySchema>;
export type TaskStatus = z.infer<typeof taskStatusSchema>;
export type BoardVisibility = z.infer<typeof boardVisibilitySchema>;
export type LabelColor = z.infer<typeof labelColorSchema>;

// Aliases para compatibilidade
export type Workspace = OptimizedWorkspace;
export type Board = OptimizedBoard;
export type Column = EmbeddedColumn;
export type Task = EmbeddedTask;
export type Label = EmbeddedLabel;
export type Checklist = EmbeddedChecklist;
export type ChecklistItem = EmbeddedChecklistItem;
export type Comment = EmbeddedComment;
export type Attachment = EmbeddedAttachment;

// Tipos para formulários (aliases)
export type WorkspaceFormData = CreateWorkspaceData;
export type BoardFormData = CreateBoardData;
export type ColumnFormData = CreateColumnData;
export type TaskFormData = CreateTaskData;

// Helper para encontrar task em um board
export function findTaskInBoard(board: OptimizedBoard, taskId: string): { task: EmbeddedTask; columnId: string } | null {
  for (const column of board.columns) {
    const task = column.tasks.find(t => t.id === taskId);
    if (task) {
      return { task, columnId: column.id };
    }
  }
  return null;
}

// Helper para encontrar column em um board
export function findColumnInBoard(board: OptimizedBoard, columnId: string): EmbeddedColumn | null {
  return board.columns.find(c => c.id === columnId) || null;
}

// Tipos para compatibilidade com estrutura antiga
export type BoardWithData = OptimizedBoard;

// Tipo para drag and drop (compatibilidade)
export interface DragEndResult {
  destination?: {
    droppableId: string;
    index: number;
  } | null;
  source: {
    droppableId: string;
    index: number;
  };
  draggableId: string;
  type: string;
}
