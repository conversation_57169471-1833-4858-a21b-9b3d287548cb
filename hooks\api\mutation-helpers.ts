/**
 * Mutation Helpers
 * Provides reusable patterns for optimistic updates and cache management
 */

import { useQueryClient, type UseMutationOptions } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useAuth } from '../use-auth';
import type { BaseDocument } from '@/schemas/database';
// ApiResponse type removed - using direct Appwrite responses now

// ============================================================================
// TYPES
// ============================================================================

export interface MutationConfig<TData, TVariables, TContext = unknown> {
  mutationFn: (variables: TVariables) => Promise<TData>;
  queryKey: readonly unknown[];
  successMessage?: string;
  errorMessage?: string;
  onSuccessCallback?: (data: TData, variables: TVariables, context: TContext) => void | Promise<void>;
  onErrorCallback?: (error: Error, variables: TVariables, context: TContext) => void;
}

export interface OptimisticCreateConfig<TData extends BaseDocument, TVariables>
  extends MutationConfig<TData, TVariables, { previousData: TData[] | undefined; optimisticItem: TData }> {
  generateOptimisticData: (variables: TVariables, userId?: string) => TData;
}

export interface OptimisticUpdateConfig<TData extends BaseDocument, TVariables extends { id: string }>
  extends MutationConfig<TData, TVariables, { previousData: TData[] | undefined }> {
}

export interface OptimisticDeleteConfig<TData extends BaseDocument>
  extends MutationConfig<void, string, { previousData: TData[] | undefined }> {
}

// ============================================================================
// BASIC MUTATION HELPER
// ============================================================================

/**
 * Creates a standardized mutation with success/error handling
 */
export function useBasicMutation<TData, TVariables, TContext = unknown>(
  config: MutationConfig<TData, TVariables, TContext>,
  options?: UseMutationOptions<TData, Error, TVariables, TContext>
) {
  const queryClient = useQueryClient();

  return {
    mutationFn: config.mutationFn,
    onSuccess: async (data: TData, variables: TVariables, context: TContext) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: config.queryKey });

      // Show success message
      if (config.successMessage) {
        toast.success(config.successMessage);
      }

      // Execute custom callback
      if (config.onSuccessCallback) {
        await config.onSuccessCallback(data, variables, context);
      }
    },
    onError: (error: Error, variables: TVariables, context: TContext) => {
      // Show error message
      const errorMsg = config.errorMessage || error.message || 'Operação falhou';
      toast.error(errorMsg);

      // Execute custom callback
      if (config.onErrorCallback) {
        config.onErrorCallback(error, variables, context);
      }
    },
    ...options,
  };
}

// ============================================================================
// OPTIMISTIC UPDATE HELPERS
// ============================================================================

/**
 * Creates a mutation with optimistic updates for create operations
 */
export function useOptimisticCreateMutation<TData extends BaseDocument, TVariables>(
  config: OptimisticCreateConfig<TData, TVariables>,
  options?: UseMutationOptions<TData, Error, TVariables, { previousData: TData[] | undefined; optimisticItem: TData }>
) {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return {
    mutationFn: config.mutationFn,

    onMutate: async (variables: TVariables) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: config.queryKey });

      // Snapshot previous value
      const previousData = queryClient.getQueryData<TData[]>(config.queryKey);

      // Generate optimistic data
      const optimisticItem = config.generateOptimisticData(variables, user?.$id);

      // Optimistically update cache
      queryClient.setQueryData<TData[]>(
        config.queryKey,
        (old = []) => [...old, optimisticItem]
      );

      return { previousData, optimisticItem };
    },

    onSuccess: async (data: TData, variables: TVariables, context: { previousData: TData[] | undefined; optimisticItem: TData } | undefined) => {
      if (config.successMessage) {
        toast.success(config.successMessage);
      }

      if (config.onSuccessCallback && context) {
        await config.onSuccessCallback(data, variables, context);
      }
    },

    onError: (error: Error, variables: TVariables, context: { previousData: TData[] | undefined; optimisticItem: TData } | undefined) => {
      // Rollback optimistic update
      if (context) {
        queryClient.setQueryData(config.queryKey, context.previousData);
      }

      const errorMsg = config.errorMessage || error.message || 'Falha ao criar item';
      toast.error(errorMsg);

      if (config.onErrorCallback && context) {
        config.onErrorCallback(error, variables, context);
      }
    },

    ...options,
  };
}

/**
 * Creates a mutation with optimistic updates for update operations
 */
export function useOptimisticUpdateMutation<TData extends BaseDocument, TVariables extends { id: string }>(
  config: OptimisticUpdateConfig<TData, TVariables>,
  options?: UseMutationOptions<TData, Error, TVariables, { previousData: TData[] | undefined }>
) {
  const queryClient = useQueryClient();

  return {
    mutationFn: config.mutationFn,

    onMutate: async (variables: TVariables) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: config.queryKey });

      // Snapshot previous value
      const previousData = queryClient.getQueryData<TData[]>(config.queryKey);

      // Optimistically update cache
      queryClient.setQueryData<TData[]>(
        config.queryKey,
        (old = []) => old.map(item =>
          item.$id === variables.id
            ? { ...item, ...variables, $updatedAt: new Date().toISOString() } as TData
            : item
        )
      );

      return { previousData };
    },

    onSuccess: async (data: TData, variables: TVariables, context: { previousData: TData[] | undefined } | undefined) => {
      if (config.successMessage) {
        toast.success(config.successMessage);
      }

      if (config.onSuccessCallback && context) {
        await config.onSuccessCallback(data, variables, context);
      }
    },

    onError: (error: Error, variables: TVariables, context: { previousData: TData[] | undefined } | undefined) => {
      // Rollback optimistic update
      if (context) {
        queryClient.setQueryData(config.queryKey, context.previousData);
      }

      const errorMsg = config.errorMessage || error.message || 'Falha ao atualizar item';
      toast.error(errorMsg);

      if (config.onErrorCallback && context) {
        config.onErrorCallback(error, variables, context);
      }
    },

    ...options,
  };
}

/**
 * Creates a mutation with optimistic updates for delete operations
 */
export function useOptimisticDeleteMutation<TData extends BaseDocument>(
  config: OptimisticDeleteConfig<TData>,
  options?: UseMutationOptions<void, Error, string, { previousData: TData[] | undefined }>
) {
  const queryClient = useQueryClient();

  return {
    mutationFn: config.mutationFn,

    onMutate: async (id: string) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: config.queryKey });

      // Snapshot previous value
      const previousData = queryClient.getQueryData<TData[]>(config.queryKey);

      // Optimistically remove from cache
      queryClient.setQueryData<TData[]>(
        config.queryKey,
        (old = []) => old.filter(item => item.$id !== id)
      );

      return { previousData };
    },

    onSuccess: async (data: void, variables: string, context: { previousData: TData[] | undefined } | undefined) => {
      if (config.successMessage) {
        toast.success(config.successMessage);
      }

      if (config.onSuccessCallback && context) {
        await config.onSuccessCallback(data, variables, context);
      }
    },

    onError: (error: Error, variables: string, context: { previousData: TData[] | undefined } | undefined) => {
      // Rollback optimistic update
      if (context) {
        queryClient.setQueryData(config.queryKey, context.previousData);
      }

      const errorMsg = config.errorMessage || error.message || 'Falha ao excluir item';
      toast.error(errorMsg);

      if (config.onErrorCallback && context) {
        config.onErrorCallback(error, variables, context);
      }
    },

    ...options,
  };
}

// ============================================================================
// API RESPONSE HELPERS
// ============================================================================

/**
 * Simple wrapper for direct Appwrite function calls
 */
export function wrapAppwriteFunction<TData, TVariables>(
  mutationFn: (variables: TVariables) => Promise<TData>
) {
  return async (variables: TVariables): Promise<TData> => {
    return await mutationFn(variables);
  };
}

/**
 * Legacy alias for compatibility
 */
export const wrapApiResponse = wrapAppwriteFunction;
