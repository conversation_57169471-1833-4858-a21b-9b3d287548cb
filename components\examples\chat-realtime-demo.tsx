'use client';

/**
 * Componente de demonstração do Chat em Tempo Real
 * Mostra como usar o sistema de chat com Appwrite Realtime
 */

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { Alert, AlertDescription } from '../ui/alert';
import {
  MessageCircle,
  Wifi,
  WifiOff,
  Send,
  Users,
  Clock,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react';
import { useRealtimeStatus } from '../../hooks/use-realtime-status';
import { useMessages, useSendMessage, useChatRealtime } from '../../hooks/use-api';
import { useAuth } from '../../hooks/use-auth';
import { toast } from 'sonner';

interface ChatRealtimeDemoProps {
  teamId?: string;
  chatId?: string; // ID do chat específico para demo
  className?: string;
}

export function ChatRealtimeDemo({
  teamId = 'demo-team',
  chatId = 'demo-chat', // Chat fixo para demo
  className
}: ChatRealtimeDemoProps) {
  const [message, setMessage] = useState('');
  const [events, setEvents] = useState<Array<{ type: string; message: string; timestamp: Date }>>([]);

  const { user } = useAuth();
  const { isConnected, isConnecting, error } = useRealtimeStatus();
  const status = isConnecting ? 'connecting' : isConnected ? 'connected' : 'disconnected';
  // Usar chatId para buscar mensagens diretamente
  const { data: messagesData = [], isLoading } = useMessages(chatId, teamId);
  const sendMessageMutation = useSendMessage();
  const { isConnected: chatConnected } = useChatRealtime(teamId);

  // Adicionar evento ao log
  const addEvent = (type: string, message: string) => {
    setEvents(prev => [
      { type, message, timestamp: new Date() },
      ...prev.slice(0, 9) // Manter apenas os últimos 10 eventos
    ]);
  };

  // Monitorar mudanças de conexão
  useEffect(() => {
    if (isConnected) {
      addEvent('success', 'Conectado ao Appwrite Realtime');
    } else {
      addEvent('error', 'Desconectado do Appwrite Realtime');
    }
  }, [isConnected]);

  // Monitorar erros
  useEffect(() => {
    if (error) {
      addEvent('error', `Erro de conexão: ${error}`);
    }
  }, [error]);

  // Monitorar novas mensagens
  useEffect(() => {
    if (messagesData && messagesData.length > 0) {
      const lastMessage = messagesData[messagesData.length - 1];
      if (lastMessage && lastMessage.senderId !== user?.$id) {
        addEvent('info', `Nova mensagem de ${lastMessage.senderName}`);
      }
    }
  }, [messagesData, user?.$id]);

  const handleSendMessage = async () => {
    if (!message.trim() || sendMessageMutation.isPending) return;

    try {
      addEvent('info', 'Enviando mensagem...');

      await sendMessageMutation.mutateAsync({
        content: message.trim(),
        chatId, // OBRIGATÓRIO - ID do chat pai
        teamId, // Para compatibilidade
        type: 'text',
      });

      setMessage('');
      addEvent('success', 'Mensagem enviada com sucesso');
      toast.success('Mensagem enviada!');
    } catch (error) {
      addEvent('error', 'Erro ao enviar mensagem');
      toast.error('Erro ao enviar mensagem');
    }
  };

  const handleTestTyping = () => {
    addEvent('info', 'Indicador de digitação enviado (simulado)');

    setTimeout(() => {
      addEvent('info', 'Indicador de digitação removido (simulado)');
    }, 3000);
  };

  const getConnectionIcon = () => {
    switch (status) {
      case 'connected':
        return <Wifi className="h-4 w-4 text-green-500" />;
      case 'connecting':
        return <Clock className="h-4 w-4 text-yellow-500 animate-spin" />;
      default:
        return <WifiOff className="h-4 w-4 text-red-500" />;
    }
  };

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-3 w-3 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-3 w-3 text-red-500" />;
      default:
        return <Info className="h-3 w-3 text-blue-500" />;
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Status da Conexão */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Demo: Chat em Tempo Real
          </CardTitle>
          <CardDescription>
            Demonstração do sistema de chat com Appwrite Realtime
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Status */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getConnectionIcon()}
              <span className="font-medium">Status da Conexão:</span>
              <Badge variant={isConnected ? 'default' : 'destructive'}>
                {status}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              <span className="text-sm text-muted-foreground">
                Team ID: {teamId}
              </span>
            </div>
          </div>

          {/* Alertas */}
          {!user && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Você precisa estar logado para enviar mensagens.
              </AlertDescription>
            </Alert>
          )}

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Erro de conexão: {error}
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Teste de Mensagens */}
      <Card>
        <CardHeader>
          <CardTitle>Enviar Mensagem de Teste</CardTitle>
          <CardDescription>
            Teste o envio de mensagens em tempo real
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Input
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Digite uma mensagem de teste..."
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage();
                }
              }}
              disabled={!user || sendMessageMutation.isPending}
            />
            <Button
              onClick={handleSendMessage}
              disabled={!message.trim() || !user || sendMessageMutation.isPending}
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>

          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleTestTyping}
              disabled={!user}
              size="sm"
            >
              Testar Indicador de Digitação
            </Button>
          </div>

          {/* Estatísticas */}
          <div className="grid grid-cols-2 gap-4 pt-4">
            <div className="text-center">
              <div className="text-2xl font-bold">
                {messagesData?.length || 0}
              </div>
              <div className="text-sm text-muted-foreground">
                Total de Mensagens
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                {isConnected ? '✓' : '✗'}
              </div>
              <div className="text-sm text-muted-foreground">
                Tempo Real
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Log de Eventos */}
      <Card>
        <CardHeader>
          <CardTitle>Log de Eventos em Tempo Real</CardTitle>
          <CardDescription>
            Acompanhe os eventos do sistema em tempo real
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {events.length === 0 ? (
              <div className="text-center text-muted-foreground py-4">
                Nenhum evento ainda...
              </div>
            ) : (
              events.map((event, index) => (
                <div key={index} className="flex items-start gap-2 text-sm">
                  {getEventIcon(event.type)}
                  <div className="flex-1">
                    <span className="font-medium">{event.message}</span>
                    <div className="text-xs text-muted-foreground">
                      {event.timestamp.toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Mensagens Recentes */}
      <Card>
        <CardHeader>
          <CardTitle>Mensagens Recentes</CardTitle>
          <CardDescription>
            Últimas mensagens do chat (atualizadas em tempo real)
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-4">Carregando mensagens...</div>
          ) : messagesData?.length === 0 ? (
            <div className="text-center text-muted-foreground py-4">
              Nenhuma mensagem ainda
            </div>
          ) : (
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {messagesData?.slice(-5).map((msg: any) => (
                <div key={msg.$id} className="flex items-start gap-2 p-2 rounded border">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-sm">{msg.senderName}</span>
                      <span className="text-xs text-muted-foreground">
                        {new Date(msg.$createdAt).toLocaleTimeString()}
                      </span>
                    </div>
                    <div className="text-sm">{msg.content}</div>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {msg.type}
                  </Badge>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
