/**
 * Activity Logging Types
 * Comprehensive type definitions for the activity logging system
 */

import type { Models } from 'appwrite';
import type { BaseDocument } from './database';

// ============================================================================
// ACTIVITY TYPES
// ============================================================================

export type ActivityType = 
  | 'auth'           // Authentication events
  | 'client'         // Client management
  | 'team'           // Team management
  | 'chat'           // Chat activities
  | 'file'           // File operations
  | 'system'         // System events
  | 'admin'          // Admin actions
  | 'notification'   // Notification events
  | 'preference'     // Settings changes
  | 'calendar'       // Calendar events
  | 'document';      // Document management

export type ActivityAction = 
  | 'create'
  | 'update'
  | 'delete'
  | 'view'
  | 'login'
  | 'logout'
  | 'invite'
  | 'join'
  | 'leave'
  | 'upload'
  | 'download'
  | 'share'
  | 'archive'
  | 'restore'
  | 'send'
  | 'receive'
  | 'approve'
  | 'reject'
  | 'export'
  | 'import';

export type ActivityPriority = 'low' | 'normal' | 'high' | 'critical';

// ============================================================================
// MAIN ACTIVITY INTERFACE
// ============================================================================

export interface Activity extends BaseDocument {
  // Core identification
  userId: string;
  createdBy: string;
  teamId?: string;

  // Activity details
  type: ActivityType;
  action: ActivityAction;
  resource: string;
  resourceId?: string;
  
  // Content
  title: string;
  description?: string;
  details?: Record<string, unknown>;
  
  // Metadata
  priority: ActivityPriority;
  tags?: string[];
  category?: string;
  
  // Technical details
  ipAddress?: string;
  userAgent?: string;
  
  // Context
  metadata?: {
    oldValues?: Record<string, unknown>;
    newValues?: Record<string, unknown>;
    affectedUsers?: string[];
    relatedResources?: string[];
    [key: string]: unknown;
  };
}

// ============================================================================
// ACTIVITY CREATION TYPES
// ============================================================================

export interface CreateActivityData {
  type: ActivityType;
  action: ActivityAction;
  resource: string;
  resourceId?: string;
  title: string;
  description?: string;
  details?: Record<string, unknown>;
  priority?: ActivityPriority;
  tags?: string[];
  category?: string;
  teamId?: string;
  metadata?: Activity['metadata'];
}

export interface ActivityFilters {
  type?: ActivityType | ActivityType[];
  action?: ActivityAction | ActivityAction[];
  resource?: string;
  userId?: string;
  teamId?: string;
  priority?: ActivityPriority;
  dateFrom?: string;
  dateTo?: string;
  tags?: string[];
  search?: string;
}

// ============================================================================
// ACTIVITY DISPLAY TYPES
// ============================================================================

export interface ActivityDisplayConfig {
  type: ActivityType;
  icon: string;
  color: string;
  bgColor: string;
  label: string;
}

export interface ActivityActionConfig {
  action: ActivityAction;
  label: string;
  pastTense: string;
}

// ============================================================================
// ACTIVITY STATISTICS
// ============================================================================

export interface ActivityStats {
  total: number;
  byType: Record<ActivityType, number>;
  byAction: Record<ActivityAction, number>;
  byPriority: Record<ActivityPriority, number>;
  recentCount: number;
  todayCount: number;
  weekCount: number;
  monthCount: number;
}

// ============================================================================
// ACTIVITY QUERY TYPES
// ============================================================================

export interface ActivityQueryOptions {
  limit?: number;
  offset?: number;
  orderBy?: 'createdAt' | 'priority' | 'type';
  orderDirection?: 'asc' | 'desc';
  filters?: ActivityFilters;
  includeMetadata?: boolean;
}

export interface ActivityListResponse {
  activities: Activity[];
  total: number;
  hasMore: boolean;
  stats?: ActivityStats;
}

// ============================================================================
// ACTIVITY LOGGING HELPERS
// ============================================================================

export interface LogActivityParams {
  type: ActivityType;
  action: ActivityAction;
  resource: string;
  resourceId?: string;
  title?: string;
  description?: string;
  details?: Record<string, unknown>;
  priority?: ActivityPriority;
  tags?: string[];
  teamId?: string;
  metadata?: Activity['metadata'];
}

// ============================================================================
// ACTIVITY INTEGRATION TYPES
// ============================================================================

export interface ActivityContext {
  userId: string;
  teamId?: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface ActivityLogger {
  log: (params: LogActivityParams) => Promise<void>;
  logAuth: (action: 'login' | 'logout' | 'register', details?: Record<string, unknown>) => Promise<void>;
  logClient: (action: ActivityAction, clientId: string, details?: Record<string, unknown>) => Promise<void>;
  logTeam: (action: ActivityAction, teamId: string, details?: Record<string, unknown>) => Promise<void>;
  logFile: (action: ActivityAction, fileId: string, details?: Record<string, unknown>) => Promise<void>;
  logSystem: (action: ActivityAction, details?: Record<string, unknown>) => Promise<void>;
}

// ============================================================================
// EXPORTS
// ============================================================================

export type {
  Models,
  BaseDocument,
};
