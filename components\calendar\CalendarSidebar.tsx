/**
 * Calendar Sidebar Component
 * Sidebar com mini calendário, categorias e eventos próximos
 */

import React from 'react';
import { format, isSameDay, startOfMonth, endOfMonth } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useSnapshot } from 'valtio';
import {
  Plus,
  Calendar as CalendarIcon,
  Clock,
  Filter,
  Tag,
  Users,
  ChevronRight,
  Dot
} from 'lucide-react';
import { cn } from '../../lib/utils';
import { useCalendarState } from '../../lib/stores/calendar-state';
import { useCalendarModals } from '../../lib/stores/calendar-modals';
import { useEvents, useUpcomingEvents, useEventCategories } from '../../hooks/api/use-events';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Calendar } from '../ui/calendar';
import { Badge } from '../ui/badge';
import { ScrollArea } from '../ui/scroll-area';
import { Separator } from '../ui/separator';
import { Skeleton } from '../ui/skeleton';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '../ui/collapsible';
import type { Event } from '@/schemas/events';

// ============================================================================
// INTERFACES
// ============================================================================

interface CalendarSidebarProps {
  teamId?: string;
}

// ============================================================================
// COMPONENTE
// ============================================================================

export function CalendarSidebar({ teamId }: CalendarSidebarProps) {
  const { store: calendarState, actions: calendarActions } = useCalendarState();
  const { actions: modalsActions } = useCalendarModals();
  const calendarSnapshot = useSnapshot(calendarState);

  // Estados locais
  const [categoriesOpen, setCategoriesOpen] = React.useState(true);
  const [upcomingOpen, setUpcomingOpen] = React.useState(true);

  // Buscar dados
  const { data: events = [] } = useEvents(teamId);
  const { data: upcomingEvents = [], isLoading: upcomingLoading } = useUpcomingEvents(teamId);
  const { data: categories = [], isLoading: categoriesLoading } = useEventCategories(teamId);

  // Eventos do mês atual para o mini calendário
  const currentMonthEvents = React.useMemo(() => {
    const monthStart = startOfMonth(calendarSnapshot.view.startDate);
    const monthEnd = endOfMonth(calendarSnapshot.view.startDate);

    return events.filter(event => {
      const eventDate = new Date(event.startDate);
      return eventDate >= monthStart && eventDate <= monthEnd;
    });
  }, [events, calendarSnapshot.view.startDate]);

  // Agrupar eventos por data para o mini calendário
  const eventsByDate = React.useMemo(() => {
    const grouped: Record<string, Event[]> = {};

    currentMonthEvents.forEach(event => {
      const dateKey = format(new Date(event.startDate), 'yyyy-MM-dd');
      if (!grouped[dateKey]) {
        grouped[dateKey] = [];
      }
      grouped[dateKey].push(event);
    });

    return grouped;
  }, [currentMonthEvents]);

  // Handler para seleção de data no mini calendário
  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      calendarActions.navigateToDate(date);
      calendarActions.setView('day');
    }
  };

  // Modificador para dias com eventos
  const modifiers = React.useMemo(() => {
    const hasEvents = Object.keys(eventsByDate).map(dateStr => new Date(dateStr));
    return {
      hasEvents,
      selected: calendarSnapshot.ui.selectedDate,
    };
  }, [eventsByDate, calendarSnapshot.ui.selectedDate]);

  const modifiersStyles = {
    hasEvents: {
      position: 'relative' as const,
    },
  };

  return (
    <div className="w-80 border-r bg-background/50 backdrop-blur supports-[backdrop-filter]:bg-background/50">
      <ScrollArea className="h-full">
        <div className="p-4 space-y-6">
          {/* Quick Actions */}
          <div className="space-y-2">
            <Button
              onClick={() => modalsActions.openCreateEvent()}
              className="w-full gap-2"
            >
              <Plus className="h-4 w-4" />
              Novo Evento
            </Button>
          </div>

          {/* Mini Calendar */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <CalendarIcon className="h-4 w-4" />
                Calendário
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <Calendar
                mode="single"
                selected={calendarSnapshot.ui.selectedDate}
                onSelect={handleDateSelect}
                month={calendarSnapshot.view.startDate}
                onMonthChange={calendarActions.setStartDate}
                modifiers={modifiers}
                modifiersStyles={modifiersStyles}
                className="w-full"
                // Custom day rendering with events indicators
                // components={{
                //   DayContent: ({ date, ...props }) => {
                //     const dateKey = format(date, 'yyyy-MM-dd');
                //     const dayEvents = eventsByDate[dateKey] || [];

                //     return (
                //       <div className="relative w-full h-full flex items-center justify-center">
                //         <span {...props}>{date.getDate()}</span>
                //         {dayEvents.length > 0 && (
                //           <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2">
                //             <div className="flex gap-0.5">
                //               {dayEvents.slice(0, 3).map((event, index) => (
                //                 <Dot
                //                   key={index}
                //                   className="h-2 w-2 p-0"
                //                   style={{ color: event.color }}
                //                 />
                //               ))}
                //               {dayEvents.length > 3 && (
                //                 <Dot className="h-2 w-2 p-0 text-muted-foreground" />
                //               )}
                //             </div>
                //           </div>
                //         )}
                //       </div>
                //     );
                //   },
                // }}
              />
            </CardContent>
          </Card>

          {/* Event Categories */}
          <Card>
            <Collapsible open={categoriesOpen} onOpenChange={setCategoriesOpen}>
              <CardHeader className="pb-3">
                <CollapsibleTrigger asChild>
                  <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                    <CardTitle className="text-sm flex items-center gap-2">
                      <Tag className="h-4 w-4" />
                      Categorias
                    </CardTitle>
                    <ChevronRight className={cn(
                      "h-4 w-4 transition-transform",
                      categoriesOpen && "rotate-90"
                    )} />
                  </Button>
                </CollapsibleTrigger>
              </CardHeader>
              <CollapsibleContent>
                <CardContent className="pt-0 space-y-2">
                  {categoriesLoading ? (
                    <div className="space-y-2">
                      {[...Array(3)].map((_, i) => (
                        <Skeleton key={i} className="h-8 w-full" />
                      ))}
                    </div>
                  ) : categories.length > 0 ? (
                    <>
                      {categories.map((category) => {
                        const isActive = calendarSnapshot.filters.categories?.includes(category.name);
                        const categoryEvents = events.filter(e => e.category === category.name);

                        return (
                          <Button
                            key={category.$id}
                            variant={isActive ? "secondary" : "ghost"}
                            size="sm"
                            className="w-full justify-between h-auto p-2"
                            onClick={() => {
                              const currentCategories = calendarSnapshot.filters.categories || [];
                              if (isActive) {
                                calendarActions.setCategoryFilter(
                                  currentCategories.filter(c => c !== category.name)
                                );
                              } else {
                                calendarActions.setCategoryFilter([...currentCategories, category.name]);
                              }
                            }}
                          >
                            <div className="flex items-center gap-2 min-w-0">
                              <div
                                className="w-3 h-3 rounded-full flex-shrink-0"
                                style={{ backgroundColor: category.color }}
                              />
                              <span className="truncate text-sm">{category.name}</span>
                            </div>
                            <Badge variant="outline" className="text-xs">
                              {categoryEvents.length}
                            </Badge>
                          </Button>
                        );
                      })}
                      <Separator className="my-2" />
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={modalsActions.openCreateCategory}
                        className="w-full gap-2 text-muted-foreground"
                      >
                        <Plus className="h-3 w-3" />
                        Nova Categoria
                      </Button>
                    </>
                  ) : (
                    <div className="text-center py-4">
                      <p className="text-sm text-muted-foreground mb-2">
                        Nenhuma categoria criada
                      </p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={modalsActions.openCreateCategory}
                        className="gap-2"
                      >
                        <Plus className="h-3 w-3" />
                        Criar Categoria
                      </Button>
                    </div>
                  )}
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>

          {/* Upcoming Events */}
          <Card>
            <Collapsible open={upcomingOpen} onOpenChange={setUpcomingOpen}>
              <CardHeader className="pb-3">
                <CollapsibleTrigger asChild>
                  <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                    <CardTitle className="text-sm flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      Próximos Eventos
                    </CardTitle>
                    <ChevronRight className={cn(
                      "h-4 w-4 transition-transform",
                      upcomingOpen && "rotate-90"
                    )} />
                  </Button>
                </CollapsibleTrigger>
              </CardHeader>
              <CollapsibleContent>
                <CardContent className="pt-0 space-y-2">
                  {upcomingLoading ? (
                    <div className="space-y-2">
                      {[...Array(3)].map((_, i) => (
                        <Skeleton key={i} className="h-16 w-full" />
                      ))}
                    </div>
                  ) : upcomingEvents.length > 0 ? (
                    upcomingEvents.slice(0, 5).map((event) => (
                      <Button
                        key={event.$id}
                        variant="ghost"
                        className="w-full h-auto p-2 justify-start"
                        onClick={() => modalsActions.openViewEvent(event)}
                      >
                        <div className="flex items-start gap-2 w-full">
                          <div
                            className="w-3 h-3 rounded-full flex-shrink-0 mt-1"
                            style={{ backgroundColor: event.color }}
                          />
                          <div className="flex-1 min-w-0 text-left">
                            <div className="font-medium text-sm truncate">
                              {event.title}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {format(new Date(event.startDate), 'EEE, dd/MM', { locale: ptBR })}
                              {!event.allDay && (
                                <> às {format(new Date(event.startDate), 'HH:mm')}</>
                              )}
                            </div>
                            {event.location && (
                              <div className="text-xs text-muted-foreground truncate">
                                📍 {event.location}
                              </div>
                            )}
                          </div>
                        </div>
                      </Button>
                    ))
                  ) : (
                    <div className="text-center py-4">
                      <p className="text-sm text-muted-foreground">
                        Nenhum evento próximo
                      </p>
                    </div>
                  )}
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>

          {/* Quick Filters */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Filter className="h-4 w-4" />
                Filtros Rápidos
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0 space-y-2">
              <Button
                variant={calendarSnapshot.filters.status?.includes('agendado') ? "secondary" : "ghost"}
                size="sm"
                className="w-full justify-start"
                onClick={() => {
                  const current = calendarSnapshot.filters.status || [];
                  const isActive = current.includes('agendado');
                  if (isActive) {
                    calendarActions.setStatusFilter(current.filter(s => s !== 'agendado'));
                  } else {
                    calendarActions.setStatusFilter([...current, 'agendado']);
                  }
                }}
              >
                Eventos Agendados
              </Button>

              <Button
                variant={calendarSnapshot.filters.types?.includes('meeting') ? "secondary" : "ghost"}
                size="sm"
                className="w-full justify-start"
                onClick={() => {
                  const current = calendarSnapshot.filters.types || [];
                  const isActive = current.includes('meeting');
                  if (isActive) {
                    calendarActions.setTypeFilter(current.filter(t => t !== 'meeting'));
                  } else {
                    calendarActions.setTypeFilter([...current, 'meeting']);
                  }
                }}
              >
                Reuniões
              </Button>

              <Button
                variant={calendarSnapshot.filters.priorities?.includes('alta') ? "secondary" : "ghost"}
                size="sm"
                className="w-full justify-start"
                onClick={() => {
                  const current = calendarSnapshot.filters.priorities || [];
                  const isActive = current.includes('alta');
                  if (isActive) {
                    calendarActions.setPriorityFilter(current.filter(p => p !== 'alta'));
                  } else {
                    calendarActions.setPriorityFilter([...current, 'alta']);
                  }
                }}
              >
                Alta Prioridade
              </Button>

              <Separator className="my-2" />

              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-start text-muted-foreground"
                onClick={calendarActions.clearFilters}
              >
                Limpar Filtros
              </Button>
            </CardContent>
          </Card>
        </div>
      </ScrollArea>
    </div>
  );
}
