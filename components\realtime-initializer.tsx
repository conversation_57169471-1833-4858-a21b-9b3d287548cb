/**
 * Inicializador Realtime Simples
 * Componente React que inicia todos os hooks realtime
 */

import { useEffect } from 'react';
import { useAuth } from '../hooks/use-auth';
import { connect, disconnect } from '../lib/realtime/realtime';
import { useClientsController } from '../hooks/controllers/use-clients-controller';
import { useNotificationsController } from '../hooks/controllers/use-notifications-controller';
import { useChatsController } from '../hooks/controllers/use-chats-controller';
import { useMessagesController } from '../hooks/controllers/use-messages-controller';
import { useEventsController } from '../hooks/controllers/use-events-controller';
import { useKanbanController } from '../hooks/controllers/use-kanban-controller';
import { useActivitiesController } from '../hooks/controllers/use-activities-controller';
import { useTeamsController } from '../hooks/controllers/use-teams-controller';
import { useStorageController } from '../hooks/controllers/use-storage-controller';

export function RealtimeInitializer() {
  const { user } = useAuth();

  // Inicializar controllers sempre (hooks devem ser chamados sempre)
  useClientsController();
  useNotificationsController();
  useChatsController();
  useMessagesController();
  useEventsController();
  useKanbanController();
  useActivitiesController();
  useTeamsController();
  useStorageController();

  useEffect(() => {
    if (user) {
      console.log('🔗 Usuário logado, conectando realtime...');
      connect().catch(error => {
        console.error('❌ Erro ao conectar realtime:', error);
      });

      return () => {
        console.log('🔌 Desconectando realtime...');
        disconnect();
        // Controllers fazem cleanup automaticamente via useEffect
      };
    } else {
      disconnect();
    }
  }, [user]);

  return null; // Componente invisível
}
