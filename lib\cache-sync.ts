/**
 * Sistema Simplificado de Sincronização de Cache
 * Estratégia: IndexedDB local-first sem cache de preferências do usuário
 * - Se IndexedDB tem dados: servir dados locais e ignorar fetch
 * - Se IndexedDB vazio: fazer fetch do servidor
 * - onSuccess: sincronizar dados atualizados baseado em updatedAt/createdAt
 */

import { databases, DATABASE_ID, COLLECTIONS } from "./appwrite/config";
import { Query } from "appwrite";
import { getCacheConfig, getCollectionStores, getStoreIndexes } from './cache-config';

// ============================================================================
// TIPOS E INTERFACES
// ============================================================================

export interface CacheTimestamp {
  collection: string;
  timestamp: string; // ISO string
  lastSync: number; // timestamp local
}

export interface CacheSyncOptions {
  collection: string;
  userId?: string;
}

// ============================================================================
// FUNÇÕES DE INDEXEDDB
// ============================================================================

/**
 * Verifica se IndexedDB está disponível
 */
export function isIndexedDBAvailable(): boolean {
  return typeof window !== 'undefined' && 'indexedDB' in window;
}

/**
 * Cache para conexão do IndexedDB para evitar múltiplas aberturas
 */
let dbConnection: IDBDatabase | null = null;
let dbPromise: Promise<IDBDatabase> | null = null;

/**
 * Obtém a versão atual do banco sem abrir para upgrade
 */
async function getCurrentDBVersion(): Promise<number> {
  const config = getCacheConfig();

  return new Promise((resolve) => {
    const request = indexedDB.open(config.dbName);

    request.onsuccess = () => {
      const db = request.result;
      const version = db.version;
      db.close();
      resolve(version);
    };

    request.onerror = () => {
      // Se não existe, começar com versão 1
      resolve(0);
    };

    request.onupgradeneeded = () => {
      // Se trigger upgrade, cancelar e retornar versão 0
      request.transaction?.abort();
      resolve(0);
    };
  });
}

/**
 * Obtém conexão com IndexedDB com suporte a criação dinâmica de stores
 * Sistema robusto que evita conflitos de versão
 */
async function getIndexedDB(requiredStores: string[] = []): Promise<IDBDatabase> {
  const config = getCacheConfig();

  // Se já temos uma conexão válida, verificar se tem os stores necessários
  if (dbConnection && !dbConnection.objectStoreNames) {
    // Conexão inválida, limpar
    dbConnection = null;
    dbPromise = null;
  }

  if (dbConnection) {
    const missingStores = requiredStores.filter(store => !dbConnection!.objectStoreNames.contains(store));
    if (missingStores.length === 0) {
      return dbConnection;
    }
    // Se faltam stores, fechar conexão atual e recriar
    dbConnection.close();
    dbConnection = null;
    dbPromise = null;
  }

  // Se já há uma promise em andamento, aguardar
  if (dbPromise) {
    return dbPromise;
  }

  dbPromise = new Promise(async (resolve, reject) => {
    try {
      // Obter versão atual do banco
      const currentVersion = await getCurrentDBVersion();

      // Primeiro, tentar abrir sem especificar versão para verificar stores
      let request = indexedDB.open(config.dbName);

      request.onerror = () => {
        dbPromise = null;
        reject(request.error);
      };

      request.onsuccess = () => {
        const db = request.result;

        // Verificar se todos os stores necessários existem
        const missingStores = requiredStores.filter(store => !db.objectStoreNames.contains(store));

        if (missingStores.length > 0) {
          // Fechar conexão atual e reabrir com versão incrementada
          db.close();

          const newVersion = Math.max(currentVersion + 1, db.version + 1);
          console.log(`🔄 Criando stores ausentes: ${missingStores.join(', ')} (versão ${newVersion})`);

          const upgradeRequest = indexedDB.open(config.dbName, newVersion);

          upgradeRequest.onerror = () => {
            dbPromise = null;
            reject(upgradeRequest.error);
          };

          upgradeRequest.onsuccess = () => {
            dbConnection = upgradeRequest.result;

            // Adicionar handler para detectar quando a conexão é fechada
            dbConnection!.onclose = () => {
              dbConnection = null;
              dbPromise = null;
            };

            dbConnection!.onerror = (event) => {
              console.error('IndexedDB connection error:', event);
            };

            resolve(dbConnection!);
          };

          upgradeRequest.onupgradeneeded = (event) => {
            const upgradeDb = (event.target as IDBOpenDBRequest).result;

            // Criar stores iniciais se não existirem
            createInitialStores(upgradeDb);

            // Criar stores ausentes
            createMissingStores(upgradeDb, missingStores);
          };

          upgradeRequest.onblocked = () => {
            console.warn('⚠️ IndexedDB upgrade bloqueado. Feche outras abas do aplicativo.');
            // Tentar novamente após um delay
            setTimeout(() => {
              dbPromise = null;
              getIndexedDB(requiredStores).then(resolve).catch(reject);
            }, 1000);
          };
        } else {
          dbConnection = db;

          // Adicionar handlers
          dbConnection.onclose = () => {
            dbConnection = null;
            dbPromise = null;
          };

          dbConnection.onerror = (event) => {
            console.error('IndexedDB connection error:', event);
          };

          resolve(dbConnection);
        }
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        createInitialStores(db);
        createMissingStores(db, requiredStores);
      };

    } catch (error) {
      dbPromise = null;
      reject(error);
    }
  });

  return dbPromise;
}

/**
 * Cria stores iniciais obrigatórios
 */
function createInitialStores(db: IDBDatabase): void {
  const allStores = getCollectionStores();

  allStores.forEach(storeName => {
    if (!db.objectStoreNames.contains(storeName)) {
      console.log(`📦 Criando store inicial: ${storeName}`);

      const store = db.createObjectStore(storeName, {
        keyPath: storeName === 'cache_metadata' ? 'key' : '$id',
      });

      // Criar índices específicos para cada store
      const indexes = getStoreIndexes(storeName);
      indexes.forEach((index) => {
        try {
          store.createIndex(index.name, index.keyPath, {
            unique: index.unique || false,
          });
        } catch (indexError) {
          console.warn(`⚠️ Falha ao criar índice ${index.name} em ${storeName}:`, indexError);
        }
      });
    }
  });
}

/**
 * Cria stores dinâmicos conforme necessário
 */
function createMissingStores(db: IDBDatabase, storeNames: string[]): void {
  storeNames.forEach(storeName => {
    if (!db.objectStoreNames.contains(storeName)) {
      console.log(`📦 Criando store dinâmico: ${storeName}`);

      const store = db.createObjectStore(storeName, {
        keyPath: storeName === 'cache_metadata' ? 'key' : '$id',
      });

      // Criar índices específicos para este store
      const indexes = getStoreIndexes(storeName);
      indexes.forEach((index) => {
        try {
          store.createIndex(index.name, index.keyPath, {
            unique: index.unique || false,
          });
        } catch (indexError) {
          console.warn(`⚠️ Falha ao criar índice ${index.name} em ${storeName}:`, indexError);
        }
      });
    }
  });
}

/**
 * Sanitiza dados para IndexedDB removendo propriedades não serializáveis
 */
function sanitizeForIndexedDB<T>(data: T): T {
  try {
    // Usar JSON.parse(JSON.stringify()) para remover propriedades não serializáveis
    // e garantir que o objeto seja compatível com structured clone algorithm
    const sanitized = JSON.parse(JSON.stringify(data));

    // Log para debug apenas em desenvolvimento
    if (process.env.NODE_ENV === "development") {
      const originalKeys = typeof data === 'object' && data !== null ? Object.keys(data) : [];
      const sanitizedKeys = typeof sanitized === 'object' && sanitized !== null ? Object.keys(sanitized) : [];
      const removedKeys = originalKeys.filter(key => !sanitizedKeys.includes(key));

      if (removedKeys.length > 0) {
        console.warn(`🧹 Propriedades removidas durante sanitização:`, removedKeys);
      }
    }

    return sanitized;
  } catch (error) {
    console.error('❌ Erro ao sanitizar dados para IndexedDB:', error);
    // Se falhar, tentar criar um objeto limpo apenas com propriedades básicas
    if (typeof data === 'object' && data !== null) {
      const sanitized = {} as T;
      let removedCount = 0;

      for (const [key, value] of Object.entries(data)) {
        try {
          // Testar se a propriedade é serializável
          JSON.stringify(value);
          (sanitized as any)[key] = value;
        } catch {
          console.warn(`⚠️ Propriedade ${key} removida por não ser serializável`);
          removedCount++;
        }
      }

      if (removedCount > 0) {
        console.warn(`🧹 Total de ${removedCount} propriedades não serializáveis removidas`);
      }

      return sanitized;
    }
    return data;
  }
}

/**
 * Limpa completamente o IndexedDB em caso de erros irrecuperáveis
 */
export async function clearIndexedDB(): Promise<void> {
  if (!isIndexedDBAvailable()) {
    return;
  }

  const config = getCacheConfig();

  try {
    // Fechar conexão atual se existir
    if (dbConnection) {
      dbConnection.close();
      dbConnection = null;
      dbPromise = null;
    }

    // Deletar o banco completamente
    await new Promise<void>((resolve, reject) => {
      const deleteRequest = indexedDB.deleteDatabase(config.dbName);

      deleteRequest.onsuccess = () => {
        console.log(`🗑️ IndexedDB limpo completamente: ${config.dbName}`);
        resolve();
      };

      deleteRequest.onerror = () => {
        console.error('❌ Erro ao limpar IndexedDB:', deleteRequest.error);
        reject(deleteRequest.error);
      };

      deleteRequest.onblocked = () => {
        console.warn('⚠️ Limpeza do IndexedDB bloqueada. Feche outras abas.');
        // Tentar novamente após delay
        setTimeout(() => {
          clearIndexedDB().then(resolve).catch(reject);
        }, 1000);
      };
    });
  } catch (error) {
    console.error('❌ Erro ao limpar IndexedDB:', error);
  }
}

/**
 * Salva dados no IndexedDB com tratamento robusto de erros
 */
export async function saveToIndexedDB<T extends { $id: string; $updatedAt: string }>(
  collection: string,
  data: T | T[],
  options: Partial<CacheSyncOptions> = {}
): Promise<void> {
  if (!isIndexedDBAvailable()) {
    console.warn('IndexedDB não disponível');
    return;
  }

  let retryCount = 0;
  const maxRetries = 2;

  while (retryCount <= maxRetries) {
    try {
      const db = await getIndexedDB([collection, 'cache_metadata']);
      const transaction = db.transaction([collection, 'cache_metadata'], 'readwrite');
      const store = transaction.objectStore(collection);
      const metadataStore = transaction.objectStore('cache_metadata');

      // Salvar dados (sanitizados)
      const items = Array.isArray(data) ? data : [data];
      for (const item of items) {
        const sanitizedItem = sanitizeForIndexedDB(item);
        await new Promise<void>((resolve, reject) => {
          const request = store.put(sanitizedItem);
          request.onsuccess = () => resolve();
          request.onerror = () => reject(request.error);
        });
      }

      // Atualizar metadata do cache
      const metadata = {
        key: `${collection}_${options.userId || 'global'}`,
        lastUpdated: Date.now(),
        collection,
        userId: options.userId,
        itemCount: items.length
      };

      await new Promise<void>((resolve, reject) => {
        const request = metadataStore.put(metadata);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });

      // Não fechar a conexão aqui - deixar o cache gerenciar
      console.log(`✅ Salvos ${items.length} itens em ${collection} no IndexedDB`);
      return; // Sucesso, sair do loop

    } catch (error: any) {
      console.error(`❌ Erro ao salvar em ${collection} no IndexedDB (tentativa ${retryCount + 1}):`, error);

      // Se é erro de versão e ainda temos tentativas
      if (retryCount < maxRetries && (
        error?.name === 'VersionError' ||
        error?.message?.includes('version') ||
        error?.message?.includes('Version')
      )) {
        console.log(`🔄 Tentando limpar cache e recriar (tentativa ${retryCount + 1})`);

        // Limpar conexão atual
        if (dbConnection) {
          dbConnection.close();
          dbConnection = null;
          dbPromise = null;
        }

        retryCount++;

        // Se é a última tentativa, limpar completamente
        if (retryCount === maxRetries) {
          await clearIndexedDB();
        }

        // Aguardar um pouco antes de tentar novamente
        await new Promise(resolve => setTimeout(resolve, 100 * retryCount));
        continue;
      }

      // Erro não relacionado a versão ou esgotaram tentativas
      break;
    }
  }
}

/**
 * Obtém dados do IndexedDB com tratamento robusto de erros
 */
export async function getFromIndexedDB<T>(
  collection: string,
  userId?: string
): Promise<T[]> {
  if (!isIndexedDBAvailable()) {
    return [];
  }

  let retryCount = 0;
  const maxRetries = 2;

  while (retryCount <= maxRetries) {
    try {
      const db = await getIndexedDB([collection]);
      const transaction = db.transaction([collection], 'readonly');
      const store = transaction.objectStore(collection);

      let request: IDBRequest;

      if (userId) {
        // Filtrar por userId usando index (se existir)
        try {
          if (store.indexNames.contains('userId')) {
            const index = store.index('userId');
            request = index.getAll(userId);
          } else {
            // Se não há índice userId, buscar todos e filtrar manualmente
            console.warn(`⚠️ Índice 'userId' não encontrado em ${collection}, filtrando manualmente`);
            request = store.getAll();
          }
        } catch (error) {
          console.warn(`⚠️ Erro ao acessar índice userId em ${collection}:`, error);
          request = store.getAll();
        }
      } else {
        // Obter todos os dados
        request = store.getAll();
      }

      const result = await new Promise<T[]>((resolve, reject) => {
        request.onsuccess = () => {
          let data = request.result;

          // Se buscamos por userId mas não usamos índice, filtrar manualmente
          if (userId && (!store.indexNames.contains('userId') || request === store.getAll())) {
            data = data.filter((item: any) => item.userId === userId);
          }

          resolve(data);
        };
        request.onerror = () => reject(request.error);
      });

      // Não fechar a conexão aqui - deixar o cache gerenciar
      return result;

    } catch (error: any) {
      console.error(`❌ Erro ao obter dados de ${collection} do IndexedDB (tentativa ${retryCount + 1}):`, error);

      // Se é erro de versão e ainda temos tentativas
      if (retryCount < maxRetries && (
        error?.name === 'VersionError' ||
        error?.message?.includes('version') ||
        error?.message?.includes('Version')
      )) {
        console.log(`🔄 Tentando limpar cache e recriar para leitura (tentativa ${retryCount + 1})`);

        // Limpar conexão atual
        if (dbConnection) {
          dbConnection.close();
          dbConnection = null;
          dbPromise = null;
        }

        retryCount++;

        // Se é a última tentativa, limpar completamente
        if (retryCount === maxRetries) {
          await clearIndexedDB();
        }

        // Aguardar um pouco antes de tentar novamente
        await new Promise(resolve => setTimeout(resolve, 100 * retryCount));
        continue;
      }

      // Erro não relacionado a versão ou esgotaram tentativas
      return [];
    }
  }

  return [];
}

/**
 * Remove item do IndexedDB com tratamento robusto de erros
 */
export async function removeFromIndexedDB(
  collection: string,
  id: string
): Promise<void> {
  if (!isIndexedDBAvailable()) {
    return;
  }

  let retryCount = 0;
  const maxRetries = 2;

  while (retryCount <= maxRetries) {
    try {
      const db = await getIndexedDB([collection]);
      const transaction = db.transaction([collection], 'readwrite');
      const store = transaction.objectStore(collection);

      await new Promise<void>((resolve, reject) => {
        const request = store.delete(id);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });

      // Não fechar a conexão aqui - deixar o cache gerenciar
      console.log(`✅ Item ${id} removido de ${collection} no IndexedDB`);
      return; // Sucesso, sair do loop

    } catch (error: any) {
      console.error(`❌ Erro ao remover item de ${collection} no IndexedDB (tentativa ${retryCount + 1}):`, error);

      // Se é erro de versão e ainda temos tentativas
      if (retryCount < maxRetries && (
        error?.name === 'VersionError' ||
        error?.message?.includes('version') ||
        error?.message?.includes('Version')
      )) {
        console.log(`🔄 Tentando limpar cache e recriar para remoção (tentativa ${retryCount + 1})`);

        // Limpar conexão atual
        if (dbConnection) {
          dbConnection.close();
          dbConnection = null;
          dbPromise = null;
        }

        retryCount++;

        // Se é a última tentativa, limpar completamente
        if (retryCount === maxRetries) {
          await clearIndexedDB();
        }

        // Aguardar um pouco antes de tentar novamente
        await new Promise(resolve => setTimeout(resolve, 100 * retryCount));
        continue;
      }

      // Erro não relacionado a versão ou esgotaram tentativas
      break;
    }
  }
}

/**
 * Função utilitária para detectar e recuperar de erros de IndexedDB
 */
export async function handleIndexedDBError(error: any, operation: string): Promise<boolean> {
  console.error(`❌ Erro no IndexedDB durante ${operation}:`, error);

  // Verificar se é um erro que pode ser resolvido limpando o cache
  const isRecoverableError = (
    error?.name === 'VersionError' ||
    error?.name === 'InvalidStateError' ||
    error?.name === 'UnknownError' ||
    error?.message?.includes('version') ||
    error?.message?.includes('Version') ||
    error?.message?.includes('database') ||
    error?.message?.includes('connection')
  );

  if (isRecoverableError) {
    console.log(`🔄 Tentando recuperar do erro de IndexedDB limpando o cache...`);

    try {
      await clearIndexedDB();
      console.log(`✅ Cache limpo com sucesso após erro em ${operation}`);
      return true; // Indica que a recuperação foi tentada
    } catch (clearError) {
      console.error(`❌ Falha ao limpar cache após erro em ${operation}:`, clearError);
      return false;
    }
  }

  return false; // Erro não recuperável
}

/**
 * Wrapper para executar operações IndexedDB com recuperação automática
 */
export async function executeWithRecovery<T>(
  operation: () => Promise<T>,
  operationName: string,
  defaultValue: T
): Promise<T> {
  try {
    return await operation();
  } catch (error) {
    const recovered = await handleIndexedDBError(error, operationName);

    if (recovered) {
      // Tentar a operação novamente após recuperação
      try {
        console.log(`🔄 Tentando ${operationName} novamente após recuperação...`);
        return await operation();
      } catch (retryError) {
        console.error(`❌ Falha na segunda tentativa de ${operationName}:`, retryError);
        return defaultValue;
      }
    }

    return defaultValue;
  }
}

// ============================================================================
// FUNÇÕES DE SINCRONIZAÇÃO PRINCIPAL
// ============================================================================

/**
 * Verifica se IndexedDB tem dados para uma coleção
 */
export async function hasDataInIndexedDB(
  collection: string,
  userId?: string
): Promise<boolean> {
  const data = await getFromIndexedDB(collection, userId);
  return data.length > 0;
}

/**
 * Função pública para limpar cache em caso de problemas
 * Pode ser chamada manualmente quando há problemas persistentes
 */
export async function clearCacheManually(): Promise<void> {
  console.log('🧹 Limpeza manual do cache solicitada...');
  await clearIndexedDB();

  // Limpar também o cache do React Query se disponível
  if (typeof window !== 'undefined' && (window as any).queryClient) {
    (window as any).queryClient.clear();
    console.log('🧹 Cache do React Query também limpo');
  }

  console.log('✅ Limpeza manual do cache concluída');
}

/**
 * Obtém o maior timestamp (updatedAt) dos dados locais
 */
export async function getLatestLocalTimestamp(
  collection: string,
  userId?: string
): Promise<string | null> {
  const data = await getFromIndexedDB<{ $updatedAt: string }>(collection, userId);

  if (data.length === 0) {
    return null;
  }

  // Encontrar o item com maior updatedAt
  const latestItem = data.reduce((latest, current) =>
    new Date(current.$updatedAt) > new Date(latest.$updatedAt) ? current : latest
  );

  return latestItem.$updatedAt;
}

/**
 * Busca dados atualizados do servidor baseado em timestamp
 * INCLUINDO documentos soft deleted para sincronização completa
 */
export async function fetchUpdatedDataFromServer<T>(
  collection: string,
  lastTimestamp: string,
  userId?: string
): Promise<T[]> {
  try {
    const queries = [
      Query.greaterThan('$updatedAt', lastTimestamp),
      Query.orderDesc('$updatedAt'),
      Query.limit(1000)
      // ⚠️ NÃO filtrar isDeleted aqui - precisamos de TODOS os documentos atualizados
      // incluindo os que foram soft deleted para remover do cache local
    ];

    if (userId) {
      queries.unshift(Query.equal('userId', userId));
    }

    // Mapear nome da coleção para o ID correto
    const collectionId = collection === 'clients' ? COLLECTIONS.CLIENTS : collection;

    // Usar databases.listDocuments diretamente (sem filtros de soft delete)
    const response = await databases.listDocuments(
      DATABASE_ID,
      collectionId,
      queries
    );

    console.log(`🔄 Encontrados ${response.documents.length} itens atualizados em ${collection} desde ${lastTimestamp}`);

    // Log para debug: quantos são deletados vs ativos
    const deletedCount = response.documents.filter((doc: any) => doc.isDeleted === true).length;
    const activeCount = response.documents.length - deletedCount;
    console.log(`📊 Sync ${collection}: ${activeCount} ativos, ${deletedCount} deletados`);

    return response.documents as T[];
  } catch (error) {
    console.error(`❌ Erro ao buscar dados atualizados de ${collection}:`, error);
    return [];
  }
}

/**
 * Sincroniza dados após mutação (create, update, delete)
 */
export async function syncAfterMutation<T extends { $id: string; $updatedAt: string }>(
  collection: string,
  operation: 'create' | 'update' | 'delete',
  data: T | string, // T para create/update, string (id) para delete
  userId?: string
): Promise<void> {
  try {
    if (operation === 'delete') {
      // Remover do IndexedDB
      await removeFromIndexedDB(collection, data as string);
    } else {
      // Adicionar/atualizar no IndexedDB (dados já são sanitizados na saveToIndexedDB)
      await saveToIndexedDB(collection, data as T, {
        collection,
        userId
      });
    }

    console.log(`✅ Sincronização ${operation} concluída para ${collection}`);
  } catch (error) {
    console.error(`❌ Erro na sincronização ${operation} para ${collection}:`, error);
  }
}

/**
 * Sincroniza dados atualizados do servidor (para usar no onSuccess)
 * Só executa se IndexedDB não estiver vazio
 * Processa documentos deletados removendo-os do cache local
 */
export async function syncUpdatedDataFromServer<T extends { $id: string; $updatedAt: string; isDeleted?: boolean }>(
  collection: string,
  userId?: string,
  onDataUpdated?: (activeData: T[], deletedData: T[]) => void
): Promise<void> {
  try {
    // Verificar se IndexedDB tem dados
    const hasData = await hasDataInIndexedDB(collection, userId);

    if (!hasData) {
      console.log(`⏭️ IndexedDB vazio para ${collection}, pulando sincronização`);
      return;
    }

    // Obter timestamp mais recente dos dados locais
    const latestTimestamp = await getLatestLocalTimestamp(collection, userId);

    if (!latestTimestamp) {
      console.log(`⏭️ Nenhum timestamp local encontrado para ${collection}`);
      return;
    }

    // Buscar dados atualizados do servidor (incluindo soft deleted)
    const updatedData = await fetchUpdatedDataFromServer<T>(collection, latestTimestamp, userId);

    if (updatedData.length > 0) {
      // Separar documentos ativos dos deletados
      const activeDocuments = updatedData.filter(doc => !doc.isDeleted);
      const deletedDocuments = updatedData.filter(doc => doc.isDeleted === true);

      // Processar documentos ativos (salvar/atualizar no IndexedDB)
      if (activeDocuments.length > 0) {
        await saveToIndexedDB(collection, activeDocuments, { collection, userId });
        console.log(`✅ Salvos ${activeDocuments.length} documentos ativos em ${collection}`);
      }

      // Processar documentos deletados (remover do IndexedDB)
      if (deletedDocuments.length > 0) {
        for (const deletedDoc of deletedDocuments) {
          await removeFromIndexedDB(collection, deletedDoc.$id);
        }
        console.log(`🗑️ Removidos ${deletedDocuments.length} documentos deletados de ${collection}`);
      }

      // Notificar sobre dados atualizados
      if (onDataUpdated) {
        onDataUpdated(activeDocuments, deletedDocuments);
      }

      console.log(`✅ Sincronizados ${updatedData.length} itens em ${collection} (${activeDocuments.length} ativos, ${deletedDocuments.length} deletados)`);
    } else {
      console.log(`✅ Nenhum dado novo encontrado para ${collection}`);
    }
  } catch (error) {
    console.error(`❌ Erro na sincronização de dados atualizados para ${collection}:`, error);
  }
}
