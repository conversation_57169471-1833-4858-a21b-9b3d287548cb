import { account, teams } from './config';

/**
 * Utilitários para debug do Appwrite
 */

/**
 * Verifica o status da configuração do Appwrite
 */
export async function checkAppwriteStatus() {
  try {
    console.log('🔍 Verificando configuração do Appwrite...');
    
    // Verificar conexão com o account
    const user = await account.get();
    console.log('✅ Usuário autenticado:', user.email);
    
    // Verificar teams disponíveis
    const userTeams = await teams.list();
    console.log('✅ Teams disponíveis:', userTeams.total);
    
    // Verificar configurações do projeto
    console.log('✅ Configurações:', {
      endpoint: process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT,
      projectId: process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID,
      databaseId: process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
    });
    
    return {
      status: 'ok',
      user,
      teams: userTeams.total,
    };
  } catch (error) {
    console.error('❌ Erro na configuração do Appwrite:', error);
    return {
      status: 'error',
      error: error instanceof Error ? error.message : 'Erro desconhecido',
    };
  }
}

/**
 * Testa o convite de membro com dados de debug
 */
export async function testTeamInvite(teamId: string, email: string) {
  try {
    console.log('🧪 Testando convite de membro...');
    console.log('Dados:', { teamId, email });
    
    // Verificar se o team existe
    const team = await teams.get(teamId);
    console.log('✅ Team encontrado:', team.name);
    
    // Verificar membros atuais
    const members = await teams.listMemberships(teamId);
    console.log('✅ Membros atuais:', members.total);
    
    // Tentar criar convite
    const invite = await teams.createMembership(
      teamId,
      ['member'], // roles
      email, // email
      undefined, // userId
      undefined, // phone - IMPORTANTE: não usar telefone
      `${window.location.origin}/dashboard/teams`, // url
      email.split('@')[0] // name
    );
    
    console.log('✅ Convite criado com sucesso:', invite);
    return {
      status: 'success',
      invite,
    };
  } catch (error) {
    console.error('❌ Erro ao testar convite:', error);
    return {
      status: 'error',
      error: error instanceof Error ? error.message : 'Erro desconhecido',
    };
  }
}

/**
 * Verifica as configurações de autenticação
 */
export function checkAuthConfig() {
  const config = {
    endpoint: process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT,
    projectId: process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID,
    databaseId: process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
    bucketId: process.env.NEXT_PUBLIC_APPWRITE_BUCKET_ID,
  };
  
  console.log('🔧 Configurações de autenticação:', config);
  
  const missing = Object.entries(config)
    .filter(([_, value]) => !value)
    .map(([key]) => key);
  
  if (missing.length > 0) {
    console.warn('⚠️ Configurações faltando:', missing);
    return { status: 'incomplete', missing };
  }
  
  console.log('✅ Todas as configurações estão presentes');
  return { status: 'complete', config };
}

/**
 * Função para debug no console do navegador
 * Use: window.debugAppwrite()
 */
export function setupDebugHelpers() {
  if (typeof window !== 'undefined') {
    (window as any).debugAppwrite = {
      checkStatus: checkAppwriteStatus,
      testInvite: testTeamInvite,
      checkConfig: checkAuthConfig,
    };
    
    console.log('🛠️ Debug helpers disponíveis em window.debugAppwrite');
  }
}
