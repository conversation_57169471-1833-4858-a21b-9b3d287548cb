# 📡 Sistema de Realtime Simples

Sistema de realtime ultra-simples e expansível com apenas 4 arquivos principais.

## 🏗️ Arquitetura

### 1. Store Valtio (`app/lib/realtime/store.ts`)
Estado global apenas para dados novos em tempo real:

```typescript
export const realtimeStore = proxy({
  // Dados novos das collections (apenas create/update)
  clients: [] as Client[],
  notifications: [] as Notification[],
  chats: [] as TeamChat[],
  messages: [] as ChatMessage[],

  // Status da conexão
  isConnected: false,
  isConnecting: false,
  error: null as string | null,
});
```

### 2. Listener Realtime (`app/lib/realtime/realtime.ts`)
Escuta eventos do Appwrite e atualiza o store Valtio:

- Conecta ao canal geral do database
- Filtra apenas create/update (ignora delete)
- Atualiza arrays correspondentes no store

### 3. Inicializador (`app/components/realtime-initializer.tsx`)
Componente React que inicia todos os controllers:

- Conecta/desconecta baseado no usuário logado
- Inicializa todos os controllers
- Cleanup automático

### 4. Controllers (`app/hooks/controllers/`)
Sincronizam Valtio → IndexedDB + React Query:

- Usam `subscribe` do Valtio (não useEffect)
- Adicionam dados novos aos existentes (não substituem)
- Salvam no IndexedDB
- Limpam store após processar

## 🚀 Como Usar

### Configuração Inicial
O sistema é inicializado automaticamente no `root.tsx`:

```tsx
<AuthProvider>
  <RealtimeInitializer />
  {/* resto da app */}
</AuthProvider>
```

### Status da Conexão
```tsx
import { useRealtimeStatus } from '@/hooks/use-realtime-status';

function MyComponent() {
  const { isConnected, isConnecting, error } = useRealtimeStatus();
  
  return (
    <div>
      Status: {isConnected ? 'Conectado' : 'Desconectado'}
    </div>
  );
}
```

## 🔧 Como Expandir

Para adicionar uma nova collection (ex: `tasks`):

### 1. Adicionar ao Store
```typescript
// app/lib/realtime/store.ts
export const realtimeStore = proxy({
  // ... existentes
  tasks: [] as Task[], // ← Adicionar aqui
});
```

### 2. Adicionar ao Listener
```typescript
// app/lib/realtime/realtime.ts
private updateStore(collection: string, action: string, payload: any) {
  // ... existentes
  case 'kanban_tasks': // ← Nome da collection no Appwrite
    this.updateArray('tasks', action, payload);
    break;
}
```

### 3. Criar Controller
```typescript
// app/hooks/controllers/use-tasks-controller.ts
export function useTasksController() {
  const queryClient = useQueryClient();

  const unsubscribe = subscribe(realtimeStore, () => {
    const tasks = realtimeStore.tasks;
    
    if (tasks.length === 0) return;

    tasks.forEach(task => {
      queryClient.setQueryData(['tasks'], (oldData: any) => {
        if (!oldData) return [task];
        
        const exists = oldData.find((item: any) => item.$id === task.$id);
        if (exists) {
          return oldData.map((item: any) => 
            item.$id === task.$id ? task : item
          );
        } else {
          return [...oldData, task];
        }
      });

      saveToIndexedDB('kanban_tasks', task, {
        collection: 'kanban_tasks',
        userId: task.userId
      });
    });

    realtimeStore.tasks = [];
  });

  return { unsubscribe };
}
```

### 4. Adicionar ao Inicializador
```typescript
// app/components/realtime-initializer.tsx
import { useTasksController } from '@/hooks/controllers/use-tasks-controller';

export function RealtimeInitializer() {
  // ... existentes
  const tasksController = useTasksController(); // ← Adicionar

  useEffect(() => {
    if (user) {
      // ...
      return () => {
        // ...
        tasksController.unsubscribe(); // ← Adicionar cleanup
      };
    }
  }, [user]);

  return null;
}
```

## ✨ Características

- **Simples**: Apenas 4 arquivos principais
- **Expansível**: Adicionar nova collection = 2-4 linhas de código
- **Performático**: Não substitui dados, apenas adiciona novos
- **Type-safe**: TypeScript em todos os lugares
- **Local-first**: Sincroniza com IndexedDB automaticamente
- **Limpo**: Sem useEffect, useState ou complexidade desnecessária

## 📝 Notas Importantes

1. **Delete é ignorado**: O sistema só processa create/update
2. **Dados são adicionados**: Não substitui arrays existentes
3. **Store é limpo**: Após processar, arrays são zerados
4. **IndexedDB automático**: Salvamento transparente
5. **React Query sync**: Atualização automática das queries

Total de linhas: ~200 linhas de código limpo e funcional.
