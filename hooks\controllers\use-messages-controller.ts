/**
 * Controller para Messages
 * Usa subscribe do Valtio - sem useEffect, useState, nada
 */

import { useEffect, useRef } from 'react';
import { subscribe } from 'valtio';
import { useQueryClient } from '@tanstack/react-query';
import { realtimeStore } from '../../lib/realtime/store';
import { saveToIndexedDB } from '../../lib/cache-sync';

export function useMessagesController() {
  const queryClient = useQueryClient();
  const unsubscribeRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    console.log('🎛️ Inicializando controller de mensagens...');

    // Subscribe do Valtio para mudanças no store
    unsubscribeRef.current = subscribe(realtimeStore, () => {
      const messages = realtimeStore.messages;

      if (messages.length === 0) return;

      console.log(`📦 Processando ${messages.length} mensagens do realtime...`);

      // Adicionar dados novos aos existentes no React Query
      messages.forEach(message => {
        console.log(`📝 Atualizando mensagem: ${message.$id} (chat: ${message.chatId}, team: ${message.teamId})`);

        // Atualizar query específica do chat usando teamId como key
        queryClient.setQueryData(['messages', message.chat, message.teamId], (oldData: any) => {
          if (!oldData) return [message];

          const exists = oldData.find((item: any) => item.$id === message.$id);
          if (exists) {
            // Atualizar existente
            return oldData.map((item: any) =>
              item.$id === message.$id ? message : item
            );
          } else {
            // Adicionar novo
            console.log(message)
            console.log(oldData)
            return [...oldData, message];
          }
        });

        // Salvar no IndexedDB usando teamId como userId
        saveToIndexedDB(`messages_${message.chat}`, message, {
          collection: `messages_${message.chat}`,
          userId: message.teamId
        });
      });

      
    });

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    };
  }, [queryClient]);

  return {
    unsubscribe: () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    }
  };
}
