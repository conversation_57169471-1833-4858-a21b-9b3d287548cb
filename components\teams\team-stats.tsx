import { IconTrendingDown, IconTrendingUp, IconUsers, IconUser<PERSON>heck, IconUserX, IconUserPlus } from "@tabler/icons-react";
import { Badge } from "../ui/badge";
import {
  Card,
  CardAction,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { useTeams, useTeamMembers } from "../../hooks/use-api";
import { useAuth } from "../../hooks/use-auth";

interface TeamStatsProps {
  teamId?: string;
  className?: string;
}

export function TeamStats({ teamId, className }: TeamStatsProps) {
  const { user } = useAuth();
  const { data: teams, isLoading: teamsLoading } = useTeams();
  const { data: members, isLoading: membersLoading } = useTeamMembers(teamId || '');

  // Se não há teamId, mostra stats gerais dos times do usuário
  const currentTeam = teamId ? teams?.find(t => t.$id === teamId) : null;
  const userTeams = teams || [];
  const teamMembers = members || [];

  const isLoading = teamsLoading || (teamId && membersLoading);

  // Determine padding classes based on className prop
  const paddingClasses = className?.includes('!px-0') ? '' : 'px-4 lg:px-6';
  const cleanClassName = className?.replace('!px-0', '').trim() || '';

  if (isLoading) {
    return (
      <div className={`grid grid-cols-1 gap-4 ${paddingClasses} @xl/main:grid-cols-2 @5xl/main:grid-cols-4 ${cleanClassName}`}>
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-4 bg-muted rounded w-24"></div>
              <div className="h-8 bg-muted rounded w-16"></div>
            </CardHeader>
          </Card>
        ))}
      </div>
    );
  }

  // Stats para um time específico
  if (teamId && currentTeam) {
    const totalMembers = teamMembers.length;
    const activeMembers = teamMembers.filter(m => m.confirm).length;
    const pendingInvites = teamMembers.filter(m => !m.confirm).length;
    const adminCount = teamMembers.filter(m => m.roles.includes('admin') || m.roles.includes('owner')).length;

    return (
      <div className={`*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 ${paddingClasses} *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs @xl/main:grid-cols-2 @5xl/main:grid-cols-4 ${cleanClassName}`}>
        {/* Total Members */}
        <Card className="@container/card">
          <CardHeader>
            <CardDescription>Total de Membros</CardDescription>
            <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
              {totalMembers}
            </CardTitle>
            <CardAction>
              <Badge variant="outline">
                <IconUsers />
                {currentTeam.name}
              </Badge>
            </CardAction>
          </CardHeader>
        </Card>

        {/* Active Members */}
        <Card className="@container/card">
          <CardHeader>
            <CardDescription>Membros Ativos</CardDescription>
            <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
              {activeMembers}
            </CardTitle>
            <CardAction>
              <Badge variant="outline">
                <IconUserCheck />
                {totalMembers > 0 ? Math.round((activeMembers / totalMembers) * 100) : 0}%
              </Badge>
            </CardAction>
          </CardHeader>
        </Card>

        {/* Pending Invites */}
        <Card className="@container/card">
          <CardHeader>
            <CardDescription>Convites Pendentes</CardDescription>
            <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
              {pendingInvites}
            </CardTitle>
            <CardAction>
              <Badge variant="outline">
                <IconUserPlus />
                Aguardando
              </Badge>
            </CardAction>
          </CardHeader>
        </Card>

        {/* Admins */}
        <Card className="@container/card">
          <CardHeader>
            <CardDescription>Administradores</CardDescription>
            <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
              {adminCount}
            </CardTitle>
            <CardAction>
              <Badge variant="outline">
                <IconUserCheck />
                Gestores
              </Badge>
            </CardAction>
          </CardHeader>
        </Card>
      </div>
    );
  }

  // Stats gerais dos times do usuário
  const totalTeams = userTeams.length;
  const ownedTeams = userTeams.filter(t => {
    // Verificar se o usuário é owner através dos memberships
    return true; // Simplificado por enquanto
  }).length;

  return (
    <div className={`*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 ${paddingClasses} *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs @xl/main:grid-cols-2 @5xl/main:grid-cols-4 ${cleanClassName}`}>
      {/* Total Teams */}
      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Total de Times</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
            {totalTeams}
          </CardTitle>
          <CardAction>
            <Badge variant="outline">
              <IconUsers />
              Participando
            </Badge>
          </CardAction>
        </CardHeader>
      </Card>

      {/* Owned Teams */}
      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Times que Lidero</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
            {ownedTeams}
          </CardTitle>
          <CardAction>
            <Badge variant="outline">
              <IconUserCheck />
              Proprietário
            </Badge>
          </CardAction>
        </CardHeader>
      </Card>

      {/* Placeholder for future stats */}
      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Membros Totais</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
            {userTeams.reduce((acc, team) => acc + (team.total || 0), 0)}
          </CardTitle>
          <CardAction>
            <Badge variant="outline">
              <IconUsers />
              Colaboradores
            </Badge>
          </CardAction>
        </CardHeader>
      </Card>

      {/* Active Teams */}
      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Times Ativos</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
            {totalTeams}
          </CardTitle>
          <CardAction>
            <Badge variant="outline">
              <IconTrendingUp />
              100%
            </Badge>
          </CardAction>
        </CardHeader>
      </Card>
    </div>
  );
}
