
'use client';

import { useEffect, useRef, useCallback } from "react";
import { Textarea } from "../ui/textarea";
import { cn } from "../../lib/utils";
import {
  ArrowUpIcon,
  Paperclip,
  Loader2,
} from "lucide-react";
import { EmojiPicker } from './emoji-picker';

interface UseAutoResizeTextareaProps {
  minHeight: number;
  maxHeight?: number;
}

function useAutoResizeTextarea({
  minHeight,
  maxHeight,
}: UseAutoResizeTextareaProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const adjustHeight = useCallback(
    (reset?: boolean) => {
      const textarea = textareaRef.current;
      if (!textarea) return;

      if (reset) {
        textarea.style.height = `${minHeight}px`;
        return;
      }

      // Temporarily shrink to get the right scrollHeight
      textarea.style.height = `${minHeight}px`;

      // Calculate new height
      const newHeight = Math.max(
        minHeight,
        Math.min(
          textarea.scrollHeight,
          maxHeight ?? Number.POSITIVE_INFINITY
        )
      );

      textarea.style.height = `${newHeight}px`;
    },
    [minHeight, maxHeight]
  );

  useEffect(() => {
    // Set initial height
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = `${minHeight}px`;
    }
  }, [minHeight]);

  // Adjust height on window resize
  useEffect(() => {
    const handleResize = () => adjustHeight();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [adjustHeight]);

  return { textareaRef, adjustHeight };
}

interface ModernChatInputProps {
  value: string;
  onChange: (value: string) => void;
  onSend: () => void;
  onKeyDown?: (e: React.KeyboardEvent) => void;
  placeholder?: string;
  disabled?: boolean;
  isLoading?: boolean;
  showFileUpload?: boolean;
  showEmojiPicker?: boolean;
  onFileUpload?: (fileData: { fileId: string; url: string; fileName: string; fileType: string; fileSize: number; }) => void;
  onEmojiSelect?: (emoji: string) => void;
  className?: string;
  minHeight?: number;
  maxHeight?: number;
}

export function ModernChatInput({
  value,
  onChange,
  onSend,
  onKeyDown,
  placeholder = "Digite sua mensagem...",
  disabled = false,
  isLoading = false,
  showFileUpload = true,
  showEmojiPicker = true,
  onFileUpload,
  onEmojiSelect,
  className,
  minHeight = 60,
  maxHeight = 200,
}: ModernChatInputProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { textareaRef, adjustHeight } = useAutoResizeTextarea({
    minHeight,
    maxHeight,
  });

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      if (value.trim() && !disabled && !isLoading) {
        onSend();
        adjustHeight(true);
      }
    }
    onKeyDown?.(e);
  };

  const handleEmojiSelect = (emoji: string) => {
    if (onEmojiSelect) {
      onEmojiSelect(emoji);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file || !onFileUpload) return;

    const fileData = {
      fileId: `file-${Date.now()}`,
      url: URL.createObjectURL(file),
      fileName: file.name,
      fileType: file.type,
      fileSize: file.size,
    };

    onFileUpload(fileData);

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const canSend = value.trim().length > 0 && !disabled && !isLoading;

  return (
    <div className={cn("w-full", className)}>
      <div className="relative bg-background rounded-xl border border-border shadow-sm">
        <div className="overflow-y-auto">
          <Textarea
            ref={textareaRef}
            value={value}
            onChange={(e) => {
              onChange(e.target.value);
              adjustHeight();
            }}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={disabled}
            className={cn(
              "w-full px-4 py-3 pr-16",
              "resize-none",
              "bg-transparent",
              "border-none",
              "text-foreground text-sm",
              "focus:outline-none",
              "focus-visible:ring-0 focus-visible:ring-offset-0",
              "placeholder:text-muted-foreground placeholder:text-sm",
              `min-h-[${minHeight}px]`
            )}
            style={{
              overflow: "hidden",
            }}
          />
        </div>

        <div className="flex items-center justify-between p-3 border-t border-border/50">
          <div className="flex items-center gap-2">
            {/* File Upload */}
            {showFileUpload && (
              <>
                <input
                  ref={fileInputRef}
                  type="file"
                  className="hidden"
                  onChange={handleFileSelect}
                  accept="image/*,video/*,.pdf,.doc,.docx,.txt"
                />
                <button
                  type="button"
                  className="group p-2 hover:bg-muted rounded-lg transition-colors flex items-center gap-1"
                  disabled={disabled}
                  onClick={() => fileInputRef.current?.click()}
                >
                  <Paperclip className="w-4 h-4 text-muted-foreground" />
                  <span className="text-xs text-muted-foreground hidden group-hover:inline transition-opacity">
                    Anexar
                  </span>
                </button>
              </>
            )}

            {/* Emoji Picker */}
            {showEmojiPicker && (
              <EmojiPicker
                onEmojiSelect={handleEmojiSelect}
                disabled={disabled}
              />
            )}
          </div>

          <div className="flex items-center gap-2">
            {/* Send Button */}
            <button
              type="button"
              className={cn(
                "px-2 py-2 rounded-lg text-sm transition-colors border flex items-center justify-center gap-1",
                canSend
                  ? "bg-primary text-primary-foreground border-primary hover:bg-primary/90"
                  : "text-muted-foreground border-border hover:border-border/80 hover:bg-muted"
              )}
              disabled={!canSend}
              onClick={onSend}
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <ArrowUpIcon className="w-4 h-4" />
              )}
              <span className="sr-only">Enviar</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
