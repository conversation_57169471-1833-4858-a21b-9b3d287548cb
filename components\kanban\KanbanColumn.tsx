/**
 * KanbanColumn Component
 * Individual column in the kanban board with sortable tasks
 */

import React from 'react';
import { useSortable, SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { useDroppable } from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';
import { Plus, MoreHorizontal, Eye, EyeOff } from 'lucide-react';

import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '../ui/dropdown-menu';

import { KanbanTask } from './KanbanTask';
import { kanbanActions } from '../../stores/kanban-store';
import { useDeleteColumn } from '../../hooks/api/use-kanban';
import { useAuth } from '../../hooks/use-auth';
import { useIsMobile } from '../../hooks/use-mobile';
import type { Column, Task } from '@/schemas/kanban';

interface KanbanColumnProps {
  column: Column;
  tasks: Task[];
  boardId: string;
}

export function KanbanColumn({ column, tasks, boardId }: KanbanColumnProps) {
  const { user } = useAuth();
  const deleteColumnMutation = useDeleteColumn();
  const isMobile = useIsMobile();

  // Para estrutura otimizada, labels estão embarcadas no board
  const labels: any[] = [];

  // Sortable for column reordering
  const {
    attributes,
    listeners,
    setNodeRef: setSortableRef,
    transform,
    transition,
    isDragging: isColumnDragging,
  } = useSortable({
    id: column.id,
    data: {
      type: 'column',
      column,
    },
  });

  // Droppable for tasks
  const { setNodeRef: setDroppableRef } = useDroppable({
    id: `column-${column.id}`,
    data: {
      type: 'column',
      column,
    },
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const handleDeleteColumn = () => {
    if (!user) return;

    const confirmMessage = tasks.length > 0
      ? `Esta coluna contém ${tasks.length} tarefa(s). Todas as tarefas serão excluídas junto com a coluna. Deseja continuar?`
      : 'Tem certeza que deseja excluir esta coluna?';

    if (window.confirm(confirmMessage)) {
      deleteColumnMutation.mutate({
        boardId,
        columnId: column.id,
        userId: user.$id,
      });
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critica':
        return 'bg-red-500';
      case 'alta':
        return 'bg-orange-500';
      case 'media':
        return 'bg-yellow-500';
      case 'baixa':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'todo':
        return 'bg-gray-500';
      case 'in_progress':
        return 'bg-blue-500';
      case 'review':
        return 'bg-purple-500';
      case 'done':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };

  // Calculate task statistics
  const taskStats = {
    total: tasks.length,
    completed: tasks.filter(task => task.status === 'done').length,
    overdue: tasks.filter(task =>
      task.dueDate && new Date(task.dueDate) < new Date() && task.status !== 'done'
    ).length,
    highPriority: tasks.filter(task =>
      task.priority === 'alta' || task.priority === 'critica'
    ).length,
  };

  return (
    <div
      ref={setSortableRef}
      style={style}
      className={`${isMobile ? 'min-w-72 max-w-72' : 'min-w-80 max-w-80'} ${isColumnDragging ? 'opacity-50' : ''}`}
      {...attributes}
    >
      <Card className="h-full flex flex-col">
        {/* Column Header */}
        <CardHeader className={isMobile ? "pb-2 px-3 pt-3" : "pb-3"}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 flex-1 min-w-0" {...listeners}>
              <div
                className={`w-3 h-3 rounded-full shrink-0 ${column.color ? `bg-[${column.color}]` : 'bg-gray-400'}`}
              />
              <CardTitle className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium truncate`}>
                {column.title}
              </CardTitle>
              <Badge variant="secondary" className="text-xs shrink-0">
                {taskStats.total}
              </Badge>
            </div>

            <div className="flex items-center gap-1">
              {/* Task limit indicator */}
              {column.taskLimit && taskStats.total >= column.taskLimit && (
                <Badge variant="destructive" className="text-xs">
                  Limite
                </Badge>
              )}

              {/* Column actions */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <MoreHorizontal className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => kanbanActions.openTaskCreate(column.id, boardId)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Nova Tarefa
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => kanbanActions.openColumnEdit(column)}>
                    Editar Coluna
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={handleDeleteColumn}
                    className="text-destructive"
                  >
                    Excluir Coluna
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Column description */}
          {column.description && !isMobile && (
            <p className="text-xs text-muted-foreground mt-1">
              {column.description}
            </p>
          )}

          {/* Task statistics */}
          {taskStats.total > 0 && !isMobile && (
            <div className="flex items-center gap-2 mt-2">
              {taskStats.completed > 0 && (
                <Badge variant="outline" className="text-xs">
                  ✓ {taskStats.completed}
                </Badge>
              )}
              {taskStats.overdue > 0 && (
                <Badge variant="destructive" className="text-xs">
                  ⚠ {taskStats.overdue}
                </Badge>
              )}
              {taskStats.highPriority > 0 && (
                <Badge variant="secondary" className="text-xs">
                  🔥 {taskStats.highPriority}
                </Badge>
              )}
            </div>
          )}
        </CardHeader>

        {/* Tasks Container */}
        <CardContent className={`flex-1 pt-0 ${isMobile ? 'pb-2 px-3' : 'pb-3'}`}>
          <div
            ref={setDroppableRef}
            className={`${isMobile ? 'min-h-24 space-y-1' : 'min-h-32 space-y-2'}`}
          >
            <SortableContext
              items={tasks.map(task => task.id)}
              strategy={verticalListSortingStrategy}
            >
              {tasks.map((task) => (
                <KanbanTask
                  key={task.id}
                  task={task}
                  columnId={column.id}
                  labels={labels}
                />
              ))}
            </SortableContext>

            {/* Add task button */}
            <Button
              variant="ghost"
              size="sm"
              className="w-full justify-start text-muted-foreground hover:text-foreground"
              onClick={() => kanbanActions.openTaskCreate(column.id, boardId)}
            >
              <Plus className="h-4 w-4 mr-2" />
              {isMobile ? "Adicionar" : "Adicionar tarefa"}
            </Button>
          </div>

          {/* Column footer info */}
          {column.taskLimit && (
            <div className="mt-3 pt-3 border-t">
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <span>Limite WIP</span>
                <span className={taskStats.total >= column.taskLimit ? 'text-destructive font-medium' : ''}>
                  {taskStats.total}/{column.taskLimit}
                </span>
              </div>
              <div className="w-full bg-muted rounded-full h-1 mt-1">
                <div
                  className={`h-1 rounded-full transition-all ${
                    taskStats.total >= column.taskLimit ? 'bg-destructive' : 'bg-primary'
                  }`}
                  style={{
                    width: `${Math.min((taskStats.total / column.taskLimit) * 100, 100)}%`,
                  }}
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
