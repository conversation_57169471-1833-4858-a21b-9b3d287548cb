import { useSnapshot } from 'valtio';
import { But<PERSON> } from '../ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { Badge } from '../ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import {
  Mail,
  Phone,
  Building,
  User,
  Calendar,
  Edit
} from 'lucide-react';
import { clientModalsStore } from '../../lib/stores/client-modals';
import type { Client } from '@/schemas/clients';

export function ClientViewModal() {
  const snap = useSnapshot(clientModalsStore);
  const client = snap.view.client;

  const handleClose = () => {
    clientModalsStore.view.isOpen = false;
    clientModalsStore.view.client = null;
  };

  const handleEdit = () => {
    if (client) {
      // Fechar modal de visualização e abrir de edição
      clientModalsStore.view.isOpen = false;
      clientModalsStore.view.client = null;
      clientModalsStore.edit.isOpen = true;
      clientModalsStore.edit.client = {
        ...client,
        tags: client.tags ? [...client.tags] : []
      } as Client;
    }
  };

  if (!client) return null;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ativo':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'inativo':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'prospecto':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'arquivado':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'alta':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'media':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'baixa':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'critica':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <Dialog open={snap.view.isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <Avatar className="h-10 w-10">
              <AvatarImage src={client.avatar} alt={client.name} />
              <AvatarFallback>
                {client.name?.charAt(0)?.toUpperCase() || 'C'}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="text-xl font-semibold">{client.name}</div>
              <div className="flex items-center gap-2 mt-1">
                <Badge className={getStatusColor(client.status || 'ativo')}>
                  {client.status === 'ativo' ? 'Ativo' :
                   client.status === 'inativo' ? 'Inativo' :
                   client.status === 'prospecto' ? 'Prospecto' : 'Arquivado'}
                </Badge>
                <Badge className={getPriorityColor(client.priority || 'media')}>
                  Prioridade {client.priority === 'alta' ? 'Alta' :
                             client.priority === 'media' ? 'Média' :
                             client.priority === 'critica' ? 'Crítica' : 'Baixa'}
                </Badge>
              </div>
            </div>
          </DialogTitle>
          <DialogDescription>
            Visualize todas as informações do cliente.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Informações de Contato */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium flex items-center gap-2">
              <User className="h-5 w-5" />
              Informações de Contato
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {client.email && (
                <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="text-sm font-medium">Email</div>
                    <div className="text-sm text-muted-foreground">{client.email}</div>
                  </div>
                </div>
              )}

              {client.phone && (
                <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="text-sm font-medium">Telefone</div>
                    <div className="text-sm text-muted-foreground">{client.phone}</div>
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="border-t" />

          {/* Informações Pessoais/Empresariais */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium flex items-center gap-2">
              <Building className="h-5 w-5" />
              Informações {client.type === 'pessoa_fisica' ? 'Pessoais' : 'Empresariais'}
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                <div>
                  <div className="text-sm font-medium">Tipo</div>
                  <div className="text-sm text-muted-foreground">
                    {client.type === 'pessoa_fisica' ? 'Pessoa Física' : 'Pessoa Jurídica'}
                  </div>
                </div>
              </div>

              {client.document && (
                <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                  <div>
                    <div className="text-sm font-medium">
                      {client.type === 'pessoa_fisica' ? 'CPF' : 'CNPJ'}
                    </div>
                    <div className="text-sm text-muted-foreground">{client.document}</div>
                  </div>
                </div>
              )}

              {client.company && (
                <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                  <div>
                    <div className="text-sm font-medium">Nome da Empresa</div>
                    <div className="text-sm text-muted-foreground">{client.company}</div>
                  </div>
                </div>
              )}

              {client.companyDocument && (
                <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                  <div>
                    <div className="text-sm font-medium">CNPJ da Empresa</div>
                    <div className="text-sm text-muted-foreground">{client.companyDocument}</div>
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="border-t" />

          {/* Tags */}
          {client.tags && client.tags.length > 0 && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Tags</h3>
              <div className="flex flex-wrap gap-2">
                {client.tags.map((tag, index) => (
                  <Badge key={index} variant="secondary">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          <div className="border-t" />

          {/* Informações do Sistema */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Informações do Sistema
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                <div>
                  <div className="text-sm font-medium">Criado em</div>
                  <div className="text-sm text-muted-foreground">
                    {formatDate(client.$createdAt)}
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                <div>
                  <div className="text-sm font-medium">Atualizado em</div>
                  <div className="text-sm text-muted-foreground">
                    {formatDate(client.$updatedAt)}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Fechar
          </Button>
          <Button onClick={handleEdit}>
            <Edit className="mr-2 h-4 w-4" />
            Editar Cliente
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
