import { ID, type Models } from 'appwrite';
import { account } from '../config';

/**
 * Simple Appwrite authentication functions
 * Direct calls without unnecessary complexity
 */

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  name: string;
}

export interface User extends Models.User<Models.Preferences> {}

/**
 * Register a new user
 */
export async function registerUser(data: RegisterData) {
  const result = await account.create(
    ID.unique(),
    data.email,
    data.password,
    data.name
  );

  return result;
}

/**
 * Login user with email and password
 */
export async function loginUser(credentials: LoginCredentials) {
  const result = await account.createEmailPasswordSession(
    credentials.email,
    credentials.password
  );

  return result;
}

/**
 * Logout current user
 */
export async function logoutUser() {
  const result = await account.deleteSession('current');
  return result;
}

/**
 * Get current user
 */
export async function getCurrentUser() {
  const result = await account.get();
  return result;
}

/**
 * Get current session
 */
export async function getCurrentSession() {
  const result = await account.getSession('current');
  return result;
}

/**
 * Send password recovery email
 */
export async function sendPasswordRecovery(email: string) {
  const result = await account.createRecovery(
    email,
    `${window.location.origin}/reset-password`
  );
  return result;
}

/**
 * Complete password recovery
 */
export async function completePasswordRecovery(
  userId: string,
  secret: string,
  password: string
) {
  const result = await account.updateRecovery(userId, secret, password);
  return result;
}

/**
 * Update user password
 */
export async function updatePassword(currentPassword: string, newPassword: string) {
  const result = await account.updatePassword(newPassword, currentPassword);
  return result;
}

/**
 * Update user name
 */
export async function updateUserName(name: string) {
  const result = await account.updateName(name);
  return result;
}

/**
 * Update user email
 */
export async function updateUserEmail(email: string, password: string) {
  const result = await account.updateEmail(email, password);
  return result;
}

/**
 * Get user preferences
 */
export async function getUserPreferences() {
  const result = await account.getPrefs();
  return result;
}



/**
 * Send email verification
 */
export async function sendEmailVerification() {
  const result = await account.createVerification(`${window.location.origin}/verify-email`);
  return result;
}

/**
 * Complete email verification
 */
export async function completeEmailVerification(userId: string, secret: string) {
  const result = await account.updateVerification(userId, secret);
  return result;
}

/**
 * Create OAuth2 session (Google, Facebook, etc.)
 */
export function createOAuthSession(
  provider: string,
  success?: string,
  failure?: string
) {
  // Type assertion for Appwrite OAuth provider
  account.createOAuth2Session(
    provider as Parameters<typeof account.createOAuth2Session>[0],
    success || `${window.location.origin}/`,
    failure || `${window.location.origin}/login`
  );
}

/**
 * Update user phone
 */
export async function updateUserPhone(phone: string, password: string) {
  const result = await account.updatePhone(phone, password);
  return result;
}

/**
 * Delete current user session (logout)
 */
export async function deleteCurrentSession() {
  const result = await account.deleteSession('current');
  return result;
}

/**
 * Delete user account (block account)
 * Note: Complete account deletion requires server-side implementation
 */
export async function deleteUserAccount() {
  // Appwrite doesn't provide client-side account deletion
  // This method blocks the account instead
  const result = await account.updateStatus();
  return result;
}
