# 🎨 Sistema de Temas

O template inclui um sistema completo de temas com suporte a modo claro/escuro e múltiplas variações de cores.

## Temas Disponíveis

### Temas <PERSON>drão
- **Default** - Tema padrão neutro
- **Blue** - Tema azul profissional
- **Green** - Tema verde natural
- **Amber** - Tema âmbar caloroso
- **E-commerce** - Tema moderno para e-commerce

### Temas Criativos
- **Candyland** - Tema colorido e vibrante com cores doces
- **Cyberpunk** - Tema futurista com cores neon
- **Sunset** - Tema com cores quentes de pôr do sol
- **Ocean** - Tema azul/verde oceânico
- **Nature** - Tema verde natural e orgânico

### Temas Especiais
- **Mono** - Tema minimalista monocromático
- **Scaled** - Versões compactas dos temas principais

## Como Usar

### Hook useThemeConfig
```typescript
import { useThemeConfig } from '@/hooks/use-theme-config';

function MyComponent() {
  const { activeTheme, setActiveTheme, availableThemes } = useThemeConfig();
  
  return (
    <button onClick={() => setActiveTheme('cyberpunk')}>
      Tema Cyberpunk
    </button>
  );
}
```

### Componente ThemeSelector
```typescript
import { ThemeSelector } from '@/components/theme-selector';

function Settings() {
  return (
    <div>
      <h2>Configurações</h2>
      <ThemeSelector />
    </div>
  );
}
```

## Estrutura Técnica

### CSS Variables
Cada tema define variáveis CSS que são aplicadas automaticamente:

```css
.theme-cyberpunk {
  --primary: oklch(0.6726 0.2904 341.4084);
  --background: oklch(0.9816 0.0017 247.8390);
  /* ... outras variáveis */
}
```

### TypeScript Types
```typescript
export type ThemeVariant = 
  | "default" 
  | "blue" 
  | "green" 
  | "amber" 
  | "ecommerce" 
  | "mono"
  | "candyland"
  | "cyberpunk"
  | "sunset"
  | "ocean"
  | "nature";
```

### Aplicação Automática
Os temas são aplicados automaticamente através de classes CSS no body:

```typescript
// Aplicado automaticamente pelo ActiveThemeProvider
document.body.classList.add(`theme-${activeTheme}`);
```

## Personalização

### Adicionando Novos Temas

1. **Adicione as variáveis CSS** em `app/app.css`:
```css
.theme-meu-tema,
.theme-meu-tema-scaled {
  --primary: oklch(/* suas cores */);
  --background: oklch(/* suas cores */);
  /* ... outras variáveis */
  
  @variant dark {
    /* versão dark do tema */
  }
}
```

2. **Atualize os tipos** em `app/lib/theme-utils.ts`:
```typescript
export type ThemeVariant = 
  | "default" 
  // ... outros temas
  | "meu-tema";
```

3. **Adicione ao seletor** em `app/components/theme-selector.tsx`:
```typescript
const CREATIVE_THEMES = [
  // ... outros temas
  {
    name: "Meu Tema",
    value: "meu-tema",
    description: "Descrição do meu tema",
  },
];
```

4. **Atualize o hook** em `app/hooks/use-theme-config.ts`:
```typescript
const availableThemes = [
  // ... outros temas
  'meu-tema'
];
```

## Modo Claro/Escuro

Todos os temas suportam modo claro e escuro automaticamente através do `@variant dark` no CSS.

### Hook useTheme
```typescript
import { useTheme } from '@/hooks/use-theme';

function ThemeToggle() {
  const { theme, toggleTheme, isDark } = useTheme();
  
  return (
    <button onClick={toggleTheme}>
      {isDark ? '☀️' : '🌙'}
    </button>
  );
}
```

## Persistência

Os temas são salvos automaticamente no localStorage e restaurados na próxima sessão.

## Compatibilidade

- ✅ Suporte completo a SSR
- ✅ Transições suaves entre temas
- ✅ Compatibilidade com todos os componentes shadcn/ui
- ✅ Suporte a modo escuro automático
- ✅ Responsivo em todos os dispositivos
