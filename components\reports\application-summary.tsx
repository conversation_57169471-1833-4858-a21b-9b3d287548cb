'use client';

import { useMemo } from 'react'
import { BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, ResponsiveContainer } from 'recharts'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { ChartContainer, ChartTooltip, ChartTooltipContent, type ChartConfig } from '../ui/chart'
import { useClients } from '../../hooks/api/use-clients'
import { useActivities } from '../../hooks/api/use-activities'
import { useTeams } from '../../hooks/api/use-teams'
import { useEvents } from '../../hooks/api/use-events'
import { useBoards } from '../../hooks/api/use-kanban'
import { useFiles } from '../../hooks/api/use-storage'
import { Users, Activity, Calendar, Folder, <PERSON><PERSON><PERSON>, <PERSON>r<PERSON>heck, BarChart3 } from 'lucide-react'

const chartConfig = {
  count: {
    label: "Quantidade",
    color: "hsl(var(--chart-1))",
  },
  clients: {
    label: "Clientes",
    color: "hsl(var(--chart-1))",
  },
  events: {
    label: "Eventos",
    color: "hsl(var(--chart-2))",
  },
  boards: {
    label: "Boards",
    color: "hsl(var(--chart-3))",
  },
  files: {
    label: "Arquivos",
    color: "hsl(var(--chart-4))",
  },
  teams: {
    label: "Teams",
    color: "hsl(var(--chart-5))",
  },
  activities: {
    label: "Atividades",
    color: "hsl(var(--chart-6))",
  },
} satisfies ChartConfig

export function ApplicationSummary() {
  const { data: clients = [], isLoading: clientsLoading } = useClients()
  const { data: activities = [], isLoading: activitiesLoading } = useActivities({})
  const { data: teams = [], isLoading: teamsLoading } = useTeams()
  const { data: events = [], isLoading: eventsLoading } = useEvents()
  const { data: boards = [], isLoading: boardsLoading } = useBoards()
  const { data: files, isLoading: filesLoading } = useFiles()

  const isLoading = clientsLoading || activitiesLoading || teamsLoading || eventsLoading || boardsLoading || filesLoading

  // Dados para gráfico de barras - Distribuição por módulo
  const moduleData = useMemo(() => [
    {
      module: "Clientes",
      count: clients.length,
      icon: Users
    },
    {
      module: "Teams",
      count: teams.length,
      icon: UserCheck
    },
    {
      module: "Eventos",
      count: events.length,
      icon: Calendar
    },
    {
      module: "Boards",
      count: boards.length,
      icon: Kanban
    },
    {
      module: "Arquivos",
      count: files?.files?.length || 0,
      icon: Folder
    },
    {
      module: "Atividades",
      count: activities.length,
      icon: Activity
    }
  ], [clients, teams, events, boards, files, activities])

  // Dados para gráfico de pizza - Status dos clientes
  const clientStatusData = useMemo(() => {
    const statusCount = clients.reduce((acc, client) => {
      const status = client.status || 'indefinido'
      acc[status] = (acc[status] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const colors = [
      "hsl(var(--chart-1))",
      "hsl(var(--chart-2))",
      "hsl(var(--chart-3))",
      "hsl(var(--chart-4))",
      "hsl(var(--chart-5))"
    ]

    return Object.entries(statusCount).map(([status, count], index) => ({
      status: status.charAt(0).toUpperCase() + status.slice(1),
      count,
      fill: colors[index % colors.length]
    }))
  }, [clients])

  // Dados para gráfico de tarefas por board
  const boardTasksData = useMemo(() => {
    return boards.slice(0, 5).map(board => {
      const tasksCount = board.columns?.reduce((total, column) => {
        return total + (column.tasks?.length || 0)
      }, 0) || 0

      return {
        board: board.title?.substring(0, 10) + (board.title?.length > 10 ? '...' : '') || 'Sem título',
        tasks: tasksCount
      }
    })
  }, [boards])

  if (isLoading) {
    return (
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: 3 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-4 bg-muted rounded w-3/4"></div>
              <div className="h-3 bg-muted rounded w-1/2"></div>
            </CardHeader>
            <CardContent>
              <div className="h-48 bg-muted rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  const totalItems = moduleData.reduce((sum, item) => sum + item.count, 0)

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold">Sumário da Aplicação</h2>
          <p className="text-sm text-muted-foreground">
            Dados reais de todos os módulos do sistema ({totalItems.toLocaleString('pt-BR')} itens total)
          </p>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Gráfico de Distribuição por Módulo */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Distribuição por Módulo
            </CardTitle>
            <CardDescription>
              Quantidade de itens em cada módulo da aplicação
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig}>
              <BarChart data={moduleData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="module"
                  tick={{ fontSize: 12 }}
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis />
                <ChartTooltip content={<ChartTooltipContent />} />
                <Bar dataKey="count" fill="var(--color-count)" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Gráfico de Status dos Clientes */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Status dos Clientes
            </CardTitle>
            <CardDescription>
              Distribuição por status ({clients.length} total)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="mx-auto aspect-square max-h-[250px]">
              <PieChart>
                <ChartTooltip content={<ChartTooltipContent hideLabel />} />
                <Pie
                  data={clientStatusData}
                  dataKey="count"
                  nameKey="status"
                  innerRadius={60}
                  strokeWidth={2}
                >
                  {clientStatusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.fill} />
                  ))}
                </Pie>
              </PieChart>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Gráfico de Tarefas por Board */}
        {boards.length > 0 && (
          <Card className="lg:col-span-3">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Kanban className="h-5 w-5" />
                Tarefas por Board Kanban
              </CardTitle>
              <CardDescription>
                Top 5 boards com mais tarefas ({boards.length} boards total)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ChartContainer config={chartConfig}>
                <BarChart data={boardTasksData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="board" />
                  <YAxis />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <Bar dataKey="tasks" fill="var(--color-count)" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ChartContainer>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
