import type { Metadata } from "next";
import "./app.css";
import "./styles/calendar.css";
import "./styles/event-calendar.css";
import { ClientProviders } from "@/components/client-providers";

export const metadata: Metadata = {
  title: "Appwrite Template",
  description: "Template completo com Appwrite, Next.js, TypeScript e shadcn/ui",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "Appwrite Template",
  },
  applicationName: "Appwrite Template",
  other: {
    "msapplication-TileColor": "#000000",
  },
};

export const viewport = {
  themeColor: "#000000",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        {/* PWA Meta Tags */}
        <meta name="theme-color" content="#000000" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Appwrite Template" />
        <meta name="application-name" content="Appwrite Template" />
        <meta name="msapplication-TileColor" content="#000000" />

        {/* Fonts */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link
          rel="stylesheet"
          href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap"
        />

        {/* PWA Manifest */}
        <link rel="manifest" href="/manifest.json" />
        <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />
      </head>
      <body>
        <ClientProviders>
          {children}
        </ClientProviders>
      </body>
    </html>
  );
}
