/**
 * React Query hooks for workspace operations
 */

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import {
  createWorkspace,
  updateWorkspace,
  deleteWorkspace,
  getWorkspace,
  getUserWorkspaces,
  getWorkspaceWithBoards,
  addWorkspaceMember,
  removeWorkspaceMember,
  archiveWorkspace,
  restoreWorkspace,
  getWorkspaceStats,
  canUserAccessWorkspace,
  canUserEditWorkspace
} from '../../lib/appwrite/functions/workspaces';
import type { 
  CreateWorkspaceData, 
  UpdateWorkspaceData,
  Workspace 
} from '@/schemas/kanban';

// ============================================================================
// QUERY KEYS
// ============================================================================

export const workspaceKeys = {
  all: ['workspaces'] as const,
  lists: () => [...workspaceKeys.all, 'list'] as const,
  list: (filters: string) => [...workspaceKeys.lists(), { filters }] as const,
  details: () => [...workspaceKeys.all, 'detail'] as const,
  detail: (id: string) => [...workspaceKeys.details(), id] as const,
  withBoards: (id: string) => [...workspaceKeys.detail(id), 'boards'] as const,
  stats: (id: string) => [...workspaceKeys.detail(id), 'stats'] as const,
  userWorkspaces: (userId: string) => [...workspaceKeys.all, 'user', userId] as const,
};

// ============================================================================
// QUERIES
// ============================================================================

export function useWorkspace(workspaceId: string) {
  return useQuery({
    queryKey: workspaceKeys.detail(workspaceId),
    queryFn: () => getWorkspace(workspaceId),
    enabled: !!workspaceId,
  });
}

export function useUserWorkspaces(userId?: string) {
  return useQuery({
    queryKey: workspaceKeys.userWorkspaces(userId || ''),
    queryFn: () => getUserWorkspaces(userId!),
    enabled: !!userId,
  });
}

export function useWorkspaceWithBoards(workspaceId: string) {
  return useQuery({
    queryKey: workspaceKeys.withBoards(workspaceId),
    queryFn: () => getWorkspaceWithBoards(workspaceId),
    enabled: !!workspaceId,
  });
}

export function useWorkspaceStats(workspaceId: string) {
  return useQuery({
    queryKey: workspaceKeys.stats(workspaceId),
    queryFn: () => getWorkspaceStats(workspaceId),
    enabled: !!workspaceId,
  });
}

export function useWorkspacePermissions(workspaceId: string, userId: string) {
  return useQuery({
    queryKey: [...workspaceKeys.detail(workspaceId), 'permissions', userId],
    queryFn: async () => {
      const [canAccess, canEdit] = await Promise.all([
        canUserAccessWorkspace(workspaceId, userId),
        canUserEditWorkspace(workspaceId, userId)
      ]);
      return { canAccess, canEdit };
    },
    enabled: !!workspaceId && !!userId,
  });
}

// ============================================================================
// MUTATIONS
// ============================================================================

export function useCreateWorkspace() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateWorkspaceData) => createWorkspace(data),
    onSuccess: (workspace, variables) => {
      // Invalidate user workspaces
      queryClient.invalidateQueries({
        queryKey: workspaceKeys.userWorkspaces(variables.userId)
      });
      
      // Add to cache
      queryClient.setQueryData(
        workspaceKeys.detail(workspace.$id),
        workspace
      );

      toast.success('Workspace criado com sucesso!');
    },
    onError: (error) => {
      console.error('Erro ao criar workspace:', error);
      toast.error('Erro ao criar workspace. Tente novamente.');
    },
  });
}

export function useUpdateWorkspace() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ workspaceId, data }: { workspaceId: string; data: UpdateWorkspaceData }) =>
      updateWorkspace(workspaceId, data),
    onSuccess: (workspace, { workspaceId }) => {
      // Update cache
      queryClient.setQueryData(
        workspaceKeys.detail(workspaceId),
        workspace
      );

      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: workspaceKeys.withBoards(workspaceId)
      });
      queryClient.invalidateQueries({
        queryKey: workspaceKeys.userWorkspaces(workspace.userId)
      });

      toast.success('Workspace atualizado com sucesso!');
    },
    onError: (error) => {
      console.error('Erro ao atualizar workspace:', error);
      toast.error('Erro ao atualizar workspace. Tente novamente.');
    },
  });
}

export function useDeleteWorkspace() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (workspaceId: string) => deleteWorkspace(workspaceId),
    onSuccess: (_, workspaceId) => {
      // Remove from cache
      queryClient.removeQueries({
        queryKey: workspaceKeys.detail(workspaceId)
      });

      // Invalidate lists
      queryClient.invalidateQueries({
        queryKey: workspaceKeys.lists()
      });

      toast.success('Workspace excluído com sucesso!');
    },
    onError: (error) => {
      console.error('Erro ao excluir workspace:', error);
      toast.error('Erro ao excluir workspace. Tente novamente.');
    },
  });
}

export function useArchiveWorkspace() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ workspaceId, userId }: { workspaceId: string; userId: string }) =>
      archiveWorkspace(workspaceId, userId),
    onSuccess: (workspace, { workspaceId, userId }) => {
      // Update cache
      queryClient.setQueryData(
        workspaceKeys.detail(workspaceId),
        workspace
      );

      // Invalidate user workspaces
      queryClient.invalidateQueries({
        queryKey: workspaceKeys.userWorkspaces(userId)
      });

      toast.success('Workspace arquivado com sucesso!');
    },
    onError: (error) => {
      console.error('Erro ao arquivar workspace:', error);
      toast.error('Erro ao arquivar workspace. Tente novamente.');
    },
  });
}

export function useRestoreWorkspace() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ workspaceId, userId }: { workspaceId: string; userId: string }) =>
      restoreWorkspace(workspaceId, userId),
    onSuccess: (workspace, { workspaceId, userId }) => {
      // Update cache
      queryClient.setQueryData(
        workspaceKeys.detail(workspaceId),
        workspace
      );

      // Invalidate user workspaces
      queryClient.invalidateQueries({
        queryKey: workspaceKeys.userWorkspaces(userId)
      });

      toast.success('Workspace restaurado com sucesso!');
    },
    onError: (error) => {
      console.error('Erro ao restaurar workspace:', error);
      toast.error('Erro ao restaurar workspace. Tente novamente.');
    },
  });
}

// ============================================================================
// MEMBER MANAGEMENT
// ============================================================================

export function useAddWorkspaceMember() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ workspaceId, userId }: { workspaceId: string; userId: string }) =>
      addWorkspaceMember(workspaceId, userId),
    onSuccess: (workspace, { workspaceId }) => {
      // Update cache
      queryClient.setQueryData(
        workspaceKeys.detail(workspaceId),
        workspace
      );

      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: workspaceKeys.withBoards(workspaceId)
      });
      queryClient.invalidateQueries({
        queryKey: workspaceKeys.stats(workspaceId)
      });

      toast.success('Membro adicionado ao workspace!');
    },
    onError: (error) => {
      console.error('Erro ao adicionar membro:', error);
      toast.error('Erro ao adicionar membro. Tente novamente.');
    },
  });
}

export function useRemoveWorkspaceMember() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ 
      workspaceId, 
      userId, 
      removedBy 
    }: { 
      workspaceId: string; 
      userId: string; 
      removedBy: string; 
    }) => removeWorkspaceMember(workspaceId, userId, removedBy),
    onSuccess: (workspace, { workspaceId }) => {
      // Update cache
      queryClient.setQueryData(
        workspaceKeys.detail(workspaceId),
        workspace
      );

      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: workspaceKeys.withBoards(workspaceId)
      });
      queryClient.invalidateQueries({
        queryKey: workspaceKeys.stats(workspaceId)
      });

      toast.success('Membro removido do workspace!');
    },
    onError: (error) => {
      console.error('Erro ao remover membro:', error);
      toast.error('Erro ao remover membro. Tente novamente.');
    },
  });
}

// ============================================================================
// UTILITY HOOKS
// ============================================================================

export function useWorkspaceSelection() {
  const queryClient = useQueryClient();

  const invalidateWorkspaceData = (workspaceId?: string) => {
    if (workspaceId) {
      queryClient.invalidateQueries({
        queryKey: workspaceKeys.detail(workspaceId)
      });
    } else {
      queryClient.invalidateQueries({
        queryKey: workspaceKeys.all
      });
    }
  };

  return {
    invalidateWorkspaceData,
  };
}
