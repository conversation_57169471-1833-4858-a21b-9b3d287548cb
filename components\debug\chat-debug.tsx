'use client';

import { useState } from 'react';
import { But<PERSON> } from '../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Separator } from '../ui/separator';
import { MessageCircle, Send, Users, Settings, Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { useTeams, useGetOrCreateTeamChat, useTeamChat } from '../../hooks/api/use-teams';
import { useMessages, useSendMessage } from '../../hooks/use-api';
import { useAuth } from '../../hooks/use-auth';
import { toast } from 'sonner';

export function ChatDebug() {
  const { user } = useAuth();
  const [selectedTeamId, setSelectedTeamId] = useState<string>('');
  const [message, setMessage] = useState('');

  // Buscar teams do usuário
  const { data: teams, isLoading: loadingTeams } = useTeams();

  // Hooks para o team selecionado
  const selectedTeam = teams?.find(team => team.$id === selectedTeamId);
  const { data: teamChat, isLoading: loadingTeamChat, error: teamChatError } = useTeamChat(selectedTeamId);
  const getOrCreateChatMutation = useGetOrCreateTeamChat();
  const { data: messages = [], isLoading: loadingMessages, error: messagesError } = useMessages(teamChat?.$id || '', selectedTeamId);
  const sendMessageMutation = useSendMessage();

  const handleCreateChat = async () => {
    if (!selectedTeam) return;

    try {
      console.log('🔧 Criando chat para team:', selectedTeam);
      const result = await getOrCreateChatMutation.mutateAsync({
        teamId: selectedTeam.$id,
        teamName: selectedTeam.name,
      });
      console.log('✅ Chat criado/encontrado:', result);
      toast.success('Chat configurado com sucesso!');
    } catch (error) {
      console.error('❌ Erro ao configurar chat:', error);
      toast.error('Erro ao configurar chat');
    }
  };

  const handleSendMessage = async () => {
    if (!message.trim() || !teamChat?.$id || !selectedTeamId) return;

    try {
      console.log('🔧 Enviando mensagem:', {
        content: message.trim(),
        chatId: teamChat.$id,
        teamId: selectedTeamId,
      });

      await sendMessageMutation.mutateAsync({
        content: message.trim(),
        chatId: teamChat.$id,
        teamId: selectedTeamId,
        type: 'text',
      });
      setMessage('');
      console.log('✅ Mensagem enviada com sucesso');
    } catch (error) {
      console.error('❌ Erro ao enviar mensagem:', error);
      toast.error('Erro ao enviar mensagem');
    }
  };

  if (loadingTeams) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Carregando teams...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Debug de Chat
          </CardTitle>
          <CardDescription>
            Ferramenta para testar criação e funcionamento de chats
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Seleção de Team */}
          <div>
            <label className="text-sm font-medium">Selecionar Team:</label>
            <select
              value={selectedTeamId}
              onChange={(e) => setSelectedTeamId(e.target.value)}
              className="w-full mt-1 p-2 border rounded-md"
            >
              <option value="">Selecione um team...</option>
              {teams?.map((team) => (
                <option key={team.$id} value={team.$id}>
                  {team.name}
                </option>
              ))}
            </select>
          </div>

          {selectedTeamId && (
            <>
              <Separator />

              {/* Status do Team Chat */}
              <div className="space-y-2">
                <h3 className="font-medium">Status do Chat:</h3>
                <div className="flex items-center gap-2">
                  {loadingTeamChat ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : teamChat ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-yellow-500" />
                  )}
                  <span>
                    {loadingTeamChat
                      ? 'Verificando chat...'
                      : teamChat
                        ? `Chat encontrado: ${teamChat.name}`
                        : 'Chat não configurado'
                    }
                  </span>
                </div>

                {teamChatError && (
                  <div className="text-red-500 text-sm">
                    Erro ao buscar chat: {teamChatError.message}
                  </div>
                )}
              </div>

              {/* Ações */}
              <div className="flex gap-2">
                <Button
                  onClick={handleCreateChat}
                  disabled={getOrCreateChatMutation.isPending}
                  variant={teamChat ? "outline" : "default"}
                >
                  {getOrCreateChatMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {teamChat ? 'Recriar Chat' : 'Configurar Chat'}
                </Button>
              </div>

              {/* Debug Info */}
              <div className="bg-gray-50 p-3 rounded-md text-xs space-y-1">
                <div><strong>Team ID:</strong> {selectedTeamId}</div>
                <div><strong>Chat ID:</strong> {teamChat?.$id || 'N/A'}</div>
                <div><strong>Chat Name:</strong> {teamChat?.name || 'N/A'}</div>
                <div><strong>Members:</strong> {teamChat?.members?.length || 0}</div>
                <div><strong>Messages:</strong> {messages.length || 0}</div>
              </div>

              {/* Envio de Mensagem */}
              {teamChat && (
                <>
                  <Separator />
                  <div className="space-y-2">
                    <h3 className="font-medium">Enviar Mensagem de Teste:</h3>
                    <div className="flex gap-2">
                      <Input
                        value={message}
                        onChange={(e) => setMessage(e.target.value)}
                        placeholder="Digite uma mensagem de teste..."
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            handleSendMessage();
                          }
                        }}
                      />
                      <Button
                        onClick={handleSendMessage}
                        disabled={!message.trim() || sendMessageMutation.isPending}
                        size="icon"
                      >
                        <Send className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </>
              )}

              {/* Mensagens */}
              {messages && messages.length > 0 && (
                <>
                  <Separator />
                  <div className="space-y-2">
                    <h3 className="font-medium">Mensagens ({messages.length}):</h3>
                    <div className="max-h-40 overflow-y-auto space-y-2">
                      {messages.map((msg: any) => (
                        <div key={msg.$id} className="bg-gray-50 p-2 rounded text-sm">
                          <div className="font-medium">{msg.senderName}</div>
                          <div>{msg.content}</div>
                          <div className="text-xs text-gray-500">
                            {new Date(msg.$createdAt).toLocaleString()}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              )}

              {messagesError && (
                <div className="text-red-500 text-sm">
                  Erro ao carregar mensagens: {messagesError.message}
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
