/**
 * Controller para Events
 * Usa subscribe do Valtio - sem useEffect, useState, nada
 */

import { useEffect, useRef } from 'react';
import { subscribe } from 'valtio';
import { useQueryClient } from '@tanstack/react-query';
import { realtimeStore } from '../../lib/realtime/store';
import { saveToIndexedDB } from '../../lib/cache-sync';

export function useEventsController() {
  const queryClient = useQueryClient();
  const unsubscribeRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    console.log('🎛️ Inicializando controller de events...');

    // Subscribe do Valtio - função nativa, sem useEffect
    unsubscribeRef.current = subscribe(realtimeStore.events, () => {
      const events = realtimeStore.events;

      if (events.length === 0) return;
      
      console.log(`📝 Processando ${events.length} events do realtime...`);

      // Adicionar dados novos aos existentes no React Query
      events.forEach(event => {
        console.log(`📝 Atualizando event: ${event.$id}`);

        // Invalidar queries relacionadas a events
        queryClient.setQueryData(['events', event.userId, event.teamId], (oldData: any) => {
          if (!oldData) return [event];

          const exists = oldData.find((item: any) => item.$id === event.$id);
          if (exists) {
            // Atualizar existente
            return oldData.map((item: any) =>
              item.$id === event.$id ? event : item
            );
          } else {
            // Adicionar novo
            return [...oldData, event];
          }
        });

        // Invalidar outras queries relacionadas
        queryClient.invalidateQueries({ queryKey: ['events'] });
        queryClient.invalidateQueries({ queryKey: ['eventStats'] });

        // Salvar no IndexedDB com tratamento de erro (sem await para não bloquear)
        saveToIndexedDB('events', event, {
          collection: 'events',
          userId: event.userId
        }).catch(error => {
          console.error(`❌ Erro ao salvar event ${event.$id} no IndexedDB:`, error);
          // Não interromper o fluxo, apenas logar o erro
        });
      });


    });

    return () => {
      console.log('🧹 Limpando controller de events...');
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    };
  }, [queryClient]);

  return {};
}
