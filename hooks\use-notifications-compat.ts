/**
 * Compatibility wrapper for notifications
 * Provides the same interface as the old localStorage-based hook
 * but uses the new API-based hooks under the hood
 */

import { useMemo } from 'react';
import {
  useNotifications as useNotificationsApi,
  useDeleteNotification,
  useMarkAsRead,
  useMarkAllAsRead
} from './api/use-notifications';

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  timestamp: string;
  read: boolean;
  userId: string;
  metadata?: Record<string, any>;
}

/**
 * Compatibility hook that mimics the old useNotifications interface
 */
export function useNotifications() {
  const { data: notificationsResponse, isLoading } = useNotificationsApi();
  const deleteNotificationMutation = useDeleteNotification();
  const markAsReadMutation = useMarkAsRead();
  const markAllAsReadMutation = useMarkAllAsRead();

  // Transform API response to match old interface
  const notifications = useMemo(() => {
    if (!notificationsResponse || !Array.isArray(notificationsResponse)) {
      return [];
    }

    return notificationsResponse.map((notification: any) => ({
      id: notification.$id,
      title: notification.title,
      message: notification.message,
      type: notification.type as 'info' | 'success' | 'warning' | 'error',
      timestamp: notification.$createdAt,
      read: notification.read || false,
      userId: notification.userId,
      metadata: notification.metadata,
    }));
  }, [notificationsResponse]);

  const unreadCount = useMemo(() => {
    if (!notifications) return 0;
    return notifications.filter(n => !n.read).length;
  }, [notifications]);

  // Compatibility functions with real implementations
  const markAsReadFn = (notificationId: string) => {
    markAsReadMutation.mutate(notificationId);
  };

  const markAllAsReadFn = () => {
    markAllAsReadMutation.mutate();
  };

  const removeNotification = (notificationId: string) => {
    deleteNotificationMutation.mutate(notificationId);
  };

  // Note: addNotification is not implemented as it should go through the API
  const addNotification = () => {
    console.warn('addNotification is not available in API mode. Use the API service directly.');
  };

  const clearAll = () => {
    console.warn('clearAll is not available in API mode. Use the API service directly.');
  };

  return {
    notifications,
    unreadCount,
    isLoading,
    markAsRead: markAsReadFn,
    markAllAsRead: markAllAsReadFn,
    removeNotification,
    addNotification,
    clearAll,
  };
}

/**
 * Compatibility hook for unread count
 */
export function useUnreadNotificationCount() {
  const { data: notificationsResponse } = useNotificationsApi();

  return useMemo(() => {
    if (!notificationsResponse || !Array.isArray(notificationsResponse)) return 0;
    return notificationsResponse.filter(n => !n.read).length;
  }, [notificationsResponse]);
}
