'use client';

import { useEffect } from "react";
import { PWAInitializer } from "./pwa-initializer";
import { AuthProvider } from "../contexts/auth-context";
import { ThemeProvider } from "./theme-provider";
import { ActiveThemeProvider } from "./active-theme";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "sonner";
import { ErrorBoundary as AppErrorBoundary } from "./error-boundary";
import { queryClient } from "../lib/query-client";
import { initializeGA } from "../lib/google-analytics";
import { getStoredTheme } from "../lib/theme-utils";
import { RealtimeInitializer } from "./realtime-initializer";

/**
 * Client-side providers for the Next.js app
 * This component wraps all the providers that were in the original root.tsx
 */
export function ClientProviders({ children }: { children: React.ReactNode }) {
  // Inicializar Google Analytics
  useEffect(() => {
    initializeGA();
  }, []);

  // Obter tema inicial do localStorage para SSR
  const initialTheme = typeof window !== "undefined" ? getStoredTheme() : undefined;

  return (
    <AppErrorBoundary showDetails={process.env.NODE_ENV === 'development'}>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider>
          <ActiveThemeProvider initialTheme={initialTheme}>
            <AuthProvider>
              <RealtimeInitializer />
              <PWAInitializer />
              {children}
              <Toaster richColors position="top-right" />
            </AuthProvider>
          </ActiveThemeProvider>
        </ThemeProvider>
      </QueryClientProvider>
    </AppErrorBoundary>
  );
}
