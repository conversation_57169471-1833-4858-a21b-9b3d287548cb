/**
 * Componente de chat de time
 * Interface completa para chat em tempo real entre membros do time
 */

import { useState } from 'react';
import { Users } from 'lucide-react';
import { Card, CardContent } from '../ui/card';
import { Avatar, AvatarFallback } from '../ui/avatar';
import { Badge } from '../ui/badge';
import { ScrollArea } from '../ui/scroll-area';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { ModernChat } from './modern-chat';
import type { ModernChatMessage } from './modern-chat';
import type { ChatMessage } from '@/schemas/chat';
import { ChatHeader } from './chat-header';
import { useChatRealtime, useMessages, useSendMessage, useEditMessage, useDeleteMessage } from '../../hooks/use-api';
import { useTeamMembers } from '../../hooks/use-api';
import { useAuth } from '../../hooks/use-auth';

interface TeamChatProps {
  teamId: string;
  chatId: string; // OBRIGATÓRIO - ID do chat específico
  className?: string;
}

export function TeamChat({ teamId, chatId, className }: TeamChatProps) {
  const { user } = useAuth();
  const [showMembersModal, setShowMembersModal] = useState(false);

  // Hooks simples seguindo padrão dos clientes
  const { data: messages = [] } = useMessages(chatId, teamId);
  const { data: membersData } = useTeamMembers(teamId);
  const sendMessageMutation = useSendMessage();
  const editMessageMutation = useEditMessage();
  const deleteMessageMutation = useDeleteMessage();
  const { isConnected } = useChatRealtime(chatId);

  const members = membersData || [];

  // Buscar mensagem sendo respondida
  const getReplyToMessage = (replyToId?: string): ChatMessage | undefined => {
    if (!replyToId) return undefined;
    return messages.find(msg => msg.$id === replyToId);
  };

  // Converter mensagens para o formato ModernChat (filtrando soft deleted)
  const modernMessages: ModernChatMessage[] = messages
    .filter(msg => !msg.isDeleted) // Filtrar mensagens soft deleted
    .map((msg) => ({
      id: msg.$id,
      content: msg.content,
      isUser: msg.senderId === user?.$id,
      timestamp: new Date(msg.$createdAt),
      userName: msg.senderName,
      avatar: msg.senderAvatar,
      status: 'sent',
      edited: msg.edited || false,
      editedAt: msg.editedAt ? new Date(msg.editedAt) : undefined,
      chatId: chatId,
      reactions: msg.reactions || [],
      replyTo: getReplyToMessage(msg.replyTo),
      mentions: msg.mentions || [],
    }));

  // Enviar mensagem usando o novo sistema
  const handleSendMessage = async (
    messageContent: string,
    mentions?: string[],
    replyToId?: string
  ) => {
    if (!messageContent.trim() || sendMessageMutation.isPending) return;

    try {
      await sendMessageMutation.mutateAsync({
        content: messageContent.trim(),
        chatId, // OBRIGATÓRIO - ID do chat pai
        teamId, // Para compatibilidade
        type: 'text',
        mentions: mentions || [],
        replyTo: replyToId,
      });
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
    }
  };

  // Editar mensagem
  const handleEditMessage = async (messageId: string, content: string) => {
    if (!content.trim() || editMessageMutation.isPending) return;

    try {
      await editMessageMutation.mutateAsync({
        messageId,
        content: content.trim(),
        chatId,
        teamId,
      });
    } catch (error) {
      console.error('Erro ao editar mensagem:', error);
    }
  };

  // Excluir mensagem
  const handleDeleteMessage = async (messageId: string) => {
    if (deleteMessageMutation.isPending) return;

    try {
      await deleteMessageMutation.mutateAsync({
        messageId,
        chatId,
        teamId,
      });
    } catch (error) {
      console.error('Erro ao excluir mensagem:', error);
    }
  };

  // Enviar arquivo
  const handleFileUpload = async (fileData: { fileId: string; url: string; fileName: string; fileType: string; fileSize: number }) => {
    try {
      const isImage = fileData.fileType.startsWith('image/');
      const content = isImage
        ? `📷 ${fileData.fileName}`
        : `📎 ${fileData.fileName}`;

      await sendMessageMutation.mutateAsync({
        content,
        chatId, // OBRIGATÓRIO - ID do chat pai
        teamId, // Para compatibilidade
        type: isImage ? 'image' : 'file',
        // Campos diretos de anexos (não mais em metadata)
        fileName: fileData.fileName,
        fileSize: fileData.fileSize,
        fileType: fileData.fileType,
        imageUrl: isImage ? fileData.url : undefined,
        fileUrl: !isImage ? fileData.url : undefined,
      });
    } catch (error) {
      console.error('Erro ao enviar arquivo:', error);
    }
  };

  return (
    <Card className={className}>
      {/* Header do Chat */}
      <ChatHeader
        chatName="Chat do Time"
        memberCount={members.length}
        isConnected={isConnected}
        onViewMembers={() => setShowMembersModal(true)}
      />

      <CardContent className="p-0 flex flex-col h-[600px]">
        {/* Chat Moderno */}
        <div className="flex-1 min-h-0">
          <ModernChat
            messages={modernMessages}
            onSendMessage={handleSendMessage}
            onFileUpload={handleFileUpload}
            onEditMessage={handleEditMessage}
            onDeleteMessage={handleDeleteMessage}
            isLoading={sendMessageMutation.isPending}
            placeholder="Digite sua mensagem..."
            height="100%"
            showFileUpload={true}
            showEmojiPicker={true}
            className="border-0 rounded-none"
            users={members.map(member => ({
              $id: member.userId,
              name: member.userName || 'Usuário',
              email: member.userEmail,
              prefs: { avatar: undefined }
            }))}
            enableMentions={true}
            enableReply={true}
          />
        </div>
      </CardContent>

      {/* Modal de Membros */}
      <Dialog open={showMembersModal} onOpenChange={setShowMembersModal}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Membros do Time ({members.length})
            </DialogTitle>
          </DialogHeader>

          <ScrollArea className="max-h-96">
            <div className="space-y-3">
              {members.map((member) => (
                <div key={member.$id} className="flex items-center gap-3 p-3 rounded-lg border">
                  <Avatar className="h-10 w-10">
                    <AvatarFallback className="bg-primary/10 text-primary font-medium">
                      {member.userName?.charAt(0).toUpperCase() || 'U'}
                    </AvatarFallback>
                  </Avatar>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <p className="font-medium truncate">
                        {member.userName || 'Usuário'}
                      </p>
                      {member.userId === user?.$id && (
                        <Badge variant="secondary" className="text-xs">
                          Você
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground truncate">
                      {member.userEmail}
                    </p>

                    {/* Roles/Funções */}
                    {member.roles && member.roles.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-1">
                        {member.roles.map((role) => (
                          <Badge key={role} variant="outline" className="text-xs">
                            {role}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}

              {members.length === 0 && (
                <div className="text-center text-muted-foreground py-8">
                  <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Nenhum membro encontrado neste time.</p>
                </div>
              )}
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
