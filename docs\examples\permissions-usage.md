# 🔐 Exemplos de Uso do Sistema de Permissões

Este documento contém exemplos práticos de como usar o sistema de permissões em componentes e páginas.

## 📋 Exemplos Básicos

### 1. Verificação Simples de Permissão

```typescript
import { useHasPermission } from '@/contexts/team-context';

function ClientsPage() {
  const hasPermission = useHasPermission();

  const canCreateClients = hasPermission('clients', 'create');
  const canEditClients = hasPermission('clients', 'edit');
  const canDeleteClients = hasPermission('clients', 'delete');

  return (
    <div>
      <h1>Gerenciamento de Clientes</h1>

      {canCreateClients && (
        <Button onClick={openCreateModal}>
          <Plus className="w-4 h-4 mr-2" />
          Novo Cliente
        </Button>
      )}

      <ClientsList
        showEditButton={canEditClients}
        showDeleteButton={canDeleteClients}
      />
    </div>
  );
}
```

### 2. Proteção de Rotas (Next.js App Router)

```typescript
import { useHasAccess } from '@/contexts/team-context';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

function ProtectedRoute({ children, resource }: {
  children: React.ReactNode;
  resource: string;
}) {
  const hasAccess = useHasAccess(resource);
  const router = useRouter();

  useEffect(() => {
    if (!hasAccess) {
      router.replace('/dashboard');
    }
  }, [hasAccess, router]);

  if (!hasAccess) {
    return null;
  }

  return <>{children}</>;
}

// Uso nas rotas (Next.js App Router)
// app/(dashboard)/clients/page.tsx
export default function ClientsPage() {
  return (
    <ProtectedRoute resource="clients">
      <ClientsPageContent />
    </ProtectedRoute>
  );
}

// app/(dashboard)/teams/page.tsx
export default function TeamsPage() {
  return (
    <ProtectedRoute resource="teams">
      <TeamsPageContent />
    </ProtectedRoute>
  );
}
```

### 3. Criação de Documentos com Permissões

```typescript
import { useDocumentPermissions } from '@/hooks/use-document-permissions';
import { useCreateClient } from '@/hooks/api/use-clients';

function CreateClientForm() {
  const { teamId } = useDocumentPermissions();
  const createClient = useCreateClient();

  const handleSubmit = async (data: CreateClientData) => {
    // As permissões são aplicadas automaticamente
    await createClient.mutateAsync({
      ...data,
      teamId, // Team ID é incluído automaticamente
    });
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* Campos do formulário */}
    </form>
  );
}
```

## 🎯 Exemplos Avançados

### 1. Componente com Múltiplas Verificações

```typescript
import { useTeamContext, useHasPermission } from '@/contexts/team-context';

function AdvancedClientCard({ client }: { client: Client }) {
  const { currentTeam, currentUserMembership } = useTeamContext();
  const hasPermission = useHasPermission();

  // Verificações específicas
  const canEdit = hasPermission('clients', 'edit');
  const canDelete = hasPermission('clients', 'delete');
  const canManage = hasPermission('clients', 'manage');

  // Verificar se é o criador do cliente
  const isCreator = client.createdBy === currentUserMembership?.userId;

  // Verificar se é admin ou owner
  const isAdminOrOwner = currentUserMembership?.roles.includes('admin') ||
                        currentUserMembership?.roles.includes('owner');

  return (
    <Card>
      <CardHeader>
        <CardTitle>{client.name}</CardTitle>
        <CardDescription>{client.email}</CardDescription>
      </CardHeader>

      <CardContent>
        <p>Status: {client.status}</p>
        <p>Team: {currentTeam?.name}</p>
        {isCreator && <Badge>Criado por você</Badge>}
      </CardContent>

      <CardFooter className="flex gap-2">
        {canEdit && (
          <Button variant="outline" onClick={() => editClient(client)}>
            Editar
          </Button>
        )}

        {(canDelete || (isCreator && canEdit)) && (
          <Button
            variant="destructive"
            onClick={() => deleteClient(client)}
          >
            Deletar
          </Button>
        )}

        {canManage && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="w-4 h-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => transferOwnership(client)}>
                Transferir Propriedade
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => changeTeam(client)}>
                Mover para Outro Team
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </CardFooter>
    </Card>
  );
}
```

### 2. Sidebar com Permissões

```typescript
import { useHasAccess } from '@/contexts/team-context';

function Sidebar() {
  const hasAccess = useHasAccess;

  const menuItems = [
    {
      label: 'Dashboard',
      icon: Home,
      href: '/dashboard',
      resource: 'dashboard'
    },
    {
      label: 'Clientes',
      icon: Users,
      href: '/clients',
      resource: 'clients'
    },
    {
      label: 'Kanban',
      icon: Trello,
      href: '/kanban',
      resource: 'kanban'
    },
    {
      label: 'Relatórios',
      icon: BarChart,
      href: '/reports',
      resource: 'reports'
    },
    {
      label: 'Teams',
      icon: Shield,
      href: '/teams',
      resource: 'teams'
    },
    {
      label: 'Configurações',
      icon: Settings,
      href: '/preferences',
      resource: 'preferences'
    }
  ];

  return (
    <nav className="space-y-2">
      {menuItems.map((item) => {
        if (!hasAccess(item.resource)) {
          return null; // Não mostrar item se não tem acesso
        }

        return (
          <Link
            key={item.href}
            to={item.href}
            className="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-accent"
          >
            <item.icon className="w-4 h-4" />
            {item.label}
          </Link>
        );
      })}
    </nav>
  );
}
```

### 3. Gerenciamento de Cargos

```typescript
import { useCreateTeamRole, useAssignRoleToMember } from '@/hooks/api/use-permissions';

function TeamRoleManager({ teamId }: { teamId: string }) {
  const createRole = useCreateTeamRole();
  const assignRole = useAssignRoleToMember();

  const handleCreateCustomRole = async () => {
    await createRole.mutateAsync({
      teamId,
      roleData: {
        name: 'Gerente de Vendas',
        description: 'Acesso total a clientes e relatórios',
        color: '#3b82f6',
        userType: 'user',
        permissions: [
          {
            resource: 'clients',
            actions: ['view', 'create', 'edit', 'delete']
          },
          {
            resource: 'reports',
            actions: ['view', 'create']
          },
          {
            resource: 'dashboard',
            actions: ['view']
          }
        ],
        isDefault: false
      }
    });
  };

  const handleAssignRole = async (userId: string, roleId: string) => {
    await assignRole.mutateAsync({
      teamId,
      userId,
      roleId
    });
  };

  return (
    <div>
      <Button onClick={handleCreateCustomRole}>
        Criar Cargo Customizado
      </Button>

      {/* Interface para atribuir cargos */}
    </div>
  );
}
```

## 🔄 Exemplos de Documentos

### 1. Verificar Acesso a Documento

```typescript
import { useDocumentAccess } from '@/hooks/use-document-permissions';

function DocumentViewer({ document }: { document: any }) {
  const { canRead, canWrite, canDelete } = useDocumentAccess(document.$permissions);

  if (!canRead) {
    return (
      <div className="text-center p-8">
        <Lock className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
        <h3>Acesso Negado</h3>
        <p>Você não tem permissão para visualizar este documento.</p>
      </div>
    );
  }

  return (
    <div>
      <DocumentContent document={document} />

      <div className="flex gap-2 mt-4">
        {canWrite && (
          <Button onClick={() => editDocument(document)}>
            Editar
          </Button>
        )}

        {canDelete && (
          <Button variant="destructive" onClick={() => deleteDocument(document)}>
            Deletar
          </Button>
        )}
      </div>
    </div>
  );
}
```

### 2. Lista de Documentos com Filtro de Permissões

```typescript
import { useDocumentPermissions } from '@/hooks/use-document-permissions';

function DocumentsList({ documents }: { documents: any[] }) {
  const { canAccess } = useDocumentPermissions();

  // Filtrar documentos que o usuário pode acessar
  const accessibleDocuments = documents.filter(doc =>
    canAccess(doc.$permissions, 'read')
  );

  return (
    <div className="grid gap-4">
      {accessibleDocuments.map(document => (
        <DocumentCard key={document.$id} document={document} />
      ))}

      {accessibleDocuments.length === 0 && (
        <div className="text-center p-8">
          <FileX className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
          <h3>Nenhum documento acessível</h3>
          <p>Você não tem permissão para visualizar documentos neste team.</p>
        </div>
      )}
    </div>
  );
}
```

## 🎨 Componentes Utilitários

### 1. Componente de Proteção Condicional

```typescript
interface PermissionGuardProps {
  resource: SystemResource;
  action?: ResourceAction;
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

function PermissionGuard({
  resource,
  action = 'view',
  fallback = null,
  children
}: PermissionGuardProps) {
  const hasPermission = useHasPermission();

  if (!hasPermission(resource, action)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

// Uso
function MyComponent() {
  return (
    <div>
      <h1>Página Principal</h1>

      <PermissionGuard
        resource="clients"
        action="create"
        fallback={<p>Você não pode criar clientes</p>}
      >
        <Button>Criar Cliente</Button>
      </PermissionGuard>
    </div>
  );
}
```

### 2. Hook Customizado para Permissões Específicas

```typescript
function useClientPermissions() {
  const hasPermission = useHasPermission();

  return {
    canViewClients: hasPermission('clients', 'view'),
    canCreateClients: hasPermission('clients', 'create'),
    canEditClients: hasPermission('clients', 'edit'),
    canDeleteClients: hasPermission('clients', 'delete'),
    canManageClients: hasPermission('clients', 'manage'),
  };
}

// Uso
function ClientsPage() {
  const {
    canViewClients,
    canCreateClients,
    canEditClients,
    canDeleteClients
  } = useClientPermissions();

  if (!canViewClients) {
    return <AccessDenied />;
  }

  return (
    <div>
      {/* Interface de clientes */}
    </div>
  );
}
```

## 🔍 Debug e Troubleshooting

### 1. Componente de Debug

```typescript
function PermissionsDebug() {
  const { permissionContext, currentUserMembership } = useTeamContext();

  return (
    <div className="p-4 bg-gray-100 rounded-lg">
      <h3>Debug de Permissões</h3>

      <div className="mt-4">
        <h4>Contexto de Permissões:</h4>
        <pre className="text-xs">
          {JSON.stringify(permissionContext, null, 2)}
        </pre>
      </div>

      <div className="mt-4">
        <h4>Membership Atual:</h4>
        <pre className="text-xs">
          {JSON.stringify(currentUserMembership, null, 2)}
        </pre>
      </div>
    </div>
  );
}
```

### 2. Verificação de Permissões em Tempo Real

```typescript
function PermissionChecker() {
  const hasPermission = useHasPermission();
  const [resource, setResource] = useState<SystemResource>('clients');
  const [action, setAction] = useState<ResourceAction>('view');

  const hasAccess = hasPermission(resource, action);

  return (
    <div className="p-4 border rounded-lg">
      <h3>Verificador de Permissões</h3>

      <div className="flex gap-4 mt-4">
        <Select value={resource} onValueChange={setResource}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="clients">Clientes</SelectItem>
            <SelectItem value="teams">Teams</SelectItem>
            <SelectItem value="reports">Relatórios</SelectItem>
          </SelectContent>
        </Select>

        <Select value={action} onValueChange={setAction}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="view">Visualizar</SelectItem>
            <SelectItem value="create">Criar</SelectItem>
            <SelectItem value="edit">Editar</SelectItem>
            <SelectItem value="delete">Deletar</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="mt-4">
        <Badge variant={hasAccess ? "default" : "destructive"}>
          {hasAccess ? "✅ Permitido" : "❌ Negado"}
        </Badge>
      </div>
    </div>
  );
}
```

## 🎯 Próximos Passos

1. **Implemente verificações básicas** nos seus componentes
2. **Proteja rotas sensíveis** com ProtectedRoute
3. **Configure cargos customizados** conforme necessário
4. **Teste com diferentes tipos de usuário** para validar
5. **Use componentes de debug** durante desenvolvimento

Para mais informações, consulte:
- [teams.md](../teams.md) - Sistema de teams completo
- [permissions.md](../permissions.md) - Documentação detalhada de permissões
