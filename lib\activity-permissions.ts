/**
 * Activity Permissions Utilities
 * Centralized functions for checking activity logging permissions
 */

import type { Models } from 'appwrite';
import { getUserPlan } from './plan-limits';

/**
 * Check if user has permission to view activities
 * Only users with enterprise plan or admin/moderator role can view activities
 */
export function canViewActivities(user: Models.User<Models.Preferences> | null): boolean {
  if (!user) return false;
  
  // Check if user is admin or moderator
  if (user.labels?.includes('admin') || user.labels?.includes('moderator')) {
    return true;
  }
  
  // Check if user has enterprise plan
  const userPlan = getUserPlan(user.labels || []);
  return userPlan === 'enterprise';
}

/**
 * Check if user has permission to log activities
 * Same as canViewActivities - only enterprise users or admins can log activities
 */
export function canLogActivities(user: Models.User<Models.Preferences> | null): boolean {
  return canViewActivities(user);
}

/**
 * Get activity permission info for UI display
 */
export function getActivityPermissionInfo(user: Models.User<Models.Preferences> | null): {
  canView: boolean;
  canLog: boolean;
  reason?: string;
  planRequired?: string;
} {
  const canView = canViewActivities(user);
  
  if (canView) {
    return {
      canView: true,
      canLog: true,
    };
  }
  
  return {
    canView: false,
    canLog: false,
    reason: 'O log de atividades está disponível apenas para usuários com plano Enterprise ou administradores.',
    planRequired: 'enterprise',
  };
}
