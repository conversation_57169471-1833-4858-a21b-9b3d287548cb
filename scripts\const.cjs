// Configuração das collections do Appwrite (Template Simplificado)
// Este arquivo define a estrutura das collections que serão criadas no banco de dados
// Baseado no padrão scripts_example com coleções essenciais do template
//
// ⚠️ IMPORTANTE: REGRAS GERAIS
// Cada coleção não pode passar de 16MB de dados estimados
//
// ⚠️ IMPORTANTE: RELACIONAMENTOS
// Os atributos de relacionamento (como team_chats.messages e chat_messages.chat)
// NÃO são definidos aqui. Eles são criados automaticamente pelo script setup-relationships.cjs
//
// ORDEM DE EXECUÇÃO:
// 1. yarn setup (cria collections + atributos)
// 2. yarn setup:relationships (cria relacionamentos entre collections)
//
// Para ver todos os relacionamentos configurados, consulte: scripts/setup-relationships.cjs

const collections = [
  {
    name: "clients",
    envKey: "NEXT_PUBLIC_APPWRITE_CLIENTS_ID",
    attributes: [
      // Ownership e auditoria
      { name: "userId", type: "string", size: 36, required: true },
      { name: "createdBy", type: "string", size: 36, required: true },
      { name: "updatedBy", type: "string", size: 36, required: false },
      { name: "teamId", type: "string", size: 36, required: false },

      // Informações básicas
      { name: "name", type: "string", size: 200, required: true, min: 2 },
      { name: "email", type: "email", required: false },
      { name: "phone", type: "string", size: 50, required: false },
      { name: "document", type: "string", size: 50, required: false }, // CPF ou CNPJ
      { name: "type", type: "enum", elements: ["pessoa_fisica", "pessoa_juridica"], required: true },

      // Informações da empresa (se pessoa jurídica)
      { name: "company", type: "string", size: 200, required: false },
      { name: "companyDocument", type: "string", size: 50, required: false }, // CNPJ

      // Status e classificação
      { name: "status", type: "enum", elements: ["ativo", "inativo", "prospecto", "arquivado"], default: "ativo" },
      { name: "priority", type: "enum", elements: ["baixa", "media", "alta", "critica"], default: "media" },

      // Informações adicionais
      { name: "tags", type: "string", size: 1000, required: false, array: true },
      { name: "avatar", type: "url", required: false },

      // Soft delete
      { name: "isDeleted", type: "boolean", default: false, required: false },

      // Índices
      { name: "userId", index: { type: "key" } },
      { name: "createdBy", index: { type: "key" } },
      { name: "teamId", index: { type: "key" } },
      { name: "name", index: { type: "fulltext" } },
      { name: "email", index: { type: "key" } },
      { name: "status", index: { type: "key" } },
      { name: "type", index: { type: "key" } },
      { name: "priority", index: { type: "key" } },
      { name: "document", index: { type: "key" } },
      { name: "companyDocument", index: { type: "key" } },
      { name: "isDeleted", index: { type: "key" } }
    ]
  },
  {
    name: "notifications",
    envKey: "NEXT_PUBLIC_APPWRITE_NOTIFICATIONS_ID",
    attributes: [
      // Ownership e auditoria
      { name: "userId", type: "string", size: 36, required: true },
      { name: "createdBy", type: "string", size: 36, required: true },
      { name: "updatedBy", type: "string", size: 36, required: false },

      // Atributos básicos
      { name: "title", type: "string", size: 100, required: true, min: 1 },
      { name: "message", type: "string", size: 500, required: true, min: 1 },
      { name: "content", type: "string", size: 500, required: false }, // Campo alias para compatibilidade
      { name: "type", type: "enum", elements: ["mention", "assignment", "comment", "update", "deadline", "system", "invite", "alert", "report", "summary", "success", "info", "warning", "error"], default: "info" },
      { name: "read", type: "boolean", default: false },
      { name: "actionUrl", type: "string", size: 500, required: false },
      { name: "relatedId", type: "string", size: 36, required: false },
      { name: "relatedType", type: "string", size: 50, required: false },

      // Soft delete
      { name: "isDeleted", type: "boolean", default: false, required: false },

      // Índices
      { name: "userId", index: { type: "key" } },
      { name: "createdBy", index: { type: "key" } },
      { name: "type", index: { type: "key" } },
      { name: "read", index: { type: "key" } },
      { name: "relatedType", index: { type: "key" } },
      { name: "isDeleted", index: { type: "key" } }
    ]
  },
  {
    name: "public_profiles",
    envKey: "NEXT_PUBLIC_APPWRITE_PUBLIC_PROFILES_ID",
    attributes: [
      // Ownership e auditoria
      { name: "userId", type: "string", size: 36, required: true },
      { name: "createdBy", type: "string", size: 36, required: true },
      { name: "updatedBy", type: "string", size: 36, required: false },

      // Atributos básicos
      { name: "name", type: "string", size: 100, required: true, min: 1 },
      { name: "bio", type: "string", size: 500, required: false },
      { name: "avatar", type: "url", required: false },
      { name: "isPublic", type: "boolean", default: true },
      { name: "website", type: "url", required: false },
      { name: "location", type: "string", size: 100, required: false },

      // Soft delete
      { name: "isDeleted", type: "boolean", default: false, required: false },

      // Índices
      { name: "userId", index: { type: "unique" } },
      { name: "createdBy", index: { type: "key" } },
      { name: "name", index: { type: "fulltext" } },
      { name: "isPublic", index: { type: "key" } },
      { name: "isDeleted", index: { type: "key" } }
    ]
  },
  {
    name: "activity_logs",
    envKey: "NEXT_PUBLIC_APPWRITE_ACTIVITY_LOGS_ID",
    attributes: [
      // Ownership e auditoria
      { name: "userId", type: "string", size: 36, required: true },
      { name: "createdBy", type: "string", size: 36, required: true },
      { name: "teamId", type: "string", size: 36, required: false },

      // Atributos básicos
      { name: "type", type: "enum", elements: ["auth", "client", "team", "chat", "file", "system", "admin", "notification", "preference", "calendar", "document"], required: true },
      { name: "action", type: "string", size: 100, required: true },
      { name: "resource", type: "string", size: 100, required: true },
      { name: "resourceId", type: "string", size: 36, required: false },
      { name: "title", type: "string", size: 200, required: true },
      { name: "description", type: "string", size: 500, required: false },
      { name: "details", type: "string", size: 2000, required: false }, // JSON string
      { name: "priority", type: "enum", elements: ["low", "normal", "high", "critical"], default: "normal" },
      { name: "tags", type: "string", size: 500, required: false, array: true },
      { name: "category", type: "string", size: 100, required: false },
      { name: "metadata", type: "string", size: 2000, required: false }, // JSON string
      { name: "ipAddress", type: "string", size: 45, required: false },
      { name: "userAgent", type: "string", size: 500, required: false },

      // Soft delete
      { name: "isDeleted", type: "boolean", default: false, required: false },

      // Índices
      { name: "userId", index: { type: "key" } },
      { name: "createdBy", index: { type: "key" } },
      { name: "teamId", index: { type: "key" } },
      { name: "type", index: { type: "key" } },
      { name: "action", index: { type: "key" } },
      { name: "resource", index: { type: "key" } },
      { name: "resourceId", index: { type: "key" } },
      { name: "priority", index: { type: "key" } },
      { name: "title", index: { type: "fulltext" } },
      { name: "tags", index: { type: "key" } },
      { name: "isDeleted", index: { type: "key" } }
    ]
  },
  {
    name: "team_chats",
    envKey: "NEXT_PUBLIC_APPWRITE_TEAM_CHATS_ID",
    attributes: [
      // Ownership e auditoria
      { name: "userId", type: "string", size: 36, required: true },
      { name: "createdBy", type: "string", size: 36, required: true },
      { name: "updatedBy", type: "string", size: 36, required: false },

      // Atributos básicos do chat
      { name: "teamId", type: "string", size: 36, required: true },
      { name: "name", type: "string", size: 100, required: true, min: 1 },
      { name: "description", type: "string", size: 500, required: false },
      { name: "isPrivate", type: "boolean", default: false },
      { name: "isActive", type: "boolean", default: true },

      // Membros do chat
      { name: "members", type: "string", size: 36, required: true, array: true }, // Array de user IDs

      // Atividade e estatísticas
      { name: "lastActivity", type: "datetime", required: false },
      { name: "unreadCount", type: "integer", min: 0, default: 0 },

      // Configurações do chat
      { name: "allowFileSharing", type: "boolean", default: true },
      { name: "allowReactions", type: "boolean", default: true },
      { name: "retentionDays", type: "integer", min: 1, default: 30 },

      // ⚠️ IMPORTANTE: RELACIONAMENTOS NÃO SÃO DEFINIDOS AQUI
      // O atributo 'messages' (array de chat_messages) é criado AUTOMATICAMENTE
      // pelo script setup-relationships.cjs via createRelationshipAttribute()
      //
      // NUNCA adicione manualmente:
      // - messages: array de IDs
      // - chatId em chat_messages
      //
      // O Appwrite gerencia esses campos automaticamente via relacionamento TWO-WAY

      // Soft delete
      { name: "isDeleted", type: "boolean", default: false, required: false },

      // Índices
      { name: "userId", index: { type: "key" } },
      { name: "createdBy", index: { type: "key" } },
      { name: "teamId", index: { type: "key" } },
      { name: "name", index: { type: "fulltext" } },
      { name: "isActive", index: { type: "key" } },
      { name: "isPrivate", index: { type: "key" } },
      { name: "members", index: { type: "key" } },
      { name: "isDeleted", index: { type: "key" } }
    ]
  },
  {
    name: "chat_messages",
    envKey: "NEXT_PUBLIC_APPWRITE_CHAT_MESSAGES_ID",
    attributes: [
      // Ownership e auditoria
      { name: "userId", type: "string", size: 36, required: true },
      { name: "createdBy", type: "string", size: 36, required: true },
      { name: "updatedBy", type: "string", size: 36, required: false },

      // Atributos básicos - SINCRONIZADO COM TIPOS
      { name: "content", type: "string", size: 2000, required: true, min: 1 }, // Conteúdo da mensagem
      { name: "senderId", type: "string", size: 36, required: true }, // ID do remetente
      { name: "senderName", type: "string", size: 100, required: true }, // Nome do remetente
      { name: "senderAvatar", type: "url", required: false }, // Avatar do remetente
      { name: "teamId", type: "string", size: 36, required: true }, // ID do time (para compatibilidade)
      { name: "type", type: "enum", elements: ["text", "image", "file", "system"], default: "text" },
      { name: "status", type: "enum", elements: ["sending", "sent", "delivered", "read", "failed"], default: "sent" },

      // Edição e resposta
      { name: "edited", type: "boolean", default: false }, // Renomeado de isEdited para edited
      { name: "editedAt", type: "datetime", required: false },
      { name: "replyTo", type: "string", size: 36, required: false }, // ID da mensagem sendo respondida

      // ⚠️ IMPORTANTE: RELACIONAMENTOS NÃO SÃO DEFINIDOS AQUI
      // O atributo 'chat' (referência para team_chats) é criado AUTOMATICAMENTE
      // pelo script setup-relationships.cjs via createRelationshipAttribute()
      //
      // NUNCA adicione manualmente:
      // - chatId: string (referência para o chat pai)
      // - chat: objeto do chat pai
      //
      // O Appwrite gerencia esses campos automaticamente via relacionamento TWO-WAY
      // Quando uma mensagem é criada, ela automaticamente recebe o chatId
      // Quando um chat é deletado, todas as mensagens são deletadas (CASCADE)

      // Anexos e mídia (metadata)
      { name: "attachments", type: "string", size: 1000, required: false, array: true },
      { name: "imageUrl", type: "url", required: false }, // Para metadata.imageUrl
      { name: "fileUrl", type: "url", required: false },
      { name: "fileName", type: "string", size: 255, required: false }, // Para metadata.fileName
      { name: "fileSize", type: "integer", min: 0, required: false }, // Para metadata.fileSize
      { name: "fileType", type: "string", size: 100, required: false }, // Para metadata.fileType

      // Reações e menções
      { name: "reactions", type: "string", size: 100, required: false, array: true }, // Array de reações no formato "emoji:userId"
      { name: "mentions", type: "string", size: 36, required: false, array: true }, // Array de user IDs mencionados

      // Soft delete
      { name: "isDeleted", type: "boolean", default: false, required: false },

      // Índices
      { name: "userId", index: { type: "key" } },
      { name: "createdBy", index: { type: "key" } },
      { name: "senderId", index: { type: "key" } },
      { name: "teamId", index: { type: "key" } },
      { name: "senderName", index: { type: "key" } },
      { name: "type", index: { type: "key" } },
      { name: "status", index: { type: "key" } },
      { name: "content", index: { type: "fulltext" } }, // Renomeado de message para content
      { name: "replyTo", index: { type: "key" } },
      { name: "reactions", index: { type: "key" } },
      { name: "mentions", index: { type: "key" } },
      { name: "isDeleted", index: { type: "key" } }
    ]
  },
  {
    name: "events",
    envKey: "NEXT_PUBLIC_APPWRITE_EVENTS_ID",
    attributes: [
      // Ownership e auditoria
      { name: "userId", type: "string", size: 36, required: true },
      { name: "createdBy", type: "string", size: 36, required: true },
      { name: "updatedBy", type: "string", size: 36, required: false },
      { name: "teamId", type: "string", size: 36, required: false },

      // Informações básicas do evento
      { name: "title", type: "string", size: 200, required: true, min: 1 },
      { name: "description", type: "string", size: 1000, required: false },

      // Data e hora
      { name: "startDate", type: "datetime", required: true },
      { name: "endDate", type: "datetime", required: true },
      { name: "allDay", type: "boolean", default: false },
      { name: "timezone", type: "string", size: 50, default: "America/Sao_Paulo" },

      // Localização
      { name: "location", type: "string", size: 300, required: false },
      { name: "locationUrl", type: "url", required: false },

      // Classificação
      { name: "type", type: "enum", elements: ["meeting", "task", "reminder", "appointment", "deadline", "personal", "work", "other"], default: "other" },
      { name: "priority", type: "enum", elements: ["baixa", "media", "alta", "critica"], default: "media" },
      { name: "status", type: "enum", elements: ["agendado", "em_andamento", "concluido", "cancelado", "adiado"], default: "agendado" },

      // Categoria e cor
      { name: "category", type: "string", size: 50, required: false },
      { name: "color", type: "string", size: 7, default: "#3B82F6" },

      // Recorrência
      { name: "recurrenceType", type: "enum", elements: ["none", "daily", "weekly", "monthly", "yearly"], default: "none" },
      { name: "recurrenceInterval", type: "integer", min: 1, max: 365, default: 1 },
      { name: "recurrenceEndDate", type: "datetime", required: false },
      { name: "recurrenceCount", type: "integer", min: 1, max: 1000, required: false },
      { name: "recurrenceWeekdays", type: "string", size: 20, required: false, array: true }, // Array de números como strings
      { name: "recurrenceMonthDay", type: "integer", min: 1, max: 31, required: false },
      { name: "parentEventId", type: "string", size: 36, required: false },

      // Lembretes
      { name: "reminderType", type: "enum", elements: ["none", "5min", "15min", "30min", "1hour", "2hours", "1day", "1week"], default: "none" },
      { name: "customReminderMinutes", type: "integer", min: 0, max: 10080, required: false },
      { name: "reminderSent", type: "boolean", default: false },

      // Participantes e compartilhamento
      { name: "attendees", type: "string", size: 36, required: false, array: true },
      { name: "isPublic", type: "boolean", default: false },
      { name: "allowGuestInvites", type: "boolean", default: false },

      // Anexos e links
      { name: "attachments", type: "string", size: 500, required: false, array: true },
      { name: "links", type: "string", size: 1000, required: false }, // JSON string

      // Metadata adicional
      { name: "tags", type: "string", size: 30, required: false, array: true },
      { name: "notes", type: "string", size: 2000, required: false },

      // Soft delete
      { name: "isDeleted", type: "boolean", default: false, required: false },

      // Índices
      { name: "userId", index: { type: "key" } },
      { name: "createdBy", index: { type: "key" } },
      { name: "teamId", index: { type: "key" } },
      { name: "title", index: { type: "fulltext" } },
      { name: "startDate", index: { type: "key" } },
      { name: "endDate", index: { type: "key" } },
      { name: "type", index: { type: "key" } },
      { name: "priority", index: { type: "key" } },
      { name: "status", index: { type: "key" } },
      { name: "category", index: { type: "key" } },
      { name: "recurrenceType", index: { type: "key" } },
      { name: "parentEventId", index: { type: "key" } },
      { name: "attendees", index: { type: "key" } },
      { name: "tags", index: { type: "key" } },
      { name: "isDeleted", index: { type: "key" } }
    ]
  },
  {
    name: "event_categories",
    envKey: "NEXT_PUBLIC_APPWRITE_EVENT_CATEGORIES_ID",
    attributes: [
      // Ownership e auditoria
      { name: "userId", type: "string", size: 36, required: true },
      { name: "teamId", type: "string", size: 36, required: false },

      // Informações da categoria
      { name: "name", type: "string", size: 50, required: true, min: 1 },
      { name: "color", type: "string", size: 7, required: true },
      { name: "description", type: "string", size: 200, required: false },
      { name: "isDefault", type: "boolean", default: false },
      { name: "sortOrder", type: "integer", default: 0 },

      // Soft delete
      { name: "isDeleted", type: "boolean", default: false, required: false },

      // Índices
      { name: "userId", index: { type: "key" } },
      { name: "teamId", index: { type: "key" } },
      { name: "name", index: { type: "fulltext" } },
      { name: "isDefault", index: { type: "key" } },
      { name: "isDeleted", index: { type: "key" } }
    ]
  },
  {
    name: "workspaces",
    envKey: "NEXT_PUBLIC_APPWRITE_WORKSPACES_ID",
    attributes: [
      // Ownership e auditoria
      { name: "userId", type: "string", size: 36, required: true },
      { name: "createdBy", type: "string", size: 36, required: true },
      { name: "updatedBy", type: "string", size: 36, required: false },

      // Informações básicas
      { name: "name", type: "string", size: 100, required: true, min: 1 },
      { name: "description", type: "string", size: 500, required: false },

      // Configurações visuais
      { name: "color", type: "string", size: 7, required: false },
      { name: "icon", type: "string", size: 50, required: false },

      // Configurações
      { name: "visibility", type: "enum", elements: ["private", "team", "public"], default: "private" },
      { name: "allowInvites", type: "boolean", default: true },

      // Membros (array de user IDs)
      { name: "members", type: "string", size: 36, required: false, array: true },

      // Metadados
      { name: "isArchived", type: "boolean", default: false },

      // Soft delete
      { name: "isDeleted", type: "boolean", default: false, required: false },

      // Índices
      { name: "userId", index: { type: "key" } },
      { name: "createdBy", index: { type: "key" } },
      { name: "name", index: { type: "fulltext" } },
      { name: "visibility", index: { type: "key" } },
      { name: "members", index: { type: "key" } },
      { name: "isArchived", index: { type: "key" } },
      { name: "isDeleted", index: { type: "key" } }
    ]
  },
  {
    name: "kanban_boards_optimized",
    envKey: "NEXT_PUBLIC_APPWRITE_KANBAN_BOARDS_OPTIMIZED_ID",
    attributes: [
      // Ownership e auditoria
      { name: "userId", type: "string", size: 36, required: true },
      { name: "teamId", type: "string", size: 36, required: false },
      { name: "createdBy", type: "string", size: 36, required: true },
      { name: "updatedBy", type: "string", size: 36, required: false },

      // Relacionamentos
      { name: "workspaceId", type: "string", size: 36, required: false },

      // Informações básicas do board
      { name: "title", type: "string", size: 200, required: true, min: 1 },
      { name: "description", type: "string", size: 1000, required: false },

      // Configurações de visibilidade
      { name: "visibility", type: "enum", elements: ["private", "team", "public"], default: "private" },

      // Configurações visuais
      { name: "backgroundColor", type: "string", size: 7, required: false },
      { name: "backgroundImage", type: "url", required: false },

      // Membros do board (array de user IDs)
      { name: "members", type: "string", size: 36, required: false, array: true },

      // Configurações funcionais
      { name: "allowComments", type: "boolean", default: true },
      { name: "allowAttachments", type: "boolean", default: true },
      { name: "enableTimeTracking", type: "boolean", default: false },
      { name: "enableLabels", type: "boolean", default: true },
      { name: "enableChecklists", type: "boolean", default: true },
      { name: "enableDueDates", type: "boolean", default: true },

      // Configurações de automação
      { name: "enableAutomation", type: "boolean", default: false },
      { name: "automationRules", type: "string", size: 5000, required: false }, // JSON string

      // DADOS EMBARCADOS - Tudo em um lugar para máxima performance!
      { name: "labels", type: "string", size: 10000, required: false }, // JSON string com array de labels
      { name: "columns", type: "string", size: 50000, required: false }, // JSON string com array de columns (incluindo tasks)

      // Metadados
      { name: "isTemplate", type: "boolean", default: false },
      { name: "isArchived", type: "boolean", default: false },
      { name: "isFavorite", type: "boolean", default: false },
      { name: "isStarred", type: "boolean", default: false },

      // Estatísticas (calculadas)
      { name: "tasksCount", type: "integer", min: 0, default: 0 },
      { name: "membersCount", type: "integer", min: 0, default: 0 },
      { name: "lastActivity", type: "datetime", required: false },

      // Soft delete
      { name: "isDeleted", type: "boolean", default: false, required: false },

      // Índices
      { name: "userId", index: { type: "key" } },
      { name: "createdBy", index: { type: "key" } },
      { name: "teamId", index: { type: "key" } },
      { name: "workspaceId", index: { type: "key" } },
      { name: "title", index: { type: "fulltext" } },
      { name: "visibility", index: { type: "key" } },
      { name: "members", index: { type: "key" } },
      { name: "isTemplate", index: { type: "key" } },
      { name: "isArchived", index: { type: "key" } },
      { name: "isFavorite", index: { type: "key" } },
      { name: "isStarred", index: { type: "key" } },
      { name: "lastActivity", index: { type: "key" } },
      { name: "isDeleted", index: { type: "key" } }
    ]
  }
];

module.exports = { collections };
