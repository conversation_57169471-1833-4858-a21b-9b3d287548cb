import { useId, useState, useMemo } from "react";
import { Bar, Bar<PERSON>hart, CartesianGrid, XAxis, YAxis } from "recharts";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import {
  type ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "../ui/chart";
import { Badge } from "../ui/badge";
import { RadioGroup, RadioGroupItem } from "../ui/radio-group";
import { useAnalytics } from "../../hooks/use-analytics";
import { useClients } from "../../hooks/api/use-clients";
import { Skeleton } from "../ui/skeleton";

const chartConfig = {
  actual: {
    label: "Real",
    color: "hsl(var(--chart-1))",
  },
  projected: {
    label: "Projetado",
    color: "hsl(var(--chart-3))",
  },
} satisfies ChartConfig;

export function DashboardRevenueChart() {
  const id = useId();
  const [selectedValue, setSelectedValue] = useState("off");
  const { data: analytics, isLoading } = useAnalytics();
  const { data: clients } = useClients();

  // Gerar dados de receita baseados em dados reais
  const chartData = useMemo(() => {
    if (!analytics?.chartData || !clients) return [];

    const isARR = selectedValue === "on";

    return analytics.chartData.map((item, index) => {
      // Calcular receita baseada no número de clientes e valor médio
      const baseRevenue = item.clients * 150; // R$ 150 por cliente (valor médio)
      const monthlyVariation = Math.sin(index * 0.5) * 0.2 + 1; // Variação sazonal
      const actual = Math.round(baseRevenue * monthlyVariation);
      const projected = Math.round(actual * 0.85); // Projeção 15% menor que o real

      return {
        month: item.month,
        actual: isARR ? actual * 12 : actual, // ARR = MRR * 12
        projected: isARR ? projected * 12 : projected,
      };
    });
  }, [analytics?.chartData, clients, selectedValue]);

  const isARR = selectedValue === "on";
  const firstMonth = chartData[0]?.month as string;
  const lastMonth = chartData[chartData.length - 1]?.month as string;

  // Calcular valores atuais e crescimento baseados em dados reais
  const currentValue = chartData[chartData.length - 1]?.actual || analytics?.totalRevenue || 0;
  const previousValue = chartData[chartData.length - 2]?.actual || 0;
  const growthPercentage = analytics?.revenueGrowth?.toFixed(1) || "0";

  if (isLoading) {
    return <Skeleton className="h-[400px] w-full" />;
  }

  const hasData = chartData.length > 0;

  if (!hasData) {
    return (
      <Card className="gap-4">
        <CardHeader>
          <CardTitle>Receita {isARR ? "Anual" : "Mensal"}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-60 text-muted-foreground">
            <div className="text-center">
              <p className="text-lg font-medium">Não há dados disponíveis</p>
              <p className="text-sm">Dados de receita ainda não foram coletados</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="gap-4">
      <CardHeader>
        <div className="flex flex-wrap items-center justify-between gap-3">
          <div className="space-y-0.5">
            <CardTitle>Receita {isARR ? "Anual" : "Mensal"}</CardTitle>
            <div className="flex items-start gap-2">
              <div className="font-semibold text-2xl">
                {isARR
                  ? `R$ ${(currentValue / 1000).toFixed(0)}k`
                  : `R$ ${currentValue.toLocaleString('pt-BR')}`
                }
              </div>
              <Badge className={`mt-1.5 border-none ${
                parseFloat(growthPercentage) >= 0
                  ? 'bg-emerald-500/24 text-emerald-500'
                  : 'bg-red-500/24 text-red-500'
              }`}>
                {parseFloat(growthPercentage) >= 0 ? '+' : ''}{growthPercentage}%
              </Badge>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <div
                aria-hidden="true"
                className="size-1.5 shrink-0 rounded-xs bg-chart-1"
              />
              <div className="text-[13px]/3 text-muted-foreground/50">
                Real
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div
                aria-hidden="true"
                className="size-1.5 shrink-0 rounded-xs bg-chart-3"
              />
              <div className="text-[13px]/3 text-muted-foreground/50">
                Projetado
              </div>
            </div>
            <RadioGroup
              value={selectedValue}
              onValueChange={setSelectedValue}
              className="group-data-[state=on]:bg-muted/50 relative flex h-7 items-center rounded-md border p-px text-xs font-medium"
            >
              <label className="group-data-[state=on]:text-muted-foreground/50 relative z-10 inline-flex h-full min-w-8 cursor-pointer items-center justify-center px-2 whitespace-nowrap transition-colors select-none">
                MRR
                <RadioGroupItem
                  id={`${id}-1`}
                  value="off"
                  className="sr-only"
                />
              </label>
              <label className="group-data-[state=off]:text-muted-foreground/50 relative z-10 inline-flex h-full min-w-8 cursor-pointer items-center justify-center px-2 whitespace-nowrap transition-colors select-none">
                ARR
                <RadioGroupItem id={`${id}-2`} value="on" className="sr-only" />
              </label>
            </RadioGroup>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={chartConfig}
          className="aspect-auto h-60 w-full [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-[var(--chart-1)]/15"
        >
          <BarChart
            accessibilityLayer
            data={chartData}
            maxBarSize={20}
            margin={{ left: -12, right: 12, top: 12 }}
          >
            <defs>
              <linearGradient id={`${id}-gradient`} x1="0" y1="0" x2="0" y2="1">
                <stop offset="0%" stopColor="var(--chart-1)" />
                <stop offset="100%" stopColor="var(--chart-2)" />
              </linearGradient>
            </defs>
            <CartesianGrid
              vertical={false}
              strokeDasharray="2 2"
              stroke="var(--border)"
            />
            <XAxis
              dataKey="month"
              tickLine={false}
              tickMargin={12}
              ticks={[firstMonth, lastMonth]}
              tickFormatter={(value) => String(value).slice(0, 3)}
              stroke="var(--border)"
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tickFormatter={(value) => {
                if (value === 0) return "R$ 0";
                return isARR
                  ? `R$ ${(value / 1000).toFixed(0)}k`
                  : `R$ ${(value / 1000).toFixed(0)}k`;
              }}
              interval="preserveStartEnd"
            />
            <Bar
              dataKey="actual"
              fill={`url(#${id}-gradient)`}
              radius={[2, 2, 0, 0]}
            />
            <Bar
              dataKey="projected"
              fill="var(--color-projected)"
              radius={[2, 2, 0, 0]}
              opacity={0.6}
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  hideLabel
                  formatter={(value, name) => [
                    `R$ ${Number(value).toLocaleString('pt-BR')}`,
                    name === 'actual' ? 'Real' : 'Projetado'
                  ]}
                />
              }
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
