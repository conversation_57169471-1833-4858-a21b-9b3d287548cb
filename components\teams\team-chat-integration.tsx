/**
 * Componente para integrar chat do time nas preferências
 * Gerencia criação automática e exibição do chat
 */

import { useState } from 'react';
import { Button } from '../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { MessageCircle, Users, Settings, ExternalLink, Loader2 } from 'lucide-react';
import { useGetOrCreateTeamChat, useTeamChat } from '../../hooks/api/use-teams';
import { useChat, useChatRealtime } from '../../hooks/api/use-team-chat';
import { toast } from 'sonner';
import type { Team } from '@/schemas/teams';

interface TeamChatIntegrationProps {
  team: Team;
  className?: string;
}

export function TeamChatIntegration({ team, className }: TeamChatIntegrationProps) {
  const [showChatPreview, setShowChatPreview] = useState(false);

  // Hooks para gerenciar chat
  const { data: teamChat, isLoading: loadingTeamChat } = useTeamChat(team.$id);
  const getOrCreateChatMutation = useGetOrCreateTeamChat();
  const { data: chat, isLoading: loadingChat } = useChat(teamChat?.$id || '');
  const { isConnected } = useChatRealtime(team.$id);

  const handleCreateOrAccessChat = async () => {
    try {
      if (teamChat) {
        // Chat já existe, apenas mostrar preview
        setShowChatPreview(true);
        toast.success('Chat do time carregado!');
      } else {
        // Criar novo chat
        await getOrCreateChatMutation.mutateAsync({
          teamId: team.$id,
          teamName: team.name,
        });
        setShowChatPreview(true);
      }
    } catch (error) {
      console.error('Erro ao acessar chat:', error);
      toast.error('Erro ao acessar chat do time');
    }
  };

  const chatExists = !!teamChat;
  const isLoading = loadingTeamChat || getOrCreateChatMutation.isPending;

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            <CardTitle>Chat do Time</CardTitle>
            {isConnected && (
              <Badge variant="outline" className="text-green-600">
                Online
              </Badge>
            )}
          </div>
          <CardDescription>
            {chatExists
              ? 'Chat principal do time para comunicação em tempo real'
              : 'Configure o chat principal do time para comunicação entre membros'
            }
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Status do Chat */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className={`w-2 h-2 rounded-full ${chatExists ? 'bg-green-500' : 'bg-gray-400'}`} />
              <span className="text-sm font-medium">
                {chatExists ? 'Chat Configurado' : 'Chat Não Configurado'}
              </span>
            </div>

            {chatExists && chat && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Users className="h-4 w-4" />
                <span>{chat.members?.length || 0} membros</span>
              </div>
            )}
          </div>

          {/* Informações do Chat */}
          {chatExists && chat && (
            <div className="space-y-2">
              <div className="text-sm">
                <span className="font-medium">Nome:</span> {chat.name}
              </div>
              {chat.description && (
                <div className="text-sm">
                  <span className="font-medium">Descrição:</span> {chat.description}
                </div>
              )}
              <div className="text-sm">
                <span className="font-medium">Última atividade:</span>{' '}
                {chat.lastActivity
                  ? new Date(chat.lastActivity).toLocaleString('pt-BR')
                  : 'Nenhuma atividade'
                }
              </div>
            </div>
          )}

          <Separator />

          {/* Ações */}
          <div className="flex gap-2">
            <Button
              onClick={handleCreateOrAccessChat}
              disabled={isLoading}
              className="flex-1"
            >
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {chatExists ? 'Acessar Chat' : 'Configurar Chat'}
            </Button>

            {chatExists && (
              <Button
                variant="outline"
                size="icon"
                onClick={() => setShowChatPreview(!showChatPreview)}
                title="Configurações do Chat"
              >
                <Settings className="h-4 w-4" />
              </Button>
            )}
          </div>

          {/* Preview do Chat */}
          {showChatPreview && chat && (
            <Card className="mt-4">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base">Preview do Chat</CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowChatPreview(false)}
                  >
                    Fechar
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-sm space-y-1">
                  <div><strong>ID:</strong> {chat.$id}</div>
                  <div><strong>Membros:</strong> {chat.members?.length || 0}</div>
                  <div><strong>Mensagens não lidas:</strong> {chat.unreadCount || 0}</div>
                  <div><strong>Compartilhamento de arquivos:</strong> {chat.allowFileSharing ? 'Ativado' : 'Desativado'}</div>
                  <div><strong>Reações:</strong> {chat.allowReactions ? 'Ativadas' : 'Desativadas'}</div>
                  <div><strong>Retenção:</strong> {chat.retentionDays} dias</div>
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => {
                    // TODO: Implementar navegação para página do chat
                    toast.info('Navegação para chat será implementada');
                  }}
                >
                  <ExternalLink className="mr-2 h-4 w-4" />
                  Abrir Chat Completo
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Loading States */}
          {loadingChat && chatExists && (
            <div className="flex items-center justify-center py-4">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2 text-sm text-muted-foreground">Carregando chat...</span>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
