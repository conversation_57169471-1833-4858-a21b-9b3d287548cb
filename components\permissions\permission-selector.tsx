/**
 * <PERSON><PERSON><PERSON> de Permis<PERSON>es
 * Permite selecionar permissões granulares para recursos do sistema
 */

import { useState } from 'react';
import { Check, ChevronDown, ChevronRight } from 'lucide-react';
import { cn } from '../../lib/utils';
import { Button } from '../ui/button';
import { Checkbox } from '../ui/checkbox';
import { Badge } from '../ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '../ui/collapsible';
import {
  RESOURCE_CONFIG,
  ACTION_LABELS,
  type ResourcePermission,
  type SystemResource,
  type ResourceAction,
} from '../../schemas/permissions';

interface PermissionSelectorProps {
  value: ResourcePermission[];
  onChange: (permissions: ResourcePermission[]) => void;
}

export function PermissionSelector({ value, onChange }: PermissionSelectorProps) {
  const [expandedCategories, setExpandedCategories] = useState<string[]>(['core']);

  // Agrupar recursos por categoria
  const resourcesByCategory = Object.entries(RESOURCE_CONFIG).reduce((acc, [resource, config]) => {
    if (!acc[config.category]) {
      acc[config.category] = [];
    }
    acc[config.category].push({ resource: resource as SystemResource, ...config });
    return acc;
  }, {} as Record<string, Array<{ resource: SystemResource } & typeof RESOURCE_CONFIG[SystemResource]>>);

  const categories = {
    core: 'Funcionalidades Principais',
    data: 'Gerenciamento de Dados',
    communication: 'Comunicação',
    settings: 'Configurações',
  };

  // Verificar se um recurso tem uma ação específica
  const hasAction = (resource: SystemResource, action: ResourceAction): boolean => {
    const permission = value.find(p => p.resource === resource);
    return permission?.actions.includes(action) || false;
  };

  // Verificar se um recurso tem todas as ações
  const hasAllActions = (resource: SystemResource): boolean => {
    const permission = value.find(p => p.resource === resource);
    const allActions: ResourceAction[] = ['view', 'create', 'edit', 'delete', 'manage'];
    return allActions.every(action => permission?.actions.includes(action));
  };

  // Verificar se um recurso tem algumas ações
  const hasSomeActions = (resource: SystemResource): boolean => {
    const permission = value.find(p => p.resource === resource);
    return !!(permission && permission.actions.length > 0);
  };

  // Toggle de ação específica
  const toggleAction = (resource: SystemResource, action: ResourceAction) => {
    const existingPermission = value.find(p => p.resource === resource);

    if (!existingPermission) {
      // Criar nova permissão
      onChange([...value, { resource, actions: [action] }]);
    } else {
      // Atualizar permissão existente
      const hasActionNow = existingPermission.actions.includes(action);

      if (hasActionNow) {
        // Remover ação
        const newActions = existingPermission.actions.filter(a => a !== action);
        if (newActions.length === 0) {
          // Remover permissão completamente se não há ações
          onChange(value.filter(p => p.resource !== resource));
        } else {
          // Atualizar ações
          onChange(value.map(p =>
            p.resource === resource
              ? { ...p, actions: newActions }
              : p
          ));
        }
      } else {
        // Adicionar ação
        onChange(value.map(p =>
          p.resource === resource
            ? { ...p, actions: [...p.actions, action] }
            : p
        ));
      }
    }
  };

  // Toggle de todas as ações de um recurso
  const toggleAllActions = (resource: SystemResource) => {
    const allActions: ResourceAction[] = ['view', 'create', 'edit', 'delete', 'manage'];
    const hasAll = hasAllActions(resource);

    if (hasAll) {
      // Remover todas as ações
      onChange(value.filter(p => p.resource !== resource));
    } else {
      // Adicionar todas as ações
      const existingPermission = value.find(p => p.resource === resource);
      if (existingPermission) {
        onChange(value.map(p =>
          p.resource === resource
            ? { ...p, actions: allActions }
            : p
        ));
      } else {
        onChange([...value, { resource, actions: allActions }]);
      }
    }
  };

  // Toggle categoria expandida
  const toggleCategory = (category: string) => {
    setExpandedCategories(prev =>
      prev.includes(category)
        ? prev.filter(c => c !== category)
        : [...prev, category]
    );
  };

  return (
    <div className="space-y-4">
      <div className="text-sm text-muted-foreground">
        Selecione as permissões que este cargo terá no sistema.
        Permissões mais específicas sobrescrevem as gerais.
      </div>

      {Object.entries(categories).map(([categoryKey, categoryLabel]) => {
        const resources = resourcesByCategory[categoryKey] || [];
        const isExpanded = expandedCategories.includes(categoryKey);

        if (resources.length === 0) return null;

        return (
          <Card key={categoryKey}>
            <Collapsible open={isExpanded} onOpenChange={() => toggleCategory(categoryKey)}>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
                  <CardTitle className="flex items-center justify-between text-base">
                    <span>{categoryLabel}</span>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">
                        {resources.filter(r => hasSomeActions(r.resource)).length} / {resources.length}
                      </Badge>
                      {isExpanded ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </div>
                  </CardTitle>
                </CardHeader>
              </CollapsibleTrigger>

              <CollapsibleContent>
                <CardContent className="pt-0">
                  <div className="space-y-4">
                    {resources.map(({ resource, label, description }) => {
                      const hasAll = hasAllActions(resource);
                      const hasSome = hasSomeActions(resource);

                      return (
                        <div key={resource} className="border rounded-lg p-4">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center gap-3">
                              <Checkbox
                                checked={hasAll}
                                ref={(el) => {
                                  if (el && 'indeterminate' in el) {
                                    (el as any).indeterminate = hasSome && !hasAll;
                                  }
                                }}
                                onCheckedChange={() => toggleAllActions(resource)}
                              />
                              <div>
                                <div className="font-medium">{label}</div>
                                <div className="text-sm text-muted-foreground">
                                  {description}
                                </div>
                              </div>
                            </div>
                            {hasSome && (
                              <Badge variant="secondary">
                                {value.find(p => p.resource === resource)?.actions.length || 0} ações
                              </Badge>
                            )}
                          </div>

                          <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
                            {(['view', 'create', 'edit', 'delete', 'manage'] as ResourceAction[]).map(action => (
                              <Button
                                key={action}
                                variant={hasAction(resource, action) ? 'default' : 'outline'}
                                size="sm"
                                className="justify-start"
                                onClick={() => toggleAction(resource, action)}
                              >
                                {hasAction(resource, action) && (
                                  <Check className="h-3 w-3 mr-1" />
                                )}
                                {ACTION_LABELS[action]}
                              </Button>
                            ))}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>
        );
      })}

      {/* Resumo das permissões selecionadas */}
      {value.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Resumo das Permissões</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {value.map(permission => (
                <div key={permission.resource} className="flex items-center justify-between">
                  <span className="text-sm font-medium">
                    {RESOURCE_CONFIG[permission.resource].label}
                  </span>
                  <div className="flex gap-1">
                    {permission.actions.map(action => (
                      <Badge key={action} variant="secondary" className="text-xs">
                        {ACTION_LABELS[action]}
                      </Badge>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
