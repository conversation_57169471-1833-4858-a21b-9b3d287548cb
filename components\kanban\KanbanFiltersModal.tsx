/**
 * Modal de Filtros do Kanban
 * Permite configurar filtros avançados para tarefas
 */

import React, { useState, useEffect } from 'react';
import { useSnapshot } from 'valtio';
import { X, Filter, Search, Tag, User, Calendar, Archive, RotateCcw } from 'lucide-react';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { Checkbox } from '../ui/checkbox';
import { ScrollArea } from '../ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';

import { kanbanStore, kanbanActions, kanbanSelectors } from '../../stores/kanban-store';
import { useIsMobile } from '../../hooks/use-mobile';
import { useBoardWithData } from '../../hooks/api/use-kanban';
import { useUsers } from '../../hooks/api/use-users';
import type { TaskPriority, TaskStatus } from '@/schemas/kanban';

const priorityOptions: { value: TaskPriority; label: string; color: string }[] = [
  { value: 'baixa', label: 'Baixa', color: 'bg-green-500' },
  { value: 'media', label: 'Média', color: 'bg-yellow-500' },
  { value: 'alta', label: 'Alta', color: 'bg-orange-500' },
  { value: 'critica', label: 'Crítica', color: 'bg-red-500' },
];

const statusOptions: { value: TaskStatus; label: string; color: string }[] = [
  { value: 'todo', label: 'A Fazer', color: 'bg-gray-500' },
  { value: 'in_progress', label: 'Em Progresso', color: 'bg-blue-500' },
  { value: 'review', label: 'Em Revisão', color: 'bg-purple-500' },
  { value: 'done', label: 'Concluído', color: 'bg-green-500' },
];

interface KanbanFiltersModalProps {
  boardId?: string;
}

export function KanbanFiltersModal({ boardId }: KanbanFiltersModalProps) {
  const snap = useSnapshot(kanbanStore);
  const isMobile = useIsMobile();
  const isOpen = snap.filtersModal.isOpen;

  // Get board data to extract available tags and users
  const { data: boardData } = useBoardWithData(boardId || '');
  const { data: users } = useUsers();

  // Local state for filters (to allow cancel)
  const [localFilters, setLocalFilters] = useState(snap.filters);

  // Update local filters when modal opens
  useEffect(() => {
    if (isOpen) {
      setLocalFilters({ ...snap.filters });
    }
  }, [isOpen, snap.filters]);

  // Extract available tags from board tasks - ESTRUTURA OTIMIZADA
  const availableTags = React.useMemo(() => {
    if (!boardData?.columns) return [];
    const tagSet = new Set<string>();
    boardData.columns.forEach((column: any) => {
      column.tasks?.forEach((task: any) => {
        if (task.tags) {
          task.tags.forEach((tag: string) => tagSet.add(tag));
        }
      });
    });
    return Array.from(tagSet).sort();
  }, [boardData?.columns]);

  // Extract assigned users from board tasks - ESTRUTURA OTIMIZADA
  const assignedUsers = React.useMemo(() => {
    if (!boardData?.columns || !users) return [];
    const userSet = new Set<string>();
    boardData.columns.forEach((column: any) => {
      column.tasks?.forEach((task: any) => {
        if (task.assignedTo) {
          userSet.add(task.assignedTo);
        }
      });
    });
    return users.filter(user => userSet.has(user.$id));
  }, [boardData?.columns, users]);

  const handleClose = () => {
    kanbanActions.closeFiltersModal();
  };

  const handleApply = () => {
    // Apply all filters
    kanbanActions.setSearch(localFilters.search);
    kanbanActions.setPriorityFilter([...localFilters.priority]);
    kanbanActions.setStatusFilter([...localFilters.status]);
    kanbanActions.setAssignedToFilter([...localFilters.assignedTo]);
    kanbanActions.setTagsFilter([...localFilters.tags]);
    if (localFilters.showArchived !== snap.filters.showArchived) {
      kanbanActions.toggleShowArchived();
    }
    handleClose();
  };

  const handleClear = () => {
    const clearedFilters = {
      search: '',
      priority: [],
      status: [],
      assignedTo: [],
      tags: [],
      showArchived: false,
    };
    setLocalFilters(clearedFilters);
  };

  const handleReset = () => {
    setLocalFilters({ ...snap.filters });
  };

  const togglePriority = (priority: TaskPriority) => {
    setLocalFilters(prev => ({
      ...prev,
      priority: prev.priority.includes(priority)
        ? prev.priority.filter(p => p !== priority)
        : [...prev.priority, priority]
    }));
  };

  const toggleStatus = (status: TaskStatus) => {
    setLocalFilters(prev => ({
      ...prev,
      status: prev.status.includes(status)
        ? prev.status.filter(s => s !== status)
        : [...prev.status, status]
    }));
  };

  const toggleTag = (tag: string) => {
    setLocalFilters(prev => ({
      ...prev,
      tags: prev.tags.includes(tag)
        ? prev.tags.filter(t => t !== tag)
        : [...prev.tags, tag]
    }));
  };

  const toggleUser = (userId: string) => {
    setLocalFilters(prev => ({
      ...prev,
      assignedTo: prev.assignedTo.includes(userId)
        ? prev.assignedTo.filter(u => u !== userId)
        : [...prev.assignedTo, userId]
    }));
  };

  const activeFiltersCount = Object.values(localFilters).flat().filter(Boolean).length;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className={`${isMobile ? 'max-w-[95vw] max-h-[90vh]' : 'max-w-2xl max-h-[80vh]'} overflow-hidden flex flex-col`}>
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtros Avançados
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFiltersCount}
              </Badge>
            )}
          </DialogTitle>
          <DialogDescription>
            Configure filtros para encontrar tarefas específicas no quadro kanban.
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="flex-1 pr-4">
          <div className="space-y-6">
            {/* Search */}
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <Search className="h-4 w-4" />
                Buscar por título
              </Label>
              <Input
                placeholder="Digite para buscar tarefas..."
                value={localFilters.search}
                onChange={(e) => setLocalFilters(prev => ({ ...prev, search: e.target.value }))}
              />
            </div>

            <Separator />

            {/* Priority Filters */}
            <div className="space-y-3">
              <Label className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Prioridade
              </Label>
              <div className="grid grid-cols-2 gap-2">
                {priorityOptions.map((option) => (
                  <div
                    key={option.value}
                    className={`flex items-center space-x-2 p-2 rounded-md border cursor-pointer transition-colors ${
                      localFilters.priority.includes(option.value)
                        ? 'bg-primary/10 border-primary'
                        : 'hover:bg-muted'
                    }`}
                    onClick={() => togglePriority(option.value)}
                  >
                    <Checkbox
                      checked={localFilters.priority.includes(option.value)}
                      onChange={() => togglePriority(option.value)}
                    />
                    <div className={`w-3 h-3 rounded-full ${option.color}`} />
                    <span className="text-sm">{option.label}</span>
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            {/* Status Filters */}
            <div className="space-y-3">
              <Label>Status</Label>
              <div className="grid grid-cols-2 gap-2">
                {statusOptions.map((option) => (
                  <div
                    key={option.value}
                    className={`flex items-center space-x-2 p-2 rounded-md border cursor-pointer transition-colors ${
                      localFilters.status.includes(option.value)
                        ? 'bg-primary/10 border-primary'
                        : 'hover:bg-muted'
                    }`}
                    onClick={() => toggleStatus(option.value)}
                  >
                    <Checkbox
                      checked={localFilters.status.includes(option.value)}
                      onChange={() => toggleStatus(option.value)}
                    />
                    <div className={`w-3 h-3 rounded-full ${option.color}`} />
                    <span className="text-sm">{option.label}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Tags */}
            {availableTags.length > 0 && (
              <>
                <Separator />
                <div className="space-y-3">
                  <Label className="flex items-center gap-2">
                    <Tag className="h-4 w-4" />
                    Tags
                  </Label>
                  <div className="flex flex-wrap gap-2">
                    {availableTags.map((tag) => (
                      <Badge
                        key={tag}
                        variant={localFilters.tags.includes(tag) ? "default" : "outline"}
                        className="cursor-pointer"
                        onClick={() => toggleTag(tag)}
                      >
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              </>
            )}

            {/* Assigned Users */}
            {assignedUsers.length > 0 && (
              <>
                <Separator />
                <div className="space-y-3">
                  <Label className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Atribuído a
                  </Label>
                  <div className="space-y-2">
                    {assignedUsers.map((user) => (
                      <div
                        key={user.$id}
                        className={`flex items-center space-x-2 p-2 rounded-md border cursor-pointer transition-colors ${
                          localFilters.assignedTo.includes(user.$id)
                            ? 'bg-primary/10 border-primary'
                            : 'hover:bg-muted'
                        }`}
                        onClick={() => toggleUser(user.$id)}
                      >
                        <Checkbox
                          checked={localFilters.assignedTo.includes(user.$id)}
                          onChange={() => toggleUser(user.$id)}
                        />
                        <span className="text-sm">{user.name || user.email}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </>
            )}

            {/* Show Archived */}
            <Separator />
            <div className="space-y-3">
              <div
                className={`flex items-center space-x-2 p-2 rounded-md border cursor-pointer transition-colors ${
                  localFilters.showArchived
                    ? 'bg-primary/10 border-primary'
                    : 'hover:bg-muted'
                }`}
                onClick={() => setLocalFilters(prev => ({ ...prev, showArchived: !prev.showArchived }))}
              >
                <Checkbox
                  checked={localFilters.showArchived}
                  onChange={() => setLocalFilters(prev => ({ ...prev, showArchived: !prev.showArchived }))}
                />
                <Archive className="h-4 w-4" />
                <span className="text-sm">Mostrar tarefas arquivadas</span>
              </div>
            </div>
          </div>
        </ScrollArea>

        {/* Actions */}
        <div className="flex-shrink-0 flex items-center justify-between pt-4 border-t">
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleClear}
              className="flex items-center gap-2"
            >
              <X className="h-4 w-4" />
              Limpar
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleReset}
              className="flex items-center gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              Resetar
            </Button>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleClose}>
              Cancelar
            </Button>
            <Button onClick={handleApply}>
              Aplicar Filtros
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
