# 🔐 Sistema de Autenticação

Este documento explica como funciona o sistema de autenticação completo do template, baseado na API nativa do Appwrite.

## 📋 Visão Geral

O sistema de autenticação é construído sobre:

- **Appwrite Account API** - Autenticação nativa
- **React Context** - Gerenciamento de estado global
- **Route Guards** - Proteção automática de rotas
- **Type Safety** - TypeScript completo
- **Error Handling** - Tratamento robusto de erros

## 🏗️ Arquitetura

### Camadas do Sistema
```
┌─────────────────┐
│   Components    │ ← Formulários de login/registro
├─────────────────┤
│   Auth Context  │ ← Estado global do usuário
├─────────────────┤
│   Auth Functions│ ← Funções do Appwrite
├─────────────────┤
│   Appwrite API  │ ← Backend de autenticação
└─────────────────┘
```

### Fluxo de Autenticação
1. **Usuário** submete formulário
2. **Auth Functions** chamam Appwrite API
3. **Auth Context** atualiza estado global
4. **Route Guards** redirecionam conforme necessário
5. **Components** reagem às mudanças de estado

## 🔧 Funções de Autenticação

### Registro de Usuário
```typescript
interface RegisterData {
  email: string;
  password: string;
  name: string;
}

async function registerUser(data: RegisterData) {
  return await account.create(
    ID.unique(),
    data.email,
    data.password,
    data.name
  );
}
```

### Login
```typescript
interface LoginCredentials {
  email: string;
  password: string;
}

async function loginUser(credentials: LoginCredentials) {
  return await account.createEmailPasswordSession(
    credentials.email,
    credentials.password
  );
}
```

### Logout
```typescript
async function logoutUser() {
  return await account.deleteSession('current');
}
```

### Usuário Atual
```typescript
async function getCurrentUser() {
  return await account.get();
}
```

## 🌐 Context de Autenticação

### Interface do Context
```typescript
interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  updateProfile: (data: { name?: string; phone?: string }) => Promise<void>;
  deleteAccount: () => Promise<void>;
}
```

### Uso do Context
```typescript
import { useAuth } from '@/hooks/use-auth';

function MyComponent() {
  const { user, isAuthenticated, login, logout } = useAuth();

  if (!isAuthenticated) {
    return <LoginForm onSubmit={login} />;
  }

  return <Dashboard user={user} onLogout={logout} />;
}
```

## 🛡️ Proteção de Rotas

### Route Guard Component
```typescript
interface RouteGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
}

function RouteGuard({ children, requireAuth = true, redirectTo = '/login' }) {
  const { isAuthenticated, isLoading } = useAuth();

  // Lógica de redirecionamento automático
  // Salva URL atual para redirect após login
}
```

### Configuração de Rotas (Next.js App Router)
```typescript
// Estrutura de rotas com Next.js App Router
app/
├── (auth)/                 # Grupo de rotas de autenticação
│   ├── login/
│   │   └── page.tsx       # Página de login
│   ├── register/
│   │   └── page.tsx       # Página de registro
│   └── layout.tsx         # Layout para rotas de auth
├── (dashboard)/           # Grupo de rotas protegidas
│   ├── dashboard/
│   │   └── page.tsx       # Dashboard principal
│   ├── clients/
│   │   └── page.tsx       # Página de clientes
│   └── layout.tsx         # Layout para rotas protegidas
├── layout.tsx             # Layout raiz
└── page.tsx               # Página inicial
```

## 📝 Formulários de Autenticação

### Login Form
```typescript
interface LoginFormData {
  email: string;
  password: string;
}

function LoginPage() {
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: ''
  });

  const { login } = useAuth();
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      await login(formData);
      toast.success('Login realizado com sucesso!');
      navigate('/dashboard');
    } catch (error) {
      toast.error('Erro no login. Verifique suas credenciais.');
    } finally {
      setIsLoading(false);
    }
  };
}
```

### Register Form
```typescript
function RegisterPage() {
  const [formData, setFormData] = useState<RegisterData>({
    name: '',
    email: '',
    password: ''
  });

  const { register } = useAuth();

  const validateForm = () => {
    if (formData.name.length < 2) return 'Nome muito curto';
    if (!formData.email.includes('@')) return 'Email inválido';
    if (formData.password.length < 8) return 'Senha deve ter 8+ caracteres';
    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    const validationError = validateForm();
    if (validationError) {
      toast.error(validationError);
      return;
    }

    try {
      await register(formData);
      toast.success('Conta criada com sucesso!');
    } catch (error) {
      toast.error('Erro ao criar conta');
    }
  };
}
```

## 🔄 Recuperação de Senha

### Enviar Email de Recuperação
```typescript
async function sendPasswordRecovery(email: string) {
  return await account.createRecovery(
    email,
    `${window.location.origin}/reset-password`
  );
}
```

### Completar Recuperação
```typescript
async function completePasswordRecovery(
  userId: string,
  secret: string,
  password: string
) {
  return await account.updateRecovery(userId, secret, password);
}
```

### Fluxo Completo
1. Usuário solicita recuperação com email
2. Appwrite envia email com link
3. Usuário clica no link (com userId e secret)
4. Página de reset valida parâmetros
5. Usuário define nova senha
6. Sistema completa a recuperação

## ✉️ Verificação de Email

### Enviar Verificação
```typescript
async function sendEmailVerification() {
  return await account.createVerification(
    `${window.location.origin}/verify-email`
  );
}
```

### Completar Verificação
```typescript
async function completeEmailVerification(userId: string, secret: string) {
  return await account.updateVerification(userId, secret);
}
```

## 🌐 OAuth (Social Login)

### Provedores Suportados
- Google
- Facebook
- GitHub
- Discord
- Apple
- Microsoft

### Implementação
```typescript
function createOAuthSession(
  provider: string,
  success?: string,
  failure?: string
) {
  account.createOAuth2Session(
    provider,
    success || `${window.location.origin}/`,
    failure || `${window.location.origin}/login`
  );
}

// Uso
function SocialLoginButtons() {
  return (
    <div className="grid grid-cols-2 gap-4">
      <Button
        variant="outline"
        onClick={() => createOAuthSession('google')}
      >
        <GoogleIcon /> Google
      </Button>
      <Button
        variant="outline"
        onClick={() => createOAuthSession('github')}
      >
        <GitHubIcon /> GitHub
      </Button>
    </div>
  );
}
```

## 👤 Gerenciamento de Perfil

### Atualizar Nome
```typescript
async function updateUserName(name: string) {
  return await account.updateName(name);
}
```

### Atualizar Email
```typescript
async function updateUserEmail(email: string, password: string) {
  return await account.updateEmail(email, password);
}
```

### Atualizar Senha
```typescript
async function updatePassword(currentPassword: string, newPassword: string) {
  return await account.updatePassword(newPassword, currentPassword);
}
```

### Atualizar Telefone
```typescript
async function updateUserPhone(phone: string, password: string) {
  return await account.updatePhone(phone, password);
}
```

## 🔒 Preferências do Usuário

### Salvar Preferências
```typescript
async function updateUserPreferences(prefs: Record<string, any>) {
  return await account.updatePrefs(prefs);
}
```

### Obter Preferências
```typescript
async function getUserPreferences() {
  return await account.getPrefs();
}
```

### Exemplo de Uso
```typescript
// Salvar tema preferido
await updateUserPreferences({ theme: 'dark', language: 'pt-BR' });

// Obter preferências
const prefs = await getUserPreferences();
console.log(prefs.theme); // 'dark'
```

## 📊 Logs de Atividade

### Log Automático
O sistema registra automaticamente:
- Login/logout
- Registro de usuário
- Alterações de perfil
- Tentativas de login falhadas
- Verificações de email

### Implementação
```typescript
import { logActivity } from '@/lib/appwrite/functions/activities';

// Após login bem-sucedido
await logActivity({
  type: 'auth',
  action: 'login',
  description: 'User logged in successfully',
  metadata: { email: user.email }
});
```

## 🛠️ Hooks Customizados

### useAuth Hook
```typescript
export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
}
```

### useRequireAuth Hook
```typescript
export function useRequireAuth() {
  const { isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate('/login');
    }
  }, [isAuthenticated, isLoading, navigate]);

  return { isAuthenticated, isLoading };
}
```

## ⚠️ Tratamento de Erros

### Tipos de Erro Comuns
```typescript
// Email já existe
if (error.code === 409) {
  toast.error('Este email já está em uso');
}

// Credenciais inválidas
if (error.code === 401) {
  toast.error('Email ou senha incorretos');
}

// Rate limit
if (error.code === 429) {
  toast.error('Muitas tentativas. Tente novamente em alguns minutos');
}
```

### Error Boundary
```typescript
function AuthErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary
      fallback={<AuthErrorFallback />}
      onError={(error) => {
        console.error('Auth error:', error);
        // Log para sistema de monitoramento
      }}
    >
      {children}
    </ErrorBoundary>
  );
}
```

## ✅ Boas Práticas

### 1. **Segurança**
- Sempre valide dados no frontend E backend
- Use HTTPS em produção
- Implemente rate limiting
- Monitore tentativas de login suspeitas

### 2. **UX/UI**
- Mostre loading states durante operações
- Forneça feedback claro sobre erros
- Implemente "Lembrar-me" com cuidado
- Redirecione automaticamente após login

### 3. **Performance**
- Cache dados do usuário no context
- Evite chamadas desnecessárias à API
- Use React.memo para componentes pesados
- Implemente lazy loading para rotas

### 4. **Acessibilidade**
- Use labels apropriados em formulários
- Implemente navegação por teclado
- Forneça feedback para screen readers
- Mantenha contraste adequado

---

**📖 Próximos Passos:**
- [types.md](./types.md) - Tipos relacionados à autenticação
- [hooks.md](./hooks.md) - Hooks para autenticação
- [components.md](./components.md) - Componentes de auth
