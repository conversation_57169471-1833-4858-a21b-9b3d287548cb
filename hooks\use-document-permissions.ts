/**
 * Hook para Permissões de Documentos
 * Facilita o uso do sistema de permissões para documentos
 */

import { useMemo } from 'react';
import { useAuth } from './use-auth';
import { useTeamContext } from '../contexts/team-context';
import { 
  createDocumentPermissions,
  getDocumentTeamId,
  canAccessDocument,
  canEditDocument,
  canDeleteDocument,
  type DocumentPermissionOptions
} from '../lib/document-permissions';

// ============================================================================
// TIPOS
// ============================================================================

export interface UseDocumentPermissionsOptions {
  isPublic?: boolean;
  teamId?: string | null;
}

export interface DocumentPermissionsResult {
  // Permissões para criação de documentos
  permissions: string[];
  teamId: string;
  
  // Funções de verificação
  canAccess: (documentPermissions: string[], action?: 'read' | 'write' | 'delete') => boolean;
  canEdit: (documentPermissions: string[]) => boolean;
  canDelete: (documentPermissions: string[]) => boolean;
  
  // Informações do contexto
  userId: string;
  currentTeamId: string | null;
  hasTeamAccess: boolean;
}

// ============================================================================
// HOOK PRINCIPAL
// ============================================================================

/**
 * Hook para gerenciar permissões de documentos
 */
export function useDocumentPermissions(options: UseDocumentPermissionsOptions = {}): DocumentPermissionsResult {
  const { user } = useAuth();
  const { currentTeamId, teams, permissionContext } = useTeamContext();
  
  const { isPublic = false, teamId: customTeamId } = options;
  
  // Determinar o team ID a ser usado
  const documentTeamId = useMemo(() => {
    if (!user) return '';
    
    // Se foi fornecido um teamId customizado, usar ele
    if (customTeamId) {
      return customTeamId;
    }
    
    // Caso contrário, usar a lógica padrão
    return getDocumentTeamId(user.$id, teams);
  }, [user, customTeamId, teams]);
  
  // Gerar permissões para novos documentos
  const permissions = useMemo(() => {
    if (!user) return [];
    
    return createDocumentPermissions(
      user.$id,
      documentTeamId,
      permissionContext,
      isPublic
    );
  }, [user, documentTeamId, permissionContext, isPublic]);
  
  // Funções de verificação
  const canAccess = useMemo(() => {
    return (documentPermissions: string[], action: 'read' | 'write' | 'delete' = 'read') => {
      if (!user) return false;
      
      return canAccessDocument(
        documentPermissions,
        user.$id,
        currentTeamId,
        action
      );
    };
  }, [user, currentTeamId]);
  
  const canEdit = useMemo(() => {
    return (documentPermissions: string[]) => {
      if (!user) return false;
      
      return canEditDocument(
        documentPermissions,
        user.$id,
        currentTeamId
      );
    };
  }, [user, currentTeamId]);
  
  const canDelete = useMemo(() => {
    return (documentPermissions: string[]) => {
      if (!user) return false;
      
      return canDeleteDocument(
        documentPermissions,
        user.$id,
        currentTeamId
      );
    };
  }, [user, currentTeamId]);
  
  // Verificar se tem acesso ao team
  const hasTeamAccess = useMemo(() => {
    if (!permissionContext) return false;
    
    return permissionContext.accessibleResources.includes('documents');
  }, [permissionContext]);
  
  return {
    permissions,
    teamId: documentTeamId,
    canAccess,
    canEdit,
    canDelete,
    userId: user?.$id || '',
    currentTeamId,
    hasTeamAccess,
  };
}

// ============================================================================
// HOOKS ESPECÍFICOS
// ============================================================================

/**
 * Hook para permissões de documentos públicos
 */
export function usePublicDocumentPermissions() {
  return useDocumentPermissions({ isPublic: true });
}

/**
 * Hook para permissões de documentos privados
 */
export function usePrivateDocumentPermissions(teamId?: string | null) {
  return useDocumentPermissions({ isPublic: false, teamId });
}

/**
 * Hook para verificar permissões de um documento específico
 */
export function useDocumentAccess(documentPermissions: string[]) {
  const { canAccess, canEdit, canDelete } = useDocumentPermissions();
  
  return useMemo(() => ({
    canRead: canAccess(documentPermissions, 'read'),
    canWrite: canEdit(documentPermissions),
    canDelete: canDelete(documentPermissions),
  }), [canAccess, canEdit, canDelete, documentPermissions]);
}

/**
 * Hook para obter informações do team para documentos
 */
export function useDocumentTeam() {
  const { user } = useAuth();
  const { currentTeamId, currentTeam, teams } = useTeamContext();
  
  const documentTeamId = useMemo(() => {
    if (!user) return null;
    return getDocumentTeamId(user.$id, teams);
  }, [user, teams]);
  
  const documentTeam = useMemo(() => {
    if (!documentTeamId) return null;
    return teams?.find(team => team.$id === documentTeamId) || null;
  }, [documentTeamId, teams]);
  
  return {
    teamId: documentTeamId,
    team: documentTeam,
    currentTeamId,
    currentTeam,
    isUsingCurrentTeam: documentTeamId === currentTeamId,
  };
}
