/**
 * Hook para processamento de arquivos com Gemini AI
 *
 * Permite upload e processamento de arquivos de clientes usando Google Gemini AI
 * para extrair dados estruturados de documentos, imagens, etc.
 */

import { useState } from 'react';
import { useCloudFunction } from './use-cloud-function';
import { storage, STORAGE_BUCKET_ID } from '../lib/appwrite/config';
import { ID } from 'appwrite';
import { toast } from 'sonner';

export interface GeminiProcessingOptions {
  processingType?: 'general' | 'client_data' | 'invoice_data' | 'identity_document' | 'address_proof' | 'contract' | 'receipt' | 'bank_statement';
  customPrompt?: string;
  autoDetectType?: boolean;
}

export interface ProcessedFileData {
  file_id: string;
  processing_type: string;
  processed_data: any;
  file_info: {
    name: string;
    size: number;
    mimeType: string;
  };
  processed_at: string;
}

export interface GeminiProcessorState {
  isUploading: boolean;
  isProcessing: boolean;
  uploadProgress: number;
  processedData: ProcessedFileData | null;
  error: string | null;
}

/**
 * Hook principal para processamento com Gemini
 */
export function useGeminiProcessor() {
  const [state, setState] = useState<GeminiProcessorState>({
    isUploading: false,
    isProcessing: false,
    uploadProgress: 0,
    processedData: null,
    error: null,
  });

  const geminiFunction = useCloudFunction('GEMINI_FILE_PROCESSOR');

  /**
   * Processa um arquivo com Gemini AI
   */
  const processFile = async (
    file: File,
    options: GeminiProcessingOptions = {}
  ): Promise<ProcessedFileData | null> => {
    try {
      setState(prev => ({
        ...prev,
        isUploading: true,
        isProcessing: false,
        uploadProgress: 0,
        error: null,
        processedData: null,
      }));

      // 1. Upload do arquivo para o Appwrite Storage
      const uploadedFile = await storage.createFile(
        STORAGE_BUCKET_ID,
        ID.unique(),
        file
      );

      setState(prev => ({
        ...prev,
        isUploading: false,
        isProcessing: true,
        uploadProgress: 100,
      }));

      // 2. Processar com Gemini
      const result = await geminiFunction.execute({
        data: {
          fileId: uploadedFile.$id,
          bucketId: STORAGE_BUCKET_ID,
          processingType: options.processingType || 'general',
          customPrompt: options.customPrompt,
        },
      });

      if (result.success && result.data) {
        const processedData = result.data as ProcessedFileData;

        setState(prev => ({
          ...prev,
          isProcessing: false,
          processedData,
        }));

        toast.success('Arquivo processado com sucesso!');
        return processedData;
      } else {
        throw new Error(result.error || 'Falha no processamento');
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';

      setState(prev => ({
        ...prev,
        isUploading: false,
        isProcessing: false,
        error: errorMessage,
      }));

      toast.error(`Erro ao processar arquivo: ${errorMessage}`);
      return null;
    }
  };

  /**
   * Processa múltiplos arquivos em lote
   */
  const processMultipleFiles = async (
    files: File[],
    options: GeminiProcessingOptions = {}
  ): Promise<ProcessedFileData[]> => {
    const results: ProcessedFileData[] = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];

      setState(prev => ({
        ...prev,
        uploadProgress: (i / files.length) * 50, // 50% para upload
      }));

      const result = await processFile(file, options);
      if (result) {
        results.push(result);
      }

      setState(prev => ({
        ...prev,
        uploadProgress: ((i + 1) / files.length) * 100,
      }));
    }

    return results;
  };

  /**
   * Limpa o estado
   */
  const reset = () => {
    setState({
      isUploading: false,
      isProcessing: false,
      uploadProgress: 0,
      processedData: null,
      error: null,
    });
  };

  /**
   * Verifica se um tipo de arquivo é suportado
   */
  const isSupportedFileType = (file: File): boolean => {
    const supportedTypes = [
      'image/jpeg',
      'image/png',
      'image/webp',
      'application/pdf'
    ];

    return supportedTypes.includes(file.type);
  };

  /**
   * Valida tamanho do arquivo
   */
  const isValidFileSize = (file: File): boolean => {
    const maxSize = 10 * 1024 * 1024; // 10MB
    return file.size <= maxSize;
  };

  /**
   * Valida arquivo antes do processamento
   */
  const validateFile = (file: File): { valid: boolean; error?: string } => {
    if (!isSupportedFileType(file)) {
      return {
        valid: false,
        error: 'Tipo de arquivo não suportado. Use JPEG, PNG, WebP ou PDF.',
      };
    }

    if (!isValidFileSize(file)) {
      return {
        valid: false,
        error: 'Arquivo muito grande. Tamanho máximo: 10MB.',
      };
    }

    return { valid: true };
  };

  return {
    // Estado
    ...state,
    isLoading: state.isUploading || state.isProcessing,

    // Ações
    processFile,
    processMultipleFiles,
    reset,

    // Validações
    isSupportedFileType,
    isValidFileSize,
    validateFile,

    // Estado da cloud function
    isFunctionConfigured: geminiFunction.isConfigured,
  };
}

/**
 * Hook simplificado para processamento rápido
 */
export function useQuickGeminiProcessor() {
  const processor = useGeminiProcessor();

  const quickProcess = async (
    file: File,
    type: GeminiProcessingOptions['processingType'] = 'general'
  ) => {
    const validation = processor.validateFile(file);
    if (!validation.valid) {
      toast.error(validation.error);
      return null;
    }

    return processor.processFile(file, { processingType: type });
  };

  return {
    quickProcess,
    isLoading: processor.isLoading,
    processedData: processor.processedData,
    error: processor.error,
    reset: processor.reset,
  };
}

/**
 * Tipos de processamento disponíveis
 */
export const PROCESSING_TYPES = {
  GENERAL: 'general',
  CLIENT_DATA: 'client_data',
  INVOICE_DATA: 'invoice_data',
  IDENTITY_DOCUMENT: 'identity_document',
  ADDRESS_PROOF: 'address_proof',
  CONTRACT: 'contract',
  RECEIPT: 'receipt',
  BANK_STATEMENT: 'bank_statement',
} as const;

/**
 * Descrições dos tipos de processamento
 */
export const PROCESSING_TYPE_DESCRIPTIONS = {
  [PROCESSING_TYPES.GENERAL]: 'Análise geral do documento',
  [PROCESSING_TYPES.CLIENT_DATA]: 'Extração de dados do cliente',
  [PROCESSING_TYPES.INVOICE_DATA]: 'Processamento de faturas/notas fiscais',
  [PROCESSING_TYPES.IDENTITY_DOCUMENT]: 'Documentos de identidade (RG, CPF, CNH)',
  [PROCESSING_TYPES.ADDRESS_PROOF]: 'Comprovantes de endereço',
  [PROCESSING_TYPES.CONTRACT]: 'Contratos e acordos',
  [PROCESSING_TYPES.RECEIPT]: 'Recibos e comprovantes',
  [PROCESSING_TYPES.BANK_STATEMENT]: 'Extratos bancários',
} as const;
