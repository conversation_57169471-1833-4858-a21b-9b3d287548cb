import { IconTrendingDown, IconTrendingUp, IconUsers, IconUser<PERSON>heck, IconUserX, IconUserPlus } from "@tabler/icons-react";
import { Badge } from "../ui/badge";
import {
  Card,
  CardAction,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { useClients } from "../../hooks/use-api";
import { useAuth } from "../../hooks/use-auth";
import type { ClientFilters } from '@/schemas/clients';

interface ClientStatsProps {
  filters?: Partial<ClientFilters>;
  className?: string;
}

export function ClientStats({ filters = {}, className }: ClientStatsProps) {
  const { user } = useAuth();
  const { data: allClients = [], isLoading, error } = useClients();

  // Aplicar filtros aos clientes
  const filteredClients = allClients.filter(client => {
    // Sempre filtrar por usuário
    if (client.userId !== user?.$id) return false;

    // Aplicar filtros adicionais se fornecidos
    if (filters.status && filters.status.length > 0 && !filters.status.includes(client.status)) return false;
    if (filters.type && filters.type.length > 0 && !filters.type.includes(client.type)) return false;
    if (filters.priority && filters.priority.length > 0 && !filters.priority.includes(client.priority)) return false;
    if (filters.search && !client.name.toLowerCase().includes(filters.search.toLowerCase())) return false;
    if (filters.teamId && client.teamId !== filters.teamId) return false;

    return true;
  });

  // Calcular datas para comparação de crescimento
  const now = new Date();
  const currentMonth = now.getMonth();
  const currentYear = now.getFullYear();
  const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1;
  const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear;

  // Separar clientes por período
  const clientsThisMonth = filteredClients.filter(c => {
    const createdAt = new Date(c.$createdAt);
    return createdAt.getMonth() === currentMonth && createdAt.getFullYear() === currentYear;
  });

  const clientsLastMonth = filteredClients.filter(c => {
    const createdAt = new Date(c.$createdAt);
    return createdAt.getMonth() === lastMonth && createdAt.getFullYear() === lastMonthYear;
  });

  // Calcular estatísticas dos clientes
  const stats = {
    total: filteredClients.length,
    active: filteredClients.filter(c => c.status === 'ativo').length,
    inactive: filteredClients.filter(c => c.status === 'inativo').length,
    prospects: filteredClients.filter(c => c.status === 'prospecto').length,
    archived: filteredClients.filter(c => c.status === 'arquivado').length,
    newThisMonth: clientsThisMonth.length,
    newLastMonth: clientsLastMonth.length,
    // Métricas simplificadas baseadas apenas em dados básicos
    totalRevenue: 0, // Removido - não temos mais este campo
    averageProjectValue: 0, // Removido - não temos mais este campo
    totalProjects: 0, // Removido - não temos mais este campo
  };

  // Determine padding classes based on className prop
  const paddingClasses = className?.includes('!px-0') ? '' : 'px-4 lg:px-6';
  const cleanClassName = className?.replace('!px-0', '').trim() || '';

  if (isLoading) {
    return (
      <div className={`grid grid-cols-1 gap-4 ${paddingClasses} md:grid-cols-2 lg:grid-cols-4 ${cleanClassName}`}>
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-4 bg-muted rounded w-24"></div>
              <div className="h-8 bg-muted rounded w-16"></div>
            </CardHeader>
          </Card>
        ))}
      </div>
    );
  }

  if (error || !stats) {
    return (
      <div className={`${paddingClasses} ${cleanClassName}`}>
        <Card>
          <CardHeader>
            <CardTitle className="text-destructive">Erro ao carregar estatísticas</CardTitle>
            <CardDescription>
              Não foi possível carregar as estatísticas dos clientes. Tente novamente.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  // Calcular métricas de crescimento baseadas em dados reais
  const totalGrowth = stats.newLastMonth > 0
    ? ((stats.newThisMonth - stats.newLastMonth) / stats.newLastMonth) * 100
    : stats.newThisMonth > 0 ? 100 : 0;

  const activePercentage = stats.total > 0 ? ((stats.active / stats.total) * 100) : 0;

  // Crescimento de receita removido - campo não existe mais
  const revenueGrowth = 0;

  // Taxa de conversão: prospects que se tornaram ativos
  const conversionRate = (stats.prospects + stats.active) > 0
    ? (stats.active / (stats.prospects + stats.active)) * 100
    : 0;

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('pt-BR').format(value);
  };

  return (
    <div className={`grid grid-cols-1 gap-4 ${paddingClasses} md:grid-cols-2 lg:grid-cols-4 ${cleanClassName}`}>
      {/* Total Clients */}
      <Card>
        <CardHeader>
          <CardDescription>Total de Clientes</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums">
            {formatNumber(stats.total)}
          </CardTitle>
          <CardAction>
            <Badge variant="outline">
              {totalGrowth > 0 ? <IconTrendingUp /> : <IconTrendingDown />}
              {totalGrowth > 0 ? '+' : ''}{totalGrowth.toFixed(1)}%
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            {stats.newThisMonth} novos este mês <IconUserPlus className="size-4" />
          </div>
          <div className="text-muted-foreground">
            {stats.newThisMonth > 0 ? 'Crescimento positivo' : 'Sem novos clientes'}
          </div>
        </CardFooter>
      </Card>

      {/* Active Clients */}
      <Card>
        <CardHeader>
          <CardDescription>Clientes Ativos</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums">
            {formatNumber(stats.active)}
          </CardTitle>
          <CardAction>
            <Badge variant="outline">
              <IconUserCheck />
              {activePercentage.toFixed(1)}%
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            {stats.inactive} inativos <IconUserX className="size-4" />
          </div>
          <div className="text-muted-foreground">
            Taxa de atividade: {activePercentage.toFixed(1)}%
          </div>
        </CardFooter>
      </Card>

      {/* Client Types */}
      <Card>
        <CardHeader>
          <CardDescription>Tipos de Cliente</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums">
            {formatNumber(filteredClients.filter(c => c.type === 'pessoa_juridica').length)}
          </CardTitle>
          <CardAction>
            <Badge variant="outline">
              <IconUsers />
              PJ
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            {formatNumber(filteredClients.filter(c => c.type === 'pessoa_fisica').length)} Pessoa Física <IconUsers className="size-4" />
          </div>
          <div className="text-muted-foreground">
            Distribuição por tipo de cliente
          </div>
        </CardFooter>
      </Card>

      {/* Prospects */}
      <Card>
        <CardHeader>
          <CardDescription>Prospects</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums">
            {formatNumber(stats.prospects)}
          </CardTitle>
          <CardAction>
            <Badge variant="outline">
              <IconUsers />
              {conversionRate.toFixed(1)}%
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            Taxa de conversão <IconTrendingUp className="size-4" />
          </div>
          <div className="text-muted-foreground">
            {stats.archived} arquivados • {stats.inactive} inativos
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
