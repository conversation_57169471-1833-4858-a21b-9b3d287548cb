import { QueryClient } from '@tanstack/react-query';

/**
 * React Query client configuration
 * Provides caching, background updates, and error handling
 */

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Stale time: how long data is considered fresh
      staleTime: 5 * 60 * 1000, // 5 minutes

      // Cache time: how long data stays in cache after becoming unused
      gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)

      // Retry failed requests
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx errors (client errors)
        if (error?.status >= 400 && error?.status < 500) {
          return false;
        }
        // Retry up to 3 times for other errors
        return failureCount < 3;
      },

      // Retry delay with exponential backoff
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),

      // Refetch on window focus
      refetchOnWindowFocus: false,

      // Refetch on reconnect
      refetchOnReconnect: true,

      // Refetch on mount if data is stale
      refetchOnMount: true,
    },
    mutations: {
      // Retry failed mutations
      retry: 1,

      // Retry delay for mutations
      retryDelay: 1000,
    },
  },
});

/**
 * Query key factories for consistent cache management
 */
export const queryKeys = {
  // Auth
  auth: {
    all: ['auth'] as const,
    user: () => [...queryKeys.auth.all, 'user'] as const,
    session: () => [...queryKeys.auth.all, 'session'] as const,
    preferences: () => [...queryKeys.auth.all, 'preferences'] as const,
  },

  // Database
  database: {
    all: ['database'] as const,
    collections: () => [...queryKeys.database.all, 'collections'] as const,
    collection: (collectionId: string) => [...queryKeys.database.collections(), collectionId] as const,
    documents: (collectionId: string) => [...queryKeys.database.collection(collectionId), 'documents'] as const,
    document: (collectionId: string, documentId: string) => [...queryKeys.database.documents(collectionId), documentId] as const,
  },

  // Storage
  storage: {
    all: ['storage'] as const,
    buckets: () => [...queryKeys.storage.all, 'buckets'] as const,
    bucket: (bucketId: string) => [...queryKeys.storage.buckets(), bucketId] as const,
    files: (bucketId: string) => [...queryKeys.storage.bucket(bucketId), 'files'] as const,
    file: (bucketId: string, fileId: string) => [...queryKeys.storage.files(bucketId), fileId] as const,
  },

  // App-specific
  clients: {
    all: ['clients'] as const,
    lists: () => [...queryKeys.clients.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.clients.lists(), filters] as const,
    details: () => [...queryKeys.clients.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.clients.details(), id] as const,
    search: (query: string) => [...queryKeys.clients.all, 'search', query] as const,
    stats: () => [...queryKeys.clients.all, 'stats'] as const,
  },

  users: {
    all: ['users'] as const,
    lists: () => [...queryKeys.users.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.users.lists(), filters] as const,
    details: () => [...queryKeys.users.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.users.details(), id] as const,
    profile: (id: string) => [...queryKeys.users.all, 'profile', id] as const,
  },
} as const;

/**
 * Utility functions for cache management
 */
export const cacheUtils = {
  /**
   * Invalidate all queries for a specific entity
   */
  invalidateEntity: (entity: keyof typeof queryKeys) => {
    queryClient.invalidateQueries({ queryKey: queryKeys[entity].all });
  },

  /**
   * Clear all cache
   */
  clearAll: () => {
    queryClient.clear();
  },

  /**
   * Remove specific query from cache
   */
  removeQuery: (queryKey: readonly unknown[]) => {
    queryClient.removeQueries({ queryKey });
  },

  /**
   * Set query data manually
   */
  setQueryData: <T>(queryKey: readonly unknown[], data: T) => {
    queryClient.setQueryData(queryKey, data);
  },

  /**
   * Get query data from cache
   */
  getQueryData: <T>(queryKey: readonly unknown[]): T | undefined => {
    return queryClient.getQueryData<T>(queryKey);
  },

  /**
   * Prefetch query
   */
  prefetchQuery: async (queryKey: readonly unknown[], queryFn: () => Promise<any>) => {
    await queryClient.prefetchQuery({ queryKey, queryFn });
  },
};
