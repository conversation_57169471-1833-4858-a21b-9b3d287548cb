/**
 * Calendar Modals Store
 * Gerencia estado dos modais do sistema de calendário usando Valtio
 */

import { proxy } from 'valtio';
import type { Event, EventCategory } from '@/schemas/events';

// ============================================================================
// INTERFACES
// ============================================================================

export interface CalendarModalsState {
  // Modal de criação de evento
  createEvent: {
    isOpen: boolean;
    defaultDate?: Date;
    defaultStartTime?: string;
    defaultEndTime?: string;
    defaultAllDay?: boolean;
  };

  // Modal de edição de evento
  editEvent: {
    isOpen: boolean;
    event: Event | null;
  };

  // Modal de visualização de evento
  viewEvent: {
    isOpen: boolean;
    event: Event | null;
  };

  // Modal de exclusão de evento
  deleteEvent: {
    isOpen: boolean;
    event: Event | null;
  };

  // Modal de criação de categoria
  createCategory: {
    isOpen: boolean;
  };

  // Modal de edição de categoria
  editCategory: {
    isOpen: boolean;
    category: EventCategory | null;
  };

  // Modal de exclusão de categoria
  deleteCategory: {
    isOpen: boolean;
    category: EventCategory | null;
  };

  // Modal de configurações do calendário
  settings: {
    isOpen: boolean;
  };

  // Modal de exportação
  export: {
    isOpen: boolean;
  };

  // Modal de importação
  import: {
    isOpen: boolean;
  };

  // Modal de filtros avançados
  filters: {
    isOpen: boolean;
  };

  // Modal de busca
  search: {
    isOpen: boolean;
  };
}

// ============================================================================
// STORE
// ============================================================================

/**
 * Store reativo para modais do calendário
 * Uso: calendarModalsStore.createEvent.isOpen = true
 */
export const calendarModalsStore = proxy<CalendarModalsState>({
  createEvent: {
    isOpen: false,
    defaultDate: undefined,
    defaultStartTime: undefined,
    defaultEndTime: undefined,
    defaultAllDay: false,
  },
  editEvent: {
    isOpen: false,
    event: null,
  },
  viewEvent: {
    isOpen: false,
    event: null,
  },
  deleteEvent: {
    isOpen: false,
    event: null,
  },
  createCategory: {
    isOpen: false,
  },
  editCategory: {
    isOpen: false,
    category: null,
  },
  deleteCategory: {
    isOpen: false,
    category: null,
  },
  settings: {
    isOpen: false,
  },
  export: {
    isOpen: false,
  },
  import: {
    isOpen: false,
  },
  filters: {
    isOpen: false,
  },
  search: {
    isOpen: false,
  },
});

// ============================================================================
// ACTIONS
// ============================================================================

/**
 * Actions para manipular os modais de forma mais semântica
 */
export const calendarModalsActions = {
  // Event modals
  openCreateEvent: (options?: {
    date?: Date;
    startTime?: string;
    endTime?: string;
    allDay?: boolean;
  }) => {
    calendarModalsStore.createEvent.isOpen = true;
    calendarModalsStore.createEvent.defaultDate = options?.date;
    calendarModalsStore.createEvent.defaultStartTime = options?.startTime;
    calendarModalsStore.createEvent.defaultEndTime = options?.endTime;
    calendarModalsStore.createEvent.defaultAllDay = options?.allDay || false;
  },

  closeCreateEvent: () => {
    calendarModalsStore.createEvent.isOpen = false;
    calendarModalsStore.createEvent.defaultDate = undefined;
    calendarModalsStore.createEvent.defaultStartTime = undefined;
    calendarModalsStore.createEvent.defaultEndTime = undefined;
    calendarModalsStore.createEvent.defaultAllDay = false;
  },

  openEditEvent: (event: Event) => {
    calendarModalsStore.editEvent.isOpen = true;
    calendarModalsStore.editEvent.event = event;
  },

  closeEditEvent: () => {
    calendarModalsStore.editEvent.isOpen = false;
    calendarModalsStore.editEvent.event = null;
  },

  openViewEvent: (event: Event) => {
    calendarModalsStore.viewEvent.isOpen = true;
    calendarModalsStore.viewEvent.event = event;
  },

  closeViewEvent: () => {
    calendarModalsStore.viewEvent.isOpen = false;
    calendarModalsStore.viewEvent.event = null;
  },

  openDeleteEvent: (event: Event) => {
    calendarModalsStore.deleteEvent.isOpen = true;
    calendarModalsStore.deleteEvent.event = event;
  },

  closeDeleteEvent: () => {
    calendarModalsStore.deleteEvent.isOpen = false;
    calendarModalsStore.deleteEvent.event = null;
  },

  // Category modals
  openCreateCategory: () => {
    calendarModalsStore.createCategory.isOpen = true;
  },

  closeCreateCategory: () => {
    calendarModalsStore.createCategory.isOpen = false;
  },

  openEditCategory: (category: EventCategory) => {
    calendarModalsStore.editCategory.isOpen = true;
    calendarModalsStore.editCategory.category = category;
  },

  closeEditCategory: () => {
    calendarModalsStore.editCategory.isOpen = false;
    calendarModalsStore.editCategory.category = null;
  },

  openDeleteCategory: (category: EventCategory) => {
    calendarModalsStore.deleteCategory.isOpen = true;
    calendarModalsStore.deleteCategory.category = category;
  },

  closeDeleteCategory: () => {
    calendarModalsStore.deleteCategory.isOpen = false;
    calendarModalsStore.deleteCategory.category = null;
  },

  // Utility modals
  openSettings: () => {
    calendarModalsStore.settings.isOpen = true;
  },

  closeSettings: () => {
    calendarModalsStore.settings.isOpen = false;
  },

  openExport: () => {
    calendarModalsStore.export.isOpen = true;
  },

  closeExport: () => {
    calendarModalsStore.export.isOpen = false;
  },

  openImport: () => {
    calendarModalsStore.import.isOpen = true;
  },

  closeImport: () => {
    calendarModalsStore.import.isOpen = false;
  },

  openFilters: () => {
    calendarModalsStore.filters.isOpen = true;
  },

  closeFilters: () => {
    calendarModalsStore.filters.isOpen = false;
  },

  openSearch: () => {
    calendarModalsStore.search.isOpen = true;
  },

  closeSearch: () => {
    calendarModalsStore.search.isOpen = false;
  },

  // Close all modals
  closeAll: () => {
    calendarModalsStore.createEvent.isOpen = false;
    calendarModalsStore.editEvent.isOpen = false;
    calendarModalsStore.viewEvent.isOpen = false;
    calendarModalsStore.deleteEvent.isOpen = false;
    calendarModalsStore.createCategory.isOpen = false;
    calendarModalsStore.editCategory.isOpen = false;
    calendarModalsStore.deleteCategory.isOpen = false;
    calendarModalsStore.settings.isOpen = false;
    calendarModalsStore.export.isOpen = false;
    calendarModalsStore.import.isOpen = false;
    calendarModalsStore.filters.isOpen = false;
    calendarModalsStore.search.isOpen = false;

    // Reset data
    calendarModalsStore.createEvent.defaultDate = undefined;
    calendarModalsStore.createEvent.defaultStartTime = undefined;
    calendarModalsStore.createEvent.defaultEndTime = undefined;
    calendarModalsStore.createEvent.defaultAllDay = false;
    calendarModalsStore.editEvent.event = null;
    calendarModalsStore.viewEvent.event = null;
    calendarModalsStore.deleteEvent.event = null;
    calendarModalsStore.editCategory.category = null;
    calendarModalsStore.deleteCategory.category = null;
  },
};

// ============================================================================
// HOOKS
// ============================================================================

/**
 * Hook para usar o store de modais do calendário
 * Retorna o store e as actions
 */
export function useCalendarModals() {
  return {
    store: calendarModalsStore,
    actions: calendarModalsActions,
  };
}
