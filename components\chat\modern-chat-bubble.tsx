
'use client';

import * as React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "../../lib/utils";
import { Avatar, AvatarImage, AvatarFallback } from "../ui/avatar";
import { Badge } from "../ui/badge";
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Bot, User, MoreVertical, Edit2, Trash2, Check, X, Reply } from "lucide-react";
import { Button } from "../ui/button";
import { Textarea } from "../ui/textarea";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "../ui/alert-dialog";
import { MessageReactions } from './message-reactions';
import { ReplyIndicator } from './reply-preview';
import type { ChatMessage } from '@/schemas/chat';

interface ModernChatBubbleProps {
  message: string;
  isUser: boolean;
  timestamp?: Date;
  isLoading?: boolean;
  avatar?: string;
  userName?: string;
  status?: 'sending' | 'sent' | 'error';
  metadata?: {
    model?: string;
    [key: string]: any;
  };
  className?: string;
  messageId?: string;
  chatId?: string;
  edited?: boolean;
  editedAt?: Date;
  reactions?: string[];
  replyTo?: ChatMessage;
  mentions?: string[];
  onEdit?: (messageId: string, content: string) => void;
  onDelete?: (messageId: string) => void;
  onReply?: (message: ChatMessage) => void;
  onReactionClick?: (messageId: string, emoji: string) => void;
}

export function ModernChatBubble({
  message,
  isUser,
  timestamp,
  isLoading = false,
  avatar,
  userName,
  status = 'sent',
  metadata,
  className,
  messageId,
  chatId,
  edited = false,
  editedAt,
  reactions = [],
  replyTo,
  mentions = [],
  onEdit,
  onDelete,
  onReply,
  onReactionClick,
}: ModernChatBubbleProps) {
  const [isEditing, setIsEditing] = React.useState(false);
  const [editValue, setEditValue] = React.useState(message);
  const [showDeleteDialog, setShowDeleteDialog] = React.useState(false);
  const textareaRef = React.useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  const adjustTextareaHeight = React.useCallback(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
    }
  }, []);

  React.useEffect(() => {
    if (isEditing) {
      adjustTextareaHeight();
    }
  }, [isEditing, editValue, adjustTextareaHeight]);

  const handleEdit = () => {
    if (!messageId || !onEdit) return;
    setIsEditing(true);
    setEditValue(message);
    // Focus textarea after state update
    setTimeout(() => {
      textareaRef.current?.focus();
    }, 0);
  };

  const handleSaveEdit = () => {
    if (!messageId || !onEdit || !editValue.trim()) return;
    onEdit(messageId, editValue.trim());
    setIsEditing(false);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditValue(message);
  };

  const handleDelete = () => {
    if (!messageId || !onDelete) return;
    onDelete(messageId);
    setShowDeleteDialog(false);
  };

  const handleReply = () => {
    if (!messageId || !onReply) return;

    const messageData: ChatMessage = {
      $id: messageId,
      $createdAt: timestamp?.toISOString() || new Date().toISOString(),
      $updatedAt: timestamp?.toISOString() || new Date().toISOString(),
      $permissions: [],
      $databaseId: '',
      $collectionId: '',
      content: message,
      senderId: isUser ? 'current-user' : 'other-user',
      senderName: userName || (isUser ? 'Você' : 'Usuário'),
      senderAvatar: avatar,
      teamId: '',
      type: 'text',
      status: 'sent',
      userId: isUser ? 'current-user' : 'other-user',
      createdBy: isUser ? 'current-user' : 'other-user',
      isDeleted: false,
      edited,
      editedAt: editedAt?.toISOString(),
      reactions,
      mentions,
    } as ChatMessage;

    onReply(messageData);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      handleCancelEdit();
    }
  };

  const handleEmojiSelect = (emoji: string) => {
    setEditValue(prev => prev + emoji);
    // Focus back to textarea after emoji selection
    setTimeout(() => {
      textareaRef.current?.focus();
    }, 0);
  };

  return (
    <>
      <div
        className={cn(
          "flex gap-3 max-w-[85%] group",
          isUser ? "ml-auto flex-row-reverse" : "mr-auto",
          className
        )}
      >
        {/* Avatar */}
        <div className="flex-shrink-0">
          <Avatar className="w-8 h-8">
            <AvatarImage src={avatar} alt={userName || (isUser ? "Você" : "AI")} />
            <AvatarFallback className={cn(
              "text-xs font-medium",
              isUser
                ? "bg-primary text-primary-foreground"
                : "bg-muted text-muted-foreground"
            )}>
              {isUser ? (
                userName?.charAt(0).toUpperCase() || <User className="w-4 h-4" />
              ) : (
                <Bot className="w-4 h-4" />
              )}
            </AvatarFallback>
          </Avatar>
        </div>

        {/* Message Content */}
        <div className={cn(
          "flex flex-col gap-1",
          isUser ? "items-end" : "items-start"
        )}>
          {/* User Name (only for received messages) */}
          {!isUser && userName && (
            <div className="text-xs font-medium text-muted-foreground px-3">
              {userName}
            </div>
          )}

          {/* Reply Indicator */}
          {replyTo && (
            <ReplyIndicator
              replyTo={replyTo}
              className="mb-2"
            />
          )}

          {/* Message Bubble */}
          <div className="relative">
            <div
              className={cn(
                "relative px-4 py-3 rounded-2xl text-sm leading-relaxed",
                "break-words whitespace-pre-wrap",
                isUser
                  ? "bg-primary text-primary-foreground rounded-br-md"
                  : "bg-muted text-muted-foreground rounded-bl-md",
                "shadow-sm border border-border/20"
              )}
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="flex gap-1">
                    <div className="w-2 h-2 bg-current rounded-full animate-pulse" />
                    <div className="w-2 h-2 bg-current rounded-full animate-pulse delay-75" />
                    <div className="w-2 h-2 bg-current rounded-full animate-pulse delay-150" />
                  </div>
                  <span className="text-xs opacity-70">Digitando...</span>
                </div>
              ) : isEditing ? (
                <div className="space-y-2">
                  <div className="relative">
                    <Textarea
                      ref={textareaRef}
                      value={editValue}
                      onChange={(e) => {
                        setEditValue(e.target.value);
                        adjustTextareaHeight();
                      }}
                      onKeyDown={handleKeyDown}
                      className="text-sm bg-background text-foreground border-border resize-none pr-10 min-h-[40px] max-h-[120px]"
                      placeholder="Digite sua mensagem..."
                      style={{ overflow: 'hidden' }}
                    />
                    {/* Emoji Picker Button */}
                    <div className="absolute right-2 top-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0 hover:bg-muted"
                        onClick={() => {
                          // Simple emoji insertion for now
                          const commonEmojis = ['😊', '👍', '❤️', '😂', '🎉', '👏', '🔥', '💯'];
                          const randomEmoji = commonEmojis[Math.floor(Math.random() * commonEmojis.length)];
                          handleEmojiSelect(randomEmoji);
                        }}
                      >
                        😊
                      </Button>
                    </div>
                  </div>
                  <div className="flex gap-2 justify-end">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={handleCancelEdit}
                      className="h-6 px-2 text-xs"
                    >
                      <X className="w-3 h-3 mr-1" />
                      Cancelar
                    </Button>
                    <Button
                      size="sm"
                      onClick={handleSaveEdit}
                      className="h-6 px-2 text-xs"
                      disabled={!editValue.trim()}
                    >
                      <Check className="w-3 h-3 mr-1" />
                      Salvar
                    </Button>
                  </div>
                </div>
              ) : (
                <>
                  {message}
                  {edited && (
                    <span className="text-xs opacity-60 ml-2">(editada)</span>
                  )}
                </>
              )}
            </div>

            {/* Message Actions */}
            {!isLoading && !isEditing && messageId && (onEdit || onDelete || onReply) && (
              <div className="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="secondary"
                      size="sm"
                      className="h-6 w-6 p-0 rounded-full shadow-md"
                    >
                      <MoreVertical className="w-3 h-3" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-32">
                    {onReply && (
                      <DropdownMenuItem onClick={handleReply}>
                        <Reply className="w-3 h-3 mr-2" />
                        Responder
                      </DropdownMenuItem>
                    )}
                    {onEdit && isUser && (
                      <DropdownMenuItem onClick={handleEdit}>
                        <Edit2 className="w-3 h-3 mr-2" />
                        Editar
                      </DropdownMenuItem>
                    )}
                    {onDelete && isUser && (
                      <DropdownMenuItem
                        onClick={() => setShowDeleteDialog(true)}
                        className="text-destructive focus:text-destructive"
                      >
                        <Trash2 className="w-3 h-3 mr-2" />
                        Excluir
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            )}
          </div>

          {/* Message Reactions */}
          {messageId && chatId && (
            <MessageReactions
              messageId={messageId}
              chatId={chatId}
              reactions={reactions}
              className="mt-1"
            />
          )}

          {/* Timestamp and Status */}
          {(timestamp || status === 'error' || metadata?.model || edited) && (
            <div className={cn(
              "flex items-center gap-2 text-xs text-muted-foreground/70",
              isUser ? "justify-end" : "justify-start"
            )}>
              {timestamp && (
                <span>
                  {formatDistanceToNow(timestamp, {
                    addSuffix: true,
                    locale: ptBR,
                  })}
                </span>
              )}

              {edited && editedAt && (
                <span>
                  • editada {formatDistanceToNow(editedAt, {
                    addSuffix: true,
                    locale: ptBR,
                  })}
                </span>
              )}

              {status === 'error' && (
                <Badge variant="destructive" className="text-xs h-5">
                  Erro
                </Badge>
              )}

              {status === 'sending' && (
                <Badge variant="secondary" className="text-xs h-5">
                  Enviando...
                </Badge>
              )}

              {metadata?.model && !isUser && (
                <Badge variant="outline" className="text-xs h-5">
                  {metadata.model}
                </Badge>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Excluir mensagem</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir esta mensagem? Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Excluir
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}

interface ModernChatListProps {
  messages: Array<{
    id: string;
    content: string;
    isUser: boolean;
    timestamp?: Date;
    isLoading?: boolean;
    avatar?: string;
    userName?: string;
    status?: 'sending' | 'sent' | 'error';
    metadata?: {
      model?: string;
      [key: string]: any;
    };
    edited?: boolean;
    editedAt?: Date;
    chatId?: string;
    reactions?: string[];
    replyTo?: ChatMessage;
    mentions?: string[];
  }>;
  className?: string;
  onEditMessage?: (messageId: string, content: string) => void;
  onDeleteMessage?: (messageId: string) => void;
  onReplyMessage?: (message: ChatMessage) => void;
}

export function ModernChatList({
  messages,
  className,
  onEditMessage,
  onDeleteMessage,
  onReplyMessage,
}: ModernChatListProps) {
  const messagesEndRef = React.useRef<HTMLDivElement>(null);
  const previousMessagesRef = React.useRef<string[]>([]);
  const [newMessageIds, setNewMessageIds] = React.useState<Set<string>>(new Set());

  const scrollToBottom = React.useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, []);

  // Detectar mensagens realmente novas para animação
  React.useEffect(() => {
    const currentMessageIds = messages.map(m => m.id);
    const previousMessageIds = previousMessagesRef.current;

    // Encontrar IDs que são realmente novos
    const reallyNewIds = currentMessageIds.filter(id =>
      !previousMessageIds.includes(id)
    );

    if (reallyNewIds.length > 0) {
      setNewMessageIds(new Set(reallyNewIds));
      scrollToBottom();

      // Limpar animações após um tempo
      setTimeout(() => {
        setNewMessageIds(new Set());
      }, 500);
    }

    previousMessagesRef.current = currentMessageIds;
  }, [messages, scrollToBottom]);

  return (
    <div className={cn("flex flex-col gap-6 p-4", className)}>
      {messages.length === 0 ? (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="flex items-center justify-center h-32 text-muted-foreground"
        >
          <div className="text-center">
            <Bot className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">Nenhuma mensagem ainda</p>
            <p className="text-xs opacity-70">Comece uma conversa!</p>
          </div>
        </motion.div>
      ) : (
        <AnimatePresence mode="popLayout">
          {messages.map((message, index) => {
            // Show timestamp if it's the last message or if there's a significant time gap
            const showTimestamp = index === messages.length - 1 ||
              (messages[index + 1] && message.timestamp && messages[index + 1].timestamp &&
                Math.abs(messages[index + 1].timestamp!.getTime() - message.timestamp.getTime()) > 300000);

            const shouldAnimate = newMessageIds.has(message.id);

            return (
              <motion.div
                key={message.id}
                initial={shouldAnimate ? {
                  opacity: 0,
                  scale: 0.8,
                  y: 20,
                  x: message.isUser ? 20 : -20
                } : false}
                animate={shouldAnimate ? {
                  opacity: 1,
                  scale: 1,
                  y: 0,
                  x: 0
                } : {}}
                exit={{
                  opacity: 0,
                  scale: 0.9,
                  y: -10,
                  transition: { duration: 0.2 }
                }}
                transition={shouldAnimate ? {
                  type: "spring",
                  stiffness: 500,
                  damping: 30,
                  mass: 1,
                  duration: 0.3
                } : {}}
                layout
              >
                <ModernChatBubble
                  message={message.content}
                  isUser={message.isUser}
                  timestamp={showTimestamp ? message.timestamp : undefined}
                  isLoading={message.isLoading}
                  avatar={message.avatar}
                  userName={message.userName}
                  status={message.status}
                  metadata={message.metadata}
                  messageId={message.id}
                  chatId={message.chatId}
                  edited={message.edited}
                  editedAt={message.editedAt}
                  reactions={message.reactions}
                  replyTo={message.replyTo}
                  mentions={message.mentions}
                  onEdit={onEditMessage}
                  onDelete={onDeleteMessage}
                  onReply={onReplyMessage}
                />
              </motion.div>
            );
          })}
        </AnimatePresence>
      )}

      <div ref={messagesEndRef} />
    </div>
  );
}
